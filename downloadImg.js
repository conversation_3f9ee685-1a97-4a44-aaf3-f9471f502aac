const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

const imageUrl = 'https://thumb.pccomponentes.com/w-150-150/articles/1084/10848354/1988-oneplus-nord-3-5g-8-128gb-misty-green-libre.jpg';

(async () => {
    const browser = await puppeteer.launch();
    const page = await browser.newPage();

    try {
        // Go to the page where the image is hosted
        await page.goto(imageUrl, { waitUntil: 'networkidle2' });

        // Get image buffer from the network response
        const imageBuffer = await page.evaluate(async (imageUrl) => {
            const response = await fetch(imageUrl);
            const blob = await response.blob();
            const arrayBuffer = await blob.arrayBuffer();
            return Array.from(new Uint8Array(arrayBuffer));
        }, imageUrl);

        // Convert back to Buffer
        const buffer = Buffer.from(imageBuffer);

        // Save the image locally (or you can send it to PHP)
        const filename = path.basename(imageUrl);
        fs.writeFileSync(`./${filename}`, buffer);
        console.log(`Image saved: ${filename}`);
    } catch (error) {
        console.error('Failed to download image:', error.message);
    } finally {
        await browser.close();
    }
})();
