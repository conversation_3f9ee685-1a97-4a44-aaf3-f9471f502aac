<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ApiEndpoint>
 */
class ApiEndpointFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $services = ['GitHub', 'Stripe', 'Twilio', 'SendGrid', 'Slack', 'Discord', 'PayPal', 'AWS', 'Google', 'Facebook'];
        $endpoints = ['users', 'payments', 'messages', 'notifications', 'webhooks', 'analytics', 'reports', 'data'];

        return [
            'name' => $this->faker->randomElement($services) . ' ' . ucfirst($this->faker->randomElement($endpoints)) . ' API',
            'url' => $this->faker->url() . '/api/v1/' . $this->faker->randomElement($endpoints),
            'api_key' => 'sk_' . $this->faker->regexify('[a-zA-Z0-9]{32}'),
            'active' => $this->faker->boolean(80), // 80% chance of being active
            'count' => $this->faker->numberBetween(0, 1000),
            'description' => $this->faker->optional(0.7)->sentence(10),
        ];
    }

    /**
     * Indicate that the endpoint is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'active' => true,
        ]);
    }

    /**
     * Indicate that the endpoint is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'active' => false,
        ]);
    }
}
