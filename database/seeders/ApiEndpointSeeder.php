<?php

namespace Database\Seeders;

use App\Models\ApiEndpoint;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ApiEndpointSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some sample API endpoints
        ApiEndpoint::factory()->count(10)->create();

        // Create some specific examples
        ApiEndpoint::factory()->create([
            'name' => 'Stripe Payment API',
            'url' => 'https://api.stripe.com/v1/charges',
            'api_key' => 'sk_test_' . str_repeat('x', 32),
            'active' => true,
            'count' => 150,
            'description' => 'Stripe API for processing payments and charges.',
        ]);

        ApiEndpoint::factory()->create([
            'name' => 'GitHub Repositories API',
            'url' => 'https://api.github.com/user/repos',
            'api_key' => 'ghp_' . str_repeat('x', 36),
            'active' => true,
            'count' => 75,
            'description' => 'GitHub API for accessing user repositories.',
        ]);

        ApiEndpoint::factory()->create([
            'name' => 'SendGrid Email API',
            'url' => 'https://api.sendgrid.com/v3/mail/send',
            'api_key' => 'SG.' . str_repeat('x', 22) . '.' . str_repeat('x', 22),
            'active' => false,
            'count' => 0,
            'description' => 'SendGrid API for sending transactional emails.',
        ]);
    }
}
