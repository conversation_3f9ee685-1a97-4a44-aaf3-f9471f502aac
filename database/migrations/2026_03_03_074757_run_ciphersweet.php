<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        if (app()->runningUnitTests()) {
            return;
        }

        if (app()->isProduction()) {
            echo "This command can only be run in development environment\n";
            echo "For production, we will run this manually\n";
            return;
        }

        $process = \Symfony\Component\Process\Process::fromShellCommandline(<<<EOF
export CIPHERSWEET_KEY=$(grep CIPHERSWEET_KEY .env | cut -d '=' -f2)
php artisan ciphersweet:encrypt "App\Models\EmailProvider" \$CIPHERSWEET_KEY
php artisan ciphersweet:encrypt "App\Models\CloudProvider" \$CIPHERSWEET_KEY
php artisan ciphersweet:encrypt "App\Models\Cloudflare" \$CIPHERSWEET_KEY
php artisan ciphersweet:encrypt "App\Models\ConnectedAccount" \$CIPHERSWEET_KEY
php artisan ciphersweet:encrypt "App\Models\StorageProvider" \$CIPHERSWEET_KEY
EOF
        );
        $process->run(function ($type, $buffer) {
            echo $buffer;
        });

        $process->wait();
    }

    public function down(): void
    {

    }
};
