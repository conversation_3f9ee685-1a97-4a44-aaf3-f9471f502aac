<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    public function up(): void
    {
        if (app()->isProduction()) {
            echo "This command can only be run in development environment\n";
            echo "For production, we will run this manually\n";
            return;
        }

        $process = \Symfony\Component\Process\Process::fromShellCommandline(<<<EOF
export CIPHERSWEET_KEY=$(grep CIPHERSWEET_KEY .env | cut -d '=' -f2)
php artisan ciphersweet:encrypt "App\Models\PatchstackVulnerability" \$CIPHERSWEET_KEY
EOF
        );
        $process->run(function ($type, $buffer) {
            echo $buffer;
        });

        $process->wait();
    }

    public function down(): void
    {

    }
};
