<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Product;
use App\Enums\XcloudBilling\BillingServices;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (app()->runningUnitTests()) {
            return;
        }

        Product::all()->each(function ($product) {
            // If product exists
            if (Product::where("depends_on_product_id", $product->id)->exists()) {
                dump("Product exists: " . $product->id);
                return;
            }

            $backupService = null;
            if ($product->service_type === BillingServices::xCloudManagedHosting) {
                $backupService = BillingServices::BackupXCloudManagedHosting;
            } elseif ($product->service_type === BillingServices::xCloudProviderHosting) {
                $backupService = BillingServices::BackupXCloudProviderHosting;
            } elseif ($product->service_type === BillingServices::ManagedHosting) {
                $backupService = BillingServices::BackupManagedHosting;
            }

            if ($backupService) {
                $clonedProduct = $product->replicate(["id", "created_at", "updated_at"]);

                $clonedProduct->title = $product->title . " - (" . $backupService->toShortTitle().")";
                $clonedProduct->service_type = $backupService;
                $clonedProduct->price = $product->price * 0.2;
                $clonedProduct->depends_on_product_id = $product->id;
                $clonedProduct->sku = "bkup-" . $product->sku;

                try {
                    // Attempt to save the cloned product
                    $saved = $clonedProduct->save();

                    if ($saved) {
                        dump("New product created: " . $clonedProduct->id);
                    } else {
                        dump("Failed to save product for: " . $product->id . " (unknown error)");
                        // Log the error for further investigation
                        \Illuminate\Support\Facades\Log::error("Failed to save cloned product", [
                            'original_product_id' => $product->id,
                            'service_type' => $backupService->value
                        ]);
                    }
                } catch (\Exception $e) {
                    dump("Error saving product for: " . $product->id . " - " . $e->getMessage());
                    // Log the exception for further investigation
                    \Illuminate\Support\Facades\Log::error("Exception while saving cloned product", [
                        'original_product_id' => $product->id,
                        'service_type' => $backupService->value,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            } else {
                dump("Product: " . $product->id . " (no backup service)");
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
