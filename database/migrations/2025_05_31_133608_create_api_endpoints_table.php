<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_endpoints', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable(); // Optional name for the endpoint
            $table->text('url'); // URL endpoint
            $table->text('api_key'); // API key for authentication
            $table->boolean('active')->default(true); // Active status
            $table->integer('count')->default(0); // Counter
            $table->text('description')->nullable(); // Optional description
            $table->timestamps();

            // Add indexes for better performance
            $table->index('active');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_endpoints');
    }
};
