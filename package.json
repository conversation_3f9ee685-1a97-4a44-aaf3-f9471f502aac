{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll"}, "dependencies": {"line-awesome": "^1.3.0", "swiper": "^5.4.5", "vue-awesome-swiper": "^4.1.1", "vue-i18n": "^8.26.1", "vue-meta": "^2.4.0", "vue-numeric-input": "^1.0.6", "vue-router": "^3.5.2", "vue-slick-carousel": "^1.0.6", "vue-social-sharing": "^3.0.8", "vue-tel-input": "^5.6.2", "vue2-flip-countdown": "^0.11.2", "vuelidate": "^0.7.5", "vuetify": "^2.6.4", "vuex": "^3.6.0"}, "devDependencies": {"axios": "^0.21.4", "cross-env": "^7.0", "laravel-mix": "^5.0.4", "lodash": "^4.17.13", "prettier": "2.5.1", "resolve-url-loader": "^3.1.4", "sass": "^1.42.1", "sass-loader": "^8.0.0", "vue": "^2.6.14", "vue-facebook": "^1.1.0", "vue-template-compiler": "^2.6.14"}}