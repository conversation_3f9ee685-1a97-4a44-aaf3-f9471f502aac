<?php

use  App\Services\Provisioning\ServerProvisioning;
use App\Services\Provisioning\SiteProvisioning;

return [
    'verifying_payment' =>  [
        [
            'stage' => 'Vérification de la carte et prélèvement du paiement',
            'tasks' => [
                ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT => 'Vérification de la carte et prélèvement du paiement de $:amount',
            ]
        ]
    ],
    'server_creating' => [
        [
            'stage' => 'Création du serveur',
            'tasks' => [
                ServerProvisioning::INIT => 'Initialisation du provisionnement du serveur',
                ServerProvisioning::CREATING_SERVER => 'Création du serveur sur :provider',
                ServerProvisioning::SERVER_CREATED => 'Serveur créé :ip',
                ServerProvisioning::WAITING_FOR_SERVER_TO_START => 'En attente du démarrage du serveur',
            ]
        ]
    ],
    'server_provisioning' => [
        [
            'stage' => 'Connexion au serveur',
            'tasks' => [
                ServerProvisioning::CONNECTING => 'Connexion à SSH',
                ServerProvisioning::CONNECTED => 'Connexion établie',
            ]
        ],
        [
            'stage' => 'Configuration du serveur',
            'tasks' => [
                ServerProvisioning::CREATING_SWAP => 'Configuration du Swapfile',
                ServerProvisioning::UPGRADING_SYSTEM => 'Mise à jour du système',
                ServerProvisioning::INSTALLING_BASE => 'Installation des dépendances de base',
                ServerProvisioning::AUTHENTICATION_METHOD => 'Mise à jour de la méthode d\'authentification',
                ServerProvisioning::UPDATING_HOSTNAME => 'Mise à jour du nom d\'hôte',
                ServerProvisioning::UPDATING_TIMEZONE => 'Mise à jour du fuseau horaire',
                ServerProvisioning::XCLOUD_USER => 'Configuration des utilisateurs',

                ServerProvisioning::SETUP_SSH => 'Configuration de SSH',
                ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS => 'Configuration des permissions Sudo',
                ServerProvisioning::SETTING_UP_GIT => 'Configuration de Git',
                ServerProvisioning::SETUP_FIREWALL => 'Configuration du pare-feu',
                ServerProvisioning::SETTING_UP_CLEANING_SCRIPT => 'Configuration du script de nettoyage',
            ]
        ],
        [
            'stage' => 'Installation des applications',
            'tasks' => [
                ServerProvisioning::INSTALLING_PHP => 'Installation de PHP :php_version',
                ServerProvisioning::INSTALLING_WEBSERVER => 'Installation de :stack',
                ServerProvisioning::INSTALLING_NODE => 'Installation de Node',
                ServerProvisioning::INSTALLING_REDIS => 'Installation de Redis',
                ServerProvisioning::INSTALLING_DATABASE => 'Installation de la base de données :database_type',
                ServerProvisioning::INSTALLING_WP_CLI => 'Installation de WP CLI',
            ]
        ],
        [
            'stage' => 'Finalisation',
            'tasks' => [
                ServerProvisioning::SETTING_SSH_PERMISSIONS => 'Configuration des permissions SSH',
                ServerProvisioning::INSTALL_MONITORING_SCRIPT => 'Installation du script de surveillance',
                ServerProvisioning::READY_TO_DO_MAGIC => 'Prêt à faire de la magie !',
            ]
        ]
    ],

    'site_provisioning' => [
        [
            'stage' => 'Vérification et validation',
            'tasks' => [
                SiteProvisioning::CHECKING_STATUS => 'Vérification de l\'espace de stockage du serveur et de la connexion',
                SiteProvisioning::VERIFYING_DNS => 'Vérification des DNS pour votre site',
            ]
        ],
        [
            'stage' => 'Installation des applications',
            'tasks' => [
                SiteProvisioning::INSTALLING_PHP => 'Installation de :site_stack :site_stack_version',
                SiteProvisioning::INSTALLING_DATABASE => 'Installation de la base de données',
                SiteProvisioning::INSTALLING_WORDPRESS => 'Configuration de :type',
            ]
        ],
        [
            'stage' => 'Configuration',
            'tasks' => [
                SiteProvisioning::CONFIGURING_SSL => 'Configuration de SSL',
                SiteProvisioning::CONFIGURING_HTTPS => 'Configuration de HTTPS',
                SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => 'Configuration de :cache',
                SiteProvisioning::CONFIGURING_NGINX => 'Configuration de :stack',
                SiteProvisioning::CONFIGURING_REDIS_CACHE => 'Configuration du cache Redis d\'objets',
                SiteProvisioning::INSTALL_BLUEPRINT => 'Installation de Blueprint',
                SiteProvisioning::DEPLOY_SCRIPT => 'Script de déploiement',
                SiteProvisioning::INSTALL_MONITORING => 'Installation de la surveillance',
                SiteProvisioning::INSTALLING_WP_CRON_JOB => 'Installation du cron job WP',
                SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => 'Configuration du service de messagerie géré xCloud',
                SiteProvisioning::HANDLE_INDEXING => 'Gestion de l\'indexation du site',
                SiteProvisioning::FINISHING_UP => 'Finalisation',
            ]
        ]
    ],
];
