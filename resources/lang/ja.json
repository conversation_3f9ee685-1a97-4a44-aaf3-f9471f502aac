{"xCloud": "xCloud", "xcloud": "エックスクラウド", "Log In To": "ログイン", "Email": "メール", "Password": "パスワード", "forgot_password": "パスワードを忘れた", "Remember me": "ログイン状態を保持", "Log In": "ログイン", "Not Registered Yet? Please": "未登録ですか？ どうぞ", "Sign Up": "サインアップ", "Email Address..": "メールアドレス", "Sign Up For": "登録する", "Enter necessary informations for creating a new account": "新しいアカウントを作成するために必要な情報を入力してください。", "Name": "名前", "Use 8 or more characters with a mix of letters, numbers & symbols": "文字、数字、記号を組み合わせて8文字以上にしてください。", "Password Confirmation": "パスワード確認", "I agree to the": "利用規約に同意します", "Terms": "利用規約", "and": "および", "Privacy Policy": "プライバシーポリシー", "Already have an account? Please": "アカウントをお持ちですか？ログインしてください。", "Forgot Password": "パスワードをお忘れですか", "Please enter your email address to search for your account": "アカウントを検索するには、メールアドレスを入力してください。", "Reset Password": "パスワードをリセット", "This is a secure area of the application. Please confirm your password before continuing.": "アプリケーションの安全なエリアです。続行する前にパスワードを確認してください。", "Confirm": "確認", "Confirm Password": "パスワード確認", "Two-factor Confirmation": "2要素確認", "Please confirm access to your account by entering the authentication code provided by your authenticator application.": "認証アプリで提供された認証コードを入力して、アカウントへのアクセスを確認してください。", "Please confirm access to your account by entering one of your emergency recovery codes.": "アカウントへのアクセスを確認するために、緊急復旧コードのいずれかを入力してください。", "Code": "コード", "Recovery Code": "リカバリーコード", "Use a recovery code": "リカバリーコードを使用", "Use an authentication code": "認証コードを使用", "Verify Email Address": "メールアドレスを確認", "To continue using": "使用を続けるには", "please click on the link in the verification email sent to your email.": "確認メールに送信されたリンクをクリックしてください。", "A new verification link has been sent to the email address you provided during registration.": "登録時に提供されたメールアドレスに新しい確認リンクが送信されました。", "Please wait": "お待ちください", "seconds to resend email.": "メール再送信までの秒数", "Resend Verification Email": "認証メールを再送信", "Logout": "ログアウト", "Overview": "概要", "New Server": "新しいサーバー", "New Site": "新しいサイト", "Active Servers": "アクティブサーバー", "Total Sites": "総サイト数", "No Active Billing": "アクティブな請求なし", "Plan": "プラン", "Vulnerable Sites": "脆弱なサイト", "New Team Invitations": "新しいチーム招待状", "Server Setup In Progress": "サーバー設定中", "Site Setup In Progress": "サイト設定中", "View All": "すべて表示", "You can create 1 server and 10 sites with our Free Plan.": "無料プランでは、1つのサーバーと10のサイトを作成できます。", "Two-Factor Authentication": "2要素認証", "You have not enabled two-factor authentication, which is highly recommended for your account security.": "2要素認証が有効になっていません。アカウントのセキュリティ向上のため、設定をお勧めします。", "Skip": "スキップ", "Setup Now": "今すぐ設定", "Please check your": "確認してください", "invoices": "請求書", "has asked you to join as a": "「として参加するように求められました」", "to this team": "このチームへ", "Decline": "辞退", "Accept": "承諾", "To decline the invitation please visit": "招待を辞退するには、こちらをご覧ください", "team page": "チームページ", "Server Disk Space Low": "サーバーディスク容量不足", "Warning": "警告", "Your Server": "サーバー", "disk space is low. Please upgrade your plan to avoid any downtime.": "ディスク容量が不足しています。ダウンタイムを避けるためにプランをアップグレードしてください。", "Reboot Required": "再起動が必要です", "Security Update": "セキュリティ更新", "will automatically update and reboot on": "自動的に更新され、再起動します", "You can also reboot now.": "今すぐ再起動することもできます。", "Security Update - Server": "セキュリティ更新 - サーバー", "requires a reboot.": "再起動が必要です。", "Reboot Now": "今すぐ再起動", "Security updates were automatically installed on your server, but a reboot is required to complete the installation. The following packages were upgraded:": "サーバーにセキュリティ更新が自動的にインストールされましたが、インストールを完了するには再起動が必要です。次のパッケージがアップグレードされました：", "Be sure to test the sites on your server after rebooting.": "再起動後にサーバー上のサイトを必ずテストしてください。", "Server Size": "サーバーサイズ", "PHP": "PHP", "View Details": "詳細を表示", "Server": "サーバー", "Continue Setup": "セットアップを続ける", "View Site": "サイトを表示", "Open options": "オプションを開く", "Bill Calculator": "請求書計算機", "Billing Amount": "請求金額", "Select Renewal Period": "更新期間を選択", "Bill From": "請求元", "Bill Before": "請求書（前）", "Bill To": "請求先", "Processing payment... Please do not cancel or refresh the page.": "支払い処理中です... ページをキャンセルしたり更新したりしないでください。", "My Blueprints": "マイブループリント", "Create New Blueprint": "新しいブループリントを作成", "Created": "作成済み", "Active Plugins": "アクティブプラグイン", "Active Theme": "アクティブテーマ", "Edit Blueprint": "ブループリントを編集", "Create Blueprint": "設計図を作成", "By": "「提供」", "Active Installations": "アクティブインストール", "Update": "更新", "Create": "作成", "Staging Url": "ステージングURL", "Confirm Details": "詳細を確認", "Edit": "編集", "Old Domain": "旧ドメイン", "HTTPS": "HTTPS", "Enabled": "有効", "Disabled": "無効", "PHP Version": "PHPバージョン", "Database Type": "データベースタイプ", "Database Name": "データベース名", "Database User": "データベースユーザー", "No Database": "データベースなし", "Site User": "サイトユーザー", "Configuration & Database Connection": "設定とデータベース接続", "Create Database In Server": "サーバーにデータベースを作成", "Select Destination Server To Clone Your Site": "サイトをクローンする宛先サーバーを選択", "Choose Server": "サーバーを選択", "DNS & SSL For": "DNSとSSL用", "Demo Site": "デモサイト", "Create a demo site with our test domain and customize before going live.": "テストドメインでデモサイトを作成し、公開前にカスタマイズしてください。", "Migrate into a New Domain": "新しいドメインに移行", "Get your site up and running for the world to see by simply pointing your domain to the server.": "ドメインをサーバーにポイントするだけで、サイトを世界中に公開できます。", "Add DNS and SSL Certificate on Cloudflare": "CloudflareでDNSとSSL証明書を追加", "Integrate Cloudflare for Automatic DNS and SSL management.": "Cloudflareを統合して自動DNSおよびSSL管理を行う", "DNS Setup": "DNS設定", "Settings & Configurations": "設定と構成", "Deploy Script (Optional)": "デプロイ スクリプト (オプション)", "wp plugin install woocommerce": "WooCommerce プラグインをインストール", "Verifying": "検証中", "Uploading....": "アップロード中...", "Upload/Drop Your Zipped sql or only .sql File": ".sqlファイルまたは圧縮されたsqlファイルをアップロード/ドロップしてください", "Maximum File Size: 500 MB": "最大ファイルサイズ: 500 MB", "Use These Credentials To Transfer Database": "データベース転送用の認証情報を使用してください", "Server Connection Url": "サーバー接続URL", "Server Address": "サーバーアドレス", "Server SSH Host": "サーバーSSHホスト", "Server Username": "サーバーユーザー名", "Server database name": "サーバーデータベース名", "Database Password": "データベースパスワード", "Verify Upload": "アップロードを確認", "Upload/Drop Your Zipped/Tar File": "ZIP/TARファイルをアップロード/ドロップ", "Use These Credentials To Upload Your WordPress Website": "WordPressサイトをアップロードするには、これらの認証情報を使用してください。", "22": "22", "Port": "ポート", "Remote Path": "リモートパス", "Upload Migration Files": "マイグレーションファイルをアップロード", "Choose a method from below to install the": "以下の方法からインストールを選択してください", "Migration Plugin": "マイグレーションプラグイン", "Optional": "オプション", "Authentication Token": "認証トークン", "Plugin Page URL": "プラグインページURL", "I have added the authentication token to my site": "認証トークンをサイトに追加しました。", "Please agree that you have added the authentication token to your site": "サイトに認証トークンを追加したことに同意してください。", "Download Plugin ZIP File": "プラグイン ZIP ファイルをダウンロード", "Plugin Link": "プラグインリンク", "WP CLI": "WP CLI", "Show Previous cPanel Migrations": "以前のcPanel移行を表示", "Username of the cPanel account:": "cPanelアカウントのユーザー名:", "Enter your cPanel username": "cPanelユーザー名を入力してください", "cPanel Api Key:": "cPanel APIキー:", "Enter your cPanel API Key": "cPanel APIキーを入力してください", "cPanel Host:": "cPanel ホスト:", "Fetch Existing Backups": "既存のバックアップを取得", "Select a backup to import from the list below": "以下のリストからインポートするバックアップを選択してください", "Backup in Progress ...": "バックアップ中...", "No Backups Found": "バックアップが見つかりません", "You need to create a backup in your cPanel account first": "まず、cPanelアカウントでバックアップを作成する必要があります。", "Generate Backup": "バックアップを生成", "Migration File": "マイグレーションファイル", "Created At": "作成日時", "Actions": "アクション", "Continue Migration": "移行を続ける", "No cPanel Migrations Found": "cPanel の移行が見つかりません", "You can create a new cPanel Migration from the cPanel Migration tab": "cPanel移行タブから新しいcPanel移行を作成できます。", "Upload Completed": "アップロード完了", "Upload/Drop Your Zipped File": "圧縮ファイルをアップロード/ドロップ", "Regenerate cPanel User": "cPanelユーザーを再生成", "I have started the backup": "バックアップを開始しました。", "Waiting for the backup to complete": "バックアップの完了を待っています", "Currently transferring this file from generated backup for cpanel migration": "cPanel移行のために生成されたバックアップからこのファイルを転送中", "New cPanel Migration using SCP found, click here to continue": "新しいcPanel移行がSCPで見つかりました。続行するにはここをクリックしてください。", "Live Site": "ライブサイト", "Domain Setup": "ドメイン設定", "Domain Name": "ドメイン名", "Auto DNS management by Cloudflare is disabled for Full Server Migration.": "フルサーバー移行のため、Cloudflareによる自動DNS管理は無効になっています。", "Coming soon...": "近日公開予定...", "Your DNS setup and SSL Certificate will be done by Cloudflare and managed by xCloud.": "DNS設定とSSL証明書はCloudflareによって行われ、xCloudによって管理されます。", "Confirm Migration Details": "移行の詳細を確認", "Source site": "ソースサイト", "Destination site": "宛先サイト", "Select Websites to Migrate": "移行するウェブサイトを選択", "to": "「宛先」", "Previously migrated sites from": "以前に移行されたサイトから", "Available Sites": "利用可能なサイト", "Select All": "すべて選択", "Fetch Websites": "ウェブサイトを取得", "Fetching...": "取得中...", "Wordpress Sites To Migrate": "移行するWordPressサイト", "Type": "タイプ", "Directory": "ディレクトリ", "Non-Wordpress Sites": "非WordPressサイト", "Fetching sites...": "サイトを取得中...", "Add Websites to Migrate": "移行するウェブサイトを追加", "Add the domains you want to migrate from your Cpanel backup, we will search these domains and migrate to the new server": "Cpanelバックアップから移行したいドメインを追加してください。これらのドメインを検索し、新しいサーバーに移行します。", "Add Website": "ウェブサイトを追加", "Connect Your Source Server": "ソースサーバーを接続", "Submit": "送信", "Select your Hosting Provider": "ホスティングプロバイダーを選択", "cPanel Migration Method": "cPanel 移行方法", "Set domain for migration": "移行用ドメインを設定", "Full Server Migration In Progress from": "サーバー全体の移行中", "Migration completed successfully from": "移行が正常に完了しました：", "Successfully Migrated Sites": "サイトの移行が成功しました", "Migration In Progress": "移行中", "Migration In Queue": "キュー内の移行", "Migration Failed": "移行に失敗しました", "IP Address": "IPアドレス", "SSH Port": "SSHポート", "SSH Username": "SSHユーザー名", "root": "ルート", "SSH Authentication": "SSH認証", "SSH Password": "SSHパスワード", "Size": "サイズ", "GB": "GB", "Existing Site": "既存のサイト", "Full Page Cache": "フルページキャッシュ", "Redis Object Cache": "Redisオブジェクトキャッシュ", "Default Database In Server": "サーバーのデフォルトデータベース", "Database": "データベース", "Admin User Name": "管理者ユーザー名", "Files and Database Migration": "ファイルおよびデータベース移行", "Select Databases & File Systems To Migrate": "移行するデータベースとファイルシステムを選択", "Migrate The Following Content": "次のコンテンツを移行", "All Database Tables & Corresponding File System": "すべてのデータベーステーブルと対応するファイルシステム", "Only File System But NOT Database Tables": "ファイルシステムのみ、データベーステーブルは含まない", "Select Destination Server To Migrate Your Site": "サイトを移行する宛先サーバーを選択", "Existing WordPress Site URL": "既存のWordPressサイトURL", "Existing Site URL": "既存サイトURL", "Git Repository": "Git リポジトリ", "master": "マスター", "Git Branch": "Gitブランチ", "Enable push to deploy": "デプロイのためのプッシュを有効にする", "Deployment URL": "デプロイメントURL", "npm run deploy": "デプロイを実行", "Deploy Script": "スクリプトをデプロイ", "Run this script after every site deployment": "各サイトのデプロイ後にこのスクリプトを実行してください", "Select Databases To Migrate": "移行するデータベースを選択", "Add Your Existing Database": "既存のデータベースを追加", "Without Database": "データベースなし", "Install Migration Plugin": "マイグレーションプラグインをインストール", "Buy Now": "今すぐ購入", "Packages": "パッケージ", "Products": "製品", "Bills & Payment": "請求書と支払い", "Free plan which includes 1 server and 10 website with Self Hosting.": "セルフホスティングで1サーバーと10ウェブサイトを含む無料プラン", "You’re on the": "現在、あなたは", "Activate your": "アクティベート", "team by adding payment method today.": "チームに参加するには、今日中に支払い方法を追加してください。", "team by adding payment method.": "支払い方法を追加してチームに参加", "Estimated Cost": "推定費用", "This is an estimate of the amount based on your current month-to-date": "これは、当月の実績に基づく金額の見積もりです。", "Cost This Month": "今月の費用", "Cost Next Month": "来月の費用", "Overused Amount After 28th": "28日以降の超過額", "Billing Period Monthly": "請求期間: 月次", "renews 29": "29日更新", "Read how billing works.": "請求の仕組みを読む", "Subscriptions": "サブスクリプション", "xCloud Managed Email Provider": "xCloud 管理メールプロバイダー", "Services": "サービス", "Payment Methods": "支払い方法", "You can add Credit/Debit Card as your payment method": "お支払い方法としてクレジット/デビットカードを追加できます。", "Expires at": "有効期限", "Default": "デフォルト", "Set As Default": "デフォルトに設定", "Add A Payment Method": "お支払い方法を追加", "Billing History": "請求履歴", "Description": "説明", "Date": "日付", "Service Status": "サービスステータス", "Amount": "金額", "Payment Status": "支払い状況", "Next Billing": "次回請求", "Renewal Period": "更新期間", "Link a Credit/Debit Card": "クレジット/デビットカードをリンクする", "We welcome Visa, Mastercard, American Express, Discover, Diners Club along with China UnionPay (CUP), Japan Credit Bureau (JCB)": "ビザ、マスターカード、アメリカン・エキスプレス、ディスカバー、ダイナースクラブ、中国銀聯（CUP）、ジェーシービー（JCB）を受け付けています。", "Service": "サービス", "Amount Payable": "支払金額", "discontinued on": "販売終了日", "to lifetime": "ライフタイムへ", "I want to convert this bill to monthly and unlock full features": "この請求書を月額に変換して、すべての機能をアンロックしたい", "Please add a": "追加してください", "payment": "支払い", "method unlock more features.": "機能をアンロックする方法", "Amount Paid": "支払額", "Whitelabel Subscriptions": "ホワイトラベルサブスクリプション", "Current Plan": "現在のプラン", "Sell Up to": "最大販売数量", "Servers": "サーバー", "Subscription Date": "サブスクリプション日付", "Expires on": "有効期限:", "Your subscription is about to expire please renew it": "ご利用のサブスクリプションがまもなく期限切れです。更新してください。", "Your subscription is about to expire please renew it for uninterrupted service.": "ご利用のサブスクリプションがまもなく期限切れになります。サービスを継続するには更新してください。", "Pay Now": "今すぐ支払う", "No active subscription found": "アクティブなサブスクリプションが見つかりません", "Claim Free with LTD": "LTDで無料請求", "Server Limit": "サーバー制限", "As an existing LTD customer, you are eligible to claim this plan for free.": "既存のLTD顧客として、このプランを無料で利用できます。", "Switch Free with LTD": "LTDで無料に切り替え", "Switch to this plan for": "このプランに切り替える", "Choose a plan": "プランを選択", "SMTP Username": "SMTPユーザー名", "SMTP Password": "SMTPパスワード", "Domain": "ドメイン", "Sendgrid Username": "Sendgrid ユーザー名", "Sendgrid Api Key": "Sendgrid APIキー", "SMTP Host": "SMTPホスト", "SMTP Port": "SMTPポート", "Label": "ラベル", "Get help from our": "サポートを受ける", "Configure SMTP Provider": "SMTPプロバイダーを設定", "documentation": "ドキュメント", "From Email": "送信元メール", "To Email": "メール送信先", "Email Subscriptions": "メール購読", "With xCloud you get 100 free emails per month in each team. You can purchase additional email more from": "xCloudでは、各チームごとに毎月100通の無料メールを利用できます。追加のメールを購入することも可能です。", "here.": "「ここ」", "xCloud Email Balance": "xCloud メール残高", "This is a summary of your xCloud Email Balance.": "こちらは、xCloudメール残高の概要です。", "Total Emails": "総メール数", "Emails Available": "利用可能なメール", "Elastic Email API Key": "Elastic Email APIキー", "Invoice": "請求書", "Details": "詳細情報", "You will pay for following bills": "次の請求書を支払います", "which are under": "以下のもの", "Next Billing Amount": "次回請求額", "Amount Adjusted": "調整済み金額", "Add New Member": "メンバーを追加", "Send Invitation": "招待を送信", "Server Access": "サーバーアクセス", "Access to all server": "すべてのサーバーへのアクセス", "Choose specific server": "特定のサーバーを選択", "Select here...": "こちらを選択...", "Site Access": "サイトアクセス", "Access to all site": "サイト全体へのアクセス", "Choose specific site": "特定のサイトを選択", "Role Permission": "ロール権限", "Create New Team": "新しいチームを作成", "Save Changes": "変更を保存", "Upload Your Avatar": "アバターをアップロード", "Add a profile picture or icon for your account": "アカウントにプロフィール写真またはアイコンを追加", "Upload Image": "画像をアップロード", "Team Name": "チーム名", "Team Email": "チームメール", "Tags": "タグ", "Select or create tags": "タグを選択または作成", "Edit Team": "チームを編集", "Update Member": "メンバーを更新", "Update Invitation": "招待の更新", "Email ID": "メールID", "User Role": "ユーザー役割", "Delete Team": "チームを削除", "Leave Team": "チームを離れる", "Team Members": "チームメンバー", "Role": "役割", "Status": "ステータス", "Server Count": "サーバー数", "Action": "アクション", "Change Role": "役割を変更", "Delete": "削除", "Resend Email": "メールを再送信", "Site": "サイト", "Switch to Team": "チームに切り替え", "Add Member": "メンバーを追加", "Check Details": "詳細を確認", "All Sites": "すべてのサイト", "Refresh": "更新", "Archive Servers": "アーカイブサーバー", "List of Archive Servers": "アーカイブサーバーの一覧", "Find all the archive servers associated with your team here.": "ここでチームに関連するすべてのアーカイブサーバーを見つけてください。", "Provider": "プロバイダー", "If you proceed, this will permanently remove this service and you will not be able to access or retrieve it again.": "進行すると、このサービスが永久に削除され、再度アクセスしたり取得したりすることはできません。", "Authentication": "認証", "Please enter the two-factor authentication code sent to you below to verify:": "認証するために、送信された二要素認証コードを以下に入力してください。", "Billing Details": "請求情報", "Saved Cards": "保存済みカード", "Add your payment methods for billing.": "お支払い方法を追加してください。", "Set as Default": "デフォルトに設定", "No card available": "カードがありません", "Add Payment Method": "支払い方法を追加", "Bills": "請求書", "Find complete, downloadable receipts of your regular payments": "定期支払いの領収書をダウンロード可能な形式で見つける", "Title": "タイトル", "Amount To Pay": "支払額", "Due On": "期限日", "Next Billing Date": "次回請求日", "Paid On": "支払日", "Manage Active Browser Sessions": "アクティブなブラウザセッションを管理", "Check which sessions are still active from different devices & browsers, and manage as needed.": "異なるデバイスやブラウザからアクティブなセッションを確認し、必要に応じて管理します。", "Note: You can log out of all your active sessions; it will also log you out of this session and you will have to log back in to continue using": "注: すべてのアクティブなセッションからログアウトできます。このセッションからもログアウトされるため、続けて使用するには再度ログインする必要があります。", "Log Out Of All Sessions": "すべてのセッションからログアウト", "Browser Name": "ブラウザー名", "Last Session": "最終セッション", "This device": "このデバイス", "Last active": "最終アクティブ", "Cloudflare Integration": "Cloudflare統合", "List of your Cloudflare Integrations": "Cloudflare統合の一覧", "Find all the cloudflare integrations associated with your account here.": "ここで、アカウントに関連付けられたすべてのCloudflare統合を見つけることができます。", "New Cloudflare Integration": "新しいCloudflare統合", "Account Email": "アカウントメール", "API Token": "APIトークン", "Global API Key": "グローバルAPIキー", "Origin CA Key": "オリジン CA キー", "Add New Cloudflare Integration": "新しいCloudflare統合を追加", "Name of the integration": "統合の名前", "Your Cloudflare Account Email": "Cloudflareアカウントのメールアドレス", "Create a new API Token from your Cloudflare account and paste it here. (With Edit zone DNS permissions)": "Cloudflareアカウントから新しいAPIトークンを作成し、ここに貼り付けてください。（ゾーン編集権限付き）", "Go to your Cloudflare Profile > API Tokens > Global API Key > View": "Cloudflare プロフィール > API トークン > グローバル API キー > 表示", "Go to your Cloudflare Profile > API Tokens > Origin CA Key > View": "Cloudflare プロフィール > API トークン > Origin CA キー > 表示", "Integrate Cloudflare For DNS Management": "DNS管理のためにCloudflareを統合", "Account email": "アカウントメール", "Find complete, downloadable receipts of your regular payments.": "定期支払いの領収書をダウンロードできます。", "All Invoices": "すべての請求書", "This Month": "今月", "Last Month": "先月", "Last 6 Months": "過去6か月", "Last 1 Year": "過去1年", "Paid Invoices": "支払済み請求書", "Unpaid Invoices": "未払い請求書", "Failed Invoices": "失敗した請求書", "Invoice No": "請求書番号", "Download": "ダウンロード", "Cancel": "キャンセル", "Email Provider": "メールプロバイダー", "List of Email Providers": "メールプロバイダー一覧", "Find all the email providers associated with your account here.": "ここでアカウントに関連付けられているすべてのメールプロバイダーを見つけることができます。", "Add New Provider": "新しいプロバイダーを追加", "Username/Domain": "ユーザー名/ドメイン", "Invitation List": "招待リスト", "Invite Users": "ユーザーを招待", "Share your invitation code with others. You have": "招待コードを他の人と共有しましょう。あなたには", "invitation": "招待状", "remaining": "残り", "Invited Users": "招待されたユーザー", "You have invited following persons.": "次のユーザーを招待しました。", "Accepted": "承認済み", "Notifications": "通知", "List of Notifications": "通知リスト", "Integrate your notification platform. You can customize your": "通知プラットフォームを統合します。カスタマイズ可能です", "notification settings": "通知設定", "from": "から", "here": "こちら", "Add New Notification": "新しい通知を追加", "Disconnect": "切断", "Reconnect": "再接続", "Add Notification": "通知を追加", "Being with Country Code (e.g., ****** XXX XXXX for US)": "国コードから始める（例: 米国の場合 ****** XXX XXXX）", "WhatsApp Phone Number": "WhatsApp電話番号", "Server Notifications": "サーバー通知", "For server reboots, unavailable servers, and available upgrades are included": "サーバーの再起動には、利用不可のサーバーと利用可能なアップグレードが含まれます。", "Telegram": "テレグラム", "WhatsApp": "WhatsApp", "Slack": "スラック", "Newly Provisioned Servers": "新規プロビジョニングされたサーバー", "Site Notifications": "サイト通知", "For site upgrades, SSL certificate issues, and deployment errors": "サイトのアップグレード、SSL証明書の問題、デプロイメントエラー", "Other Notifications": "その他の通知", "Get notified about team accounts and actions": "チームアカウントとアクションの通知を受け取る", "Do Not Send Sensitive Information": "機密情報を送信しないでください", "This option will disable sending sensitive options like, sudo password, database password, wp-admin password over email and notification channels": "このオプションを選択すると、sudoパスワード、データベースパスワード、wp-adminパスワードなどの機密情報をメールや通知チャネルで送信することが無効になります。", "Vulnerability Notifications": "脆弱性通知", "Enable this option to receive notifications about vulnerabilities via Email": "このオプションを有効にして、脆弱性に関する通知をメールで受け取る", "Stay informed and take timely action to secure your systems": "システムを保護するために、情報を常に把握し、適切なタイミングで行動してください。", "Team Packages": "チームパッケージ", "Renewal type": "更新タイプ", "Price Per Server": "サーバーごとの価格", "Total Price": "合計金額", "Change Password": "パスワードを変更", "Enter a strong password to keep your profile locked": "プロファイルをロックするために強力なパスワードを入力してください", "Current Password": "現在のパスワード", "New Password": "新しいパスワード", "Pay Bills": "請求書を支払う", "Choose Your Payment Method": "お支払い方法を選択", "You are paying for following invoice": "次の請求書を支払っています", "Total Amount": "合計金額", "Team Products": "チーム製品", "Service Type": "サービス種別", "Price": "価格", "Role Management": "ロール管理", "Server Provider": "サーバープロバイダー", "List of Server Providers": "サーバープロバイダー一覧", "Find all the server providers associated with your account here.": "ここでアカウントに関連付けられているすべてのサーバープロバイダーを見つけることができます。", "Servers Count": "サーバー数", "Select Server Provider": "サーバープロバイダーを選択", "Vultr API Key": "Vultr APIキー", "API Key": "APIキー", "Hetzner API Key": "Hetzner APIキー", "Hetzner Label": "ヘッツナーラベル", "Upload JSON": "JSONをアップロード", "Label for AWS Credential": "AWS認証情報のラベル", "AWS Access Key": "AWSアクセスキー", "Access Key": "アクセスキー", "AWS Secret Key": "AWSシークレットキー", "Secret Key": "シークレットキー", "Setup A Digital Ocean Server In xCloud": "xCloudでDigital Oceanサーバーをセットアップ", "Setup A Vultr Server In xCloud": "xCloudでVultrサーバーをセットアップ", "Setup A GCP Server In xCloud": "xCloudでGCPサーバーをセットアップ", "SSH Keys": "SSHキー", "Add New SSH Key": "新しいSSHキーを追加", "ID": "ID", "Fingerprint": "指紋", "Auto Provision": "自動プロビジョニング", "Used By": "使用者", "A name to recognize this public key": "この公開鍵を識別する名前", "Key Name": "キー名", "Public Key": "公開鍵", "Don't have a key?": "キーをお持ちではありませんか？", "Learn how to": "使い方を学ぶ", "generate an SSH Key": "SSHキーを生成", "Already have a key?": "キーをお持ちですか？", "Copy and paste your key here with": "ここにキーをコピー＆ペーストしてください", "Always provision to new servers": "常に新しいサーバーにプロビジョニングする", "Select servers to push this key to": "このキーをプッシュするサーバーを選択", "Default Sudo User": "デフォルトのSudoユーザー", "Default Sudo Password": "デフォルトのSudoパスワード", "If you proceed, this will permanently remove this SSH Key and you will not be able to access or retrieve it again.": "進行すると、このSSHキーが永久に削除され、再度アクセスしたり取得したりすることはできません。", "Following sudo users will be deleted": "次のsudoユーザーが削除されます", "SSH key will be removed from the following sites": "SSHキーは、以下のサイトから削除されます", "Storage Provider": "ストレージプロバイダー", "List of Storage Providers": "ストレージプロバイダー一覧", "Bucket Name": "バケット名", "Region": "地域", "Site Count": "サイト数", "bucket": "バケット", "Enter the name of the bucket you have in your storage provider.": "ストレージプロバイダーにあるバケットの名前を入力してください。", "Select the data location": "データの場所を選択", "Select Region": "地域を選択", "Enter the access key id here.": "ここにアクセスキーIDを入力してください。", "Access Key Id": "アクセスキーID", "Enter the secret key here.": "ここに秘密キーを入力してください。", "Endpoint": "エンドポイント", "Enter the endpoint url here and make sure to add https:// in url.": "エンドポイントURLをここに入力し、URLにhttps://を追加してください。", "Site Backup": "サイトバックアップ", "Enter the region here.": "地域を入力してください。", "Team Invitations": "チーム招待", "Team Management": "チーム管理", "Add New Team": "新しいチームを追加", "It looks like you haven’t created any team yet. Create one now.": "チームがまだ作成されていないようです。今すぐ作成しましょう。", "Profile": "プロフィール", "General Information": "一般情報", "Set up your profile by providing the following information": "次の情報を入力してプロフィールを設定してください。", "Contact Number": "連絡先番号", "Extra Billing Information": "追加請求情報", "If you require the inclusion of specific contact or tax details on your receipts, such as your VAT identification number, or registered address, you may add it here.": "領収書に特定の連絡先情報や税務情報（VAT識別番号や登録住所など）を含める必要がある場合は、ここに追加できます。", "Name on Invoice": "請求書の名前", "Billing Emails": "請求書メール", "If": "「もし」", "Send billing invoices only to the team email address": "請求書はチームのメールアドレスにのみ送信してください。", "is unchecked, billing invoices will be sent to the your given email addresses, separated by commas. Example:": "未選択の場合、請求書は指定されたメールアドレスに送信されます（カンマで区切ります）。例:", "remaining.": "残り", "You have invited the following persons.": "次のユーザーを招待しました。", "Note": "メモ", "You can again enable this service.": "このサービスを再度有効にできます。", "Execute": "実行", "Proceeding will permanently delete all data stored on this server and all WordPress sites under it, which can’t be recovered later. Full Server Backups created for this server will also be deleted.": "この操作を続行すると、このサーバーに保存されているすべてのデータと、その下にあるすべてのWordPressサイトが完全に削除され、後で復元することはできません。このサーバー用に作成されたサーバーの完全バックアップも削除されます。", "Delete From Provider": "プロバイダーから削除", "Enable this to delete this server from the provider also.": "これを有効にして、プロバイダーからもこのサーバーを削除します。", "Type server name to confirm": "サーバー名を入力して確認", "Delete DNS records from your Cloudflare account": "CloudflareアカウントからDNSレコードを削除", "Your DNS records for the sites on this server will be deleted from your Cloudflare account.": "このサーバー上のサイトのDNSレコードは、Cloudflareアカウントから削除されます。", "Add site": "サイトを追加", "sites": "サイト", "Storage": "ストレージ", "Ram": "RAM", "No sites": "サイトなし", "Add New Site": "新しいサイトを追加", "View Sites": "サイトを表示", "Restart Server": "サーバーを再起動", "Hard Reboot Server": "サーバーを強制再起動", "Restart Nginx": "Nginxを再起動", "Restart LiteSpeed": "LiteSpeedを再起動", "Restart MySQL": "MySQLを再起動", "Restart MariaDB": "MariaDBを再起動", "Archive Server": "アーカイブサーバー", "Clone Server": "サーバーをクローン", "Delete Server": "サーバーを削除", "View Server": "サーバーを表示", "Individual Site Details": "個別サイト詳細", "Site Name": "サイト名", "CPU": "CPU", "RAM": "RAM", "DISK": "ディスク", "LAST UPDATED": "最終更新日", "EC2": "EC2", "Flexible, scalable virtual servers for all workloads.": "すべてのワークロードに対応する柔軟でスケーラブルな仮想サーバー。", "Lightsail": "ライトセイル", "Easy, budget-friendly servers for small projects.": "小規模プロジェクト向けの手頃な価格で簡単なサーバー。", "Set Up Your Server With": "サーバーのセットアップ", "Connect xCloud with your AWS Account": "xCloudをAWSアカウントに接続", "Connect New Account": "新しいアカウントを接続", "Verified": "認証済み", "Next": "次へ", "Provider Label": "プロバイダーラベル", "Connected": "接続済み", "Select AWS Service": "AWSサービスを選択", "Please choose an AWS service to create an instance.": "インスタンスを作成するためにAWSサービスを選択してください。", "Choose region": "地域を選択", "Choose Region": "地域を選択", "Choose Zone": "ゾーンを選択", "Loading zones...": "ロード中のゾーン...", "Choose Server Size": "サーバーサイズを選択", "Loading server types...": "サーバータイプを読み込み中...", "Select Database Server": "データベースサーバーを選択", "Select Tags": "タグを選択", "I have understood that the billing of this server will be handled on my server provider account.": "このサーバーの請求は、私のサーバープロバイダーアカウントで処理されることを理解しました。", "Demo Server for Billing Plan": "請求プラン用デモサーバー", "Connect xCloud with your Digital Ocean account": "xCloudをDigital Oceanアカウントに接続", "Existing DigitalOcean Credential": "既存のDigitalOcean認証情報", "Choose Credential": "資格を選択", "Add new credential": "新しい資格情報を追加", "Authorize on Digital Ocean": "デジタルオーシャンで認証", "You do not have permission to add new provider": "新しいプロバイダーを追加する権限がありません。", "Enable Digital Ocean": "デジタルオーシャンを有効化", "Auto Backups": "自動バックアップ", "Connect xCloud with your Google Cloud Platform account": "xCloudをGoogle Cloud Platformアカウントに接続", "Select Existing or Connect New": "既存を選択または新規接続", "Finish": "完了", "Choose your project": "プロジェクトを選択", "Choose Project": "プロジェクトを選択", "Connect xCloud with your Hetzner Account": "xCloudをHetznerアカウントに接続", "Choose Account": "アカウントを選択", "Hetzner API Token": "Hetzner API トークン", "Connect xCloud with your Linode (Akamai) account": "xCloudをLinode（Akamai）アカウントに接続", "Existing Linode Credential": "既存のLinode認証情報", "Authorize on Linode (Akamai)": "<PERSON><PERSON>（Akamai）で認証", "Connect xCloud with your Linode Account": "xCloudをLinodeアカウントに接続", "Vultr Label": "Vultrラベル", "Connect xCloud with your Vultr Account": "xCloudをVultrアカウントに接続", "Enable Vultr": "Vultrを有効にする", "I am sure that I've read the": "読んだことを確信しています", "and added Any": "追加済み", "and Any": "「および」「任意」", "both under Access Control.": "アクセス制御の下で両方", "Fill in the details below to get your server set up with xCloud": "以下の詳細を入力して、xCloudでサーバーを設定してください。", "Note down the generated database root password above and secure it as it will not be displayed again.": "生成されたデータベースのルートパスワードを上記にメモし、再表示されないため安全に保管してください。", "Server Details": "サーバーの詳細", "Server Tag(Optional)": "サーバータグ（任意）", "Server Type": "サーバータイプ", "General": "一般設定", "Cost-effective servers powered by Intel CPUs and regular SSDs.": "インテルCPUと通常のSSDを搭載したコスト効率の高いサーバー。", "Premium": "プレミアム", "Blazing-fast servers with quicker 3GHz+ Intel Xeon CPUs, speedy memory, and NVMe storage.": "3GHz以上のIntel Xeon CPU、高速メモリ、NVMeストレージを搭載した超高速サーバー。", "Billed Monthly": "月額請求", "SSD": "SSD", "vCPU": "vCPU", "Bandwidth": "帯域幅", "Recommended Option": "推奨オプション", "Backup": "バックアップ", "Enable xCloud Auto Backups": "xCloud自動バックアップを有効にする", "Total Cost": "合計費用", "Coupon Code": "クーポンコード", "Apply": "適用", "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks.": "自動バックアップを無効にすると、簡単なデータ復旧、バックアップの移動性、災害復旧が失われます。リスクを理解した上で進むにはクリックしてください。", "Disable Auto Backup": "自動バックアップを無効にする", "Choose Your Continent": "大陸を選択", "Enter Code Here...": "コードを入力してください…", "Fill in the details below to get your server set up with": "以下の詳細を入力して、サーバーをセットアップしてください", "Connect Your Existing Server": "既存のサーバーを接続", "Fill in the details below to connect xCloud with your existing fresh Ubuntu server from any cloud provider.": "以下の詳細を入力して、任意のクラウドプロバイダーから既存のUbuntuサーバーにxCloudを接続してください。", "You will need to add the xCloud public key to allow server setup access. Please SSH to the server using the default/root account and use the following command to add the public key.": "サーバー設定アクセスを許可するために、xCloudの公開鍵を追加する必要があります。デフォルト/ルートアカウントを使用してサーバーにSSH接続し、次のコマンドを使用して公開鍵を追加してください。", "I have created a new server on my provider": "プロバイダーで新しいサーバーを作成しました。", "Choose Your Server Provider": "サーバープロバイダーを選択", "Choose Hosting Managed by": "ホスティング管理を選択", "Everything You Need to Create a Website": "ウェブサイト作成に必要なすべて", "Bring and Manage Your Own Server": "サーバーを持ち込み管理する", "Integrate Your Own Provider.": "独自のプロバイダーを統合", "Manage": "管理", "First Server": "最初のサーバー", "10 Sites": "10 サイト", "without any cost.": "無料で", "Create More, Pay Less upto": "もっと作成、支払いは少なく", "Start": "開始", "LTD Server Created": "LTDサーバーが作成されました", "Create from package": "パッケージから作成", "You’re on the xCloud Free plan, which includes 1 server and 10 websites with self-hosting. Activate your": "xCloud Freeプランをご利用中です。このプランには、1台のサーバーと10のセルフホスティング可能なウェブサイトが含まれています。あなたの", "team by adding a": "チームに追加", "payment method": "支払い方法", "today.": "今日", "team by adding": "チームに追加", "payment method.": "支払い方法", "Choose Provider": "プロバイダーを選択", "Only Available for LTD Users": "LTDユーザーのみ利用可能", "is pending for setup.": "セットアップ待ちです。", "Automatic Reboot": "自動再起動", "Enable Backup": "バックアップを有効にする", "Disable Backup": "バックアップを無効にする", "This setting is to enable/disable the backup settings of cloud provider.": "この設定はクラウドプロバイダーのバックアップ設定を有効化/無効化します。", "The charge will be": "料金は", "for this server.": "このサーバー用", "You will be charged based on the provider policy.": "プロバイダーポリシーに基づいて料金が発生します。", "To prevent system abuse, the backup feature cannot be repeatedly turned on and off. Contact our support if you need any help.": "システムの悪用を防ぐため、バックアップ機能を繰り返しオン/オフにすることはできません。サポートが必要な場合は、サポートにお問い合わせください。", "Next Backup Schedule": "次回バックアップ予定", "Select Backup Time (UTC)": "バックアップ時間を選択 (UTC)", "Backup List on Cloud Provider": "クラウドプロバイダーのバックアップリスト", "No backup found.": "バックアップが見つかりません。", "If you enable backup then you will be charged": "バックアップを有効にすると料金が発生します。", "for this.": "これ用", "based on the provider policy.": "プロバイダーポリシーに基づく", "If you disable backup then you will not be able to restore the backup for the server. However, you can enable it anytime.": "バックアップを無効にすると、サーバーのバックアップを復元できなくなります。ただし、いつでも有効にすることができます。", "If you proceed, this will restore the backup for the server. This operation is irreversible.": "進行すると、サーバーのバックアップが復元されます。この操作は元に戻せません。", "Do you want to set the backup schedule for the server": "サーバーのバックアップスケジュールを設定しますか？", "Backup Schedule Details": "バックアップスケジュールの詳細", "Some features may not work due to a payment issue.": "支払いの問題により、一部の機能が動作しない場合があります。", "Payment failed": "支払いに失敗しました", "Please update your": "アップデートしてください", "to retry billing.": "請求を再試行", "Note: Your billing period has been extended until": "注: 請求期間が延長されました。", "Please pay your outstanding invoices": "未払いの請求書をお支払いください。", "to avoid any service interruptions.": "サービスの中断を避けるために", "Add Cron Job": "Cronジョブを追加", "Command": "コマンド", "You can add wget and curl commands here. But if you want to add wp-cli commands then you need to specify the site location with cd /var/www/sitename && wp command.": "ここに wget や curl コマンドを追加できます。ただし、wp-cli コマンドを追加する場合は、サイトの場所を指定して cd /var/www/sitename && wp コマンドを使用する必要があります。", "User": "ユーザー", "Frequency": "周波数", "Custom Schedule": "カスタムスケジュール", "Save": "保存", "Add Sudo User": "管理者ユーザーを追加", "Sudo User Name": "スードユーザー名", "Sudo Password": "管理者パスワード", "Add SSH Key": "SSHキーを追加", "No SSH Keys are available. Please add SSH Key.": "SSHキーがありません。SSHキーを追加してください。", "Cron Job": "クロンジョブ", "Scroll to end": "最後までスクロール", "Scroll to top": "トップにスクロール", "Last checked": "最終確認日時", "Add Database": "データベースを追加", "Loading...": "読み込み中...", "No associated site": "関連サイトなし", "No database found!": "データベースが見つかりません！", "Database Users": "データベースユーザー", "No database user found!": "データベースユーザーが見つかりません！", "xcloud_db": "xcloud_db", "Database User Name": "データベースユーザー名", "User (Optional)": "ユーザー（任意）", "Password (Required with user)": "パスワード（ユーザー必須）", "Add Database User": "データベースユーザーを追加", "Can Access": "アクセス可能", "Keep empty to keep the same password": "パスワードを変更しない場合は空のままにしてください", "If you proceed, this is permanently removed and you will not be able to access or retrieve it again.": "進行すると、これは完全に削除され、再度アクセスしたり取得したりすることはできません。", "Update Cron Job": "Cronジョブを更新", "Update Sudo User": "スードユーザーを更新", "Reset Sudo Password": "Sudoパスワードをリセット", "Add New Rule": "新しいルールを追加", "Protocol": "プロトコル", "Traffic": "交通", "Active": "アクティブ", "Fail2Ban Management": "Fail2Ban管理", "Ban New IP Address": "新しいIPアドレスを禁止", "Banned IP Addresses": "禁止されたIPアドレス", "No banned IP addresses.": "禁止されたIPアドレスはありません。", "SSH": "SSH", "port": "ポート", "You can use multiple ports separated by comma. Leave empty to allow all ports. Also you can use a range of ports by using colon(i.e 6000:7000).": "カンマで区切って複数のポートを使用できます。すべてのポートを許可するには空欄のままにしてください。また、コロンを使用してポートの範囲を指定することもできます（例：6000:7000）。", "IP Address (Optional)": "IPアドレス（任意）", "Valid IP address": "有効なIPアドレス", "You can use multiple IP addresses separated by comma. Leave empty to allow all IP addresses. Also you can use a subnet. (i.e *************, *************, *************/24)": "複数のIPアドレスをカンマで区切って使用できます。すべてのIPアドレスを許可する場合は空のままにしてください。また、サブネットも使用できます。（例：*************, *************, *************/24）", "All": "すべて", "TCP": "TCP", "UDP": "UDP", "Allow": "許可", "Deny": "拒否", "Adding Rule...": "ルールを追加中...", "Add Rule": "ルールを追加", "Enter the IP address you want to ban. You can use comma to separate multiple IP addresses (e.g xx.xx.xx.xx, xx.xx.xx.xx)": "禁止したいIPアドレスを入力してください。複数のIPアドレスを入力する場合は、カンマで区切ってください（例: xx.xx.xx.xx, xx.xx.xx.xx）。", "Banning IP...": "IPを禁止しています...", "Ban IP": "IPを禁止", "All Servers": "すべてのサーバー", "All Web Servers": "すべてのWebサーバー", "Nginx": "<PERSON><PERSON><PERSON>", "OpenLiteSpeed": "OpenLiteSpeed", "Provisioned": "プロビジョニング済み", "Provisioning": "プロビジョニング", "Upgrade Server": "サーバーをアップグレード", "Server Name": "サーバー名", "Choose Your Server Size": "サーバーサイズを選択", "Be cautious when upgrading to a larger hosting plan since you can’t switch back. Also, it’s crucial to back up important data before trying to resize partitions, which can be risky.": "ホスティングプランを大きなものにアップグレードする際は注意してください。一度変更すると元に戻せません。また、パーティションのサイズ変更を試みる前に重要なデータをバックアップすることが重要です。これはリスクを伴う可能性があります。", "Can not perform server resize action on disconnected server.": "切断されたサーバーでは、サーバーのサイズ変更を実行できません。", "Current Status": "現在のステータス", "After Upgrading": "アップグレード後", "RAM Size": "RAMサイズ", "SSD Space": "SSD容量", "Server Utilities": "サーバーユーティリティ", "Delete your Server": "サーバーを削除", "This action is irreversible, and will also remove the server from your provider, and all data will be erased. We recommend that you keep a backup before you proceed.": "この操作は元に戻せません。また、プロバイダーからサーバーが削除され、すべてのデータが消去されます。続行する前にバックアップを取ることをお勧めします。", "Warning: Before you upgrade": "警告: アップグレードする前に", "This will require a HARD restart and the process can take up to 15 min. It is recommended to take a full server backup of your server before proceeding.": "これはハード再起動が必要で、プロセスには最大15分かかる可能性があります。進行する前に、サーバーの完全バックアップを取ることをお勧めします。", "New Plan": "新しいプラン", "If you upgrade now, you’ll be charged": "今すぐアップグレードすると、料金が発生します", "Provider Name": "プロバイダー名", "Ubuntu Version": "Ubuntuバージョン", "MySQL Version": "MySQLバージョン", "Full Server Migrations": "サーバー完全移行", "Initiate Full Server Migration": "サーバー全体の移行を開始", "Public Ip": "パブリックIP", "Updated": "更新済み", "In Use": "使用中", "Available": "利用可能", "Utilization": "利用", "Cores": "コア", "Speed/Core": "速度/コア", "Threads": "スレッド", "Hard Disk Usage": "ハードディスク使用量", "Total": "合計", "Used": "使用済み", "Uptime Overview": "稼働時間の概要", "Select your site's PHP version to update php settings on the sites.": "サイトの PHP バージョンを選択して、サイトの PHP 設定を更新してください。", "Max Execution Time": "最大実行時間", "The maximum time in seconds a script is allowed to run before it is terminated by the parser. We recommend 60 seconds.": "スクリプトがパーサーによって終了されるまでの最大実行時間（秒）。推奨値は60秒です。", "Max Input Time": "最大入力時間", "The maximum time in seconds a script is allowed to parse input data, like POST and GET. We recommend 60 seconds.": "スクリプトがPOSTやGETの入力データを解析する際の最大時間（秒）。推奨値は60秒です。", "Max Input Vars": "最大入力変数", "The maximum number of input variables allowed per request. We recommend 1000 vars.": "リクエストごとに許可される入力変数の最大数。推奨は1000変数です。", "Memory Limit": "メモリ制限", "The maximum amount of memory a script may consume. We recommend 256MB.": "スクリプトが使用できる最大メモリ量。推奨値は256MBです。", "Post Max Size": "投稿最大サイズ", "The maximum size of POST data that PHP will accept. We recommend 128MB.": "PHP が受け入れる POST データの最大サイズです。128MB を推奨します。", "Max File Upload Size": "最大ファイルアップロードサイズ", "The maximum size of an uploaded file. We recommend 128MB.": "アップロードファイルの最大サイズ。推奨サイズは128MBです。", "Session GC Maxlifetime": "セッションGC最大寿命", "The maximum time in seconds a session is allowed to be idle before it is terminated by the session garbage collector. We recommend 1440 seconds.": "セッションがセッションガベージコレクタによって終了される前にアイドル状態で許可される最大時間（秒）。推奨時間は1440秒です。", "Important Note": "重要なお知らせ", "Enabling PHP OPCache will highly improve performance by storing precompiled scripts (PHP Code) in shared memory.": "PHP OPCacheを有効にすると、事前コンパイルされたスクリプト（PHPコード）を共有メモリに保存することで、パフォーマンスが大幅に向上します。", "If you allow optimizing your OPCache, you need to make sure that your deployment parse code reloads the PHP FPM services at the end of each development.": "OPCacheの最適化を許可する場合、各開発の終了時にデプロイメントがPHP FPMサービスをリロードするようにしてください。", "Disable OPCache": "OPCacheを無効にする", "Enable OPCache": "OPCacheを有効にする", "Server Statistics": "サーバー統計", "Disable it to turn off magic login feature on all sites under this server.": "このサーバー上のすべてのサイトでマジックログイン機能を無効にするには、これをオフにしてください。", "Back To Servers": "サーバーに戻る", "Team": "チーム", "WEB SERVER": "Webサーバー", "Disk Usage": "ディスク使用量", "Checked": "チェック済み", "Check Again": "再確認", "Your disk space is low. Please upgrade your plan to avoid any downtime.": "ディスク容量が不足しています。ダウンタイムを避けるためにプランをアップグレードしてください。", "Close": "閉じる", "No sites on this server": "このサーバーにはサイトがありません", "Get started by creating a new site!": "新しいサイトを作成して始めましょう！", "Sudo Users": "管理者ユーザー", "Enable Vulnerability Scan": "脆弱性スキャンを有効にする", "Enable Auto Update": "自動更新を有効にする", "If vulnerability found we will update in 24 hours": "脆弱性が見つかった場合、24時間以内に更新します。", "Enable Auto Backup": "自動バックアップを有効にする", "Select All Sites": "すべてのサイトを選択", "Choose a Server to Clone Sites from": "サイトをクローンするサーバーを選択", "Team Details": "チーム詳細", "Create a new team to collaborate with others on projects.": "プロジェクトで他のユーザーと協力するために新しいチームを作成します。", "If you proceed, this will remove all the services and sites along with other data which cannot be recovered.": "続行すると、すべてのサービスとサイトが削除され、他のデータも復元できなくなります。", "I have understood and would like to proceed.": "理解しました。進みます。", "Permanently delete this team.": "このチームを完全に削除します。", "Once a team is deleted, all of its resources and data will be permanently deleted. Before deleting this team, please download any data or information regarding this team that you wish to retain.": "チームを削除すると、そのすべてのリソースとデータが完全に削除されます。このチームを削除する前に、保持したいデータや情報をダウンロードしてください。", "Are you sure you want to delete this team? Once a team is deleted, all of its resources and data will be permanently deleted.": "このチームを削除してもよろしいですか？チームを削除すると、そのすべてのリソースとデータが完全に削除されます。", "Add Team Member": "チームメンバーを追加", "Add a new team member to your team, allowing them to collaborate with you.": "新しいチームメンバーを追加して、共同作業を行いましょう。", "Please provide the email address of the person you would like to add to this team.": "このチームに追加したい人のメールアドレスを入力してください。", "Added.": "追加済み。", "Add": "追加", "Pending Team Invitations": "保留中のチーム招待状", "These people have been invited to your team and have been sent an invitation email. They may join the team by accepting the email invitation.": "以下のユーザーがチームに招待され、招待メールが送信されました。メールの招待を承諾すると、チームに参加できます。", "All of the people that are part of this team.": "このチームの全メンバー", "Leave": "退出", "Remove": "削除", "Manage Role": "役割を管理", "Are you sure you would like to leave this team?": "このチームを退出してもよろしいですか？", "Remove Team Member": "チームメンバーを削除", "Are you sure you would like to remove this person from the team?": "このメンバーをチームから削除してもよろしいですか？", "The team's name and owner information.": "チーム名と所有者情報", "Saved.": "保存済み。", "Create Team": "チームを作成", "Team Settings": "チーム設定", "Join the Exclusive Waitlist!": "限定ウェイトリストに参加！", "Your Email Address..": "メールアドレス", "Join Waitlist": "ウェイトリストに参加", "Already have been invited?": "招待されていますか？", "Login to xCloud": "xCloudにログイン", "When you cancel migration, already migrated data will be removed from the server.": "移行をキャンセルすると、すでに移行されたデータはサーバーから削除されます。", "Cancel Migration": "移行をキャンセル", "Auth": "認証", "Cache": "キャッシュ", "Git": "Git", "Proceeding will permanently delete this Site and all of its data.": "進行すると、このサイトとそのすべてのデータが永久に削除されます。", "Delete All Files and Configurations.": "すべてのファイルと設定を削除", "Do You Want To Delete Files?": "ファイルを削除しますか？", "Delete Database": "データベースを削除", "Do You Want To Delete Database?": "データベースを削除しますか？", "Delete Site User": "サイトユーザーを削除", "Do You Want To Delete User?": "ユーザーを削除しますか？", "Delete Local Backups": "ローカルバックアップを削除", "Do You Want To Delete Local Backups?": "ローカルバックアップを削除しますか？", "Type site name to confirm": "サイト名を入力して確認", "Delete DNS record from your Cloudflare account": "CloudflareアカウントからDNSレコードを削除", "Your DNS record for the site on this server will be deleted from your Cloudflare account.": "このサーバー上のサイトのDNSレコードは、Cloudflareアカウントから削除されます。", "Proceeding will disable this Site.": "進行すると、このサイトが無効になります。", "Disable Site": "サイトを無効にする", "Also Disable Cron For This Site": "このサイトのCronも無効にする", "Disable HTML Markup": "HTMLマークアップを無効化", "After turning on Disable Site, you won’t be able to use this site on the web anymore along with losing access through SFTP/SSH. Plus, all the scheduled tasks for the site will be paused once you turn on Disable Cron For This Site.": "サイトを無効化をオンにすると、このサイトをウェブ上で使用できなくなり、SFTP/SSH経由でのアクセスも失われます。また、「このサイトのCronを無効化」をオンにすると、サイトのすべてのスケジュールされたタスクが一時停止されます。", "Disable": "無効にする", "Site is currently disabled.": "サイトは現在無効です。", "Click here": "ここをクリック", "to enable this site": "このサイトを有効にする", "The maximum amount of memory a script may consume. we recommend 128MB.": "スクリプトが使用できる最大メモリ量。推奨値は128MBです。", "The number of seconds after which data will be seen as 'garbage' and potentially cleaned up. we recommend 1440 seconds.": "データが「不要」と見なされ、削除される可能性があるまでの秒数です。1440秒を推奨します。", "Max PHP Workers": "最大PHPワーカー数", "The number of PHP workers (pm.max_children) that can be spawned to handle requests.": "リクエストを処理するために生成できるPHPワーカーの数 (pm.max_children)。", "Redirect Type": "リダイレクトタイプ", "Choose Redirect Type": "リダイレクトタイプを選択", "From": "From: 送信元", "To": "「To」を日本語でソフトウェアやアプリのインターフェース用に翻訳する場合、文脈に応じて適切な単語を選ぶ必要があります。以下は一般的な翻訳例です：\n\n- 「へ」: 方向や目的地を示す場合\n- 「に」: 目的や対象を示す場合\n\n具体的な文脈があれば、より適切な翻訳を提供できます。", "View": "表示", "Magic Login": "マジックログイン", "Site Logs": "サイトログ", "Site Events": "サイトイベント", "Purge Cache": "キャッシュを消去", "Clone Site": "サイトをクローン", "Delete Site": "サイトを削除", "Create New Database": "新しいデータベースを作成", "Database Cluster Name": "データベースクラスター名", "Cluster Name": "クラスタ名", "Select Existing Database Cluster": "既存のデータベースクラスターを選択", "Additional Domain Name": "追加ドメイン名", "Additional Domain (Optional)": "追加ドメイン（オプション）", "Add Domain": "ドメインを追加", "Admin Password": "管理者パスワード", "Admin Email Address": "管理者メールアドレス", "WordPress Version": "WordPress バージョン", "Prefix": "プレフィックス", "wp_": "wp_", "Full Page Caching": "フルページキャッシュ", "Redis Object Caching": "Redisオブジェクトキャッシング", "Could not connect to the database server. Please check your database credentials and try again.": "データベースサーバーに接続できませんでした。データベースの認証情報を確認して、再試行してください。", "Database Username": "データベースユーザー名", "Database Host": "データベースホスト", "Database Port": "データベースポート", "Connection URL": "接続URL", "Please use the database connection URL provided to establish a connection between your database client and the database. We suggest using TablePlus as your database client.": "データベースクライアントとデータベースを接続するには、提供されたデータベース接続URLを使用してください。データベースクライアントとしてTablePlusの使用をお勧めします。", "Add the following record to the DNS for your domain. This required when when you want to get a free SSL certificate issued & managed by": "ドメインのDNSに次のレコードを追加してください。これは、無料のSSL証明書を発行および管理するために必要です。", "If you have manually added DNS record with Cloudflare proxy then the verify option will not work": "CloudflareプロキシでDNSレコードを手動で追加した場合、検証オプションは機能しません。", "Record": "記録", "A": "「A」", "Verify My DNS": "DNSを確認", "In your domain settings, you need to add the following records for configuring your site email.": "ドメイン設定で、サイトのメールを設定するために次のレコードを追加する必要があります。", "Record(SPF)": "レコード(SPF)", "TXT": "テキスト", "Value": "値", "CNAME": "CNAME", "Record(DKIM)": "レコード（DKIM）", "Record(DMARC)": "レコード(DMARC)", "Verify Record": "レコードを確認", "Use free SSL certificate issued & managed by": "無料のSSL証明書を発行・管理する", "I will provide the certificate and manage it myself.": "証明書を提供し、自分で管理します。", "Certificate": "証明書", "Paste Your Certificate Here": "証明書をここに貼り付けてください", "Private Key": "秘密鍵", "Paste Your Private Key Here": "秘密鍵をここに貼り付けてください", "Enabling HTTPS makes your website more secure.": "HTTPSを有効にすると、ウェブサイトのセキュリティが向上します。", "Learn more": "詳細を見る", "Beta": "ベータ", "Install New WordPress Website": "新しいWordPressサイトをインストール", "Select this option if you want to a create a fresh new WordPress website": "新しいWordPressサイトを作成する場合は、このオプションを選択してください。", "Clone a Git Repository": "Gitリポジトリをクローン", "Clone a git repository to create a new website": "Gitリポジトリをクローンして新しいウェブサイトを作成", "Migrate An Existing WordPress Website": "既存のWordPressサイトを移行", "Have an existing website already? Select this option to migrate it with ": "既存のウェブサイトがありますか？このオプションを選択して移行します。", "Manually Upload WordPress Website": "WordPressサイトを手動でアップロード", "Upload a zipped file of your existing WordPress website": "既存のWordPressサイトのZIPファイルをアップロードしてください", "Migrate Full Server": "サーバー全体を移行", "Migrate all WordPress sites from Ubuntu servers with a few clicks": "数回クリックするだけで、すべてのWordPressサイトをUbuntuサーバーから移行", "Certificate Issue": "証明書発行", "Expires On": "有効期限", "Renew Date": "更新日", "Add Your New Site Into": "新しいサイトを追加", "This server created under xCloud Free plan which includes 1 server and": "このサーバーは、1つのサーバーを含むxCloud Freeプランで作成されました。", "website with Self Hosting. To unlock full features, please consider upgrading the server bill from free to paid plan.": "セルフホスティング対応のウェブサイト。すべての機能を利用するには、サーバープランを無料から有料にアップグレードしてください。", "Your": "あなたの", "is low. Please upgrade your plan to avoid any downtime.": "残量が少なくなっています。ダウンタイムを避けるためにプランをアップグレードしてください。", "Choose a Server to add Site": "サイトを追加するサーバーを選択", "Site Title": "サイトタイトル", "New Site Title": "新しいサイトタイトル", "Add Tag (optional)": "タグを追加（任意）", "Go Live": "ライブ配信開始", "(Optional)": "（オプション）", "Integrate Cloudflare forAutomatic DNS and SSL management.": "Cloudflareを統合して自動DNSおよびSSL管理を行う。", "If you’re using different additional domains that are not part of primary domain, then you need to switch to xCloud SSL.": "プライマリドメインに含まれない追加ドメインを使用している場合は、xCloud SSLに切り替える必要があります。", "WordPress Multisite": "WordPress マルチサイト", "Enable Multisite": "マルチサイトを有効にする", "Select Multisite Type": "マルチサイトタイプを選択", "You need to install SSL for Multisite Subdomain after site creation from the site SSL/HTTPS page.": "サイト作成後、サイトのSSL/HTTPSページからマルチサイトサブドメイン用のSSLをインストールする必要があります。", "Enhancing Website Performance": "ウェブサイトのパフォーマンス向上", "Speed up your website by using smart caching!": "スマートキャッシュを使用してウェブサイトを高速化しましょう！", "Combine Full Page and Redis to make your site work faster and give your visitors a better experience.": "フルページとRedisを組み合わせて、サイトを高速化し、訪問者により良い体験を提供しましょう。", "Enable LiteSpeed Cache to make your site work faster and give your visitors a better experience.": "LiteSpeedキャッシュを有効にして、サイトの速度を向上させ、訪問者により良い体験を提供しましょう。", "LiteSpeed Cache": "LiteSpeedキャッシュ", "Email Provider Configuration": "メールプロバイダー設定", "Get free 100 emails/month to kickstart your business. Disable this option if you want to use your own domain.": "ビジネスを始めるために、毎月100通の無料メールを取得できます。独自ドメインを使用する場合は、このオプションを無効にしてください。", "Read our documentation": "ドキュメントを読む", "Blueprints": "設計図", "Choose a blueprint to install WordPress with pre-configured plugins and themes.": "WordPressをインストールするためのテンプレートを選択し、プラグインとテーマを事前設定します。", "All Blueprints": "すべてのブループリント", "Manage your all blueprints as you like, you can edit, delete or create new from here": "設計図を自由に管理できます。ここで編集、削除、新規作成が可能です。", "OK": "OK", "Create Your New Site Into": "新しいサイトを作成", "xCloud Playground": "xCloud プレイグラウンド", "Playground Environment": "プレイグラウンド環境", "This demo site will expire 24 hours after creation.": "このデモサイトは作成後24時間で期限切れになります。", "Staging Domain Setup": "ステージングドメイン設定", "This will be auto-generated according to your site title": "サイトタイトルに基づいて自動生成されます", "Speed up your website by using smart caching! Combine Full Page and Redis to make your site work faster and give your visitors a better experience.": "スマートキャッシングを使用してウェブサイトを高速化しましょう！フルページとRedisを組み合わせて、サイトをより速く動作させ、訪問者により良い体験を提供します。", "Step 1 of 3": "ステップ 1/3", "Hey you are trying to access the playground site": "プレイグラウンドサイトにアクセスしようとしています。", "but you are not in the": "参加していません", "We request you to check your email and accept the invitation to join and edit the site.": "メールを確認し、サイトに参加して編集するための招待を承諾してください。", "Enable Adminer": "Adminerを有効にする", "Adminer can enhance your database management experience. Activate it only when necessary to ensure optimal security measures.": "データベース管理を向上させるAdminerを、必要な時だけ有効化して最適なセキュリティ対策を確保しましょう。", "Database Manager": "データベースマネージャー", "Manage your databases with Adminer, an opensource database management tool.": "オープンソースのデータベース管理ツール、Adminerでデータベースを管理しましょう。", "Launch Adminer": "Adminerを起動", "Basic Authentication": "基本認証", "Protect Your Site": "サイトを保護", "Turn on to enable basic authentication for your site by adding username and password.": "ユーザー名とパスワードを追加して、サイトの基本認証を有効にするにはオンにします。", "Previous Remote Backups": "以前のリモートバックアップ", "Backup Now": "今すぐバックアップ", "File": "ファイル", "Previous Local Backups": "以前のローカルバックアップ", "Remote Backup Settings": "リモートバックアップ設定", "Backup Type": "バックアップタイプ", "Select Bucket": "バケットを選択", "Server Bucket": "サーバーバケット", "Manage Your Storage Providers": "ストレージプロバイダーを管理", "Backup Items": "アイテムのバックアップ", "Database Backup": "データベースバックアップ", "will be backed up.": "バックアップされます。", "Files Backup": "ファイルバックアップ", "Exclude Paths": "パスを除外", "Automatic Backup": "自動バックアップ", "Automatic Full Backup": "自動フルバックアップ", "Select Backup Frequency": "バックアップ頻度を選択", "Select Full Backup Frequency": "フルバックアップ頻度を選択", "Automatic Incremental Backup": "自動インクリメンタルバックアップ", "Automatic Delete": "自動削除", "Delete After Days": "日数後に削除", "30": "30", "Local Backup Settings": "ローカルバックアップ設定", "Select Incremental Backup Frequency": "増分バックアップ頻度を選択", "Change": "変更", "Additional Domains": "追加ドメイン", "Add new": "新規追加", "After changing the domain, you need to install SSL for Multisite Subdomain from the site SSL/HTTPS page.": "ドメインを変更した後、サイトのSSL/HTTPSページからマルチサイトサブドメイン用のSSLをインストールする必要があります。", "Connect New Provider": "新しいプロバイダーを接続", "Use xCloud Domain": "xCloudドメインを使用", "Use Your Own Domain": "独自ドメインを使用", "Requires Verification": "認証が必要です", "From Name": "送信者名", "This is the name that will appear in the recipient's inbox.": "受信者の受信箱に表示される名前です。", "To use your own domain, please verify your domain with xCloud by following the DNS setup below.": "独自ドメインを使用するには、以下のDNS設定に従ってxCloudでドメインを確認してください。", "Clear Provider": "プロバイダーをクリア", "We only use the Fluent SMTP plugin to configure your given SMTP credentials on the sites, ": "サイト上で指定されたSMTP資格情報を設定するために、Fluent SMTPプラグインのみを使用しています。", "so that you do not have to do anything manually. If you are not getting emails then please ": "手動で行う必要がないようにします。メールが届かない場合は、", "check your email logs from your email provider. To test your email integration, please send a test email from your fluentsmtp plugin.": "メールプロバイダーからメールログを確認してください。メール統合をテストするには、FluentSMTPプラグインからテストメールを送信してください。", "Enable File Manager": "ファイルマネージャーを有効にする", "Tiny File Manager": "小型ファイルマネージャー", "Activate it only when necessary to ensure optimal security measures.": "必要な場合にのみ有効化して、最適なセキュリティ対策を確保してください。", "File Manager": "ファイルマネージャー", "Manage your files, an opensource tool.": "ファイルを管理する、オープンソースツール。", "Launch File Manager": "ファイルマネージャーを起動", "Page Caching": "ページキャッシュ", "FastCGI Nginx": "FastCGI Nginx", "Cache Duration": "キャッシュ期間", "Unit": "ユニット", "Cache Exclusion HTTP URL Rules": "キャッシュ除外 HTTP URL ルール", "Cache Exclusion Cookie Rules": "キャッシュ除外クッキールール", "Clear Page Cache": "ページキャッシュをクリア", "This will slow down your site until the caches are rebuilt.": "キャッシュが再構築されるまで、サイトの速度が低下します。", "Purging Cache...": "キャッシュを消去中...", "Action will be available once the Object Cache operation is finished": "オブジェクトキャッシュの操作が完了すると、アクションが利用可能になります。", "Object Cache": "オブジェクトキャッシュ", "Redis User": "Redisユーザー", "Redis Password": "Redisパスワード", "Redis Object Cache Key": "Redisオブジェクトキャッシュキー", "Redis object cache optimize performance, reduces database load, and enhances response times for a seamless browsing experience.": "Redisオブジェクトキャッシュは、パフォーマンスを最適化し、データベース負荷を軽減し、応答時間を向上させ、シームレスなブラウジング体験を提供します。", "Stores the results of queries to the site’s database.": "サイトのデータベースクエリの結果を保存します。", "Clear Object Cache": "オブジェクトキャッシュをクリア", "Git Settings": "Git設定", "Pull and deploy now": "今すぐプルしてデプロイ", "Updated At": "更新日時", "Production": "生産", "Staging": "ステージング", "Demo": "デモ", "Vulnerable": "脆弱", "Migrating": "移行中", "Add IP Address": "IPアドレスを追加", "No IP addresses added yet.": "まだIPアドレスが追加されていません。", "IP Addresses": "IPアドレス", "Updating...": "更新中...", "Adding...": "追加中...", "LiteSpeed Cache for WordPress (LSCWP) is an all-in-one site acceleration plugin, featuring an exclusive server-level cache and a collection of optimization features.": "WordPress用LiteSpeed Cache（LSCWP）は、サーバーレベルの専用キャッシュと最適化機能を備えたオールインワンのサイト高速化プラグインです。", "Clear LiteSpeed Cache": "LiteSpeedキャッシュをクリア", "Showing logs from": "ログの表示元", "Loading..": "読み込み中...", "WordPress Debug Log": "WordPress デバッグログ", "Reload": "リロード", "Clear": "クリア", "Clearing..": "クリア中...", "Your Site Has Been Successfully Migrated": "サイトの移行が完了しました", "Staging URL": "ステージングURL", "Setup Email for this site": "このサイトのメールを設定", "Monitoring Stats": "統計の監視", "CPU Usages": "CPU使用率", "SSL Overview": "SSL概要", "DNS Status": "DNSステータス", "SSL Status": "SSLステータス", "Expiry": "有効期限", "WordPress Logs": "WordPressログ", "Updates": "アップデート", "Update Available": "アップデートがあります", "Up To Date": "最新", "Plugins": "プラグイン", "Themes": "テーマ", "Custom Nginx Config": "カスタム Nginx 設定", "Fetching Nginx..": "Nginxを取得中...", "Preview Nginx": "プレビュー <PERSON><PERSON>x", "Add a New Config": "新しい構成を追加", "Select Config Type": "設定タイプを選択", "Config File Name": "設定ファイル名", "Config Content": "設定コンテンツ", "Preview Content": "コンテンツをプレビュー", "Running...": "実行中...", "Run & Debug": "実行とデバッグ", "Nginx Config File": "Nginx設定ファイル", "Save Settings": "設定を保存", "Nginx & Security": "Nginxとセキュリティ", "Security": "セキュリティ", "7G Firewall": "7Gファイアウォール", "8G Firewall": "8Gファイアウォール", "Disable Nginx File Regeneration": "Nginxファイル再生成を無効化", "PHP Execution on Upload Directory": "アップロードディレクトリでのPHP実行", "Enable XML-RPC": "XML-RPCを有効にする", "Edit X-Frame-Options": "X-Frame-Optionsを編集", "Please use one of: SAMEORIGIN, DENY, ALLOWALL, ALLOW-FROM https://example.com or ALLOW-FROM https://example2.com https://example3.com": "以下のいずれかを使用してください: SAMEORIGIN、<PERSON><PERSON><PERSON>、ALLOW<PERSON><PERSON>、ALLOW-FROM https://example.com または ALLOW-FROM https://example2.com https://example3.com", "With xCloud you get 100 emails free per month in each team. Manage email for this site from": "xCloudを利用すると、各チームで毎月100通のメールを無料で利用できます。このサイトのメール管理は次から行えます：", "You can purchase more or add your own email providers from": "追加購入するか、独自のメールプロバイダーを追加できます:", "Learn more from our": "詳細情報はこちらから", "Primary Domain": "プライマリドメイン", "Add New": "新規追加", "Others": "その他", "Page Cache": "ページキャッシュ", "on": "オン", "off": "オフ", "SSL/HTTPS": "SSL/HTTPS", "Basic Auth": "ベーシック認証", "No Updates": "更新なし", "Most Recent Event": "最新のイベント", "View All Events": "すべてのイベントを表示", "Redirection": "リダイレクト", "Add Redirection": "リダイレクトを追加", "Redirections": "リダイレクト", "Edit Redirection": "リダイレクトの編集", "WP-Cron": "WP-クロン", "WP-Cron manages time-based tasks in WordPress, relying on site visits.": "WP-Cronは、WordPressで時間に基づくタスクを管理し、サイト訪問に依存します。", "If you want to also add additional custom cronjob then you can configure on your server from": "カスタムのcronジョブを追加したい場合は、サーバーで設定できます。", "Edit Site Tags": "サイトタグを編集", "WP Debug": "WPデバッグ", "Enable WP Debug": "WPデバッグを有効にする", "Enable this to view WordPress debug logs on the": "このオプションを有効にして、WordPressのデバッグログを表示します", "site's logs page": "サイトのログページ", "Delete Site Confirmation": "サイト削除の確認", "This action is irreversible. Delete sites cannot be restored.": "この操作は元に戻せません。削除したサイトは復元できません。", "Rescue Site": "救助サイト", "Run Now": "今すぐ実行", "Repair Site User": "修理サイトユーザー", "Update Directory Permissions": "ディレクトリ権限を更新", "Repair PHP": "PHP修復", "Regenerate": "再生成", "It is recommended to keep the above options turned on before running the rescue action.": "救助アクションを実行する前に、上記のオプションをオンにしておくことをお勧めします。", "Back To Sites": "サイトに戻る", "staging": "ステージング", "Add Staging environment": "ステージング環境を追加", "Visit Site": "サイトを訪問", "Deploy Staging": "ステージングをデプロイ", "WordPress": "WordPress", "Archive": "アーカイブ", "Are You Sure You Want To Deploy Staging for  ": "ステージングをデプロイしてもよろしいですか？", "A staging site is an identical copy of your production website, created for testing purposes. This isolated environment lets you test any plugin, or theme, or make any other changes before going live. This removes the risk of damaging your production website. Later, you can pull/push updates and apply to your production.": "ステージングサイトは、本番サイトと同一のコピーで、テスト目的で作成されます。この独立した環境で、プラグインやテーマのテスト、その他の変更を本番公開前に行うことができます。これにより、本番サイトを損なうリスクを排除できます。その後、更新をプル/プッシュして本番サイトに適用できます。", "Staging Site Domain": "ステージングサイトドメイン", "Preparing Deployment..": "デプロイを準備中...", "Site SSH/sFTP Access": "サイトSSH/sFTPアクセス", "Site Username": "サイトユーザー名", "Site Path": "サイトパス", "SSH String": "SSH文字列", "Database URL Connection": "データベースURL接続", "DNS Setup For Multisite SSL": "マルチサイトSSLのDNS設定", "Provide your own certificate & manage it yourself": "独自の証明書を提供し、自分で管理する", "Use Cloudflare managed SSL Certificate.": "Cloudflare 管理の SSL 証明書を使用する", "Demo Environment": "デモ環境", "Staging Environment": "ステージング環境", "Demo Site Domain": "デモサイトドメイン", "Staging Domain": "ステージングドメイン", "If you’re using different additional domains then you need to switch to xCloud SSL. With Cloudflare this is not supported.": "異なる追加ドメインを使用している場合は、xCloud SSLに切り替える必要があります。Cloudflareではサポートされていません。", "Staging Management": "ステージング管理", "Pull Data from Production to Staging": "本番環境からステージング環境へデータを取得", "Copy the changes from the live site to staging": "ライブサイトからステージングに変更をコピー", "Pulling Data...": "データを取得中...", "Pull Data": "データ取得", "Push Data from Staging to Production": "ステージングから本番環境へデータをプッシュ", "Copy the changes from the staging to live": "ステージングから本番環境へ変更をコピー", "Pushing Data...": "データをプッシュしています...", "Push Data": "データ送信", "Deployment Logs": "デプロイメントログ", "View All Logs": "すべてのログを表示", "Pull data from ": "データを取得元:", "Files": "ファイル", "Overwrite": "上書き", "Incremental": "インクリメンタル", "Full": "「フル」", "Selected Tables": "選択されたテーブル", "fetching tables...": "テーブルを取得しています...", "Fetching tables...": "テーブルを取得しています...", "Initiating Pulling...": "プルを開始しています...", "Push data from this Staging Site to the Production site(": "このステージングサイトから本番サイトへデータをプッシュ", "Push to Production": "本番環境にプッシュ", "Your staging site data will be pushed to production. The production site may be temporarily unavailable for a while.": "ステージングサイトのデータが本番環境にプッシュされます。本番サイトは一時的に利用できなくなる場合があります。", "Select Tables": "テーブルを選択", "Take backup of your production site before pushing changes(Recommended).": "変更をプッシュする前に本番サイトのバックアップを取ってください（推奨）。", "Initiating Pushing...": "プッシュを開始しています...", "Source Site": "ソースサイト", "Destination Site": "宛先サイト", "Initiated By": "開始者", "Inactive": "非アクティブ", "WordPress Core": "WordPress コア", "Your current version is": "現在のバージョンは", "Updating to": "アップデート中", "Your WordPress": "あなたのWordPress", "is up to date.": "最新の状態です。", "Version": "バージョン", "Changelog": "変更履歴", "Activating...": "アクティベート中...", "Activate": "有効化", "Activated": "有効化済み", "Vulnerability detection is currently disabled for this site. To enable it, please visit Server>Security Settings or": "このサイトの脆弱性検出は現在無効になっています。有効にするには、サーバー＞セキュリティ設定にアクセスしてください。", "click here": "こちらをクリック", "Insecure": "不安定", "Secure": "安全", "Last Scan": "最終スキャン", "An Updated Version of WordPress is Available": "WordPressの新しいバージョンが利用可能です", "CVSS Score": "CVSS スコア", "Ignored": "無視されました", "Ignore": "無視する", "Patched": "修正済み", "Fixed in": "修正済み", "Remediation": "修復", "Direct URL": "直接URL", "First Published": "初回公開", "Last Update": "最終更新", "Update Config": "設定を更新", "WP Config": "WP設定", "Invoices of Clients": "クライアントの請求書", "Here you can check all your previous invoices": "ここで過去の請求書をすべて確認できます", "Copy": "コピー", "View Nova": "ノヴァを表示", "Total Clients": "総クライアント数", "Total Products": "総製品数", "Total Servers": "総サーバー数", "You don’t have any server yet": "サーバーがまだありません", "Sites": "サイト", "You don’t have any site yet": "サイトがまだありません", "Clients Overview": "クライアント概要", "Total Active Clients": "総アクティブクライアント数", "Total Inactive Clients": "非アクティブクライアント総数", "Total Invoices": "請求書合計", "You don’t have any client yet": "まだクライアントがいません。", "Access Dashboard & Website": "ダッシュボードとウェブサイトにアクセス", "Domain Settings": "ドメイン設定", "Visit Landing Page": "ランディングページを訪問", "Dashboard": "ダッシュボード", "Brand Setup": "ブランド設定", "Brand Profile": "ブランドプロフィール", "Add Your Logo": "ロゴを追加", "Upload a logo that represent your brand profile.": "ブランドプロフィールを表すロゴをアップロードしてください。", "Please upload a logo with a minimum size of 212x40 pixels in PNG, JPEG, or JPG format (max 5MB) for best quality.": "最適な品質のために、最低サイズが212x40ピクセルのPNG、JPEG、またはJPG形式（最大5MB）のロゴをアップロードしてください。", "Upload Logo": "ロゴをアップロード", "Cloud Hosting": "クラウドホスティング", "Brand Name": "ブランド名", "<EMAIL>": "<EMAIL>", "Support Email": "サポートメール", "579 Spruce Court, Dallas, TX 75201": "スプルースコート579、ダラス、TX 75201", "Address": "住所", "Copyright Name": "著作権名", "Processing...": "処理中...", "Proceed to Checkout": "チェックアウトに進む", "Sell up to": "最大販売数量", "Checkout": "チェックアウト", "Your Order Summary": "注文概要", "Sub Total": "小計", "Processing Payment...": "支払い処理中...", "Processing Offer...": "オファーを処理中...", "Claim Free": "無料で取得", "Create Product": "商品を作成", "Create Hosting Plan": "ホスティングプランを作成", "Plan Name": "プラン名", "Renewal Type": "更新タイプ", "SKU": "SKU", "Setup Products & Start Selling": "製品を設定して販売を開始", "Add Your Domain Name": "ドメイン名を追加", "example.com": "example.com", "Add the following record to the DNS manager for your domain": "ドメインのDNSマネージャーに次のレコードを追加してください", "Skip this step, if you are not ready to point your domain server.": "ドメインサーバーを指定する準備ができていない場合は、このステップをスキップしてください。", "Payment Setup": "支払い設定", "Integrate with Stripe Connect": "Stripe Connectと統合", "Connect to your existing Stripe account or create a new account to start processing payments via Stripe.": "既存のStripeアカウントに接続するか、新しいアカウントを作成して、Stripeでの支払い処理を開始してください。", "Connect Now": "今すぐ接続", "Stripe Account": "ストライプアカウント", "If your Stripe account is set up correctly, it will automatically connect within 1–2 minutes.": "Stripeアカウントが正しく設定されている場合、1～2分以内に自動的に接続されます。", "If you’ve just created a new Stripe business account, it may take up to 3 days for Stripe to verify it.Once verified, your account should connect automatically.": "新しいStripeビジネスアカウントを作成した場合、確認に最大3日かかることがあります。確認が完了すると、アカウントは自動的に接続されます。", "To check the status of your": "ご利用状況を確認するには", "account": "アカウント", "you can visit your": "「あなたのプロフィールを訪問できます」", "Stripe dashboard.": "Stripe ダッシュボード", "Billing Account": "請求アカウント", "Stripe Account Name": "ストライプアカウント名", "Stripe Account ID": "ストライプアカウントID", "Connected On": "接続中", "Billing Currency": "請求通貨", "3D Secure Card Compatibility for Your Clients (Optional)": "3Dセキュアカード互換性（オプション）", "Stripe Publishable Key": "Stripe 公開可能キー", "If your client prefers to pay using a 3D Secure card, you need to add your": "3Dセキュアカードでの支払いを希望するクライアントがいる場合は、あなたの", "Stripe Publishable Key.": "公開可能キー", "Get your publishable key": "公開可能キーを取得", "from here.": "<PERSON><PERSON><PERSON>", "Syncing Account..": "アカウントを同期中...", "Having Trouble?": "お困りですか？", "Read our comprehensive documentation & learn to manage your hosting easily.": "包括的なドキュメントを読んで、ホスティングを簡単に管理しましょう。", "Read Documentation": "ドキュメントを読む", "Start Your Hosting Business: Resell & Earn Revenue": "ホスティングビジネスを始める: 再販して収益を得る", "Launch a cloud hosting business with xCloud Managed Servers. Resell our fully managed web hosting under your own brand or domain to maximize your revenue ensuring a reliable performance.": "xCloudマネージドサーバーでクラウドホスティングビジネスを開始しましょう。信頼性の高いパフォーマンスを確保しながら、完全管理されたウェブホスティングを自社ブランドやドメインで再販して収益を最大化できます。", "Complete control for your personal branding": "個人ブランドを完全に管理", "Manage your client billings with Stripe Connect": "Stripe Connectでクライアントの請求を管理", "Customize hosting packages & sell at your own price": "ホスティングパッケージをカスタマイズして、自分の価格で販売", "Get access to powerful features of xCloud": "xCloudの強力な機能にアクセスする", "By ticking this box, you are confirming that you have read, understood, and agree to our": "このボックスをチェックすることで、当社の内容を読み、理解し、同意したことを確認します。", "Please check this box to confirm that you accept the terms and conditions and want to proceed.": "このボックスをチェックして、利用規約に同意し、続行することを確認してください。", "Before proceeding, ensure you have an active Stripe account, as all transactions are managed via": "続行する前に、すべての取引が管理されるため、アクティブなStripeアカウントをお持ちであることを確認してください。", "Stripe Connect": "ストライプコネクト", "Please check this box to confirm that you have a Stripe account and want to proceed.": "Stripeアカウントをお持ちで、続行することを確認するには、このボックスをチェックしてください。", "Start Your Hosting Business Now": "今すぐホスティングビジネスを始める", "Product Details": "製品詳細", "Product Information": "製品情報", "Some basic information is shared over here": "基本情報がここに共有されています。", "Edit Product": "商品を編集", "Checkout URL": "チェックアウトURL", "Invoices of Product": "製品の請求書", "Source Product": "ソース製品", "Includes Up to RAM": "RAMまで含む", "Customize Package": "パッケージをカスタマイズ", "My Server": "マイサーバー", "Price/Month": "月額料金", "XCPU110": "XCPU110", "Stripe": "ストライプ", "will deduct a 3%-7% fee per sale. Your approximate profit for this sale is": "販売ごとに3%から7%の手数料が差し引かれます。この販売での概算利益は", "Active Package": "アクティブパッケージ", "Preview Plan": "プランのプレビュー", "Save and Publish": "保存して公開", "Add Product": "商品を追加", "Buying Price": "購入価格", "Selling Price": "販売価格", "Create New Product": "新しい製品を作成", "Select Server Plan at xCloud": "xCloudでサーバープランを選択", "Select Server Size": "サーバーサイズを選択", "Change Plan": "プランを変更", "Duplicate Product": "重複した製品", "Duplicate": "複製", "Favicon": "ファビコン", "Add Your Favicon": "ファビコンを追加", "Please upload a favicon with a minimum size of 16x16 pixels in PNG or ICO format (max 1MB) for best quality.": "最高の品質のために、16x16ピクセル以上のPNGまたはICO形式（最大1MB）のファビコンをアップロードしてください。", "Upload Favicon": "ファビコンをアップロード", "Custom Domain Setup": "カスタムドメイン設定", "Your Domain Name": "あなたのドメイン名", "Landing Page Settings": "ランディングページ設定", "Landing Page": "ランディングページ", "Enable it to use and customize the ready landing page settings": "使用可能にして、ランディングページ設定をカスタマイズします。", "Navbar Logo": "ナビバー ロゴ", "Update your logo for navigation bar": "ナビゲーションバーのロゴを更新してください", "Upload a logo or icon that represent your brand profile.": "ブランドプロフィールを表すロゴまたはアイコンをアップロードしてください。", "Hero Section": "ヒーローセクション", "Fast, Secure & Reliable Cloud Hosting": "高速・安全・信頼のクラウドホスティング", "Heading": "見出し", "Some more information that uou can add here": "ここに追加できる情報があります。", "Sub Heading": "サブ見出し", "Create Now": "今すぐ作成", "Button Text": "ボタンテキスト", "Button URL": "ボタンURL", "https://www.example.com": "https://www.example.com", "CTA Section": "CTAセクション", "Social Media Links": "ソーシャルメディアリンク", "https://facebook.com/xcloud": "https://facebook.com/xcloud", "Facebook": "フェイスブック", "https://instagram.com/xcloud": "https://instagram.com/xcloud", "Instagram": "インスタグラム", "https://x.com/xcloud": "https://x.com/xcloud", "X.com": "X.com", "https://linkedin.com/xcloud": "リンクトイン.com/xcloud", "Linkedin": "LinkedIn", "https://youtube.com/xcloud": "https://youtube.com/xcloud", "Youtube": "YouTube", "Preview": "プレビュー", "Saving...": "保存中...", "Connect to Stripe": "Stripeに接続", "Account Name": "アカウント名", "Stripe 3DS Card Setup": "Stripe 3DSカード設定", "Privacy Policy Settings": "プライバシーポリシー設定", "Default Privacy Policy": "デフォルトプライバシーポリシー", "Use your own Privacy Policy": "独自のプライバシーポリシーを使用", "SMTP Settings": "SMTP設定", "Use Custom SMTP": "カスタムSMTPを使用", "Use my custom smtp credentials for email sending.": "カスタムSMTP認証情報を使用してメールを送信する。", "SMTP Credentials": "SMTP認証情報", "smtp.mailgun.org": "smtp.mailgun.org", "Host": "ホスト", "587": "587", "SMTP username": "SMTPユーザー名", "Username": "ユーザー名", "Encryption": "暗号化", "ssl": "SSL", "Select Encryption": "暗号化を選択", "Terms & Services Settings": "利用規約設定", "Default TOS": "デフォルト利用規約", "Use your own TOS": "独自の利用規約を使用", "Use your own TOS URL": "独自の利用規約URLを使用", "https://example.com/tos": "利用規約: https://example.com/tos", "Enter the URL of your own Terms & Services page and make sure to add https:// in url": "独自の利用規約ページのURLを入力し、URLにhttps://を追加してください。", "Use your own Privacy Policy URL": "プライバシーポリシーのURLを使用してください", "https://example.com/privacy-policy": "プライバシーポリシー: https://example.com/privacy-policy", "Enter the URL of your own Privacy Policy page and make sure to add https:// in url": "独自のプライバシーポリシーページのURLを入力し、URLにhttps://を追加してください。", "All Clients": "すべてのクライアント", "Client Name": "クライアント名", "Press / to search": "検索するには / を押してください", "You don’t have any Product yet": "まだ製品がありません。", "Client Details": "クライアント詳細", "Client Information": "顧客情報", "Edit Information": "情報を編集", "Billing Address": "請求先住所", "Account Status": "アカウントステータス", "Payment Information": "支払い情報", "Here you can find all cards added by your clients": "ここで、クライアントが追加したすべてのカードを見つけることができます。", "No payment methods found.": "お支払い方法が見つかりません。", "Invoices": "請求書", "Here you can check all your client's previous payments": "ここで、すべてのクライアントの過去の支払いを確認できます。", "Edit Client Information": "クライアント情報を編集", "Experience Effortless Hosting With Powerful Features": "強力な機能で簡単ホスティングを体験", "Enjoy the lightning performance backed by powerful features and experience hassle-free hosting": "強力な機能でサポートされた高速パフォーマンスを楽しみ、ストレスのないホスティングを体験してください。", "Effortless Server Operations": "サーバー操作を簡単に", "Manage your server effortlessly with our intuitive dashboard. Enjoy high performance and reliability without worrying about downtime, regardless of your technical skill level.": "直感的なダッシュボードでサーバーを簡単に管理。技術レベルに関係なく、高性能と信頼性を維持し、ダウンタイムを心配する必要はありません。", "Easy Website Management": "簡単なウェブサイト管理", "We offer a comprehensive platform for managing WordPress sites with ease. With our intuitive dashboard and advanced features, you can manage your sites without any hassle.": "WordPressサイトを簡単に管理できる包括的なプラットフォームを提供します。直感的なダッシュボードと高度な機能により、手間なくサイトを管理できます。", "Powerful Security Measures": "強力なセキュリティ対策", "Our advanced security features ensure the safety of your data through advanced protective measures. Also, automated regular updates keep everything safe from threats and remains protected.": "高度なセキュリティ機能により、データの安全性が確保されます。自動定期更新により、常に脅威から保護されます。", "Real-time Resources Monitoring": "リアルタイムリソースモニタリング", "Monitoring your site and server is essential for ensuring optimal performance and reliability. With real-time measures, you can quickly identify issues provide a seamless experience to your customers.": "サイトとサーバーの監視は、最適なパフォーマンスと信頼性を確保するために不可欠です。リアルタイムの測定により、問題を迅速に特定し、お客様にシームレスな体験を提供できます。", "Transparent & Flexible Pricing for Everyone": "すべての人に透明で柔軟な料金設定", "Explore our range of plans designed to meet every needs of every web creator": "すべてのウェブクリエイターのニーズに応えるプランをご覧ください。", "Includes": "含む", "All rights reserved.": "全著作権所有。", "Terms of Service": "利用規約", "Terms & Services": "利用規約", "Just One Click Away from Completing Your Order": "注文完了まであとワンクリック", "Already have an account?": "アカウントをお持ちですか？", "Sign In": "サインイン", "Integrate any cloud provider to manage server and sites in xCloud": "xCloudでサーバーとサイトを管理するために、任意のクラウドプロバイダーを統合", "Choose Your Plan": "プランを選択", "Cost": "コスト", "Applied Coupon": "適用済みクーポン", "Purchase Limit Reached!": "購入制限に達しました！", "Only one purchase remaining!": "購入はあと1回のみです！", "Split Pay": "分割払い", "Ready to confirm your purchase?": "購入を確定しますか？", "By clicking 'Confirm', a charge of": "「確認」をクリックすると、料金が発生します:", "will be applied to your saved credit card.": "保存されたクレジットカードに適用されます。", "Please switch to your team to checkout": "チームに切り替えてチェックアウトしてください。", "You are currently in the Playground Team. You can't checkout from here": "現在、あなたはプレイグラウンドチームにいます。ここからはチェックアウトできません。", "Total Purchases": "総購入額", "Only": "のみ", "purchase remaining": "残りを購入", "Database Root Password": "データベースルートパスワード", "Do you want to turn off Auto Backups?": "自動バックアップをオフにしますか？", "Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks": "自動バックアップを無効にすると、簡単なデータ復旧、バックアップの移動性、災害復旧が失われます。リスクを理解した上で進むにはクリックしてください。", "Create Server": "サーバーを作成", "Report an Issue": "問題を報告", "Something went wrong": "問題が発生しました", "Please try again or report an issue to support": "もう一度お試しいただくか、サポートに問題を報告してください。", "Retry": "再試行", "You may exit this window and navigate away": "このウィンドウを閉じて移動できます", "Check Connection": "接続を確認", "Add Site": "サイトを追加", "Install Stack": "スタックをインストール", "SSH User": "SSHユーザー", "SSH Key": "SSHキー", "Log": "ログ", "Install stack on the server": "サーバーにスタックをインストール", "It will install these following services on the server": "サーバーに次のサービスがインストールされます。", "PHP 7 .4": "PHP 7.4", "MySQL 8 .0": "MySQL 8.0", "Redis": "レディス", "and more. .": "その他", "Install": "インストール", "Log in": "ログイン", "Register": "登録", "White Label": "ホワイトラベル", "Playground": "プレイグラウンド", "Email not verified": "メールが確認されていません", "Please check your email to verify your account. Verify your email": "アカウントを確認するためにメールをチェックしてください。メールを確認する", "Do you want to switch from": "切り替えますか？", "You can create free demo sites under Playground Team which will be removed after 24 hours. From Team Settings page you can switch back to your default team again.": "プレイグラウンドチームで無料のデモサイトを作成できますが、24時間後に削除されます。チーム設定ページからデフォルトのチームに戻すことができます。", "Info": "情報", "You can switch to any team you belong by clicking on the team name in the top right header.": "右上のヘッダーにあるチーム名をクリックすると、所属する任意のチームに切り替えることができます。", "Switch to": "切り替え", "Yes": "はい", "No": "いいえ", "Try xCloud Playground": "xCloud Playground を試す", "Do you want to Switch your team?": "チームを切り替えますか？", "Please accept or check your email to switch to": "メールを確認して切り替えてください", "team. You can also visit": "チーム。また、訪問することもできます", "to manage your teams.": "チームを管理するために", "Please accept or check your email to switch to your team. You can also visit": "チームに切り替えるには、メールを確認または承認してください。また、訪問することもできます。", "My Profile": "マイプロフィール", "Support": "サポート", "Documentation": "ドキュメント", "Affiliates": "アフィリエイト", "Admin Panel": "管理パネル", "Reports": "レポート", "Horizon": "ホライゾン", "Telescope": "望遠鏡", "Vapor UI": "Vapor UI", "Documents": "ドキュメント", "Find Servers or Sites": "サーバーまたはサイトを検索", "Search Results": "検索結果", "of": "の", "Check our ": "「チェック」", " to get free hosting for 6 months with new Vultr signups.": "新規Vultr登録で6ヶ月間の無料ホスティングを利用する", " to get a quick start.": "クイックスタートするには", "Check out our ": "ぜひご覧ください", "Quick Start.": "クイックスタート", "No vulnerabilities were found on your sites. To setup vulnerability scanner please check this": "サイトに脆弱性は見つかりませんでした。脆弱性スキャナーを設定するには、こちらをご確認ください。", "Hey there! You have no ": "こんにちは！現在、ありません。", " yet.": "まだ", "Quick Start Documentation": "クイックスタートドキュメント", "This feature is not available in Playground.": "この機能はプレイグラウンドでは利用できません。", "No custom Nginx configurations found!": "カスタムNginx設定が見つかりませんでした！", "Hey there! You have no": "こんにちは！あなたにはありません", "plugins.": "プラグイン", "themes.": "テーマ", "to claim": "請求する", "$20 free credits": "$20分の無料クレジット", "if you are a new user on Hetzner.": "<PERSON><PERSON><PERSON>の新規ユーザーの場合", " to claim ": "請求する", "Read our ": "「利用規約を読む」", " on Heztner API integration.": "Heztner API統合", "Choose AWS Services": "AWSサービスを選択", "Select one or more AWS services for which you want to use this credentials.": "この認証情報を使用する AWS サービスを選択してください。", "Verify": "確認", "$300 free credits": "$300分の無料クレジット", "if you are a new user on Google Cloud Platform.": "Google Cloud Platform の新規ユーザーの場合。", "Back": "戻る", "$100 free credits": "$100分の無料クレジット", "if you are a new user on Linode.": "Linodeの新規ユーザーの場合。", "You can connect a fresh": "新しいものを接続できます", "Ubuntu 24.04 LTS x64": "Ubuntu 24.04 LTS x64", "server from any provider if you have root access.": "ルートアクセスがあれば、任意のプロバイダーのサーバーを使用できます。", "$200 free credits": "$200分の無料クレジット", "if you are a new user on DigitalOcean.": "DigitalOceanの新規ユーザーの場合。", "to collect your API key. Read our": "APIキーを取得するには、こちらをお読みください。", "and Use": "「使用」", "XCLOUD": "XCLOUD", "coupon code to claim": "クーポンコードを入力", "$100": "￥100", "free credits on new vultr singups for 180 days.": "新規Vultrサインアップで180日間無料クレジット", "Database Info": "データベース情報", "Refreshing...": "更新中...", "By default you can use": "デフォルトで使用できます", "user. But if you want to do it for specific site enter the specific site user name here.": "ユーザー。ただし、特定のサイトで行いたい場合は、ここにそのサイトのユーザー名を入力してください。", "Update PHP Configuration": "PHP設定を更新", "Default PHP Version": "デフォルトのPHPバージョン", "Default Server PHP Version": "デフォルトサーバーPHPバージョン", "This will set the default PHP version for the CLI and for new site installations. However, it won't affect the PHP versions of any existing sites, and the default version for new sites can still be changed during installation.": "CLIおよび新しいサイトのインストールに対するデフォルトのPHPバージョンを設定します。ただし、既存のサイトのPHPバージョンには影響しません。また、新しいサイトのデフォルトバージョンはインストール時に変更可能です。", "Run Custom Command": "カスタムコマンドを実行", "Most Recent Commands": "最新のコマンド", "This command will be executed on the server. Please make sure you are running the correct command. Running incorrect commands can break your server.": "このコマンドはサーバー上で実行されます。正しいコマンドを実行していることを確認してください。誤ったコマンドを実行すると、サーバーが壊れる可能性があります。", "If you want to do it for specific site enter the specific site user name here.": "特定のサイトで行う場合は、ここにそのサイトのユーザー名を入力してください。", "Run Command": "コマンド実行", "Run New Command": "新しいコマンドを実行", "Server Health": "サーバーの状態", "Filter": "フィルター", "Event": "イベント", "Date & Time": "日付と時刻", "Firewall Management": "ファイアウォール管理", "Vulnerability Scan": "脆弱性スキャン", "Your server is set to automatically update security patches. A reboot is required to complete the updates. You can configure the server to reboot automatically at a preferred time or choose to do it manually.": "サーバーはセキュリティパッチを自動更新するように設定されています。更新を完了するには再起動が必要です。サーバーを自動で再起動する時間を設定するか、手動で再起動することができます。", "Provider Backup Setting": "プロバイダーバックアップ設定", "Information": "情報", "Notes": "メモ", "Connection Settings": "接続設定", "Server Settings": "サーバー設定", "Update Timezone": "タイムゾーンを更新", "Magic Login Settings": "マジックログイン設定", "Time Zone": "タイムゾーン", "Turn off Indexing": "インデックス作成をオフにする", "Turning off this setting will prevent search engines from indexing your staging site.": "この設定をオフにすると、検索エンジンがステージングサイトをインデックスしなくなります。", "Search Engine Visibility": "検索エンジンの可視性", "Discourage search engines from indexing this site": "このサイトを検索エンジンでインデックスしないようにする", "It is up to search engines to honor this request.": "このリクエストを尊重するかどうかは検索エンジン次第です。", " offers a temporary test domain that allows you to quickly deploy your site. ": "一時的なテストドメインを提供し、サイトを迅速に展開できます。", "This temporary domain enables you to share your work in progress with teammates or clients for review ": "この一時的なドメインを使用すると、進行中の作業をチームメイトやクライアントと共有してレビューを受けることができます。", "and input before you finalize and launch it with your own custom domain for public access.": "カスタムドメインで公開する前に、入力を確認して確定してください。", "HTTPS Is Enabled": "HTTPSが有効です", "Do You Want To Enable HTTPS?": "HTTPSを有効にしますか？", "Please make sure that the database connection configuration is correct and if you are creating a new database you may want to store the information in a secure place. Storing the wp-config.php file in Git is not recommended and if the wp-config.php file is absent from your Git repository, ": "データベース接続の設定が正しいことを確認してください。新しいデータベースを作成する場合は、情報を安全な場所に保存することをお勧めします。wp-config.phpファイルをGitに保存することは推奨されません。Gitリポジトリにwp-config.phpファイルがない場合、", " will automatically generate it from wp-config-sample.php, adding database credentials. Additional credentials can be added later on the WP Config page in the ": "wp-config-sample.php から自動的に生成され、データベースの認証情報が追加されます。追加の認証情報は、後で WP Config ページで追加できます。", " site dashboard. If wp-config.php is already in the repository, ": "サイトダッシュボード。wp-config.php がすでにリポジトリにある場合、", " won't make any changes.": "変更は行われません。", "Database Management": "データベース管理", "Your source server must be": "ソースサーバーは次の必要があります", "or": "または", "OLS": "OLS", "server with": "サーバー接続", "Ubuntu 20.04 or 22.04 LTS x64": "Ubuntu 20.04 または 22.04 LTS x64", "and should have": "「必要があります」", "access.": "アクセス", "month": "月", "Email Address": "メールアドレス", "By default, emails are sent from": "デフォルトでは、メールはから送信されます", "To send emails from your domain, enter your custom SMTP credentials (e.g., Mailgun, Elastic Email)": "ドメインからメールを送信するには、カスタムSMTP認証情報（例：Mailgun、Elastic Email）を入力してください。", "Two Factor Authentication": "2要素認証", "You have enabled two factor authentication.": "2要素認証が有効になりました。", "Finish enabling two factor authentication.": "2要素認証の有効化を完了する", "You have not enabled two factor authentication.": "二要素認証が有効になっていません。", "Enable": "有効にする", "To finish enabling two factor authentication, scan the following QR code using your phone's authenticator application.": "2要素認証を有効にするには、携帯電話の認証アプリで次のQRコードをスキャンしてください。", "Two factor authentication is now enabled.": "2要素認証が有効になりました。", "Enter the text code below instead if you can't use the barcode.": "バーコードが使用できない場合は、代わりに以下のテキストコードを入力してください。", "Or, Scan the": "または、スキャン", "QR provided": "QRコードを提供しました", "with your phone's two-factor authentication app.": "携帯電話の二要素認証アプリで。", "Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost.": "これらのリカバリーコードを安全なパスワードマネージャーに保存してください。二要素認証デバイスを紛失した場合、アカウントへのアクセスを回復するために使用できます。", "Setup Key": "セットアップキー", "Regenerate Recovery Codes": "リカバリーコードを再生成", "Show Recovery Codes": "リカバリーコードを表示", "Create SSH Key": "SSHキーを作成", "Create A New SSH Key": "新しいSSHキーを作成", "Verify & Save for": "確認して保存", "Buy & Add": "購入して追加", "Make sure you have active billing plan to use this feature.": "この機能を使用するには、有効な請求プランが必要です。", "This label will be used to identify this SMTP provider in": "このラベルは、このSMTPプロバイダーを識別するために使用されます", "Optional if you're using global API key of Mailgun": "MailgunのグローバルAPIキーを使用している場合は任意", ". i.e. Mailgun, Sendgrid, etc.": "メールガン、センドグリッドなど", "This label will be used to identify this SMTP provider in xCloud. i.e. Mailgun, Sendgrid, etc.": "このラベルは、xCloudでこのSMTPプロバイダーを識別するために使用されます。例: Mailgun、Sendgridなど。", "Authorize on WhatsApp": "WhatsAppで認証", "Authorize on": "認証する", "Please go to Telegram and start talking with": "Telegramに移動して、会話を始めてください", "To authenticate your chat, send this command to xCloud Notification Bot.": "チャットを認証するには、このコマンドを xCloud Notification Bot に送信してください。", "Due Date": "期限日", "Recent Events": "最近のイベント", "Currently No Event Available": "現在、利用可能なイベントはありません", "Mark all as read": "すべて既読にする", "No Notifications Available!": "通知はありません。", "provides": "提供します", "Environment to do all the development to your site with a temporary domain.": "サイトの開発を一時的なドメインで行う環境。", "When you are ready, simply select the GO LIVE option and add your own domain to make the site available to your user/visitor.": "準備ができたら、「GO LIVE」オプションを選択し、独自のドメインを追加してサイトをユーザー/訪問者に公開してください。", "Demo Site Management": "デモサイト管理", "Verifying DNS..": "DNSを確認中…", "Your staging SSL is being managed by": "ステージングSSLは次によって管理されています", "Your SSL is being managed by Cloudflare.": "SSLはCloudflareによって管理されています。", "If you turn on this option then": "このオプションをオンにすると", "will automatically enable NGINX to directly serve previously": "NGINXが自動的に以前のコンテンツを直接提供します", "cached files without calling WordPress or any PHP. It also adds headers to cached CSS, JS, and images via": "WordPressやPHPを呼び出さずにキャッシュされたファイルを処理します。また、キャッシュされたCSS、JS、画像にヘッダーを追加します。", "the browser cache. As a result, your website will be much faster. If someone visits your website again,": "ブラウザキャッシュ。その結果、ウェブサイトの速度が大幅に向上します。再度ウェブサイトを訪れると、", "their browser can load the cached files directly from their own computer instead of making a request to": "ブラウザは、リクエストを送信する代わりに、キャッシュされたファイルを自分のコンピュータから直接読み込むことができます。", "your web server. This reduces the number of requests to your server and further speeds up the loading of your website.": "あなたのウェブサーバー。これによりサーバーへのリクエスト数が減り、ウェブサイトの読み込みがさらに速くなります。", "You can read more from our documentation": "ドキュメントを読むことで詳細を確認できます", "Saving Changes...": "変更を保存しています...", "Make sure to add your SSH key on the SSH Authentication section.": "SSH認証セクションにSSHキーを追加してください。", "Enter the configuration file name": "設定ファイル名を入力してください", "Save Config": "設定を保存", "Create Custom Nginx Configuration for": "カスタム Nginx 設定を作成", "Select Template": "テンプレートを選択", "IP Whitelist/Blacklist": "IPホワイトリスト/ブラックリスト", "WP-Cron and xCloud-Cron": "WP-Cron と xCloud-Cron", "Update PHP Settings": "PHP設定を更新", "All existing files and data on this site will be deleted": "このサイト上のすべての既存ファイルとデータは削除されます。", "Create a new backup before restoring?": "復元する前に新しいバックアップを作成しますか？", "Restore": "復元", "Take Backup": "バックアップを取る", "Full Backup": "フルバックアップ", "Are you sure you want to take a full backup? This will create a complete backup of all your data. Future incremental backups will be based on this full backup.": "完全バックアップを実行しますか？これにより、すべてのデータの完全なバックアップが作成されます。今後の増分バックアップは、この完全バックアップを基に行われます。", "Incremental Backup": "増分バックアップ", "This will add to your previous backups by only saving the changes since the last backup.": "これは、前回のバックアップ以降の変更のみを保存することで、以前のバックアップに追加されます。", "Site List": "サイトリスト", "Management": "管理", "Settings": "設定", "Custom Cron Jobs": "カスタムCronジョブ", "PHP Configuration": "PHP設定", "Commands": "コマンド", "Monitoring": "モニタリング", "Logs": "ログ", "Events": "イベント", "Firewall management": "ファイアウォール管理", "Vulnerability Settings": "脆弱性設定", "Full Server migration": "サーバー全体の移行", "Metadata": "メタデータ", "Site Overview": "サイト概要", "Caching": "キャッシュ", "Email Configuration": "メール設定", "Previous Backups": "以前のバックアップ", "Backup Settings": "バックアップ設定", "Site Monitoring": "サイト監視", "Access Data": "データにアクセス", "SSH/sFTP": "SSH/sFTP", "Tools": "ツール", "Nginx and Security": "Nginxとセキュリティ", "Nginx Customization": "Nginx カスタマイズ", "IP Management": "IP管理", "Site Settings": "サイト設定", "User Profile": "ユーザープロフィール", "Browser Sessions": "ブラウザーセッション", "Integrations": "統合", "Cloudflare": "クラウドフレア", "Notification": "通知", "Billing": "請求書発行", "Manual Invoices": "手動請求書", "Whitelabel": "ホワイトラベル", "All Events": "すべてのイベント", "Payment": "支払い", "Brand": "ブランド", "SMTP": "SMTP", "Create Products": "製品を作成", "Setup your brand to get started": "ブランドを設定して開始してください", "Setup your payment method": "お支払い方法を設定してください", "Create customized plans": "カスタマイズプランを作成", "Setup your domain easily": "ドメインを簡単に設定", "Search sites...": "サイトを検索...", "Create Account": "アカウント作成", "Password must be at least  8 characters and should contain uppercase, number and special character": "パスワードは8文字以上で、大文字、数字、特殊文字を含めてください。", "Filter by Date": "日付でフィルター", "Name your blueprint": "設計図に名前を付ける", "Search": "検索", "Are You Sure You Want To Delete Server": "サーバーを削除してもよろしいですか？", "to confirm": "確認する", "Are You Sure You Want To Delete Site": "サイトを削除してもよろしいですか？", "Edit Server Provider": "サーバープロバイダーを編集", "Add Server Provider": "サーバープロバイダーを追加", "check connection": "接続を確認", "Clone Blueprint": "クローン設計図", "Delete Blueprint": "ブループリントを削除", "Are you sure you want to delete this blueprint?": "この設計図を削除してもよろしいですか？", "Are you sure you want to clone this blueprint?": "この設計図をクローンしてもよろしいですか？", "Clone": "クローン", "Set as Default Blueprint": "デフォルトのブループリントに設定", "Are you sure you want to set this blueprint as default?": "この設計図をデフォルトに設定してもよろしいですか？", "Are you sure?": "本当に実行しますか？", "You won't be able to revert this!": "元に戻すことはできません！", "Yes, Remove!": "はい、削除します！", "Are you sure you want to update WordPress?": "WordPressを更新してもよろしいですか？", "Update WordPress Core": "WordPressコアを更新", "Are you sure you want to active this theme?": "このテーマを有効にしてもよろしいですか？", "Activate Theme": "テーマを有効化", "Are you sure you want to update those plugin?": "プラグインを更新してもよろしいですか？", "You have selected": "選択しました", "plugins": "プラグイン", "Are you sure you want to update those plugins?": "プラグインを更新してもよろしいですか？", "Are you sure you want to update these themes?": "これらのテーマを更新してもよろしいですか？", "themes": "テーマ", "Do you want to deactivate Adminer?": "Adminerを無効にしますか？", "Do you want to activate Adminer?": "Adminerを有効にしますか？", "We recommend deactivating it when not required.": "必要ないときは無効にすることをお勧めします。", "Yes, restore it!": "はい、復元します！", "Are you sure you want to restore this server?": "このサーバーを復元してもよろしいですか？", "Are you sure you want to delete this card?": "このカードを削除してもよろしいですか？", "You can add another card.": "カードを追加できます。", "Yes, log out!": "はい、ログアウトします！", "Are you sure you want to delete this integration?": "この統合を削除してもよろしいですか？", "You want to disconnect": "切断しますか？", "Yes, Disconnect!": "はい、切断します！", "You want to reconnect": "再接続しますか？", "Yes, Reconnect!": "はい、再接続します！", "Yes, disable it!": "はい、無効にします！", "Are you sure you want to disable HTTPS?": "HTTPSを無効にしてもよろしいですか？", "Yes, Leave Team!": "はい、チームを離れる", "Edit Storage Provider": "ストレージプロバイダーを編集", "Add Storage Provider": "ストレージプロバイダーを追加", "Yes, leave team!": "はい、チームを離れる", "This will remove the cron job from the server.": "この操作により、サーバーからcronジョブが削除されます。", "Are you sure you want to disable this firewall rule?": "このファイアウォールルールを無効にしてもよろしいですか？", "You can enable it later.": "後で有効にできます。", "Yes, Disable": "はい、無効にする", "Are you sure you want to enable this firewall rule?": "このファイアウォールルールを有効にしてもよろしいですか？", "You can disable it later.": "後で無効にできます。", "Yes, Enable": "はい、有効にする", "Are you sure you want to delete this firewall rule?": "このファイアウォールルールを削除してもよろしいですか？", "Deleting firewall rule will remove it permanently.": "ファイアウォールルールを削除すると、完全に削除されます。", "Yes, Delete": "はい、削除", "Are you sure you want to unban": "本当に禁止を解除しますか？", "This will remove the IP address from the banned list.": "IPアドレスを禁止リストから削除します。", "Yes, Unban": "はい、禁止解除", "This will remove the sudo user from the server.": "この操作により、サーバーからsudoユーザーが削除されます。", "Authentication will be disabled for this site": "このサイトの認証が無効になります", "Yes, Remove": "はい、削除", "Are you sure you want to delete this backup?": "このバックアップを削除してもよろしいですか？", "This action cannot be undone.": "この操作は元に戻せません。", "Are you sure you want to remove this failed backup?": "この失敗したバックアップを削除してもよろしいですか？", "Do you want to deactivate Tiny File Manager?": "Tiny File Managerを無効にしますか？", "Do you want to activate Tiny File Manager?": "Tiny File Managerを有効にしますか？", "Are you sure you want to disable": "無効にしてもよろしいですか？", "caching?": "キャッシュしますか？", "Are you sure you want to disable redis object caching?": "Redisオブジェクトキャッシュを無効にしてもよろしいですか？", "Yes, switch it!": "はい、切り替えます！", "Are you sure you want to switch to": "切り替えてもよろしいですか？", "plugin?": "プラグイン", "Are you sure you want to disable full page caching?": "ページ全体のキャッシュを無効にしてもよろしいですか？", "Yes, Remove it!": "はい、削除します！", "Are you sure you want to remove this IP address?": "このIPアドレスを削除してもよろしいですか？", "Are you sure you want to disable LiteSpeed Cache?": "LiteSpeed Cacheを無効にしてもよろしいですか？", "Are you sure you want to delete this Nginx Configuration?": "このNginx設定を削除してもよろしいですか？", "It will be permanently deleted.": "完全に削除されます。", "Enable Nginx File Regeneration": "Nginxファイルの再生成を有効にする", "This will prevent xCloud from regenerating nginx file on any changes made to the site. You will have to manually regenerate the nginx file.": "これにより、xCloud がサイトに変更を加えた際に nginx ファイルを再生成するのを防ぎます。nginx ファイルは手動で再生成する必要があります。", "This will allow xCloud to regenerate nginx file on any changes made to the site. You will not have to manually regenerate the nginx file.": "サイトに変更が加えられると、xCloudがnginxファイルを自動的に再生成します。nginxファイルを手動で再生成する必要はありません。", "Enable Site": "サイトを有効化", "Disabling the site will make it inaccessible. Are you sure you want to disable it?": "サイトを無効にするとアクセスできなくなります。本当に無効にしますか？", "Enabling the site will make it accessible. Are you sure you want to enable it?": "サイトを有効にするとアクセス可能になります。本当に有効にしますか？", "This action will run the rescue process on the site. Are you sure you want to run it?": "この操作を実行すると、サイトでレスキュープロセスが開始されます。本当に実行しますか？", "Yes, Run Now": "はい、今すぐ実行", "Are you sure you want to update the plugin?": "プラグインを更新してもよろしいですか？", "Plugin will be updated to": "プラグインが更新されます", "Are you sure you want to not ignore this vulnerability?": "この脆弱性を無視しないことを確認しますか？", "Are you sure you want to ignore this vulnerability?": "この脆弱性を無視してもよろしいですか？", "Are you sure you want to update the theme?": "テーマを更新してもよろしいですか？", "You have select": "選択しました", "theme": "テーマ", "Are You Sure You Want To Delete this SSH Key:": "このSSHキーを削除してもよろしいですか？", "Generate Invoice": "請求書を作成", "Add A New Payment Method": "新しい支払い方法を追加", "Log Out Other Browser Sessions": "他のブラウザセッションをログアウト", "Log Out": "ログアウト", "Log Out of All Sessions": "すべてのセッションからログアウト", "Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices.": "他のすべてのデバイスでのブラウザセッションからログアウトするには、パスワードを入力してください。", "You won't be able to revert this to your current device.": "この操作を現在のデバイスに戻すことはできません。", "Are you sure you want to delete": "削除してもよろしいですか？", "You cannot delete this team": "このチームを削除できません", "You cannot delete your personal team": "個人チームを削除できません", "You cannot delete your current team": "現在のチームを削除することはできません。", "Admin Username": "管理者ユーザー名", "SIZE": "サイズ", "REGION": "地域", "UBUNTU": "ウブントゥ", "Deleting..": "削除中...", "Delete SSH Key": "SSHキーを削除", "Add a new database to your server": "サーバーに新しいデータベースを追加", "Add a new user to your database": "データベースに新しいユーザーを追加", "Edit database user:": "データベースユーザーを編集:", "Are You Sure You Want To Delete this database:": "このデータベースを削除してもよろしいですか？", "Are You Sure You Want To Delete this database user:": "このデータベースユーザーを削除してもよろしいですか？", "Cron Job Output": "クロンジョブの出力", "Add new firewall rule for": "新しいファイアウォールルールを追加", "Ban an IP Address on": "IPアドレスを禁止", "Reboot Time": "再起動時間", "Your server is set to automatically reboot at": "サーバーは自動的に再起動するよう設定されています。", "You can change the reboot time if you'd like.": "再起動時間を変更できます。", "CPU Usage": "CPU使用率", "Reboot": "再起動", "Are You Sure You Want To": "本当に実行しますか？", "Service?": "サービス？", "Are You Sure You Want To Restore Backup For The Server": "サーバーのバックアップを復元してもよろしいですか？", "Are You Sure You Want To Resize Server": "サーバーのサイズを変更してもよろしいですか？", "Resize": "リサイズ", "Initiating Resizing...": "リサイズを開始しています...", "Manage Database": "データベース管理", "Last Updated": "最終更新日", "Deactivate": "無効化", "Last Checked": "最終確認日時", "Current Version": "現在のバージョン", "RAM Usages": "RAM使用量", "Disk Usages": "ディスク使用量", "Choose SSH Keys": "SSHキーを選択", "No SSH Keys found.": "SSHキーが見つかりません。", "Search SSH Keys": "SSHキーを検索", "Are You Sure You Want To Disable Site": "サイトを無効にしてもよろしいですか？", "Add Your Own Database": "独自のデータベースを追加", "Please provide your new database information": "新しいデータベース情報を入力してください", "Cron Interval for Server": "サーバーのクロン間隔", "Add this public key in your Git repository as deploy key. This is necessary to enable": "Gitリポジトリにこの公開鍵をデプロイキーとして追加してください。これにより有効化されます。", "to clone the repository, ensuring a secure and authorized access for automated processes such as cloning.": "リポジトリをクローンするには、安全で認証されたアクセスを確保し、クローン作成などの自動化プロセスを実行します。", "Click On The Plugin Download Link": "プラグインのダウンロードリンクをクリック", "Upload the zipped plugin file to WordPress under 'Add New' in the 'Plugins' tab": "「プラグイン」タブの「新規追加」で、圧縮されたプラグインファイルをWordPressにアップロードしてください。", "Click 'Activate' to install the plugin": "「アクティベート」をクリックしてプラグインをインストール", "Copy the authentication token and paste it into the plugin page to complete the setup": "認証トークンをコピーして、プラグインページに貼り付け、セットアップを完了してください。", "Upload The Plugin Zipped File On WordPress": "WordPressにプラグインのZIPファイルをアップロード", "Click On The ‘Activate’ Button To Install Plugin": "プラグインをインストールするには「アクティベート」ボタンをクリックしてください", "Copy The Authentication Token And Paste It Into The Plugin Page": "認証トークンをコピーしてプラグインページに貼り付けてください。", "Export Zipped File From Your Existing Site": "既存サイトからZIPファイルをエクスポート", "Upload The Exported Zip or Tar File Here": "エクスポートされたZipまたはTarファイルをここにアップロード", "You must have root access to perform Full Server Migration": "フルサーバー移行を実行するには、ルートアクセスが必要です。", "Migrate multiple cPanel sites from shared hosting to xCloud easily for faster, scalable and secure hosting.": "複数のcPanelサイトを共有ホスティングからxCloudに簡単に移行し、より高速でスケーラブルかつ安全なホスティングを実現。", "If the fetched domain is incorrect, you can edit it here, the domain name needs to be accurate for successful migration": "取得したドメインが間違っている場合は、ここで編集できます。ドメイン名は、移行を成功させるために正確である必要があります。", "Total migrated": "合計移行済み", "Verifying ...": "確認中...", "Choose Web Server": "Webサーバーを選択", "Updating Backup Schedule...": "バックアップスケジュールを更新しています...", "Update Backup Schedule": "バックアップスケジュールを更新", "Set Schedule": "スケジュール設定", "Are You Sure?": "本当に実行しますか？", "Restoring...": "復元中...", "Enabling Backup...": "バックアップを有効にしています...", "Disabling Backup...": "バックアップを無効にしています...", "Backup?": "バックアップしますか？", "Provider Backup Schedule": "プロバイダーバックアップスケジュール", "Update Schedule": "スケジュール更新", "Select Day of Week": "曜日を選択", "Select Day of Month": "月の日を選択", "View all": "すべて表示", "You are now managing": "管理中", "site on": "サイトオン", "server.": "サーバー", "You can not use your own domain with xCloud Free Email Service..": "xCloud無料メールサービスでは独自ドメインを使用できません。", "For example, you can send <NAME_EMAIL> or <EMAIL>. It is up to you!": "たとえば、<EMAIL> や <EMAIL> からメールを送信できます。選択はあなた次第です！", "Are you sure you want to restore this backup?": "このバックアップを復元してもよろしいですか？", "X-Frame-Options": "X-Frame-Options", "SAMEORIGIN": "SAMEORIGIN", "Update Tags": "タグを更新", "Zone": "ゾーン", "Choose Zones": "ゾーンを選択", "Choose Regions": "地域を選択", "Choose Sizes": "サイズを選択", "site": "サイト", "No Vulnerabilities Found": "脆弱性は見つかりませんでした", "Vulnerability Scan Not Enabled": "脆弱性スキャンが有効になっていません", "Updates Available": "アップデートがあります", "sudo user": "スーパーユーザー", "IP": "IP", "Package": "パッケージ", "Can't find what you're looking for?": "お探しのものが見つかりませんか？", "Contact Support": "サポートに連絡", "Nginx Options": "Nginxオプション", "OpenLiteSpeed Options": "OpenLiteSpeed オプション", "Link Google Drive Account": "Google ドライブ アカウントをリンク", "Upload a zipped file of your existing WordPress website (max size: 500 MB)": "既存のWordPressサイトのzipファイルをアップロードしてください（最大サイズ：500 MB）", "Recreate Site from Backup": "バックアップからサイトを再作成", "Restore site backup from local or remote storage easily to create a site": "サイトを作成するために、ローカルまたはリモートストレージから簡単にサイトバックアップを復元", "Language Settings": "言語設定", "Copyright": "著作権", "Taking Payment...": "支払い処理中...", "Bill": "請求書", "Pay": "支払う", "Additional Information": "追加情報", "Name of Invoice": "請求書名", "Notification Language Settings": "通知言語設定", "Turning on this setting will prevent search engines from indexing your staging site.": "この設定をオンにすると、検索エンジンがステージングサイトをインデックスしないようにします。", "For example, you can send emails from": "例えば、からメールを送信できます", "It is up to you!": "あなた次第です！", "Adminer and File Manager won't work with 7G/8G firewall. We recommend SFTP if you're familiar with it.": "Adminerとファイルマネージャーは7G/8Gファイアウォールでは動作しません。SFTPに慣れている場合は、そちらをお勧めします。", "Demo Site Setup": "デモサイト設定", "System Cron Jobs": "システム定期ジョブ", "others": "その他", "Items": "アイテム", "List of Items": "アイテム一覧", "Integrate New Item": "新しいアイテムを統合", "Add New Item": "新しいアイテムを追加", "License": "ライセンス", "Are you sure you want to delete?": "削除してもよろしいですか？", "Item deleted successfully": "アイテムが正常に削除されました", "Failed to delete Item": "アイテムの削除に失敗しました", "Select Integrated Plugin": "統合プラグインを選択", "Boost your WordPress site’s performance with Object Cache Pro by caching frequently accessed data in Redis. This reduces database queries, enhances speed, and is especially beneficial for dynamic, content-heavy websites.": "Redisで頻繁にアクセスされるデータをキャッシュし、Object Cache ProでWordPressサイトのパフォーマンスを向上させましょう。これにより、データベースクエリが減少し、速度が向上します。特に動的でコンテンツが多いウェブサイトに効果的です。", "Debug Mode": "デバッグモード", "Object Cache Pro": "オブジェクトキャッシュプロ", "Select Plugin": "プラグインを選択", "Update Item": "アイテムを更新", "Add Item": "アイテムを追加", "Enter your license label": "ライセンスラベルを入力してください", "Enter your license key": "ライセンスキーを入力してください", "Edit Item": "アイテムを編集", "Access File Manager": "ファイルマネージャーにアクセス", "Always Enabled": "常に有効", "Keep the File Manager accessible at all times.": "ファイルマネージャーを常にアクセス可能に保つ。", "Disable File Manager": "ファイルマネージャーを無効にする", "Specify how long the File Manager should remain enabled before it’s automatically disabled.": "ファイルマネージャーが自動的に無効になるまでの有効期間を指定してください。", "Set Auto Disable Duration": "自動無効化時間の設定", "Choose to keep the File Manager active for always or schedule it to disable after a certain time.": "ファイルマネージャーを常にアクティブにするか、一定時間後に無効にするようスケジュール設定してください。", "Access Adminer": "Adminerにアクセス", "Keep the Adminer accessible at all times.": "常にAdminerにアクセスできるようにしてください。", "Disable Adminer": "Adminerを無効化", "Specify how long the Adminer should remain enabled before it’s automatically disabled.": "Adminerが自動的に無効化されるまでの有効期間を指定してください。", "Set Auto Deactivate Duration": "自動無効化時間を設定", "Choose to keep the Adminer active for always or schedule it to disable after a certain time.": "Adminerを常にアクティブにするか、一定時間後に無効にするようスケジュール設定してください。", "Customization": "カスタマイズ", "The File Manager will be disable within": "ファイルマネージャーは無効になります", "Manually Upload Website": "ウェブサイトを手動でアップロード", "Upload a zipped file of your existing website": "既存のウェブサイトのZIPファイルをアップロードしてください", "Upload a zipped file of your existing website (max size: 500 MB)": "既存のウェブサイトのZIPファイルをアップロードしてください（最大サイズ：500 MB）", "Custom PHP": "カスタムPHP", "laravel": "<PERSON><PERSON>", "Laravel": "<PERSON><PERSON>", "Coming": "近日公開", "Update Web Root": "Webルートを更新", "Web Root": "ルートディレクトリ", "Coming Soon": "近日公開", "The Adminer will be disable within": "管理者は以内に無効になります", "Login": "ログイン", "Login Options": "ログインオプション", "Log in using xCloud email account or the first admin account.": "xCloudのメールアカウントまたは最初の管理者アカウントでログインしてください。", "Use a different email and, if your login URL is custom, enter it here to log in.": "別のメールアドレスを使用し、カスタムのログインURLがある場合は、ここに入力してください。", "Custom": "カスタム", "Something went wrong, please try again.": "問題が発生しました。再試行してください。", "Use a different email or username to log in.": "別のメールアドレスまたはユーザー名でログインしてください。", "Enter your email": "メールアドレスを入力してください", "Haven’t registered for your free account yet?": "無料アカウントの登録はお済みですか？", "Sign up now": "今すぐ登録", "Glad To Have You Back!": "お帰りなさい！", "Get Started For Free": "無料で始める", "Access 1 server and 10 sites at $0 cost. No credit card required.": "1台のサーバーと10サイトに無料でアクセス。クレジットカード不要。", "Enter your name": "名前を入力してください", "Please choose a strong password. Use at least 8 characters, including a mix of letters, numbers and symbols.": "強力なパスワードを選択してください。8文字以上で、文字、数字、記号を組み合わせてください。", "Retype Password": "パスワードを再入力", "The passwords you entered do not match. Please ensure both fields contain the same password.": "入力されたパスワードが一致しません。両方のフィールドに同じパスワードを入力してください。", "By creating an account, you agree to our": "アカウントを作成することで、あなたは当社の利用規約に同意したことになります", "Create Your Account": "アカウントを作成", "Enter your credentials to sign up.": "サインアップするには資格情報を入力してください。", "We have discovered a security vulnerability in the": "セキュリティ脆弱性が発見されました", "that you are using on the following website:": "次のウェブサイトで使用しています:", "View Vulnerabilities": "脆弱性を表示", "Immediate Action Required: We found a security vulnerability in your Website": "緊急対応が必要です: あなたのウェブサイトにセキュリティ脆弱性が見つかりました。", "Community": "コミュニティ", "Want To Create A Staging Site For": "ステージングサイトを作成しますか", "A staging site is a secure clone of your live website for testing and development. It lets you experiment with plugins, themes, and changes risk-free before deploying them to live.": "ステージングサイトは、本番サイトの安全なクローンで、テストや開発に使用します。プラグイン、テーマ、変更を本番環境に反映する前に、安全に試すことができます。", "Test Domain": "テストドメイン", "Create a demo site with our test domains and customize it before going live.": "テストドメインでデモサイトを作成し、本番公開前にカスタマイズしてください。", "Custom Domain": "カスタムドメイン", "Enter your custom domain by simply pointing your domain to the server.": "ドメインをサーバーにポイントするだけでカスタムドメインを入力してください。", "Deploy Staging Site": "ステージングサイトをデプロイ", "Manage Staging": "ステージングを管理", "Integrity Monitor": "インテグリティモニター", "Scan Now": "今すぐスキャン", "Last scan": "最終スキャン", "Items found": "見つかったアイテム", "Message": "メッセージ", "Plugin": "プラグイン", "This website has found some checksum errors. This could be due to a corrupted file or a malicious attack. Please review the files and database of your site.": "このウェブサイトでいくつかのチェックサムエラーが見つかりました。これは、ファイルの破損や悪意のある攻撃が原因である可能性があります。サイトのファイルとデータベースを確認してください。", "Security Settings": "セキュリティ設定", "AI Bot Blocker": "AIボットブロッカー", "Disable Nginx Config Regeneration": "Nginx設定の再生成を無効化", "Disable OpenLiteSpeed Config Regeneration": "OpenLiteSpeed設定の再生成を無効化", "WP Fail2Ban": "WP Fail2Ban", "Block Failed Login Attempts": "ログイン試行の失敗をブロック", "Block Common Usernames": "一般的なユーザー名をブロック", "Block User Enumeration": "ユーザー列挙をブロック", "Protect Comments": "コメントを保護", "Block Spam": "スパムをブロック", "Guard Password Resets": "パスワードリセットを保護", "Guard Pingbacks": "ガードピンバック", "Enable OpenLiteSpeed Config Regeneration": "OpenLiteSpeed設定の再生成を有効にする", "Config file regeneration has been updated successfully": "設定ファイルの再生成が正常に更新されました", "This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config": "サイトに変更が加えられても、xCloudがOpenLiteSpeedの設定を再生成しないようにします。OpenLiteSpeedの設定を手動で再生成する必要があります。", "This will allow xCloud to regenerate OpenLiteSpeed config on any changes made to the site. You will not have to manually regenerate the OpenLiteSpeed config.": "これにより、xCloud はサイトに変更が加えられた際に OpenLiteSpeed の設定を自動的に再生成できます。OpenLiteSpeed の設定を手動で再生成する必要はありません。", "Serve robots.txt from file system": "ファイルシステムからrobots.txtを提供", "Failed to load nginx options": "nginxオプションの読み込みに失敗しました", "Failed to update config file regeneration": "設定ファイルの再生成に失敗しました", "This will prevent xCloud from regenerating OpenLiteSpeed config on any changes made to the site. You will have to manually regenerate the OpenLiteSpeed config.": "これにより、xCloud がサイトに加えられた変更に対して OpenLiteSpeed の設定を再生成することを防ぎます。OpenLiteSpeed の設定は手動で再生成する必要があります。", "This will prevent xCloud from regenerating nginx config on any changes made to the site. You will have to manually regenerate the nginx config.": "サイトに変更が加えられても、xCloud が nginx 設定を再生成しないようにします。nginx 設定を手動で再生成する必要があります。", "This will allow xCloud to regenerate nginx config on any changes made to the site. You will not have to manually regenerate the nginx config.": "これにより、xCloudはサイトに変更が加えられた際にnginx設定を自動的に再生成します。nginx設定を手動で再生成する必要はありません。", "Add this URL as a webhook in your Git repository settings to enable automated deployments": "Gitリポジトリ設定でこのURLをWebhookとして追加し、自動デプロイを有効にします。", "Laravel Debug Log": "<PERSON>velデバッグログ", "Environment": "環境", "Update Environment": "環境を更新", "Deploy Now": "今すぐデプロイ", "Deploying...": "デプロイ中...", "Your Laravel site deployment has been initiated. Please wait while the changes are being deployed.": "Laravelサイトのデプロイが開始されました。変更がデプロイされるまでお待ちください。", "Failed to deploy Laravel site. Please try again.": "Laravelサイトのデプロイに失敗しました。再試行してください。", "Add Supervisor Process": "スーパーバイザープロセスを追加", "Edit Supervisor Process": "スーパーバイザープロセスの編集", "Update Supervisor Process": "スーパーバイザープロセスの更新", "Supervisor Processes": "スーパーバイザープロセス", "Check Process Status": "プロセスのステータスを確認", "Supervisor": "監督者", "Process Management": "プロセス管理", "Background Processes": "バックグラウンドプロセス", "Daemon": "デーモン", "Supervisor Process Output": "プロセス出力の監督", "Processes": "プロセス", "View Logs": "ログを表示", "Restart": "再起動", "No supervisor processes found": "スーパーバイザープロセスが見つかりません", "Delete Supervisor Process": "スーパーバイザープロセスを削除", "Are you sure you want to delete this supervisor process? This action cannot be undone.": "この監督プロセスを削除してもよろしいですか？この操作は元に戻せません。", "This will remove the supervisor process from the server.": "これにより、サーバーからスーパーバイザープロセスが削除されます。", "Running": "実行中", "Stopped": "停止しました", "Fatal": "致命的", "Backoff": "バックオフ", "Starting": "開始", "Stopping": "停止中", "Exited": "終了しました", "Unknown": "不明", "Processing": "処理中", "You can run any executable command here. For Python, Node.js, or PHP scripts, specify the full command (e.g., python3 script.py).": "ここで任意の実行可能なコマンドを実行できます。Python、Node.js、またはPHPスクリプトの場合は、フルコマンドを指定してください（例: python3 script.py）。", "Optional. Specify the working directory for the process.": "オプション。プロセスの作業ディレクトリを指定します。", "By default, you can use root. To run as a different user, enter the username here.": "デフォルトでは、root を使用できます。別のユーザーとして実行するには、ここにユーザー名を入力してください。", "Number of Processes": "プロセス数", "Number of process instances to keep running.": "実行中のプロセスインスタンスの数。", "Start Seconds (Optional)": "開始秒数（任意）", "Supervisor will consider the process started after this many seconds.": "この秒数後にプロセスが開始されたとスーパーバイザーが判断します。", "Stop Seconds (Optional)": "秒数停止（オプション）", "Supervisor will wait this many seconds before force stopping the process.": "スーパーバイザーはプロセスを強制停止する前にこの秒数待機します。", "Stop Signal (Optional)": "停止信号（オプション）", "Supervisor sends this signal to stop the process. Leave empty for default (TERM).": "監督者がこのシグナルを送信してプロセスを停止します。デフォルト（TERM）を使用する場合は空白のままにしてください。", "Laravel Horizon is not installed": "<PERSON><PERSON> Horizonがインストールされていません", "Horizon is not detected in your composer.json. To use Horizon, you need to install it first:": "composer.jsonにHorizonが検出されませんでした。Horizonを使用するには、まずインストールしてください。", "Laravel Application": "Laravelアプリケーション", "Enable Debug Mode": "デバッグモードを有効にする", "When debug mode is enabled, detailed error messages will be displayed. This should be disabled in production.": "デバッグモードが有効になると、詳細なエラーメッセージが表示されます。本番環境では無効にしてください。", "Enable Maintenance Mode": "メンテナンスモードを有効にする", "When maintenance mode is enabled, a maintenance screen will be displayed for all requests to your application.": "メンテナンスモードが有効になると、すべてのリクエストに対してメンテナンス画面が表示されます。", "Application Environment": "アプリケーション環境", "Application": "アプリケーション", "The application environment affects various Laravel behaviors. This setting updates APP_ENV in your .env file": "アプリケーション環境は、さまざまなLaravelの動作に影響を与えます。この設定は、.envファイルのAPP_ENVを更新します。", "Clear Application Cache": "アプリケーションキャッシュをクリア", "Clears all Laravel caches by running the optimize:clear command.": "最適化:clear コマンドを実行して、すべての Laravel キャッシュをクリアします。", "Clearing...": "クリア中...", "Clear Cache": "キャッシュをクリア", "Laravel Horizon": "<PERSON>vel <PERSON>", "Update Process": "更新プロセス", "Start Horizon": "ホライゾンを開始", "Not Configured": "未設定", "Horizon is running": "ホライゾンが実行中", "Horizon is not running": "Horizonが実行されていません", "Stop": "停止", "Horizon provides a beautiful dashboard and code-driven configuration for your Laravel powered Redis queues.": "Horizonは、Laravelで動作するRedisキューのための美しいダッシュボードとコード駆動の設定を提供します。", "Laravel Scheduler": "Laravelスケジューラ", "Start Scheduler": "スケジューラー開始", "Your scheduler is running properly": "スケジューラーは正常に動作しています", "Scheduler needs to be configured": "スケジューラーを設定する必要があります", "Scheduler Frequency": "スケジューラー頻度", "The Laravel scheduler allows you to fluently and expressively define your command schedule within Laravel itself.": "Laravelスケジューラを使用すると、Laravel内でコマンドスケジュールを流暢かつ表現豊かに定義できます。", "Local": "ローカル", "Testing": "テスト中", "Every Minute": "毎分", "Every Five Minutes": "5分ごとに", "Every Ten Minutes": "10分ごとに", "Every Fifteen Minutes": "15分ごとに", "Every Thirty Minutes": "30分ごと", "Hourly": "毎時", "Server is not connected": "サーバーに接続されていません", "Failed to load Laravel application status": "Laravelアプリケーションのステータスを読み込めませんでした", "Laravel application settings updated successfully": "Laravelアプリケーションの設定が正常に更新されました", "Failed to update Laravel application settings": "Laravelアプリケーション設定の更新に失敗しました", "Application cache cleared successfully": "アプリケーションのキャッシュが正常にクリアされました", "Failed to clear application cache": "アプリケーションキャッシュのクリアに失敗しました", "Horizon started successfully": "ホライゾンが正常に起動しました", "Failed to start Horizon": "Horizonの起動に失敗しました", "Horizon stopped successfully": "ホライゾンが正常に停止しました", "Failed to stop Horizon": "Horizonの停止に失敗しました", "Horizon restarted successfully": "ホライゾンが正常に再起動しました", "Failed to restart Horizon": "Horizonの再起動に失敗しました", "Scheduler setup successfully": "スケジューラーの設定が完了しました", "Failed to setup scheduler": "スケジューラの設定に失敗しました", "Scheduler stopped successfully": "スケジューラーが正常に停止しました", "Failed to stop scheduler": "スケジューラの停止に失敗しました", "Laravel Queue": "<PERSON>vel<PERSON>ュー", "Add Queue Worker": "キュー ワーカーを追加", "No queue workers are running": "キュー作業者が実行されていません", "Connection": "接続", "Queue": "キュー", "Timeout": "タイムアウト", "Memory": "メモリ", "Refresh Status": "ステータスを更新", "Edit Process": "プロセスを編集", "Restart Process": "プロセスの再起動", "View Output": "出力を表示", "Queue workers process jobs in the background. They are useful for handling time-consuming tasks like sending emails, processing files, or any other task that should not block the main application.": "キュー ワーカーはバックグラウンドでジョブを処理します。メールの送信、ファイルの処理、またはメインアプリケーションをブロックしないようにする必要があるその他の時間のかかるタスクを処理するのに役立ちます。", "Update Queue Worker": "キュー作業者を更新", "redis": "Redis", "default": "デフォルト", "0": "0", "Maximum Seconds Per Job": "ジョブごとの最大秒数", "0 = No Timeout": "0 = タイムアウトなし", "256": "２５６", "Maximum Memory (Optional)": "最大メモリ（オプション）", "Memory limit in MB": "メモリ制限 (MB)", "1": "1", "Number of worker processes to run": "実行するワーカープロセスの数", "Queue Worker Output": "キュー ワーカー出力", "Queue worker updated successfully": "キュー ワーカーが正常に更新されました", "Queue worker added successfully": "キュー作業者が正常に追加されました", "Are you sure you want to stop this queue worker?": "このキュー作業者を停止してもよろしいですか？", "This will remove the queue worker process.": "キュー作業プロセスを削除します。", "Yes, Stop": "はい、中止", "Queue worker stopped successfully": "キュー ワーカーが正常に停止しました", "Failed to stop queue worker": "キュー作業者の停止に失敗しました", "Queue worker restarted successfully": "キュー ワーカーが正常に再起動されました", "Failed to restart queue worker": "キュー作業者の再起動に失敗しました", "Error loading output. Please try again.": "出力の読み込みエラーが発生しました。再試行してください。", "Failed to refresh worker status": "ワーカーのステータスを更新できませんでした", "Deployment has been initiated.": "デプロイが開始されました。", "Check": "確認", "Plugins Found": "プラグインが見つかりました", "Please configure Cloudflare SSL on this site to enable edge cache.": "このサイトでCloudflare SSLを設定し、エッジキャッシュを有効にしてください。", "Cloudflare Edge Cache is not available for staging sites. Please switch to production with Cloudflare SSL for this site to enable edge cache.": "Cloudflare Edge Cacheはステージングサイトでは利用できません。このサイトでエッジキャッシュを有効にするには、Cloudflare SSLを使用して本番環境に切り替えてください。", "You need to set up Cloudflare integration first to enable edge cache.": "エッジキャッシュを有効にするには、まずCloudflare統合を設定する必要があります。", "Domain is not available on Cloudflare. Please add your domain to Cloudflare first to enable edge cache.": "ドメインはCloudflareで利用できません。エッジキャッシュを有効にするには、まずCloudflareにドメインを追加してください。", "Cloudflare Edge Cache has been enabled successfully.": "Cloudflareエッジキャッシュが正常に有効化されました。", "Cloudflare Edge Cache has been disabled successfully.": "Cloudflareエッジキャッシュが正常に無効化されました。", "Failed to update Cloudflare Edge Cache settings.": "Cloudflare Edge Cache 設定の更新に失敗しました。", "Cloudflare Edge Cache purged successfully.": "Cloudflareエッジキャッシュが正常に削除されました。", "Failed to purge Cloudflare Edge Cache.": "Cloudflare Edgeキャッシュの削除に失敗しました。", "Cloudflare Edge Cache": "Cloudflare エッジキャッシュ", "Boost your website's performance with Cloudflare Edge Cache by caching content at Cloudflare's global edge network. This reduces server load, enhances speed, and improves user experience worldwide.": "Cloudflare Edge Cacheを使用して、Cloudflareのグローバルエッジネットワークでコンテンツをキャッシュし、ウェブサイトのパフォーマンスを向上させましょう。これにより、サーバー負荷が軽減され、速度が向上し、世界中のユーザーエクスペリエンスが改善されます。", "Clear Edge Cache": "エッジキャッシュをクリア", "This will purge all cached content from Cloudflare's edge network.": "Cloudflareのエッジネットワークからすべてのキャッシュされたコンテンツを消去します。", "Failed to update Cloudflare Edge Cache settings": "Cloudflare Edge Cache 設定の更新に失敗しました", "Are you sure you want to disable Cloudflare Edge Cache?": "Cloudflare Edge Cacheを無効にしてもよろしいですか？", "Failed to disable Cloudflare Edge Cache": "Cloudflareエッジキャッシュの無効化に失敗しました", "Cloudflare Edge Cache purged successfully": "Cloudflareエッジキャッシュが正常に削除されました", "Patchstack Subscriptions": "Patchstack サブスクリプション", "List of Patchstack Subscriptions": "Patchstackサブスクリプション一覧", "Find all the patchstack subscriptions associated with your account here.": "ここで、アカウントに関連付けられているすべてのPatchstackサブスクリプションを見つけることができます。", "Vulnerability Shield Pro is active!": "脆弱性シールドプロが有効です！", "Vulnerability Shield Pro will now deliver faster alerts to keep you ahead of threats.": "脆弱性シールドProは、より迅速なアラートを提供し、脅威に先んじます。", "WordPress Core.": "WordPressコア", "Get faster security alerts with Vulnerability Shield Pro powered by": "脆弱性シールドプロでより迅速なセキュリティアラートを取得", "Patchstack - only $4/month.": "Patchstack - 月額わずか$4。", "Upgrade to PRO": "PROにアップグレード", "Canceling...": "キャンセル中...", "Vulnerability Shield Pro": "脆弱性シールドプロ", "Powered by Patchstack": "Patchstack提供", "Stay protected with continuous vulnerability scans and advanced threat alerts to secure your WordPress website like a pro.": "プロのようにWordPressサイトを保護するために、継続的な脆弱性スキャンと高度な脅威アラートで安全を確保しましょう。", "mo": "申し訳ありませんが、提供されたテキスト「mo」は翻訳するには不十分です。もう少し文脈や内容を教えていただければ、適切な翻訳を提供できます。", "Upgrade to Pro NOW": "今すぐProにアップグレード", "Remove Subscription": "サブスクリプションを解除", "Removing...": "削除中...", "Are you sure you want to remove the subscription for this site?": "このサイトの購読を解除してもよろしいですか？", "Once confirm, this cannot be undone.": "確認すると、元に戻せません。", "Patchstack - only": "パッチスタック - のみ", "/month.": "/月", "Subscription Status": "サブスクリプションの状態", "Export Sites Data": "サイトデータをエクスポート", "Export Servers Data": "サーバーデータのエクスポート", "Export": "エクスポート", "Export site data in your preferred format by selecting the desired export type, format, and data columns.": "エクスポートタイプ、フォーマット、データ列を選択して、お好みの形式でサイトデータをエクスポートします。", "Export server data in your preferred format by selecting the desired export type, format, and data columns.": "エクスポートタイプ、フォーマット、データ列を選択して、サーバーデータをお好みの形式でエクスポートします。", "Select Export Type": "エクスポートタイプを選択", "Choose what to export": "エクスポートする項目を選択", "Excel (XLSX)": "Excel (XLSX)", "CSV": "CSV", "Select Columns": "列を選択", "Exporting...": "エクスポート中...", "Export Format": "エクスポート形式", "Issues": "問題", "No issues were detected by Vulnerability Shield Pro. You’ll be notified immediately if any issues are found.": "脆弱性シールドプロによって問題は検出されませんでした。問題が見つかった場合はすぐに通知されます。", "Excellent! Your WordPress Core is fully secure!": "素晴らしいです！WordPressコアは完全に安全です！", "Excellent! Your plugins are fully secure!": "素晴らしい！プラグインは完全に安全です！", "Excellent! Your theme is fully secure!": "素晴らしい！テーマは完全に安全です！", "Issue": "問題", "No issues were detected by Vulnerability Scanner. You’ll be notified immediately if any issues are found.": "脆弱性スキャナーによって問題は検出されませんでした。問題が見つかった場合は、すぐに通知されます。", "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $5/mo for advanced vulnerability detection and automated patching powered by Patchstack.": "脆弱性スキャナーによる問題は検出されませんでした。問題が見つかった場合はすぐに通知されます。Patchstackによる高度な脆弱性検出と自動パッチ適用を利用するには、月額$5でProにアップグレードしてください。", "Go PRO for": "PRO版にアップグレード", "Upgrade to Vulnerability Shield Pro for automated vulnerability detection & virtual patching powered by Patchstack": "Patchstackによる自動脆弱性検出と仮想パッチを備えたVulnerability Shield Proにアップグレード", "Output": "出力", "Shield Enabled": "シールド有効", "Enable Shield": "シールドを有効にする", "Vulnerability Shield": "脆弱性シールド", "Pro": "プロ", "Enable Vulnerability Shield Pro to secure this site with automated virtual patching and faster notification alerts.": "このサイトを自動仮想パッチと迅速な通知アラートで保護するために、Vulnerability Shield Proを有効にします。", "Pay now for": "今すぐ支払う", "Continue free plan": "無料プランを続ける", "I am not interested at this moment. Please, do not show this message again.": "現在は興味がありません。このメッセージを再表示しないでください。", "The plugin has a vulnerability that makes it possible for unauthorized actions.": "プラグインに脆弱性があり、不正な操作が可能です。", "This vulnerability affects": "この脆弱性は影響します", "We have discovered security vulnerabilities in the": "ソフトウェアにセキュリティの脆弱性が見つかりました。", "These": "これら", "have vulnerabilities that make it possible for unauthorized actions.": "不正な操作を可能にする脆弱性があります。", "The plugins have vulnerabilities that make it possible for unauthorized actions.": "プラグインにより、許可されていない操作が可能になる脆弱性があります。", "We have discovered security vulnerabilities in": "セキュリティの脆弱性が発見されました", "and a few more plugins that you are using on the following website:": "以下のウェブサイトで使用している他のプラグイン:", "We detected this vulnerability on": "この脆弱性を検出しました", "UTC. You can ignore this message if you have already taken care of it.": "UTC。すでに対応済みの場合は、このメッセージを無視してください。", "Thank you for being a": "ユーザーでいてくれてありがとうございます", "user!": "ユーザー", "If you have any questions, don't hesitate to contact our": "ご不明点がございましたら、お気軽にお問い合わせください。", "support team": "サポートチーム", "Thank you for being a xCloud user!": "xCloudをご利用いただきありがとうございます！", "We found a security vulnerability in your Website": "お客様のウェブサイトにセキュリティ脆弱性が見つかりました。", "Scanning Now": "スキャン中", "Last Scanned": "最終スキャン日時", "No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $": "脆弱性スキャナーによる問題は検出されませんでした。問題が見つかった場合はすぐに通知されます。$でProにアップグレードしてください。", "/mo for advanced vulnerability detection and automated patching powered by Patchstack.": "Patchstackによる高度な脆弱性検出と自動パッチ適用のための/mo。"}