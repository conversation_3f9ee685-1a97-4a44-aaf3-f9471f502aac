<?php
return [
    'server.create' => [
        'title' => 'Choosing A Provider',
        'content' => 'To get started, choose which provider you want to create your server on',
        'documentations' => [
            [
                'title' => 'Setup your first server and site with xCloud Managed Servers',
                'url' => 'https://xcloud.host/docs/setup-your-first-server-site-with-xcloud/',
            ],
            [
                'title' => 'How to Setup a Vultr Server in xCloud and Get free hosting for 6 months?',
                'url' => 'https://xcloud.host/docs/how-to-get-free-hosting-with-vultr/',
            ],
            [
                'title' => 'How to Setup a Digital Ocean Server in xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-setup-your-server-with-digital-ocean/',
            ],
            [
                'title' => 'How to Setup a GCP Server in xCloud?',
                'url' => 'https://xcloud.host/docs/first-server-with-google-cloud-platform/',
            ],[
                'title' => 'How to connect existing fresh Ubuntu server from any provider?',
                'url' => 'https://xcloud.host/docs/how-to-set-up-server-with-other-providers/',
            ],
        ],
    ],
    '/provider/create' => [
        'title' => 'Choosing A Provider',
        'content' => 'To get started, choose which provider you want to create your server on',
        'documentations' => [
            [
                'title' => 'How to set up a server with xCloud?',
                'url' => '#',
            ],
            [
                'title' => 'How to set up a server on xCloud with DigitalOcean?',
                'url' => '#',
            ],
            [
                'title' => 'How to set up a server on xCloud with other providers?',
                'url' => '#',
            ],
        ]
    ],
    '/server/create/xcloud' => [
        'title' => 'Setting up your server with xCloud',
        'content' => 'Follow the instructions given below to set up your server with xCloud as your selected provider.',
        'video' => [
            'url' => 'https://www.youtube.com/embed/O8wJUimPB2w?si=rHKEH7c98GRmmFUQ',
            'thumbnail' => 'img/video/xcloud-server.jpg'
        ],
        'accordions' => [
            [
                'title' => 'How to select the server size?',
                'content' => 'We recommend at least a 2GB/ 1 shared CPU server for 10-20 static brochure sites with cache enabled. And for a site whose pages cannot be cached (e.g. WooCommerce site) we recommend a 4 GB / 2 dedicated CPUs server. You can also later upgrade your server from xCloud.',
                'tag' => 'server_size',
                'name' => 'Server Size',
            ],
            [
                'title' => 'How to upgrade my server size?',
                'content' => 'Please follow our documentation <a href="#">here</a> to get the step-by-step guide on upgrading your server size.',
                'tag' => 'upgrade_size',
            ],
            [
                'title' => 'How to choose a region for your server?',
                'content' => 'In order to ensure fastest delivery, we recommend choosing a region that is closest to the geographical location of your target audience.',
                'tag' => 'region',
                'name' => 'Region',
            ],
            [
                'title' => 'How to Set tags?',
                'content' => 'Tags are a great way to organize your servers. You can use tags to group servers by project, environment, purpose, owner, or any other criteria that is meaningful to you. You can also use tags to filter the list of servers in the Servers page.',
                'tag' => 'tags',
                'name' => 'Tags',
            ],
            [
                'title' => 'How to select the database server?',
                'content' => 'For your WordPress website, you must choose an appropriate database server. Most WordPress websites work well with both MySQL and MariaDB databases. Whichever database you choose, select carefully as this cannot be changed later.',
                'tag' => 'database_name',
                'name' => 'Database Server',
                'depends' => true,
            ],
            [
                'title' => 'What is the database root password?',
                'content' => 'The database root password is needed when you need to do tasks on your database that require root access. This password is autogenerated. You will need to store it in a secure place if you ever need to work on your databases with root access as you will not be able to retrieve it again.',
                'tag' => 'database_password',
                'name' => 'Database Root Password',
                'depends' => true,
            ]
        ],
        'documentations' => [
            [
                'title' => 'How to set up a server with xCloud?',
                'url' => '#',
            ],
            [
                'title' => 'How to set up a server with xCloud?',
                'url' => '#',
            ],
            [
                'title' => 'How to set tags for xCloud?',
                'url' => '#',
            ],
            [
                'title' => 'How to set up a server with xCloud?',
                'url' => '#',
            ],
        ],
        'fields' => [
            'server_size',
            'region',
            'tags',
            'database_name',
            'database_password'
        ]
    ],
    '/server/create/gcp' => [
        'title' => 'Setup a GCP Server in xCloud',
        'content' => 'Follow the instructions given below to set up your gcp server in xCloud.',
        'video' => [
            'url' => 'https://www.youtube.com/embed/FLl_-L5fwGA?si=Jd6FYgI8b07iyRzT',
            'thumbnail' => 'img/video/gcp-server.jpeg'
        ],
        'accordions' => [
            [
                'title' => 'How to select the server size?',
                'content' => 'We recommend at least a 2GB/ 1 shared CPU server for 10-20 static brochure sites with cache enabled. And for a site whose pages cannot be cached (e.g. WooCommerce site) we recommend a 4 GB / 2 dedicated CPUs server. You can also later upgrade your server from xCloud.',
                'tag' => 'server_size',
                'name' => 'Server Size',
            ],
            [
                'title' => 'How to upgrade my server size?',
                'content' => 'Please follow our documentation <a href="#">here</a> to get the step-by-step guide on upgrading your server size.',
                'tag' => 'upgrade_size',
            ],
            [
                'title' => 'How to choose a region for your server?',
                'content' => 'In order to ensure fastest delivery, we recommend choosing a region that is closest to the geographical location of your target audience.',
                'tag' => 'region',
                'name' => 'Region',
            ],
            [
                'title' => 'How to Set tags?',
                'content' => 'Tags are a great way to organize your servers. You can use tags to group servers by project, environment, purpose, owner, or any other criteria that is meaningful to you. You can also use tags to filter the list of servers in the Servers page.',
                'tag' => 'tags',
                'name' => 'Tags',
            ],
            [
                'title' => 'How to select the database server?',
                'content' => 'For your WordPress website, you must choose an appropriate database server. Most WordPress websites work well with both MySQL and MariaDB databases. Whichever database you choose, select carefully as this cannot be changed later.',
                'tag' => 'database_name',
                'name' => 'Database Server',
                'depends' => true,
            ],
            [
                'title' => 'What is the database root password?',
                'content' => 'The database root password is needed when you need to do tasks on your database that require root access. This password is autogenerated. You will need to store it in a secure place if you ever need to work on your databases with root access as you will not be able to retrieve it again.',
                'tag' => 'database_password',
                'name' => 'Database Root Password',
                'depends' => true,
            ]
        ],
        'documentations' => [
            [
                'title' => 'How to Setup a GCP Server in xCloud?',
                'url' => 'https://xcloud.host/docs/first-server-with-google-cloud-platform/',
            ]
        ],
        'fields' => [
            'server_size',
            'region',
            'tags',
            'database_name',
            'database_password'
        ]
    ],
    '/server/create/digitalocean' => [
        'title' => 'Setting up your server with DigitalOcean',
        'content' => 'Follow the instructions given below to set up your server with DigitalOcean as your selected provider.',
        'video' => [
            'url' => 'https://www.youtube.com/embed/0WxxHOphX3A?si=wQ6CtSwiVRo-Tw8Y',
            'thumbnail' => 'img/video/digital-ocean-server.jpg'
        ],
        'accordions' => [
            [
                'title' => 'How to retrieve your label and API Token from DigitalOcean?',
                'content' => 'Please provide the label and API Token from your DigitalOcean account to connect with xCloud. You can learn how to retrieve the label and API from our documentation here.',
                'tag' => 'label',
                'name' => '',
            ],
            [
                'title' => 'Digital Ocean Integration',
                'content' => 'Authorize xCloud to use your DigitalOcean account for server setup with a simple and hassle-free OAuth flow.',
                'tag' => 'label',
                'name' => '',
            ]
        ],
        'fields' => [
            'label',
            // 'api_token',
        ],
        'documentations' => [
            [
                'title' => 'How to Setup a Digital Ocean Server in xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-setup-your-server-with-digital-ocean/',
            ],
        ]
    ],
    '/credential/choose/digitalocean' => [
        'title' => 'Setting up your server with DigitalOcean',
        'content' => 'Follow the instructions given below to set up your server with DigitalOcean as your selected provider.',
        'video' => [
            'url' => 'https://www.youtube.com/embed/0WxxHOphX3A?si=wQ6CtSwiVRo-Tw8Y',
            'thumbnail' => 'img/video/digital-ocean-server.jpg'
        ],
        'accordions' => [
            [
                'title' => 'How to retrieve your label and API Token from DigitalOcean?',
                'content' => 'Please provide the label and API Token from your DigitalOcean account to connect with xCloud. You can learn how to retrieve the label and API from our documentation here.',
                'tag' => 'label',
                'name' => '',
            ],
            [
                'title' => 'Digital Ocean Integration',
                'content' => 'Authorize xCloud to use your DigitalOcean account for server setup with a simple and hassle-free OAuth flow.',
                'tag' => 'label',
                'name' => '',
            ]
        ],
        'fields' => [
            'label',
            // 'api_token',
        ],
        'documentations' => [
            [
                'title' => 'How to Setup a Digital Ocean Server in xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-setup-your-server-with-digital-ocean/',
            ],
        ]
    ],
    '/credential/choose/vultr'=> [
        'title' => 'Setting up your server with Vultr',
        'content' => 'Follow the instructions given below to set up your server with Vultr as your selected provider.',
        'video' => [
            'url' => 'https://www.youtube.com/embed/8TqRK_NVkko?si=uKntoqfIEwMXhXGu',
            'thumbnail' => 'img/video/vultr-server.jpg'
        ],
        'accordions' => [
            [
                'title' => 'Vultr Integration',
                'content' => 'Sign up in Vultr with xCloud promo or log in if you have already an account. Visit the API section and click on the ‘Enable API’ button. This will generate the  Personal Access Token Copy the API Key/Token and paste it into the xCloud Vultr integration page.',
                'tag' => 'label',
                'name' => '',
            ],
        ],
        'documentations' => [
            [
                'title' => 'How to Setup a Vultr Server in xCloud and Get free hosting for 6 months?',
                'url' => 'https://xcloud.host/docs/how-to-get-free-hosting-with-vultr/',
            ],
        ]
    ],
    'server.create.digitalocean' => [
        'title' => 'Setting up your server with DigitalOcean',
        'content' => 'Follow the instructions given below to set up your server with DigitalOcean as your selected provider.',
        'accordions' => [
            [
                'title' => 'How to select the server size?',
                'content' => 'We recommend at least a 2GB/ 1 shared CPU server for 10-20 static brochure sites with cache enabled. And for a site whose pages cannot be cached (e.g. WooCommerce site) we recommend a 4 GB / 2 dedicated CPUs server. You can also later upgrade your server from xCloud.',
                'tag' => 'server_size',
                'name' => 'Server Size',
            ],
            [
                'title' => 'How to upgrade my server size?',
                'content' => 'Please follow our documentation here to get the step-by-step guide on upgrading your server size. ',
                'tag' => 'upgrade_size',
            ],
            [
                'title' => 'How to choose a region for your server?',
                'content' => 'In order to ensure fastest delivery, we recommend choosing a region that is closest to the geographical location of your target audience.',
                'tag' => 'region',
                'name' => 'Region',
            ],
            [
                'title' => 'How to select the database server?',
                'content' => 'For your WordPress website, you must choose an appropriate database server. Most WordPress websites work well with both MySQL and MariaDB databases. Whichever database you choose, select carefully as this cannot be changed later.',
                'tag' => 'database_type',
                'name' => 'Database Server',
            ],
            [
                'title' => 'What is the database root password?',
                'content' => 'The database root password is needed when you need to do tasks on your database that require root access. This password is autogenerated. You will need to store it in a secure place if you ever need to work on your databases with root access as you will not be able to retrieve it again.',
                'tag' => 'database_password',
                'name' => 'Database Root Password',
            ],
            [
                'title' => 'How to Set tags?',
                'content' => 'Tags are a great way to organize your servers. You can use tags to group servers by project, environment, purpose, owner, or any other criteria that is meaningful to you. You can also use tags to filter the list of servers in the Servers page.',
                'tag' => 'tags',
                'name' => 'Tags',
            ],
        ],
        'fields' => [
            'server_size',
            'region',
            'database_type',
            'tags',
            'database_password',
        ]
    ],
    'server.create.custom' => [
        'title' => 'Connect your server in Xcloud from other provider',
        'content' => 'Follow the instructions given below to connect xCloud with your desired server provider.',
        'video' => [
            'url' => 'https://www.youtube.com/embed/F7DCbNY8TjE?si=8BiCu0WQAWxRGwfv',
            'thumbnail' => 'img/video/any-server.jpg'
        ],
        'accordions' => [
            [
                'title' => 'What is SSH Port?',
                'content' => 'xCloud will connect with your desired server provider over SSH. Usually by default, the SSH Port is 22. However, if your configurations are different, then you need to add your SSH port here',
                'tag' => 'ssh_port',
                'name' => 'SSH',
            ],
            [
                'title' => 'What is the SSH Username?',
                'content' => 'To connect xCloud with your server provider, we need to log in to your server with full user access. For this reason, we require your SSH username. For most server providers, a newly provisioned server will have the ‘root’ username that has full access privileges to the server. However, for Amazon EC2, the username that has full access privileges to the server will be ‘ubuntu’.',
                'tag' => 'ssh_username',
            ],
            [
                'title' => 'How to Set tags?',
                'content' => 'Tags are a great way to organize your servers. You can use tags to group servers by project, environment, purpose, owner, or any other criteria that is meaningful to you. You can also use tags to filter the list of servers in the Servers page.',
                'tag' => 'tags',
                'name' => 'Tags',
            ],
            [
                'title' => 'Connect via Password',
                'content' => 'Please enter the password to connect your custom server',
                'tag' => 'password',
                'name' => 'Authentication',
                'depends' => true,
                'open_with' => 'password',
            ],
            [
                'active'=>true,
                'title' => 'Connect via Public Key',
                'content' => "To grant access for provisioning on a server where password authentication is disabled, you'll need to add the xCloud public key. SSH into the server using the default account and execute the following command to add the public key.",
                'name' => 'Authentication',
                'tag' => 'public_key',
                'depends' => true,
                'open_with' => 'public_key',
            ]
        ],
        'documentations' => [
            [
                'title' => 'How to connect existing fresh Ubuntu server from any provider?',
                'url' => 'https://xcloud.host/docs/how-to-set-up-server-with-other-providers/',
            ],
        ],
        'fields' => [
            'ssh_port',
            'ssh_username',
            'tags',
            'password',
            ]
        ],
    'server.migrate.source' => [
        'title' => 'Connect Your Source Server To Migrate Sites',
        'content' => 'Fill in the details below to connect xCloud with your source Ubuntu server from any cloud provider.',
        'video' => [
            'url' => 'https://www.youtube.com/embed/Mxji9q12rpY?si=OlV6zVClucXdB3fF',
            'thumbnail' => 'img/video/full-server-migration.jpg'
        ],
        'accordions' => [
            [
                'title' => 'IP Address',
                'content' => 'Please enter the IP address of your source server. This is the IP address of the server from which you want to migrate your sites.',
                'tag' => 'public_ip',
                'name' => 'IP Address',
            ],
            [
                'title' => 'What is SSH Port?',
                'content' => 'xCloud will connect with your desired server provider over SSH. Usually by default, the SSH Port is 22. However, if your configurations are different, then you need to add your SSH port here',
                'tag' => 'ssh_port',
                'name' => 'SSH',
            ],
            [
                'title' => 'What is the SSH Username?',
                'content' => 'To connect xCloud with your server provider, we need to log in to your server with full user access. For this reason, we require your SSH username. For most server providers, a newly provisioned server will have the ‘root’ username that has full access privileges to the server. However, for Amazon EC2, the username that has full access privileges to the server will be ‘ubuntu’.',
                'tag' => 'ssh_username',
            ],
            [
                'title' => 'Connect via Password',
                'content' => 'If your source server is enabled with SSH password then please enter the root password to connect your source server.',
                'tag' => 'password',
                'name' => 'Authentication',
                'depends' => true,
                'open_with' => 'password',
            ],
            [
                'active'=>true,
                'title' => 'Connect via Public Key',
                'content' => "To grant access for performing full server migration on a server where password authentication is disabled, you'll need to add the xCloud public key. All you need to do is copy and paste the public key in your source server as a root user.",
                'name' => 'Authentication',
                'tag' => 'public_key',
                'depends' => true,
                'open_with' => 'public_key',
            ]
        ],
        'documentations' => [
            [
                'title' => 'How To Perform A Full Server Migration With xCloud?',
                'url' => 'https://xcloud.host/docs/perform-a-full-server-migration-with-xcloud/',
            ]
        ],
        'fields' => [
            'public_ip',
            'ssh_port',
            'ssh_username',
            'tags',
            'password',
            ]
        ],
    'site.ssh' => [
        'title' => 'Connect your site in Xcloud from other provider',
        'content' => "Xcloud deploys sites with a unique system user referred to as a site user, who owns the site. The site user is granted SSH and SFTP access to the server but has limited access to the site's directory. For making any changes to a site or running WP CLI, it is recommended to use the site user. To learn more about site users, further research is suggested.",
        'accordions' => [
            [
                'title' => 'Connect via Public Key',
                'content' => 'This will add your Public Key to the server. You may add a new SSH key or select a previously added SSH key.',
                'name' => 'Authentication',
                'tag' => 'public_key',
                //'depends' => true,
                //'open_with' => 'public_key',
            ],
            [
                'title' => 'Connect via Password',
                'content' => 'Enter a password to enable sFTP access for your site.',
                'tag' => 'password',
                //'name' => 'Authentication',
                //'depends' => true,
                //'open_with' => 'password',
            ]
        ],
        'documentations' => [
            [
                'title' => 'How to Enable SSH Access for Site Users',
                'url' => 'https://xcloud.host/docs/how-to-enable-ssh-access-for-site-users/',
            ],
            [
                'title' => 'Guide to Connecting to Your Site Using SSH/SFTP',
                'url' => 'https://xcloud.host/docs/how-to-configure-sftp-access-for-sudo-users-or-site-users-in-xcloud/',
            ],
            [
                'title' => 'How To Configure SFTP Access For Sudo Users Or Site Users In xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-configure-sftp-access',
            ]
        ],
        'fields' => [
            'ssh_port',
            'ssh_username',
            'tags'
            ]
        ],
    'site.migrate.destination' => [
        'title' => 'Migrate WordPress Site',
        'video' => [
            'url' => 'https://www.youtube.com/embed/1EvBCXMCKZ0?si=FUfufWhD96T56daJ',
            'thumbnail' => 'img/video/site-migrate.jpeg'
        ],
        'accordions' => [
            [
                'title' => 'What is destination server?',
                'content' => "Select the server where you want to host your WordPress site. You can choose from the list of available servers or create a new server to install a site.",
                'tag' => 'destination_server',
                'name' => 'Destination Server',
            ],
        ],
        'fields'=> [
            'destination_server',
        ]
    ],
    'site.create' => [
        'title' => 'Adding A New Site With xCloud',
        'accordions' => [
            [
                'title' => 'Install New WordPress Website:',
                'content' => "Choose this option if you want to start from scratch and create a brand-new WordPress website. You'll be guided through the setup process with domain setup or staging site with xCloud temporary domain.",
                'tag' => 'new_wordpress',
            ],
            [
                'title' => 'Clone a Git Repository:',
                'content' => "Use this option to create a new website by cloning a Git repository. This is ideal if you have an existing project stored in a Git repository and want to deploy it on xCloud. Simply provide the Git repository URL, and xCloud will handle the rest.",
                'tag' => 'clone_git',
            ],
            [
                'title' => 'Migrate An Existing WordPress Website:',
                'content' => "If you already have a WordPress website hosted elsewhere mainly in a shared hosting and want to move it to xCloud, select this option. We'll assist you in migrating your existing website seamlessly to our platform simply by installing a plugin on your source site, ensuring minimal downtime and data integrity.",
                'tag' => 'migrate_wordpress',
            ],
            [
                'title' => 'Manually Upload WordPress Website:',
                'content' => "If you have an existing WordPress website stored as a zipped file, choose this option. You can upload your website's zipped file, and xCloud will unpack and set it up for you. This is useful when you have a website backup or prefer manual control over the migration process.",
                'tag' => 'upload_wordpress',
            ],
            [
                'title' => 'Migrate Full Server:',
                'content' => "Migrate all your WordPress sites from Ubuntu servers with ease using this feature if you have root access to your source server. Whether you have multiple WordPress websites on your current server or need to move your entire hosting environment, xCloud makes the migration process efficient and straightforward.",
                'tag' => 'migrate_full_server',
            ],
        ],
        'documentations' => [
            [
                'title' => 'Install A Fresh WP Site in xCloud',
                'url' => 'https://xcloud.host/docs/setup-your-first-server-site-with-xcloud/#2-toc-title',
            ],
            [
                'title' => 'How To Clone A Website From Git Repository With xCloud?',
                'url' => 'https://xcloud.host/docs/clone-a-website-from-git-repository-with-xcloud/',
            ],
            [
                'title' => 'How to migrate an existing WP Site? ',
                'url' => 'https://xcloud.host/docs/migrate-an-existing-wordpress-website-in-xcloud/',
            ],
            /*[
                'title' => 'Manually Upload your WP site in xCloud.',
                'url' => '#',
            ],*/[
                'title' => 'How to migrate Full Server and bulk sites in xCloud?',
                'url' => 'https://xcloud.host/docs/perform-a-full-server-migration-with-xcloud/',
            ],
        ],
    ],
    'site.migrate.plugin' => [
        'title' => 'Adding A New Site With xCloud',
        'content' => 'To migrate your WordPress site, you will need to install a plugin and add a token to the source site. There are three ways to install the plugin:',
        'accordions' => [
            [
                'title' => 'Install from WordPress.org',
                'tag' => 'wordpress_org',
                'content' => "This involves searching for the plugin in the WordPress plugin repository and installing it directly from your WordPress dashboard. Or visit using the direct URL given below: Plugin Link: https://www.wordpress.org/plugins/xcloud-migration-assistant",
                'name' => 'Install from WordPress.org',
            ],
            [
                'title' => 'Download the zipped file',
                'content' => "You can download the zipped file of the plugin from the plugin's official website and then upload and install it from your WordPress dashboard.",
                'name' => 'Download the zipped file',
                'tag' => 'zipped_file',
            ],
            [
                'title' => 'Install using WP CLI',
                'content' => "This involves using the WordPress Command Line Interface (WP CLI) to install the plugin from the command line.",
                'name' => 'Install using WP CLI',
                'tag' => 'wp_cli',
            ],
        ],
    ],
    'site.migrate.auto.database' => [
        'title' => 'Adding A New Site With xCloud',
        'content' => "By transferring all database tables and files, all of your site's content, settings, and configurations will be migrated to the new server. This option is recommended if you are moving to a new server and want to keep all of your site's data intact.
                        If you choose to only transfer the files, your site's content, images, and other media files will be migrated, but not the database. This option may be useful if you are just moving your site to a new server and don't need to change the database settings.",
        'accordions' => [
            [
                'title' => 'Files and Database',
                'tag' => 'wordpress_org',
                'content' => "Xcloud migration allows you have the option to choose the type of data you would like to transfer. You can either transfer all database tables and files or only the files, not the database.",
                'name' => 'Files and Database',
            ],
        ],
    ],
    'site.migrate.files' => [
        'title' => 'Adding A New Site With xCloud',
        'content' => "By transferring all database tables and files, all of your site's content, settings, and configurations will be migrated to the new server. This option is recommended if you are moving to a new server and want to keep all of your site's data intact.
                        If you choose to only transfer the files, your site's content, images, and other media files will be migrated, but not the database. This option may be useful if you are just moving your site to a new server and don't need to change the database settings.",
        'accordions' => [
            [
                'title' => 'Files and Database',
                'tag' => 'wordpress_org',
                'content' => "Xcloud migration allows you have the option to choose the type of data you would like to transfer. You can either transfer all database tables and files or only the files, not the database.",
                'name' => 'Files and Database',
            ],
        ],
    ],
    'site.migrate.auto.domains' => [
        'title' => 'Migrate WordPress Site',
        'content' => 'To migrate a site all we need is your existing site domain and new domain details',
        'video' => [
            'url' => 'https://www.youtube.com/embed/1EvBCXMCKZ0?si=FUfufWhD96T56daJ',
            'thumbnail' => 'img/video/site-migrate.jpeg'
        ],
        'accordions' => [
            [
                'title' => 'What is existing site url?',
                'content' => "The URL of the current WordPress site that you want to migrate to a new server.",
                'tag' => 'existing_site_url',
                'name' => 'Existing Site URL',
            ],
            [
                'title' => 'What is new site title?',
                'content' => "The title that will be assigned to your new WordPress site after migration.",
                'tag' => 'site_title',
                'depends' => true,
                'open_with' => 'migrate_into_new_domain',
            ],
            [
                'title' => 'What is new domain name?',
                'content' => "The new domain name that you want to use for your migrated WordPress site.",
                'tag' => 'domain_name',
                'depends' => true,
                'open_with' => 'migrate_into_new_domain',
            ],[
                'title' => 'What is new HTTPS?',
                'content' => "Enabling HTTPS will improve the security of your site and may be required for certain features to work correctly. You can use a free SSL certificate issued & managed by xCloud. Make sure to add the A records as instructed to point your domain to the assigned server. Otherwise, you can also provide your own certificate.
However, if you have already activated a free SSL service, such as Cloudflare, it is important to turn it off before activating our system's SSL certificate.",
                'tag' => 'ssl_provider',
                'depends' => true,
                'open_with' => 'migrate_into_new_domain',
            ],
        ],
        'fields'=> [
            'existing_site_url',
            'site_title',
            'domain_name',
            'ssl_provider',
        ]
    ],
    'site.migrate.settings' => [
        'title' => 'Migrate WordPress Site',
        'content' => 'Update Database and PHP version if needed',
        'video' => [
            'url' => 'https://www.youtube.com/embed/1EvBCXMCKZ0?si=FUfufWhD96T56daJ',
            'thumbnail' => 'img/video/site-migrate.jpeg'
        ],
        'accordions' => [
            [
                'title' => 'What is the PHP version?',
                'content' => "The version of the PHP programming language that your WordPress site will use. You should select the latest version of PHP or a version that is compatible with your WordPress version.",
                'tag' => 'php_version',
                'name' => 'Manage PHP and Database',
            ],
            [
                'title' => 'What is the prefix?',
                'content' => 'This is a string of characters that is added to the beginning of database table names. It helps to prevent naming conflicts with other websites on the same database.',
                'tag' => 'prefix',
            ],[
                'title' => "What is the site user?",
                'content' => "Xcloud deploys websites that are owned by a special type of system user called a \"site user.\" These site users have limited access to files, which stops them from seeing other sites' files and confidential files like Nginx configurations. They are able to use SSH and SFTP to connect to the server, but they can only access the site's directory. This makes site users suitable for clients who need an SFTP user to manage their website as they won't be able to see or change other sites on the server.",
                'tag' => 'site_user',
            ],
            [
                'title' => 'What is the full page cache?',
                'content' => "Enabling this will store the entire page in the server's memory, which means that when a user requests that page, it can be delivered quickly without the need for additional processing or database queries.",
                'tag' => 'full_page_cache',
            ],[
                'title' => 'What is the redis object cache?',
                'content' => 'Enabling this will store data in memory, which means that when a user requests data from your website, it can be delivered quickly without the need for additional processing or database queries. This results in faster website speed, which can improve the user experience and increase user engagement.',
                'tag' => 'redis_object_caching',
            ],
            [
                'title' => 'What is the database name?',
                'content' => 'The name of the database that will be created to store your WordPress site data. It is important to choose a unique and descriptive name to avoid confusion with other databases.',
                'tag' => 'database_name',
                'name' => 'Database Management',
                'depends' => false,
                'open_with' => 'in_server',
            ],[
                'title' => 'What is the database user name?',
                'content' => 'The username that will be used to connect to the database',
                'tag' => 'database_user',
                'depends' => false,
                'open_with' => 'in_server',
            ],[
                'title' => 'What is the database password?',
                'content' => 'The password that will be used along with the database username to connect to the database.',
                'tag' => 'database_password',
                'depends' => false,
                'open_with' => 'in_server',
            ],
            [
                'title' => 'What is the database cluster size?',
                'content' => 'The size of the database that will be allocated for your WordPress site. This will determine the maximum amount of data that can be stored in the database.',
                'tag' => 'cluster_size',
                'name'=> 'Database Management',
                'depends' => true,
                'open_with' => 'from_provider',
            ],
            [
                'title' => 'What is the database cluster region?',
                'content' => 'The region where the database will be hosted. This is important because it can affect the performance and latency of your WordPress site. Choose a region that is closest to your target audience.',
                'tag' => 'cluster_region',
                'depends' => true,
                'open_with' => 'from_provider',
            ],
            [
                'title' => 'What is the database cluster name?',
                'content' => 'The name of the database cluster that will be created to host your WordPress database. This is useful when you have multiple databases and want to easily identify which cluster they belong to',
                'tag' => 'database_cluster_name',
                'depends' => true,
                'open_with' => 'from_provider',
            ], [
                'title' => 'What is the database user?',
                'content' => 'The user account that will be used to access the database. This account will have permissions to perform actions such as creating and modifying tables, inserting data, and querying data.',
                'tag' => 'database_user',
                'depends' => true,
                'open_with' => 'from_provider',
            ], [
                'title' => 'What is the database name?',
                'content' => 'The name of the database that will be created to store your WordPress site data. It is important to choose a unique and descriptive name to avoid confusion with other databases.',
                'tag' => 'database_name',
                'depends' => true,
                'open_with' => 'from_provider',
            ],
            [
                'title' => 'What is the database host?',
                'content' => 'The hostname or IP address of the server where your database is hosted. This could be the IP address of your DigitalOcean droplet or the hostname of your GCP instance.',
                'tag' => 'database_host',
                'name' => 'Database Management',
                'depends' => true,
                'open_with' => 'custom',
            ],[
                'title' => 'What is the database port?',
                'content' => 'The port number on which your database is listening for incoming connections. The default port for MySQL databases is 3306, but it may be different depending on your database provider.',
                'tag' => 'database_port',
                'depends' => true,
                'open_with' => 'custom',
            ],
            [
                'title' => 'What is the database user?',
                'content' => 'The username that will be used to connect to the database. This should be a user account with sufficient permissions to perform the necessary actions on the database.',
                'tag' => 'database_user',
                'depends' => true,
                'open_with' => 'custom',
            ], [
                'title' => 'What is the database name?',
                'content' => 'The name of the database that you want to connect to. This should be the same database that you created for your WordPress site.',
                'tag' => 'database_name',
                'depends' => true,
                'open_with' => 'custom',
            ],
            [
                'title' => 'What is the database password?',
                'content' => 'The password associated with the database user account. This is used to authenticate the connection to the database and ensure that only authorized users can access it.',
                'tag' => 'database_password',
                'depends' => true,
                'open_with' => 'custom',
            ],
        ],
        'fields'=> [
            'php_version',
            'prefix',
            'site_user',
            'full_page_cache',
            'redis_object_caching',
            'database_name',
            'database_user',
            'database_password',
            'database_host',
            'database_port',
            'cluster_size',
            'cluster_region',
            'database_cluster_name'
        ]
    ],
    'site.create.wordpress' => [
        'title' => 'Install New WordPress Site',
        'content' => 'Follow the instructions given below to install a Wordpress Site insntantly on your server',
        'video' => [
            'url' => 'https://www.youtube.com/embed/EmOrX7yIiu8?si=pVlGmwwpTjOYApz4',
            'thumbnail' => 'img/video/site-setup.jpeg'
        ],
        'accordions' => [
            [
                'title' => 'Domain',
                'content' => 'You can configure your website to work with multiple domain names, such as for a WordPress multisite, but you need to set the main domain name as the name of your website on xCloud and on your server. You can always change this afterwards.',
                'tag' => 'name',
                'name' => 'Setup your Site',
            ],
            [
                'title' => 'Site Title',
                'content' => 'Its the name of your website and brand: Site visitors see the site title first on search engines. They may also view it at the top of your website if you dont have a logo.',
                'tag' => 'title',
            ],
            [
                'title' => 'Another domain',
                'content' => 'You need to set up additional domains if you are creating a WordPress multisite, or if your website will have multiple subdomains.',
                'tag' => 'another_domain',
            ],
            [
                'title' => 'HTTPS',
                'content' => 'To improve the security of your website, we strongly recommend installing HTTPS. Enabling this will also help you improve your SEO since many browsers flag sites without HTTPS as “not secure”.
                You can choose between a free SSL certificate issued and managed by xCloud or install a custom certificate. Learn more about HTTPS here.',
                'tag' => 'https',
                'name' => 'Enable Free SSL',
            ],
            [
                'title' => 'How to Set tags?',
                'content' => 'Tags are a great way to organize your servers. You can use tags to group sites by project, environment, purpose, owner, or any other criteria that is meaningful to you. You can also use tags to filter the list of sites in the Sites page.',
                'tag' => 'tags',
                'name' => 'Tags',
            ],
            [
                'title' => 'How to set the admin user?',
                'content' => 'Username that you will use to log in to the WordPress administrator dashboard',
                'tag' => 'admin_user',
                'name' => 'Advanced Settings',
                'depends' => true,
            ],
            [
                'title' => 'What is the admin password?',
                'content' => 'Password that you will use along with the WordPress admin username to log in to the WordPress administrator dashboard.',
                'tag' => 'admin_password',
                'depends' => true,
            ], [
                'title' => 'What is the admin email address?',
                'content' => ' Email address that will be used as the primary contact email for your WordPress site.',
                'tag' => 'admin_email',
                'depends' => true,
            ],[
                'title' => 'What is the WordPress version?',
                'content' => 'The version of the WordPress software you want to use for your site. You can choose the latest version or a specific version that is compatible with your needs.',
                'tag' => 'wordpress_version',
                'depends' => true,
            ],[
                'title' => 'What is the PHP version?',
                'content' => 'The version of the PHP programming language that your WordPress site will use. You should select the latest version of PHP or a version that is compatible with your WordPress version.',
                'tag' => 'php_version',
                'depends' => true,
            ],[
                'title' => 'What is the prefix?',
                'content' => 'This is a string of characters that is added to the beginning of database table names. It helps to prevent naming conflicts with other websites on the same database.',
                'tag' => 'prefix',
                'depends' => true,
            ],[
                'title' => "What is the site user?",
                'content' => "Xcloud deploys websites that are owned by a special type of system user called a \"site user.\" These site users have limited access to files, which stops them from seeing other sites' files and confidential files like Nginx configurations. They are able to use SSH and SFTP to connect to the server, but they can only access the site's directory. This makes site users suitable for clients who need an SFTP user to manage their website as they won't be able to see or change other sites on the server.",
                'tag' => 'site_user',
                'depends' => true,
            ],
            [
                'title' => 'What is the full page cache?',
                'content' => "Enabling this will store the entire page in the server's memory, which means that when a user requests that page, it can be delivered quickly without the need for additional processing or database queries.",
                'tag' => 'full_page_cache',
                'depends' => true,
            ],[
                'title' => 'What is the redis object cache?',
                'content' => 'Enabling this will store data in memory, which means that when a user requests data from your website, it can be delivered quickly without the need for additional processing or database queries. This results in faster website speed, which can improve the user experience and increase user engagement.',
                'tag' => 'redis_object_caching',
                'depends' => true,
            ],[
                'title' => 'What is the database name?',
                'content' => 'The name of the database that will be created to store your WordPress site data. It is important to choose a unique and descriptive name to avoid confusion with other databases.',
                'tag' => 'database_name',
                'name' => 'Database Management',
                'depends' => true,
                'open_with' => 'in_server',
            ],[
                'title' => 'What is the database user name?',
                'content' => 'The username that will be used to connect to the database',
                'tag' => 'database_user',
                'depends' => true,
                'open_with' => 'in_server',
            ],[
                'title' => 'What is the database password?',
                'content' => 'The password that will be used along with the database username to connect to the database.',
                'tag' => 'database_password',
                'depends' => true,
                'open_with' => 'in_server',
            ],
            [
                'title' => 'What is the database cluster size?',
                'content' => 'The size of the database that will be allocated for your WordPress site. This will determine the maximum amount of data that can be stored in the database.',
                'tag' => 'cluster_size',
                'name'=> 'Database Management',
                'depends' => true,
                'open_with' => 'from_provider',
            ],
            [
                'title' => 'What is the database cluster region?',
                'content' => 'The region where the database will be hosted. This is important because it can affect the performance and latency of your WordPress site. Choose a region that is closest to your target audience.',
                'tag' => 'cluster_region',
                'depends' => true,
                'open_with' => 'from_provider',
            ],
            [
                'title' => 'What is the database cluster name?',
                'content' => 'The name of the database cluster that will be created to host your WordPress database. This is useful when you have multiple databases and want to easily identify which cluster they belong to',
                'tag' => 'database_cluster_name',
                'depends' => true,
                'open_with' => 'from_provider',
            ], [
                'title' => 'What is the database user?',
                'content' => 'The user account that will be used to access the database. This account will have permissions to perform actions such as creating and modifying tables, inserting data, and querying data.',
                'tag' => 'database_user',
                'depends' => true,
                'open_with' => 'from_provider',
            ], [
                'title' => 'What is the database name?',
                'content' => 'The name of the database that will be created to store your WordPress site data. It is important to choose a unique and descriptive name to avoid confusion with other databases.',
                'tag' => 'database_name',
                'depends' => true,
                'open_with' => 'from_provider',
            ],
            [
                'title' => 'What is the database host?',
                'content' => 'The hostname or IP address of the server where your database is hosted. This could be the IP address of your DigitalOcean droplet or the hostname of your GCP instance.',
                'tag' => 'database_host',
                'name' => 'Database Management',
                'depends' => true,
                'open_with' => 'custom',
            ],[
                'title' => 'What is the database port?',
                'content' => 'The port number on which your database is listening for incoming connections. The default port for MySQL databases is 3306, but it may be different depending on your database provider.',
                'tag' => 'database_port',
                'depends' => true,
                'open_with' => 'custom',
            ],
            [
                'title' => 'What is the database user?',
                'content' => 'The username that will be used to connect to the database. This should be a user account with sufficient permissions to perform the necessary actions on the database.',
                'tag' => 'database_user',
                'depends' => true,
                'open_with' => 'custom',
            ], [
                'title' => 'What is the database name?',
                'content' => 'The name of the database that you want to connect to. This should be the same database that you created for your WordPress site.',
                'tag' => 'database_name',
                'depends' => true,
                'open_with' => 'custom',
            ],
            [
                'title' => 'What is the database password?',
                'content' => 'The password associated with the database user account. This is used to authenticate the connection to the database and ensure that only authorized users can access it.',
                'tag' => 'database_password',
                'depends' => true,
                'open_with' => 'custom',
            ],
        ],
        'documentations' => [
            [
                'title' => 'How to set up a server on xCloud with other providers?',
                'url' => '#',
            ]
        ],
        'fields' => [
            'name',
            'title',
            'another_domain',
            'https',
            'tags',
            'admin_user',
            'admin_password',
            'admin_email',
            'wordpress_version',
            'php_version',
            'site_user',
            'full_page_cache',
            'redis_object_caching',
            'database_name',
            'database_user',
            'database_password',
            'database_host',
            'database_port',
            'cluster_size',
            'cluster_region',
            'database_cluster_name'
            ]
        ],
    'server.database' => [
        'title' => 'Help Texts',
        'content' => 'Access and manage your databases with ease in one centralized location. View essential details such as database names, and associated users, & effortlessly add new database users.',
        'accordions' => [
            [
                //'active'=>true,
                'title' => 'Database',
                'content' => 'You can configure your website to work with multiple domain names, such as for a WordPress multisite, but you need to set the main domain name as the name of your website on xCloud and on your server. You can always change this afterwards.',
                'tag' => 'name',
                'name' => 'Database',
            ],
            [
                'title' => 'Database User',
                'content' => 'It is advisable to assign a dedicated user to each database, ensuring that their access is limited exclusively to that particular database. This precautionary measure serves to minimize the potential impact of a security breach by limiting the amount of data that could be compromised in such an event.',
                'tag' => 'database_user',
            ],
            [
                'title' => 'How to access and manage database?',
                'content' => 'There are alternative ways to securely and conveniently access and manage your database. One option is to utilize a desktop application that operates through SSH. We suggest considering TablePlus, a versatile application compatible with macOS, Windows, and Linux operating systems. To learn further details about establishing a connection to your database, explore the following resources.',
                'tag' => 'database_access',
            ],
        ]
    ],
    'server.sudo' => [
        'title' => 'Help Texts',
        'content' => 'Access and manage sudo users with ease in one centralized location. View important details such as user IDs, sudo user names, associated SSH keys, status, and perform relevant actions, all in one place.',
        'accordions' => [
            [
                //'active'=>true,
                'title' => 'Sudo User Name',
                'content' => 'Assign a unique username to your sudo user for enhanced security and access control.',
                'tag' => 'user_name',
            ],
            [
                'title' => 'Sudo Password',
                'content' => 'Set a strong and secure password for your sudo user account to ensure robust protection of privileged access. The sudo password acts as an additional layer of authentication for executing administrative commands.',
                'tag' => 'database_user',
            ],
            [
                'title' => 'Add SSH Keys',
                'content' => 'Associate SSH keys with sudo users to enable secure access and authentication. Easily manage and update SSH keys to ensure the integrity and confidentiality of your system.',
                'tag' => 'ssh_keys',
            ],
        ],
        'documentations' => [
            [
                'title' => 'Understanding Sudo Users And Site Users In xCloud',
                'url' => 'https://xcloud.host/docs/usudo-users-and-site-users-in-xcloud',
            ],
            [
                'title' => 'How To Generate and Add SSH Keys In xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-generate-and-add-ssh-keys-in-xcloud',
            ]
        ]
    ],
    'server.logs' => [
        'title' => 'Help Texts',
        'content' => 'Access and review all your logs in one convenient location. Easily track important information such as IP addresses, request methods, date/time stamps, browser details, and additional messages for comprehensive log analysis.',
        'accordions' => [
            [
                'active'=>true,
                'title' => 'Server Error Log',
                'tag' => 'error_log',
                'content' => 'The error log is where server records any errors that occur, such as configuration file errors or issues with content requests on your website. It is a valuable resource for identifying problems that may be affecting specific sections of your site and ensuring its proper functioning.',
            ],
            [
                'title' => 'Server Access Log',
                'tag' => 'access_log',
                'content' => "The access log keeps a record of all the actions performed by visitors on your website. It includes details such as the accessed files, server's response to each request, and the IP addresses of the users. This log is valuable for analyzing traffic patterns and identifying any abnormal or suspicious requests made to the site.",
            ],
        ]
    ],
    'server.php.settings' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'Max File Upload Size',
                'tag' => 'max_file_upload_size',
                'content' => 'Adjust the maximum file size allowed for uploads through PHP. Customize this setting to accommodate your specific needs, ensuring smooth file transfers and preventing any limitations on file uploads.',
            ],
            [
                'title' => 'Max Execution Time',
                'tag' => 'max_execution_time',
                'content' => 'Fine-tune the maximum execution time for PHP scripts. Set the appropriate duration to ensure that time-consuming scripts complete successfully without exceeding server limitations or causing timeouts.',
            ],
            [
                'title' => 'Memory Limit',
                'tag' => 'memory_limit',
                'content' => 'Adjust the maximum amount of memory that a PHP script can consume. Customize this setting to accommodate your specific needs, ensuring smooth script execution and preventing any limitations on memory usage.',
            ],
        ],
        'fields' => [
            'max_file_upload_size',
            'max_execution_time',
            'memory_limit'
        ]
    ],
    'server.events' => [
        'title' => 'Help Texts',
        'content' => 'Access a comprehensive overview of events related to your site in one convenient location. Stay informed about the site, initiator, date/time, duration, status, and action details for effective event management.',
    ],
    'server.monitoring' => [
        'title' => 'Help Texts',
        'content' => 'Access and manage sudo users with ease in one centralized location. View important details such as user IDs, sudo user names, associated SSH keys, status, and perform relevant actions, all in one place.',
        'accordions' => [
            [
                'active'=>true,
                'title' => 'Ram',
                'content' => "Monitor the utilization of your server's Random Access Memory (RAM) in real time. Stay informed about the current RAM usage to ensure optimal performance and resource allocation.",
                'tag' => 'ram',
            ],
            [
                'title' => 'CPU',
                'content' => "Keep track of your server's CPU usage in real time. Stay informed about the current CPU utilization to ensure efficient resource allocation and identify any potential performance bottlenecks.",
                'tag' => 'cpu',
            ],
            [
                'title' => 'Hard Disk',
                'content' => "Monitor your server's hard disk usage to ensure efficient storage management. Stay informed about the current disk space utilization and take necessary actions to prevent disk space shortages.",
                'tag' => 'hard_disk',
            ],
            [
                'title' => 'Uptime Overview',
                'content' => "Monitor the uptime of your server to ensure its availability and reliability. Stay informed about any interruptions or downtime and take necessary measures to minimize disruptions.",
                'tag' => 'up_time',
            ],
            [
                'title' => 'Server Workload Summary',
                'content' => "Gain a summarized overview of your server's workload. Understand the current workload trends and make informed decisions to optimize resource allocation and maintain optimal performance.",
                'tag' => 'workload_summary',
            ],
        ],
    ],
    'server.management' => [
        'title' => 'Help Texts', 'content' => 'Access and review all your logs in one convenient location. Easily track important information such as IP addresses, request methods, date/time stamps, browser details, and additional messages for comprehensive log analysis.',
        'accordions' => [
            [
                'active'=>true,
                'title' => 'Manage Server',
                'tag' => 'manage_server',
                'content' => 'Take full control of your servers with our comprehensive server management tools. Easily manage, archive, delete, and upgrade servers, all in one centralized location for efficient server administration.',
            ],
            [
                'title' => 'Upgrade Server',
                'tag' => 'upgrade_server',
                'content' => "Scale your server resources to match your evolving needs. Seamlessly upgrade your server's CPU, RAM, storage, or bandwidth to enhance performance, accommodate growth, and optimize server capabilities. Keep in mind that once you have picked your server package you can’t roll back to the older one. Upgrade your server seamlessly across multiple VPS providers with xCloud. Enhance performance, scalability, and resources effortlessly.",
            ],
            [
                'title' => 'Server Archive',
                'tag' => 'server_archive',
                'content' => "Safely archive server data for future reference or to free up storage space. Archive servers allow you to retain important information while keeping your active server list organized and clutter-free.",
            ],
            [
                'title' => 'Delete Server',
                'tag' => 'delete_server',
                'content' => "Remove servers that are no longer needed or in use. Deleting servers helps declutter your server management interface and ensures that only relevant and active servers are displayed.",
            ],
        ],
        'documentations' => [
            [
                'title' => 'How to Resize or Upgrade xCloud Server?',
                'url' => 'https://xcloud.host/docs/how-to-upgrade-my-server-in-xcloud/',
            ]
        ]
    ],
    'server.meta' => [
        'title' => 'Help Texts',
        'active_tag'=>'server_name',
        'accordions' => [
            [
                'active'=>true,
                'title' => 'Server Name',
                'tag'=> 'server_name',
                'content' => "Quickly identify your server by its unique server name. Assign meaningful names to your servers for easy recognition and organization within your infrastructure.",
            ],
            [
                'title' => 'Provider Name',
                'tag'=> 'provider_name',
                'content' => "Know the provider associated with your server. Easily track and manage servers from different providers, ensuring transparency and effective management of your server resources.",
            ],
            [
                'title' => 'Server Size',
                'tag'=> 'server_size',
                'content' => "Get insights into the size of your server in terms of its resources, such as CPU, RAM, storage, or bandwidth. Understand the capacity and capabilities of your server to make informed decisions and optimize resource allocation.",
            ],
            [
                'title' => 'Region Size',
                'tag'=> 'region_size',
                'content' => "Identify the geographical region where your server is located. Knowing the server's region can be helpful for various reasons, including compliance, latency optimization, or data sovereignty considerations.",
            ],
            [
                'title' => 'Notes',
                'tag'=> 'notes',
                'content' => "Keep important information or specific instructions about your server in the form of additional notes or comments. Capture any relevant details that may be unique to your server setup or require special attention.",
            ],
        ],
        'documentations' => [
            [
                'title' => 'How to Update Server Name in xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-update-server-name-in-xcloud/',
            ]
        ]
    ],
    'server.settings' => [
        'title' => 'Help Texts',
        'active_tag'=>'server_name',
        'accordions' => [
            [
                'active'=>true,
                'title' => 'IP Address',
                'tag'=> 'ip_address',
                'content' => "Review the assigned IP address for your server. This information is vital for accessing and connecting to your server remotely, enabling you to establish secure connections and manage server resources",
            ],
            [
                'title' => 'SSH Port',
                'tag'=> 'ssh_port',
                'content' => " Customize the SSH port used for secure remote access to your server. Set a port number that aligns with your security preferences and ensures a secure connection for managing your server remotely.",
            ],
            [
                'title' => 'Time Zone',
                'tag'=> 'time_zone',
                'content' => "Set the timezone for your server to ensure accurate time-based operations and timestamps. Aligning the server's timezone with your local time zone simplifies scheduling, log analysis, and coordination across different systems.",
            ],
        ],
        'documentations' => [
            [
                'title' => 'Adjusting Time Zone Settings In xCloud Servers',
                'url' => 'https://xcloud.host/docs/adjusting-time-zone-settings-in-xcloud-servers/',
            ]
        ]
    ],
    'server.cron.job' => [
        'documentations' => [
            [
                'title' => 'How To Add A Custom Cron Job In xCloud?',
                'url' => 'https://xcloud.host/docs/add-a-custom-cron-job-in-xcloud/',
            ]
        ]
    ],
    'server.command.runner' => [
        'documentations' => [
            [
                'title' => 'How to Run Commands in Your Site And Server Easily with xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-run-commands-in-your-site-and-server/',
            ]
        ]
    ],
    'server.firewall-management' => [
        'documentations' => [
            [
                'title' => 'How To Easily Configure Firewall Management With xCloud Hosting?',
                'url' => 'https://xcloud.host/docs/how-to-configure-firewall-management-in-xcloud/',
            ],
            [
                'title' => 'Fail2Ban in xCloud: Enhancing Server Security With Ease',
                'url' => 'https://xcloud.host/docs/fail2ban-in-xcloud-enhancing-server-security-with-ease/',
            ]
        ]
    ],
    'server.vulnerability-settings' => [
        'documentations' => [
            [
                'title' => 'How to Disable/Enable Vulnerability Scanner Feature On Server/ Sites?',
                'url' => 'https://xcloud.host/docs/vulnerability-checker-in-xcloud/#how-to-disableenable-vulnerability-scanner-feature-on-server-sites',
            ]
        ]
    ],
    'server.security-update' => [
        'documentations' => [
            [
                'title' => 'Keep Your Server up to Date with Automated Security Updates',
                'url' => 'https://xcloud.host/docs/reboot-server-with-automated-security-updates/',
            ]
        ]
    ],
    'server.backup' => [
        'documentations' => [
            [
                'title' => 'How To Configure Server Backup Management In xCloud?',
                'url' => 'https://xcloud.host/docs/configure-server-backup-management/#step-2-configure-server-backup-management',
            ]
        ]
    ],
    'user.profile' => [
        'documentations' => [
            [
                'title' => 'How To Edit My Account/Profile in xCloud?',
                'url' => 'https://xcloud.host/docs/my-account/',
            ]
        ]
    ],
    'user.vulnerability_scanner' => [
        'documentations' => [
            [
                'title' => 'How to Detect & Fix Security Bugs with Vulnerability Checker in xCloud?',
                'url' => 'https://xcloud.host/docs/vulnerability-checker-in-xcloud/',
            ]
        ]
    ],
    'user.team' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'Team Management',
                'tag' => 'team_management',
                'content' => 'In xCloud team roles for every account are created when a user signs up. xCloud teams enable you to collaborate with team members who manage servers and websites on your behalf. You can create multiple teams and include any necessary number of members in each xCloud team. You can change their access and role anytime in the Members part of Team Settings.',
            ],
        ],
        'documentations' => [
            [
                'title' => 'Team Roles & Permissions in xCloud',
                'url' => 'https://xcloud.host/docs/team-roles-permissions-in-xcloud/',
            ]
        ]
    ],
    'user.ssh' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'SSH Key',
                'tag' => 'ssh_key',
                'content' => 'Adding an SSH (Secure Shell) key to your server is a security measure and a method of authentication. SSH keys are used for secure, encrypted communication between your local computer (or client) and a remote server. With xCloud, you can easily generate and add SSH keys to your server to keep the server secure from unauthorized access.',
            ],
        ],
        'documentations' => [
            [
                'title' => 'How To Generate and Add SSH Keys In xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-generate-and-add-ssh-keys-in-xcloud/',
            ]
        ]
    ],
    'user.profile.server' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'Server Provider',
                'tag' => 'server_provider',
                'content' => 'Self managed Server Providers, such as Vultr, DigitalOcean, GCP, etc. can be integrated to xCloud from here.',
            ],
        ],
        'documentations' => [
            [
                'title' => 'How to Setup a Digital Ocean Server in xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-setup-your-server-with-digital-ocean/',
            ],
            [
                'title' => 'How To Setup a GCP(Google Cloud Platform) Server in xCloud?',
                'url' => 'https://xcloud.host/docs/first-server-with-google-cloud-platform/',
            ],
            [
                'title' => 'How To Set Up Hetzner Server In xCloud Host?',
                'url' => 'https://xcloud.host/docs/set-up-hetzner-server-in-xcloud/',
            ],
            [
                'title' => 'How To Set Up AWS Server In xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-set-up-aws-server-in-xcloud/',
            ],
            [
                'title' => 'How To Setup A Vultr Server in xCloud and Get Free Hosting For 6 Months?',
                'url' => 'https://xcloud.host/docs/how-to-get-free-hosting-with-vultr/',
            ]
        ]
    ],
    'user.storage-provider' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'Storage Provider',
                'tag' => 'storage_provider',
                'content' => 'Storage Providers such as Vultr(Object Storage) and DigitalOcean (Spaces Object Storage) can be integrated from here. Storage providers are required for Site Backups.',
            ],
        ],
        'documentations' => [
            [
                'title' => 'Site Backups In xCloud',
                'url' => 'https://xcloud.host/docs/site-backups-in-xcloud/',
            ],
            [
                'title' => 'How to Use Cloudflare R2 Storage for Site Backup in xCloud?',
                'url' => 'https://xcloud.host/docs/cloudflare-r2-storage-for-site-backup/',
            ]
        ]
    ],
    'team.email_provider' => [
        'title' => 'Help Texts',
        'video' => [
            'url' => 'https://www.youtube.com/embed/8_xDXUA-nMo?si=YPgDDpjUvAGIeQox',
            'thumbnail' => 'img/video/email-provider.jpg'
        ],
        'accordions' => [
            [
                'title' => 'Email Provider',
                'tag' => 'email_provider',
                'content' => 'WordPress frequently requires sending site emails to you or your users, known as transactional emails. These may include admin notifications, new user signups, password resets, and form submission emails from form plugins. You can integrate SMTP Email service providers such as Mailgun, SendGrid, and others here.',
            ],
        ],
        'documentations' => [
            [
                'title' => 'Setting Up Site Emails With Any SMTP Provider For WordPress On xCloud',
                'url' => 'https://xcloud.host/docs/setting-up-site-emails-for-wordpress-on-xcloud/',
            ],
            [
                'title' => 'Configure Mailgun For Site Emails',
                'url' => 'https://xcloud.host/docs/configure-mailgun-for-site-emails/',
            ],
            [
                'title' => 'How To Add xCloud Managed Email Service For WordPress Site Emails',
                'url' => 'https://xcloud.host/docs/add-xcloud-managed-email-service/',
            ]
        ]
    ],
    'user.notification-integration' => [
        'documentations' => [
            [
                'title' => 'How to Configure Slack Integration in xCloud Hosting?',
                'url' => 'https://xcloud.host/docs/configure-slack-integration-in-xcloud/',
            ],
            [
                'title' => 'How to Configure Integration with WhatsApp In xCloud Hosting?',
                'url' => 'https://xcloud.host/docs/how-to-configure-integration-with-whatsapp/',
            ],
        ]
    ],
    'user.bills-payment' => [
        'documentations' => [
            [
                'title' => 'How Pricing Plans And Billing Work In xCloud?',
                'url' => 'https://xcloud.host/docs/how-billing-works-in-xcloud/',
            ],
        ]
    ],
    'user.detailedInvoices' => [
        'documentations' => [
            [
                'title' => 'How Pricing Plans And Billing Work In xCloud?',
                'url' => 'https://xcloud.host/docs/how-billing-works-in-xcloud/',
            ],
        ]
    ],
    'user.email.subscriptions' => [
        'documentations' => [
            [
                'title' => 'How To Add xCloud Managed Email Service For WordPress Site Emails',
                'url' => 'https://xcloud.host/docs/add-xcloud-managed-email-service/',
            ],
        ]
    ],
    'user.notifications' => [
        'documentations' => [
            [
                'title' => 'How To Easily Disable Email Notifications For Credentials In xCloud Hosting',
                'url' => 'https://xcloud.host/docs/how-to-disable-email-notifications/',
            ],
        ]
    ],
    'blueprints.index' => [
        'documentations' => [
            [
                'title' => 'How To Use Blueprints in xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-use-blueprints-in-xcloud/',
            ],
        ]
    ],
    'user.all-events' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'All Events',
                'tag' => 'all_events',
                'content' => 'Stay informed about important events occurring on your server. Track changes, updates, or significant actions performed the system in a centralized event log.',
            ],
        ]
    ],
    'site.domain' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'active'=>true,
                'title' => 'Primary Domain',
                'tag' => 'primary_domain',
                'content' => "See the primary domain associated with your account. Make sure it reflects your brand and website accurately",
            ],
            [
                'title' => 'Additional Domain',
                'tag'=> 'additional_domain',
                'content' => "Expand your online presence by adding extra domains to your account. Increase your reach and target specific audiences",
            ],
        ],
        'documentations' => [
            [
                'title' => 'How to change the primary domain a site?',
                'url' => 'https://xcloud.host/docs/how-to-change-the-primary-domain-of-your-site/',
            ],
            [
                'title' => 'How to Go Live from Staging domain?',
                'url' => 'https://xcloud.host/docs/how-to-go-live-from-staging-domain/',
            ],
            [
                'title' => 'Additional Domain Feature www And Non www Redirect With xCloud',
                'url' => 'https://xcloud.host/docs/additional-domain-feature-with-xcloud/',
            ]
        ]
    ],
    'site.ssl' => [
        'title' => 'Help Texts',
        'content' => 'Use Free SSL By xCloud: Your website is eligible for a free SSL certificate. Activate it to secure your website and encrypt data transmission.',
        'accordions' => [
            [
                'active'=>true,
                'title' => 'Certificate',
                'tag'=> 'certificate',
                'content' => "Already have an SSL certificate? Easily connect your own certificate and private key for enhanced security and compatibility.",
            ],
            [
                'title' => 'Private Key',
                'tag'=> 'private_key',
                'content' => "Connect all SSL certificates to your website seamlessly with the private key, whether you choose the free certificate or add your own",
            ],
        ],
        'documentations'=>[
            [
                'title' => "How to Install Let's Encrypt SSL Certificate",
                'url' => 'https://xcloud.host/docs/how-to-easily-install-lets-encrypt-ssl-certificate/',
            ]
        ]
    ],
    'site.monitoring' => [
        'title' => 'Help Texts',
        'content' => "Monitor CPU, Memory, and Disk Usage: Keep track of your website's resource utilization in real time. Stay informed about your server's performance and ensure optimal operation.",
        'accordions' => [
            [
                'active'=>true,
                'tag'=>'ssl_overview',
                'title' => 'SSL Overview',
                'content' => "Get the overall report of your DNS status, site expiry & SSL report",
            ],
            [

                'title' => 'WordPress Logs',
                'tag'=>'wordpress',
                'content' => "Track the latest WordPress version, plugins & theme version",
            ],
        ],
        'documentations'=>[
            [
                'title' => 'How to Monitor the RAM, CPU & Disk Usage of a Site?',
                'url' => 'https://xcloud.host/docs/how-to-monitor-the-ram-cpu-disk-usage-of-a-site/',
            ]
        ]
    ],
    'site.updates' => [
        'title' => 'Help Texts',
         'accordions' => [
            [
                'tag'=>'core',
                'title' => 'Theme and Plugins',
                'content' => "Update the WordPress core version from here. You can also activate, deactivate, and update your themes and plugins with a single click.",
            ],
        ],
        'documentations'=>[
            [
                'title' => 'Manage and Update WordPress Core, Themes, & Plugins',
                'url' => 'https://xcloud.host/docs/how-to-manage-and-update-wordpress-core-themes-plugins-in-xcloud/',
            ]
        ]
    ],
    'site.redirection' => [
        'title' => 'Help Texts',
        'content' => "Monitor CPU, Memory, and Disk Usage: Keep track of your website's resource utilization in real time. Stay informed about your server's performance and ensure optimal operation.",
        'accordions' => [
            [
                'active'=>true,
                'tag'=>'domain',
                'title' => 'Domain Name',
                'content' => "Set the proper domain name that connects with your business.",
            ],
            [
                'tag'=>'matching',
                'title' => 'Matching String',
                'content' => "Define specific patterns or sequences of characters to identify and match elements within your domain name. Use matching strings to customize URL redirection or rewrite rules based on your specific requirements",
            ],
            [
                'tag'=>'type',
                'title' => 'Redirect Type',
                'content' => "Choose the appropriate redirect type that best suits your needs, whether it's a temporary (302) or permanent (301) redirect.",
            ],
            [
                'tag'=>'replacement',
                'title' => 'Replacement String',
                'content' => "Specify the replacement string or the destination URL to which the traffic should be redirected.",
            ], [
                'tag'=>'redirections',
                'title' => 'Redirections',
                'content' => "Customize your redirection strategy with matching and replacement strings. Redirect specific URLs, patterns, or entire domains based on your requirements.",
            ],
        ],
        'documentations' => [
            [
                'title' => 'How To Set Up Redirection In xCloud?',
                'url' => 'https://xcloud.host/docs/set-up-redirection-in-xcloud/',
            ],
        ]
    ],
    'site.wp-config' => [
        'title' => 'Help Texts',
        'content' => "Access and modify the WP-Config file of your WordPress installation. Customize important settings and parameters to enhance the functionality of your website.",
    ],
    'site.caching' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'tag'=>'full_page_cache',
                'title' => 'Full Page Cache',
                'content' => "Enabling a full page cache can greatly enhance your website's performance by rapidly delivering a pre-generated page instead of executing PHP code and querying the database. By doing so, not only will the loading time of your site improve, but it will also enable your server to handle a higher number of requests. Enabling this option will set up Nginx FastCGI caching, which is specifically optimized for WordPress. If your website is in production, we strongly advise keeping the full page caching enabled. However, if it is a staging or testing site, or if performance is not a concern, you have the option to disable full page caching.",
            ],
            [
                'tag'=>'redis_object_cache',
                'title' => 'Redis Object Cache',
                'content' => "The Redis object cache is a powerful tool for improving website performance by storing frequently accessed data in memory. It reduces reliance on complex database queries and speeds up load times. Implementing Redis as a caching backend in WordPress eliminates the need to fetch data from the database every time, resulting in a smoother user experience. Careful consideration is needed for sites with dynamic data, as selective object caching may be more appropriate. Overall, Redis object cache optimizes performance, reduces database load, and enhances response times for a seamless browsing experience.",
            ],
            [
                'tag'=>'compatibility',
                'title' => 'Compatibility with famous cache plugins',
                'content' => "In addition to WP Rocket, Xcloud also supports two other popular caching plugins, namely WP Super Cache and W3 Total Cache. While there is a general guideline to avoid using multiple caching plugins simultaneously, WP Rocket is an exception in the case of Xcloud. It now offers full compatibility with Xcloud, allowing its other features to function alongside Xcloud's caching mechanism. When xCloud detects that the WP Rocket's page cache is enabled, it automatically disables its own page cache. However, all other features of WP Rocket remain active. Furthermore, this compatibility between WP Rocket and Xcloud includes synchronized cache purging. Whenever WP Rocket purge",
            ],
            [
                'tag'=>'cache',
                'title' => 'Cache Duration',
                'content' => "Set the duration and unit for how long pages should be cached. Choose the appropriate time period to balance between freshness and caching efficiency.",
            ],
            [
                'tag'=>'unit',
                'title' => 'Unit',
                'content' => "Select the appropriate unit for defining cache duration and expiration. Choose the cache unit that best suits your caching needs and website performance goals.",
            ],
            [
                'tag'=>'https',
                'title' => 'HTTPS URL Rules',
                'content' => "Define specific URLs or URL patterns to exclude from caching. Ensure dynamic content, personalized pages, or sensitive information remain up-to-date and secure.",
            ],
            [
                'tag'=>'cookie_rules',
                'title' => 'Cookie Rules',
                'content' => "Specify cookies that, when present, will bypass caching. Maintain personalized experiences or e-commerce functionality by excluding relevant cookies from caching.",
            ],
        ],
        'documentations' => [
            [
                'title' => 'How Caching Works In xCloud?',
                'url' => 'https://xcloud.host/docs/how-caching-works-in-xcloud/',
            ],
        ]
    ],
    'site.logs' => [
        'title' => 'Help Texts',
        'content' => "Access all your website logs in one convenient location. Review important information such as IP addresses, methods, dates, and browsers used by visitors.",
        'documentations' => [
            [
                'title' => 'How to View Logs and Events of Your Server and Site in xCloud?',
                'url' => 'https://xcloud.host/docs/view-logs-and-events-of-your-server-and-site/',
            ],
        ]
    ],
    'site.events' => [
        'title' => 'Help Texts',
        'content' => "Stay informed about important events occurring on your website. Track changes, updates, or significant actions performed by users or the system in a centralized event log.",
        'documentations' => [
            [
                'title' => 'How to View Logs and Events of Your Server and Site in xCloud?',
                'url' => 'https://xcloud.host/docs/view-logs-and-events-of-your-server-and-site/',
            ],
        ]
    ],
    'site.file.manager' => [
        'documentations' => [
            [
                'title' => 'How to Access WordPress Files in xCloud By Tiny File Manager?',
                'url' => 'https://xcloud.host/docs/access-wordpress-files-in-xcloud/',
            ],
        ]
    ],
    'site.authentication' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'active'=>true,
                'title' => 'User Name',
                'tag'=>'user_name',
                'content' => "Access and manage your usernames and passwords in one centralized location.",
            ],
            [
                'title' => 'Password',
                'tag'=>'password',
                'content' => "Maintain control over user access and authentication for enhanced security.",
            ]
        ],
        'documentations'=>[
            [
                'title' => 'How to enable Basic Authentication on your site?',
                'url' => 'https://xcloud.host/docs/how-to-enable-basic-authentication-on-your-site/',
            ]
        ]
    ],
    'site.backups' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'Previous Backup',
                'tag'=>'previous_backup',
                'content' => "Backup Now will take a backup of your site and site database. To backup your site in a remote storage, you need to integrate your storage provider. You can also backup your site on the server where the site is hosted.",
            ]
        ],
        'documentations'=>[
            [
                'title' => 'How to backup your site in xCloud?',
                'url' => 'https://xcloud.host/docs/site-backups-in-xcloud',
            ]
        ]
    ],
    'site.staging.environment' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'Site Staging to Live domain',
                'tag'=>'site_staging',
                'content' => "From the xCloud dashboard, you can easily go live from staging domain by inserting your live domain.",
            ]
        ],
        'documentations'=>[
            [
                'title' => 'How To Go Live From Staging Domain?',
                'url' => 'https://xcloud.host/docs/how-to-go-live-from-staging-domain',
            ],
            [
                'title' => 'Additional Domain Feature www And Non www Redirect With xCloud',
                'url' => 'https://xcloud.host/docs/additional-domain-feature-with-xcloud/',
            ]
        ]
    ],
    'site.email_provider' => [
        'title' => 'Help Texts',
        'video' => [
            'url' => 'https://www.youtube.com/embed/qaNUdCFoj40?si=LG_Ae6odBipJdt5O',
            'thumbnail' => 'img/video/site-email-config.jpg'
        ],
        'accordions' => [
            [
                'title' => 'Email Provider',
                'tag'=>'email_provider',
                'content' => "Assign your preferred email provider for your site. ",
            ]
        ],
        'documentations'=>[
            [
                'title' => 'How To Add xCloud Managed Email Service For WordPress Site Emails?',
                'url' => 'https://xcloud.host/docs/add-xcloud-managed-email-service',
            ],
            [
                'title' => 'Configure Mailgun For Site Emails',
                'url' => 'https://xcloud.host/docs/configure-mailgun-for-site-emails',
            ],
            [
                'title' => 'Setting Up Site Emails With Any SMTP Provider For WordPress On xCloud',
                'url' => 'https://xcloud.host/docs/setting-up-site-emails-for-wordpress-on-xcloud',
            ]
        ]
    ],
    'site.database' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'Adminer',
                'tag'=>'adminer',
                'content' => 'Although we recommend using the SSH method to access your databases with a third party application, such as TablePlus, Adminer is an alternative. We recommend keeping this option disabled when it is not in use to ensure maximum security.'
            ]
        ],
        'documentations'=>[
            [
                'title' => 'How To Access Site Database Using Adminer In xCloud',
                'url' => 'https://xcloud.host/docs/access-site-database-using-adminer-xcloud/',
            ],
        ]
    ],
    'site.vulnerability-scan' => [
        'documentations'=>[
            [
                'title' => 'How to Detect & Fix Security Bugs with Vulnerability Checker in xCloud?',
                'url' => 'https://xcloud.host/docs/vulnerability-checker-in-xcloud/',
            ],
        ]
    ],
    'site.web-server.security' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'PHP Execution on Upload Directory',
                'tag' => 'https_execution',
                'content' => 'This is a security measure in web hosting environments that prevents potential exploitation of third-party plugin vulnerabilities. This practice adds an extra layer of defense against unauthorized code execution and helps safeguard the integrity of the website. If you wish to enable it, please let us know in the support.',
            ],
            [
                'title' => 'XML-RPC',
                'tag' => 'https_xml_rpc',
                'content' => 'XML-RPC, or Extensible Markup Language Remote Procedure Call is considered outdated and insecure for remotely posting to your WordPress website. It is advisable to disable it, if you\'re not actively utilizing it.',
            ],
            [
                'title' => 'X-Frame-Options',
                'tag' => 'https_xframe',
                'content' => 'The X-Frame-Options is an HTTP header that provides a way for web servers to control whether or not a browser should be allowed to render a page in a frame, iframe, embed, or object. Enabling this helps to protect against clickjacking and similar malicious activities.',
            ],
        ],
        'documentations'=>[
            [
                'title' => 'How To Use xCloud 7G Firewall On Nginx?',
                'url' => 'https://xcloud.host/docs/use-xcloud-7g-firewall-on-nginx/',
            ],
            [
                'title' => 'How To Enable 8G Firewall in xCloud?',
                'url' => 'https://xcloud.host/docs/enable-8g-firewall-in-xcloud/',
            ],
        ]
    ],
    'site.basic.authentication' => [
        'documentations'=>[
            [
                'title' => 'How To Enable Basic Authentication On Your Site?',
                'url' => 'https://xcloud.host/docs/how-to-enable-basic-authentication-on-your-site/',
            ]
        ]
    ],
    'site.command.runner' => [
        'documentations'=>[
            [
                'title' => 'How to Run Commands in Your Site And Server Easily with xCloud?',
                'url' => 'https://xcloud.host/docs/how-to-run-commands-in-your-site-and-server/',
            ]
        ]
    ],
    'site.nginx' => [
        'title' => 'Help Texts',
        'accordions' => [
            [
                'title' => 'Customize Nginx Options',
                'tag'=>'https',
                'content' => "By default the Customize Nginx Options is enabled.",
            ],
        ],
    ],
    'site.settings' => [
        'title' => 'Help Texts',
        'video' => [
            'url' => 'https://www.youtube.com/embed/DsZzkWYMDDM?si=zcTd3iJ0T6GThZPc',
            'thumbnail' => 'img/video/site-settings.jpg'
        ],
        'accordions' => [
            [
                'title' => 'Update PHP Settings',
                'tag'=>'php_version',
                'content' => 'Update PHP Settings feature allows you to change the PHP version of your site. It is recommended to set the recent version of PHP.',
            ],
            [
                'title' => 'Site Tags',
                'tag'=>'site_tag',
                'content' => "Customize your website's tags for better organization and searchability. Add, modify, or remove tags that accurately represent your site's content or purpose.",
            ],
            [
              'title' => 'Wp-Cron and xCloud-Cron',
              'tag' => 'wp_cron_xcloud_cron',
              'content' => '‘wp-cron’ is a scheduling system in WordPress used for tasks, such as running periodic events, scheduled posts, and other background processes. ‘wp-cron’ relies on website visits, triggering scheduled tasks when a user accesses the site, which can sometimes lead to delays if site visits are infrequent. xCloud-Cron is a server-level cron that ensures timely execution of scheduled tasks in WordPress. You may choose the interval for the xCloud-Cron execution.',
            ],
            [
                'title' => 'Delete Site',
                'content' => "Permanently remove a website from your account. Exercise caution when deleting a site, as this action cannot be undone and all associated data will be lost.",
                'tag'=>'delete_website'
            ],
            [
                'title' => 'Clone Website',
                'content' => "Duplicate an existing website to create a new instance quickly. Save time and effort by replicating an existing website's design, content, and configurations.",
                'tag'=>'clone_website'
            ]
        ],
        'documentations'=>[
            [
                'title' => 'How To Upgrade PHP Version & Customize PHP Settings With xCloud Hosting?',
                'url' => 'https://xcloud.host/docs/how-to-upgrade-php-version-with-xcloud-hosting/',
            ],
            [
                'title' => 'How To Reset File and Folder Permissions With xCloud Rescue Site?',
                'url' => 'https://xcloud.host/docs/reset-file-and-folder-permissions-with-xcloud/',
            ],
            [
                'title' => 'What Is A Cron Job And How To Configure xCloud Cron Job?',
                'url' => 'https://xcloud.host/docs/how-to-configure-xcloud-cron-job/',
            ],
            [
                'title' => 'How To Easily Enable WP Debug Tool From xCloud Dashboard?',
                'url' => 'https://xcloud.host/docs/enable-wp-debug-tool-from-xcloud-dashboard/',
            ],
        ]
    ],
    'server.create.vultr' => [
        'title' => 'Setting up your server with Vultr',
        'content' => 'Follow the instructions given below to set up your server with vultr as your selected provider.',
        'accordions' => [
            [
                'title' => 'How to select the server size?',
                'content' => 'We recommend at least a 2GB/ 1 shared CPU server for 10-20 static brochure sites with cache enabled. And for a site whose pages cannot be cached (e.g. WooCommerce site) we recommend a 4 GB / 2 dedicated CPUs server. You can also later upgrade your server from xCloud.',
                'tag' => 'server_size',
                'name' => 'Server Size',
            ],
            [
                'title' => 'How to upgrade my server size?',
                'content' => 'Please follow our documentation here to get the step-by-step guide on upgrading your server size. ',
                'tag' => 'upgrade_size',
            ],
            [
                'title' => 'How to choose a region for your server?',
                'content' => 'In order to ensure fastest delivery, we recommend choosing a region that is closest to the geographical location of your target audience.',
                'tag' => 'region',
                'name' => 'Region',
            ],
            [
                'title' => 'How to select the database server?',
                'content' => 'For your WordPress website, you must choose an appropriate database server. Most WordPress websites work well with both MySQL and MariaDB databases. Whichever database you choose, select carefully as this cannot be changed later.',
                'tag' => 'database_type',
                'name' => 'Database Server',
            ],
            [
                'title' => 'What is the database root password?',
                'content' => 'The database root password is needed when you need to do tasks on your database that require root access. This password is autogenerated. You will need to store it in a secure place if you ever need to work on your databases with root access as you will not be able to retrieve it again.',
                'tag' => 'database_password',
                'name' => 'Database Root Password',
            ],
            [
                'title' => 'How to Set tags?',
                'content' => 'Tags are a great way to organize your servers. You can use tags to group servers by project, environment, purpose, owner, or any other criteria that is meaningful to you. You can also use tags to filter the list of servers in the Servers page.',
                'tag' => 'tags',
                'name' => 'Tags',
            ],
        ],
        'fields' => [
            'server_size',
            'region',
            'database_type',
            'tags',
            'database_password',
        ]
    ],
    'server.create.gcp' => [
        'title' => 'Setting up your server with DigitalOcean',
        'content' => 'Follow the instructions given below to set up your server with DigitalOcean as your selected provider.',
        'accordions' => [
            [
                'title' => 'How to select the server size?',
                'content' => 'We recommend at least a 2GB/ 1 shared CPU server for 10-20 static brochure sites with cache enabled. And for a site whose pages cannot be cached (e.g. WooCommerce site) we recommend a 4 GB / 2 dedicated CPUs server. You can also later upgrade your server from xCloud.',
                'tag' => 'server_size',
                'name' => 'Server Size',
            ],
            [
                'title' => 'How to upgrade my server size?',
                'content' => 'Please follow our documentation here to get the step-by-step guide on upgrading your server size. ',
                'tag' => 'upgrade_size',
            ],
            [
                'title' => 'How to choose a region for your server?',
                'content' => 'In order to ensure fastest delivery, we recommend choosing a region that is closest to the geographical location of your target audience.',
                'tag' => 'region',
                'name' => 'Region',
            ],
            [
                'title' => 'How to select the database server?',
                'content' => 'For your WordPress website, you must choose an appropriate database server. Most WordPress websites work well with both MySQL and MariaDB databases. Whichever database you choose, select carefully as this cannot be changed later.',
                'tag' => 'database_type',
                'name' => 'Database Server',
            ],
            [
                'title' => 'What is the database root password?',
                'content' => 'The database root password is needed when you need to do tasks on your database that require root access. This password is autogenerated. You will need to store it in a secure place if you ever need to work on your databases with root access as you will not be able to retrieve it again.',
                'tag' => 'database_password',
                'name' => 'Database Root Password',
            ],
            [
                'title' => 'How to Set tags?',
                'content' => 'Tags are a great way to organize your servers. You can use tags to group servers by project, environment, purpose, owner, or any other criteria that is meaningful to you. You can also use tags to filter the list of servers in the Servers page.',
                'tag' => 'tags',
                'name' => 'Tags',
            ],
        ],
        'fields' => [
            'server_size',
            'region',
            'database_type',
            'tags',
            'database_password',
        ]
        ],
        'site.backup' => [
            'title' => 'Site Backup',
            'active_tag'=>'remote_backup',
            'content' => 'Site Backups include full site backups of your database and files backups (media, themes, and plugins) with the option to schedule daily backups and set deletion times.
    To configure automated site backups on xCloud, you can choose between Remote Backup (recommended) and Local Backup options',
            'video' => [
                'url' => 'https://www.youtube.com/embed/JMXH3hZJGPI?si=6d1KgIztmq2hH23x',
                'thumbnail' => 'img/video/site-backup.jpg'
            ],
            'accordions' => [
                [
                    'title' => 'Remote Backup',
                    'content' => 'For Remote Backup, you need to set up a storage provider like Digital Ocean or Vultr. To add and manage storage providers visit <a class="underline font-bold" target="_blank" href="/user/storage-provider">Storage provider</a> page under my account.',
                    'tag' => 'remote_backup',
                ],
                [
                    'title' => 'Local Backup',
                    'content' => 'With this option your backups will be stored in your server. Remote backup is recommended if your site valuable.',
                    'tag' => 'local_backup',
                ],
            ],
            'documentations'=>[
                [
                    'title' => 'Site Backup in xCloud',
                    'url' => 'https://xcloud.host/docs/site-backups-in-xcloud/',
                ]
            ]
        ],
        'user.integration.cloudflare' => [
            'title' => 'Using xCloud with Cloudflare for DNS Management',
            'content' => 'Integrating xCloud with Cloudflare simplifies DNS management. After adding your domain to Cloudflare, obtain the necessary credentials. Add your API key in xCloud to auto-configure DNS, enhancing website performance and security.',
            'documentations'=>[
                [
                    'title' => 'Using xCloud with cloudflare for DNS management',
                    'url' => 'https://xcloud.host/docs/using-xcloud-with-cloudflare-for-dns-management/',
                ],
                [
                    'title' => 'Troubleshooting Cloudflare Issues For WordPress: How To Fix Errors While Using xCloud',
                    'url' => 'https://xcloud.host/docs/troubleshooting-cloudflare-issues-for-wordpress/',
                ]
            ]
        ],
    '/credential/choose/hetzner'=> [
        'title' => 'Setting up your server with Hetzner',
        'content' => 'Follow the instructions given below to set up your server with Hetzner as your selected provider.',
        'video' => [
            'url' => 'https://www.youtube.com/embed/IDN84R30CXE?si=G4lEcqg16_QuGxz5',
            'thumbnail' => 'img/video/hetzner-server.jpg'
        ],
        'accordions' => [
            [
                'title' => 'Hetzner Integration',
                'content' => 'Sign up into Hetzner and complete your verification process (conditional) or log in if you have already an account. Choose your project and then visit the Security section of that project, then go to API Tokens section and Generate API Token from there. Copy the API Token and paste it into the xCloud Hetzner integration page.',
                'tag' => 'label',
                'name' => '',
            ],
        ],
        'documentations' => [
            [
                'title' => 'How to Setup a Hetzner Server on xCloud?',
                'url' => 'https://xcloud.host/docs/set-up-a-hetzner-server-in-xcloud/',
            ],
        ]
    ],
];
