<?php

use  App\Services\Provisioning\ServerProvisioning;
use App\Services\Provisioning\SiteProvisioning;

return [
    'verifying_payment' =>  [
        [
            'stage' => 'Verifying Card & Taking Payment',
            'tasks' => [
                ServerProvisioning::VERIFYING_CARD_AND_TAKING_PAYMENT => 'Verifying Card & Taking Payment of $:amount',
            ]
        ]
    ],
    'server_creating' => [
        [
            'stage' => 'Creating Server',
            'tasks' => [
                ServerProvisioning::INIT => 'Initializing Server Provisioning',
                ServerProvisioning::CREATING_SERVER => 'Creating Server On :provider',
                ServerProvisioning::SERVER_CREATED => 'Created Server :ip',
                ServerProvisioning::WAITING_FOR_SERVER_TO_START => 'Waiting For Server To Start',
            ]
        ]
    ],
    'server_provisioning' => [
        [
            'stage' => 'Connecting to Server',
            'tasks' => [
                ServerProvisioning::CONNECTING => 'Connecting To SSH',
                ServerProvisioning::CONNECTED => 'Connection Established',
            ]
        ],
        [
            'stage' => 'Configuring Server',
            'tasks' => [
                ServerProvisioning::CREATING_SWAP => 'Configuring Swapfile',
                ServerProvisioning::UPGRADING_SYSTEM => 'Upgrading System',
                ServerProvisioning::INSTALLING_BASE => 'Installing Base Dependencies',
                ServerProvisioning::AUTHENTICATION_METHOD => 'Updating Authentication Method',
                ServerProvisioning::UPDATING_HOSTNAME => 'Updating Hostname',
                ServerProvisioning::UPDATING_TIMEZONE => 'Updating Timezone',
                ServerProvisioning::XCLOUD_USER => 'Setting Up Users',

                ServerProvisioning::SETUP_SSH => 'Setting Up SSH',
                ServerProvisioning::SETTING_UP_SUDO_PERMISSIONS => 'Setting Up Sudo Permissions',
                ServerProvisioning::SETTING_UP_GIT => 'Setting Up Git',
                ServerProvisioning::SETUP_FIREWALL => 'Setting Up Firewall',
                ServerProvisioning::SETTING_UP_CLEANING_SCRIPT => 'Setting Up Cleaning Script',
            ]
        ],
        [
            'stage' => 'Installing Apps',
            'tasks' => [
                ServerProvisioning::INSTALLING_PHP => 'Installing PHP :php_version',
                ServerProvisioning::INSTALLING_WEBSERVER => 'Installing :stack',
                ServerProvisioning::INSTALLING_NODE => 'Installing Node',
                ServerProvisioning::INSTALLING_REDIS => 'Installing Redis',
                ServerProvisioning::INSTALLING_DATABASE => 'Installing :database_type Database',
                ServerProvisioning::INSTALLING_WP_CLI => 'Installing WP CLI',
            ]
        ],
        [
            'stage' => 'Finishing Up',
            'tasks' => [
                ServerProvisioning::SETTING_SSH_PERMISSIONS => 'Setting Ssh Permissions',
                ServerProvisioning::INSTALL_MONITORING_SCRIPT => 'Installing Monitoring Script',
                ServerProvisioning::READY_TO_DO_MAGIC => 'Ready To Do Magic!',
            ]
        ]
    ],

    'site_provisioning' => [
        [
            'stage' => 'Checking and Verifying',
            'tasks' => [
                SiteProvisioning::CHECKING_STATUS => 'Checking Server Storage & connection',
                SiteProvisioning::VERIFYING_DNS => 'Verifying DNS For Your Site',
            ]
        ],
        [
            'stage' => 'Installing Apps',
            'tasks' => [
                SiteProvisioning::INSTALLING_PHP => 'Installing :site_stack :site_stack_version',
                SiteProvisioning::INSTALLING_DATABASE => 'Installing Database',
                SiteProvisioning::INSTALLING_WORDPRESS => 'Setting Up :type',
            ]
        ],
        [
            'stage' => 'Configuring',
            'tasks' => [
                SiteProvisioning::CONFIGURING_SSL => 'Configuring SSL',
                SiteProvisioning::CONFIGURING_HTTPS => 'Configuring HTTPS',
                SiteProvisioning::CONFIGURING_FULL_PAGE_CACHE => 'Configuring :cache',
                SiteProvisioning::CONFIGURING_NGINX => 'Configuring :stack',
                SiteProvisioning::CONFIGURING_REDIS_CACHE => 'Configuring Redis Object Cache',
                SiteProvisioning::INSTALL_BLUEPRINT => 'Installing Blueprint',
                SiteProvisioning::DEPLOY_SCRIPT => 'Deploy Script',
                SiteProvisioning::INSTALL_MONITORING => 'Installing Monitoring',
                SiteProvisioning::INSTALLING_WP_CRON_JOB => 'Installing WP Cron Job',
                SiteProvisioning::SETTING_UP_EMAIL_PROVIDER => 'Setting Up xCloud Managed Email Service',
                SiteProvisioning::HANDLE_INDEXING => 'Handling Site Indexing',
                SiteProvisioning::FINISHING_UP => 'Finishing Up',
            ]
        ]
    ],
];
