@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@import url("https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Alfa+Slab+One&family=Anton&family=Fredoka+One&family=Pacifico&family=Passion+One:wght@900&family=Permanent+Marker&family=Rubik+Mono+One&family=Russo+One&family=Secular+One&display=swap");

@layer components {
    .multiselect-clear-white {
        background-image: url("data:image/svg+xml,<svg viewBox='0 0 320 512' fill='white' xmlns='http://www.w3.org/2000/svg'><path d='M207.6 256l107.72-107.72c6.23-6.23 6.23-16.34 0-22.58l-25.03-25.03c-6.23-6.23-16.34-6.23-22.58 0L160 208.4 52.28 100.68c-6.23-6.23-16.34-6.23-22.58 0L4.68 125.7c-6.23 6.23-6.23 16.34 0 22.58L112.4 256 4.68 363.72c-6.23 6.23-6.23 16.34 0 22.58l25.03 25.03c6.23 6.23 16.34 6.23 22.58 0L160 303.6l107.72 107.72c6.23 6.23 16.34 6.23 22.58 0l25.03-25.03c6.23-6.23 6.23-16.34 0-22.58L207.6 256z'></path></svg>") !important;
    }

    .multiselect-dark-caret {
        background-image: url("data:image/svg+xml,<svg viewBox='0 0 320 512' fill='white' xmlns='http://www.w3.org/2000/svg'><path d='M31.3 192h257.3c17.8 0 26.7 21.5 14.1 34.1L174.1 354.8c-7.8 7.8-20.5 7.8-28.3 0L17.2 226.1C4.6 213.5 13.5 192 31.3 192z'></path></svg>") !important;
    }
}

html {
    font-size: 14px;
}

@media all and (min-width: 1920px) {
    html {
        font-size: 15px;
    }
}

@media all and (min-width: 2224px) {
    html {
        font-size: 16px;
    }
}

@media all and (max-width: 1535.98px) {
    html {
        font-size: 13px;
    }
}

@media all and (max-width: 479.98px) {
    html {
        font-size: 14px;
    }
}

.xc-container {
    @apply w-full max-w-2560px mx-auto px-50px wide-mobile:px-40px mobile:px-25px monitor:px-100px flex flex-col;
}

.xc-container-2 {
    @apply w-full max-w-2560px mx-auto px-100px tablet:px-60px  wide-mobile:px-40px mobile:px-25px monitor:px-100px flex flex-col;
}

@media (prefers-reduced-motion: no-preference) {
    html {
        @apply scroll-smooth;
    }
}
.swal2-container.swal2-bottom-right {
    z-index: 100001 !important;
}

.swal2-popup.swal2-toast,
.swal2-popup {
    @apply bg-white dark:bg-dark text-dark dark:text-white;
}

.swal2-title {
    @apply !font-normal !text-2xl;
}

.swal2-popup.swal2-toast .swal2-title {
    @apply !text-xl !m-0 !ml-15px !mt-1.5;
}

.swal2-html-container {
    @apply text-base font-medium text-secondary-full dark:text-mode-secondary-dark !ml-15px;
}

.multiselect {
    @apply bg-white dark:bg-mode-light text-dark dark:text-white !border-1 !border-secondary-light dark:!border-mode-focus-light min-h-60px py-2 px-2px w-full focus:!outline-none min-w-0 !rounded-md focus:!shadow-none max-w-[33.33rem];
}
.multiselect .multiselect-placeholder {
    @apply rounded-md pl-20px;
}
.multiselect .multiselect-caret {
    @apply h-20px w-20px bg-light dark:bg-mode-focus-light rounded-sm p-0 opacity-100;
    background-size: 0.6275rem;
}
.multiselect .multiselect-options .multiselect-option {
    @apply bg-white dark:bg-mode-light text-secondary-full dark:text-white hover:text-white hover:bg-success-full dark:hover:bg-dark flex items-center px-20px py-2.5 text-base;
}
.multiselect .multiselect-options .multiselect-option.is-selected {
    @apply bg-success-full text-white dark:bg-dark dark:text-white;
}

.multiselect-tags-search {
    @apply !shadow-none bg-white dark:bg-mode-light text-dark dark:text-white border-secondary-light dark:border-mode-focus-light;
}

.multiselect.is-active {
    box-shadow: none !important;
}
.multiselect-single-label,
.multiselect-group-label,
.multiselect-multiple-label,
.multiselect-fake-input,
.multiselect-search {
    @apply rounded-lg;
}

.multiselect-dropdown,
.multiselect-no-options,
.multiselect-options,
.multiselect-no-results,
.multiselect-single-label,
.multiselect-group-label,
.multiselect-multiple-label,
.multiselect-fake-input,
.multiselect-search {
    @apply !bg-white dark:!bg-mode-light text-dark dark:text-white border-secondary-light dark:border-mode-focus-light;
}

.multiselect-dropdown {
    scrollbar-color: #74778e #d7e6f9;
    scrollbar-width: thin;
}
.dark .multiselect-dropdown {
    scrollbar-color: #d7e6f9 #74778e;
    scrollbar-width: thin;
}
.multiselect-dropdown::-webkit-scrollbar {
    width: 10px;
}
.multiselect-dropdown::-webkit-scrollbar-thumb {
    background-color: #74778e;
    border-radius: 5px;
}
.dark .multiselect-dropdown::-webkit-scrollbar-thumb {
    background-color: #d7e6f9;
    border-radius: 5px;
}
.multiselect-dropdown::-webkit-scrollbar-track-piece {
    background-color: #d7e6f9 !important;
    box-shadow: none !important;
}
.dark .multiselect-dropdown::-webkit-scrollbar-track-piece {
    background-color: #74778e !important;
    box-shadow: none !important;
}

.multiselect-placeholder {
    @apply !bg-white dark:!bg-mode-light text-secondary-light dark:!text-secondary-full border-secondary-light dark:border-mode-focus-light;
}

.multiselect-caret {
    @apply dark:multiselect-dark-caret;
}
.multiselect-no-options {
    @apply dark:bg-mode-focus-light !text-dark dark:!text-white border-secondary-light dark:border-mode-focus-light;
}

.multiselect-tag {
    @apply !bg-primary-light/10 dark:bg-mode-focus-light !text-dark dark:!text-white border-secondary-light dark:border-mode-focus-light !font-normal;
}

.multiselect-clear-icon,
.multiselect-tag-remove-icon {
    @apply dark:!multiselect-clear-white;
}

.multi_step_arrow_content {
    @apply after:content-['\e922'] wide-mobile:after:content-[''];
}

.xcloud-multiselect {
    @apply !bg-white dark:!bg-mode-light !text-dark dark:!text-white !border-secondary-light dark:!border-mode-focus-light !min-h-60px !py-2 !px-2px !border-1 !w-full focus:!outline-none !min-w-0 !rounded-tl-md !rounded-tr-md !rounded-bl-md !rounded-br-md focus:!shadow-none focus:!ring-0 focus-within:!border-success-light !max-w-[33.33rem];
}
.xcloud-multiselect.is-active {
    @apply !rounded-bl-none !rounded-br-none;
}

.xcloud-multiselect .multiselect-single-label {
    @apply !bg-transparent;
}

.xcloud-multiselect .multiselect-tags {
    @apply pl-4;
}

.xcloud-multiselect .multiselect-placeholder {
    @apply pl-25px;
}
.multiselect-tag {
    white-space: normal !important;
}

.add-member-multiselect .multiselect {
    @apply !max-w-full;
}
.site-select-multiselect .multiselect .multiselect-search {
    border-radius: 0.5rem!important;
}

.site-select-multiselect .multiselect {
    max-width: 100%!important
}

input[type="search"]::-webkit-search-cancel-button {
    @apply !cursor-pointer;
}
.ring-danger-light {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgba(239, 68, 68, var(--tw-ring-opacity));
}

.no-scrollbar::-webkit-scrollbar {
    display: none;
}
/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
}

.scrollbar-thin {
    scrollbar-color: #74778e #d7e6f9;
    scrollbar-width: thin;
}
.dark .dark\:scrollbar-thin {
    scrollbar-color: #d7e6f9 #74778e;
    scrollbar-width: thin;
}
.scrollbar-thin::-webkit-scrollbar,
.dark .dark\:scrollbar-thin::-webkit-scrollbar {
    width: 10px;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: #74778e;
}
.dark .dark\:scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: #d7e6f9;
}
.scrollbar-thin::-webkit-scrollbar-track {
    background-color: #d7e6f9 !important;
    box-shadow: none !important;
}
.dark .dark\:scrollbar-thin::-webkit-scrollbar-track {
    background-color: #74778e !important;
    box-shadow: none !important;
}



/*
&::-webkit-scrollbar {
    width: 2px;
}
&::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
}

&::-webkit-scrollbar-thumb {
    background-color: rgb(167, 156, 156);
    border-radius: 5px;
} */


/* custom css for scrollbar */
.custom-scrollbar {
    scrollbar-width: thin;
    /* For Firefox */
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
    /* Thumb color and Track color */
}

/* Dark mode colors */
.dark .custom-scrollbar {
    --scrollbar-thumb: #4c4f68;
    --scrollbar-thumb-hover: #61647c;
    --scrollbar-track: #1d2238;
}

/* Light mode colors */
.custom-scrollbar {
    --scrollbar-thumb: #cbd5e1;  /* Light gray for thumb */
    --scrollbar-thumb-hover: #94a3b8;  /* Slightly darker on hover */
    --scrollbar-track: #f1f5f9;  /* Very light gray for track */
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb);
    border-radius: 8px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: var(--scrollbar-thumb-hover);
    /* Thumb color on hover */
}

/* Custom styles for file icons using Flag Icons library
   Reference: "flag-icons/css/flag-icons.min.css" */
.fi-round {
    background-size: cover;
    background-position: center;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    border: 2px solid #ccc;
}
/* End Custom styles for file icons using Flag Icons library
   Reference: "flag-icons/css/flag-icons.min.css" */
