@extends('layouts.app')

@section('title', 'View API Endpoint')

@section('content')
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-eye me-2"></i>API Endpoint Details
                </h4>
                <span class="badge {{ $apiEndpoint->active ? 'bg-success' : 'bg-secondary' }} fs-6">
                    <i class="fas fa-{{ $apiEndpoint->active ? 'check' : 'times' }} me-1"></i>
                    {{ $apiEndpoint->status_label }}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Name</h6>
                        <p class="mb-3">{{ $apiEndpoint->name ?: 'Unnamed Endpoint' }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Count</h6>
                        <p class="mb-3">
                            <span class="badge bg-info fs-6">{{ number_format($apiEndpoint->count) }}</span>
                        </p>
                    </div>
                </div>

                <div class="mb-3">
                    <h6 class="text-muted mb-2">URL</h6>
                    <div class="d-flex align-items-center">
                        <code class="bg-light p-2 rounded flex-grow-1 me-2">{{ $apiEndpoint->url }}</code>
                        <a href="{{ $apiEndpoint->url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>

                <div class="mb-3">
                    <h6 class="text-muted mb-2">API Key</h6>
                    <div class="d-flex align-items-center">
                        <code class="bg-light p-2 rounded flex-grow-1 me-2" id="apiKeyDisplay">{{ $apiEndpoint->masked_api_key }}</code>
                        <button class="btn btn-sm btn-outline-secondary" type="button" id="toggleApiKey" data-full-key="{{ $apiEndpoint->api_key }}">
                            <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>

                @if($apiEndpoint->description)
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Description</h6>
                        <p class="mb-0">{{ $apiEndpoint->description }}</p>
                    </div>
                @endif

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Created</h6>
                        <p class="mb-3">{{ $apiEndpoint->created_at->format('F j, Y \a\t g:i A') }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Last Updated</h6>
                        <p class="mb-3">{{ $apiEndpoint->updated_at->format('F j, Y \a\t g:i A') }}</p>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <a href="{{ route('api-endpoints.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                        <a href="{{ route('api-endpoints.edit', $apiEndpoint) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-1"></i>Edit
                        </a>
                    </div>
                    
                    <div class="btn-group">
                        <!-- Toggle Status -->
                        <form method="POST" action="{{ route('api-endpoints.toggle-status', $apiEndpoint) }}" class="d-inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-{{ $apiEndpoint->active ? 'warning' : 'success' }}">
                                <i class="fas fa-{{ $apiEndpoint->active ? 'pause' : 'play' }} me-1"></i>
                                {{ $apiEndpoint->active ? 'Deactivate' : 'Activate' }}
                            </button>
                        </form>
                        
                        <!-- Increment Count -->
                        <form method="POST" action="{{ route('api-endpoints.increment-count', $apiEndpoint) }}" class="d-inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-plus me-1"></i>Increment
                            </button>
                        </form>
                        
                        <!-- Reset Count -->
                        <form method="POST" action="{{ route('api-endpoints.reset-count', $apiEndpoint) }}" 
                              class="d-inline" onsubmit="return confirm('Are you sure you want to reset the count to 0?')">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-outline-warning">
                                <i class="fas fa-undo me-1"></i>Reset Count
                            </button>
                        </form>
                        
                        <!-- Delete -->
                        <form method="POST" action="{{ route('api-endpoints.destroy', $apiEndpoint) }}" 
                              class="d-inline" onsubmit="return confirm('Are you sure you want to delete this endpoint? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.getElementById('toggleApiKey').addEventListener('click', function() {
    const apiKeyDisplay = document.getElementById('apiKeyDisplay');
    const toggleIcon = document.getElementById('toggleIcon');
    const fullKey = this.getAttribute('data-full-key');
    const maskedKey = '{{ $apiEndpoint->masked_api_key }}';
    
    if (toggleIcon.className.includes('fa-eye')) {
        apiKeyDisplay.textContent = fullKey;
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        apiKeyDisplay.textContent = maskedKey;
        toggleIcon.className = 'fas fa-eye';
    }
});
</script>
@endpush
