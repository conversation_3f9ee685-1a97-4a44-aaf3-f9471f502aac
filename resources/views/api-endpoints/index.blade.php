@extends('layouts.app')

@section('title', 'API Endpoints')

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="fas fa-plug me-2"></i>API Endpoints</h1>
    <a href="{{ route('api-endpoints.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>Add New Endpoint
    </a>
</div>

<!-- Search and Filter Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('api-endpoints.index') }}" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ request('search') }}" placeholder="Search by name, URL, or description...">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-search me-1"></i>Search
                </button>
                <a href="{{ route('api-endpoints.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>
</div>

@if($endpoints->count() > 0)
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>Name</th>
                            <th>URL</th>
                            <th>API Key</th>
                            <th>Status</th>
                            <th>Count</th>
                            <th>Created</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($endpoints as $endpoint)
                            <tr>
                                <td>
                                    <strong>{{ $endpoint->name ?: 'Unnamed' }}</strong>
                                    @if($endpoint->description)
                                        <br><small class="text-muted">{{ Str::limit($endpoint->description, 50) }}</small>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ $endpoint->url }}" target="_blank" class="text-decoration-none">
                                        {{ Str::limit($endpoint->url, 40) }}
                                        <i class="fas fa-external-link-alt ms-1 small"></i>
                                    </a>
                                </td>
                                <td>
                                    <span class="masked-key">{{ $endpoint->masked_api_key }}</span>
                                </td>
                                <td>
                                    <span class="badge status-badge {{ $endpoint->active ? 'bg-success' : 'bg-secondary' }}">
                                        <i class="fas fa-{{ $endpoint->active ? 'check' : 'times' }} me-1"></i>
                                        {{ $endpoint->status_label }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ number_format($endpoint->count) }}</span>
                                </td>
                                <td>
                                    <small class="text-muted">{{ $endpoint->created_at->format('M j, Y') }}</small>
                                </td>
                                <td class="table-actions text-center">
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('api-endpoints.show', $endpoint) }}" 
                                           class="btn btn-sm btn-outline-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('api-endpoints.edit', $endpoint) }}" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <!-- Toggle Status -->
                                        <form method="POST" action="{{ route('api-endpoints.toggle-status', $endpoint) }}" 
                                              class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" 
                                                    class="btn btn-sm btn-outline-{{ $endpoint->active ? 'warning' : 'success' }}" 
                                                    title="{{ $endpoint->active ? 'Deactivate' : 'Activate' }}">
                                                <i class="fas fa-{{ $endpoint->active ? 'pause' : 'play' }}"></i>
                                            </button>
                                        </form>
                                        
                                        <!-- Delete -->
                                        <form method="POST" action="{{ route('api-endpoints.destroy', $endpoint) }}" 
                                              class="d-inline" onsubmit="return confirm('Are you sure you want to delete this endpoint?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-center mt-4">
        {{ $endpoints->withQueryString()->links() }}
    </div>
@else
    <div class="text-center py-5">
        <i class="fas fa-plug fa-3x text-muted mb-3"></i>
        <h3 class="text-muted">No API Endpoints Found</h3>
        <p class="text-muted">Get started by creating your first API endpoint.</p>
        <a href="{{ route('api-endpoints.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>Add New Endpoint
        </a>
    </div>
@endif
@endsection
