@extends('layouts.app')

@section('title', 'Create API Endpoint')

@section('content')
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-plus me-2"></i>Create New API Endpoint
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('api-endpoints.store') }}">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Name <small class="text-muted">(Optional)</small></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name') }}" 
                               placeholder="Enter a descriptive name for this endpoint">
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="url" class="form-label">URL <span class="text-danger">*</span></label>
                        <input type="url" class="form-control @error('url') is-invalid @enderror" 
                               id="url" name="url" value="{{ old('url') }}" 
                               placeholder="https://api.example.com/endpoint" required>
                        @error('url')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Enter the full URL of the API endpoint.</div>
                    </div>

                    <div class="mb-3">
                        <label for="api_key" class="form-label">API Key <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="password" class="form-control @error('api_key') is-invalid @enderror" 
                                   id="api_key" name="api_key" value="{{ old('api_key') }}" 
                                   placeholder="Enter the API key" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                <i class="fas fa-eye" id="toggleIcon"></i>
                            </button>
                            @error('api_key')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <div class="form-text">The API key will be encrypted and stored securely.</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="active" name="active" 
                                   value="1" {{ old('active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="active">
                                <strong>Active</strong>
                                <small class="text-muted d-block">Enable this endpoint immediately after creation</small>
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="count" class="form-label">Initial Count</label>
                        <input type="number" class="form-control @error('count') is-invalid @enderror" 
                               id="count" name="count" value="{{ old('count', 0) }}" 
                               min="0" placeholder="0">
                        @error('count')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">Set an initial count value (defaults to 0).</div>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="form-label">Description <small class="text-muted">(Optional)</small></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" 
                                  placeholder="Enter a description for this endpoint...">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('api-endpoints.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Create Endpoint
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.getElementById('toggleApiKey').addEventListener('click', function() {
    const apiKeyInput = document.getElementById('api_key');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (apiKeyInput.type === 'password') {
        apiKeyInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        apiKeyInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
});
</script>
@endpush
