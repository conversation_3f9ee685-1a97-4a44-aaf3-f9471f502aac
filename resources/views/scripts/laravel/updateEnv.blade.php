# Copy .env.example to .env if not exists
if [ ! -f /var/www/{{ $site->name }}/.env ]; then
    cp .env.example .env
fi

# Define function to update environment variables
update_env_var() {
    local key=$1
    local value=$2
    local env_file=$3

    # Update if exists uncommented, otherwise check for commented version
    if grep -q "^${key}=" "$env_file"; then
        # Update existing uncommented variable
        sed -i "s|^${key}=.*|${key}=${value}|" "$env_file"
    elif grep -q "^#\s*${key}=" "$env_file"; then
        # Found commented variable, uncomment it and set the new value
        sed -i "s|^#\s*${key}=.*|${key}=${value}|" "$env_file"
    else
        # Variable doesn't exist at all, add it
        echo "${key}=${value}" >> "$env_file"
    fi
}

# Set environment file path
ENV_FILE="/var/www/{{ $site->name }}/.env"

# Check if APP_KEY exists in .env and generate if needed
if ! grep -q "APP_KEY=" "$ENV_FILE" || grep -q "APP_KEY=$" "$ENV_FILE"; then
    # Generate a random 32 character base64 key
    APP_KEY="base64:$(openssl rand -base64 32)"
    # Update APP_KEY in .env file
    update_env_var "APP_KEY" "${APP_KEY}" "$ENV_FILE"
fi

# Set/update environment variables
update_env_var "APP_ENV" "production" "$ENV_FILE"
update_env_var "APP_DEBUG" "false" "$ENV_FILE"
update_env_var "APP_URL" {!! escapeshellarg($site->site_url) !!} "$ENV_FILE"
update_env_var "APP_NAME" "{!! escapeshellarg($site->title ?: $site->name) !!}" "$ENV_FILE"

@if($createConfig)
    update_env_var "DB_CONNECTION" "mysql" "$ENV_FILE"
    update_env_var "DB_HOST" "{{ $site->database_host ?: '127.0.0.1' }}" "$ENV_FILE"
    update_env_var "DB_PORT" "{{ $site->database_port ?: 3306 }}" "$ENV_FILE"
    update_env_var "DB_DATABASE" "{{ $site->database_name }}" "$ENV_FILE"
    update_env_var "DB_USERNAME" "{{ $site->database_user }}" "$ENV_FILE"
    update_env_var "DB_PASSWORD" "{{ $site->database_password }}" "$ENV_FILE"
@endif
