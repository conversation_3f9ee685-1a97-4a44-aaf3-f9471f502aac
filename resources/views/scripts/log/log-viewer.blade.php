if compgen -G {{ $filePath }} > /dev/null; then
    for file in {{ $filePath }}; do
        if [ -s "$file" ]; then
            # Try to get the specified number of lines
            output=$(tail -n {{ $lineNumbers }} "$file")
            outputSize=$(echo "$output" | wc -c)
            
            # If output is larger than sizeInBytes, reduce line count until it fits
            if [ $outputSize -gt {{ $sizeInBytes }} ]; then
                # Calculate approximate lines per byte to estimate new line count
                bytesPerLine=$((outputSize / {{ $lineNumbers }}))
                newLineCount=$(({{ $sizeInBytes }} / bytesPerLine))
                # Ensure we get at least 1 line
                newLineCount=$((newLineCount > 0 ? newLineCount : 1))
                tail -n $newLineCount "$file"
            else
                echo "$output"
            fi
        else
            echo "The file $file is empty."
        fi
    done
else
    echo "The file does not exist."
fi
