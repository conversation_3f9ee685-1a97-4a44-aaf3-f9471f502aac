# -----------
# Update Other PHP Versions
# -----------
@foreach($phpVersions as $phpVersion)
    @if($server->stack->isNginx())
        php_ini_file="/etc/php/{{ $phpVersion }}/fpm/php.ini"
    @else
        php_ini_file="/usr/local/lsws/lsphp{{ str_replace('.', '', $phpVersion) }}/etc/php/{{ $phpVersion }}/litespeed/php.ini"
    @endif

    echo $php_ini_file

    if [ -f "$php_ini_file" ]; then
        echo "Updating PHP {{ $phpVersion }} settings with:" \
            "upload_max_filesize={{ $upload_max_filesize }}," \
            "post_max_size={{ $post_max_size ?? $upload_max_filesize }}," \
            "max_execution_time={{ $max_execution_time }}," \
            "max_input_time={{ $max_input_time ?? $max_execution_time }}," \
            "memory_limit={{ $memory_limit }}," \
            "max_input_vars={{ $max_input_vars ?? '' }}," \
            "session_gc_maxlifetime={{ $session_gc_maxlifetime ?? '' }}" \
            "opcache_enabled={{ $php_opcache_enabled ?? '' }}"

        sudo sed -i "s/upload_max_filesize = .*/upload_max_filesize = {{ $upload_max_filesize }}/" $php_ini_file
        sudo sed -i "s/post_max_size = .*/post_max_size = {{ $post_max_size ?? $upload_max_filesize }}/" $php_ini_file

        sudo sed -i "s/max_execution_time = .*/max_execution_time = {{ $max_execution_time }}/" $php_ini_file
        sudo sed -i "s/max_input_time = .*/max_input_time = {{ $max_input_time ?? $max_execution_time }}/" $php_ini_file

        sudo sed -i "s/memory_limit = .*/memory_limit = {{ $memory_limit }}/" $php_ini_file
        @if(isset($max_input_vars))
        if grep -q ";[[:space:]]*max_input_vars" $php_ini_file; then
            sudo sed -i "s/;[[:space:]]*max_input_vars = .*/max_input_vars = {{ $max_input_vars }}/" $php_ini_file
        else
            sudo sed -i "s/max_input_vars = .*/max_input_vars = {{ $max_input_vars }}/" $php_ini_file
        fi
        @endif

        @if(isset($session_gc_maxlifetime))
            sudo sed -i "s/session.gc_maxlifetime = .*/session.gc_maxlifetime = {{ $session_gc_maxlifetime }}/" $php_ini_file
        @endif

        @include('scripts.php.updateOpcache')
    fi

    if [ -f "/etc/php/{{ $phpVersion }}/cli/php.ini" ]; then
        sudo sed -i "s/upload_max_filesize = .*/upload_max_filesize = {{ $upload_max_filesize }}/" /etc/php/{{ $phpVersion }}/cli/php.ini
        sudo sed -i "s/post_max_size = .*/post_max_size = {{ $post_max_size ?? $upload_max_filesize }}/" /etc/php/{{ $phpVersion }}/cli/php.ini
    fi

    echo "Restarting PHP {{ $phpVersion }} FPM"
    service php{{ $phpVersion }}-fpm restart > /dev/null 2>&1
@endforeach

@includeWhen($server->stack->isNginx(), 'scripts.php.updateSettingsNginx')

@if($server->stack->isNginx())
NGINX=$(ps aux | grep nginx | grep -v grep)

if [[ -z $NGINX ]]; then
    service nginx start
    echo "Started Nginx"
else
    service nginx reload
    echo "Reloaded Nginx"
fi
@endif

@if($server->stack->isOpenLiteSpeed())
    echo "Restarting LiteSpeed Web Server"
    /usr/local/lsws/bin/openlitespeed -t
    systemctl restart lsws
    killall -9 lsphp
@endif
