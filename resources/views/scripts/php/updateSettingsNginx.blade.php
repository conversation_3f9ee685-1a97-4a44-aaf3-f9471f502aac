# ----- FUNCTIONS -----

# Function to log messages with timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to clean up existing settings in nginx.conf
clean_nginx_conf() {
    log_message "Cleaning up existing settings in nginx.conf"
    if grep -q client_max_body_size /etc/nginx/nginx.conf; then
        sudo sed -i '/client_max_body_size/d' /etc/nginx/nginx.conf
        log_message "Removed client_max_body_size from nginx.conf"
    fi

    if grep -q fastcgi_read_timeout /etc/nginx/nginx.conf; then
        sudo sed -i '/fastcgi_read_timeout/d' /etc/nginx/nginx.conf
        log_message "Removed fastcgi_read_timeout from nginx.conf"
    fi
}

# Function to find the largest filesize and input time across PHP configurations
find_largest_settings() {
    local largest_filesize=0
    local largest_filesize_file=''
    local largest_input_time=-1  # -1 is a valid value in PHP, meaning no limit
    local largest_input_time_file=''

    log_message "Scanning PHP configurations for maximum values..."

    # Loop through each PHP version directory
    for php_dir in /etc/php/*; do
        if [[ ! -d "$php_dir" ]]; then
            continue  # Skip if not a directory
        fi

        php_version=$(basename "$php_dir")
        log_message "Processing PHP version: $php_version"
        php_ini="${php_dir}/fpm/php.ini"

        # Check php.ini files
        if [[ -f "$php_ini" ]]; then
            # Extract values from php.ini
            upload_max_filesize=$(grep -Po '(?<=upload_max_filesize = )\d+' "$php_ini" 2>/dev/null || echo "0")
            max_input_time=$(grep -Po '(?<=max_input_time = )-?\d+' "$php_ini" 2>/dev/null || echo "-1")

            # Update largest filesize if current one is larger
            if (( upload_max_filesize > largest_filesize )); then
                largest_filesize=$upload_max_filesize
                largest_filesize_file=$php_ini
                log_message "Found larger upload_max_filesize: ${largest_filesize}M in $php_ini"
            fi

            # Update largest input time if current one is larger
            if (( max_input_time > largest_input_time )); then
                largest_input_time=$max_input_time
                largest_input_time_file=$php_ini
                log_message "Found larger max_input_time: $largest_input_time in $php_ini"
            fi
        fi

        # Check pool configuration files
        pool_dir="${php_dir}/fpm/pool.d"
        if [[ -d "$pool_dir" ]]; then
            for pool_file in "$pool_dir"/*.conf; do
                if [[ -f "$pool_file" ]]; then
                    # Extract max_input_time from pool files
                    pool_input_time=$(grep -Po '(?<=php_admin_value\[max_input_time\]=)-?\d+' "$pool_file" 2>/dev/null || echo "")
                    if [[ -n "$pool_input_time" ]] && (( pool_input_time > largest_input_time )); then
                        largest_input_time=$pool_input_time
                        largest_input_time_file=$pool_file
                        log_message "Found larger max_input_time: $largest_input_time in $pool_file"
                    fi

                    # Extract upload_max_filesize from pool files
                    pool_filesize=$(grep -Po '(?<=php_admin_value\[upload_max_filesize\]=)\d+M' "$pool_file" 2>/dev/null | grep -Po '\d+' || echo "")
                    if [[ -n "$pool_filesize" ]] && (( pool_filesize > largest_filesize )); then
                        largest_filesize=$pool_filesize
                        largest_filesize_file=$pool_file
                        log_message "Found larger upload_max_filesize: ${largest_filesize}M in $pool_file"
                    fi
                fi
            done
        fi
    done

    # Return values via echo
    echo "$largest_filesize|$largest_filesize_file|$largest_input_time|$largest_input_time_file"
}

# Function to update Nginx configuration files
update_nginx_conf() {
    local largest_filesize=$1
    local largest_filesize_file=$2
    local largest_input_time=$3
    local largest_input_time_file=$4

    # Create or update Nginx configuration files
    log_message "Updating Nginx configuration files with new values"

    # Update uploads.conf
    echo "client_max_body_size ${largest_filesize}M;" | sudo tee /etc/nginx/conf.d/uploads.conf > /dev/null
    log_message "Updated client_max_body_size to ${largest_filesize}M in /etc/nginx/conf.d/uploads.conf"

    # Update timeout.conf
    echo "fastcgi_read_timeout $largest_input_time;" | sudo tee /etc/nginx/conf.d/timeout.conf > /dev/null
    log_message "Updated fastcgi_read_timeout to $largest_input_time in /etc/nginx/conf.d/timeout.conf"
}

# Function to update request_terminate_timeout in PHP-FPM pool files
update_pool_timeouts() {
    local largest_input_time=$1

    log_message "Updating request_terminate_timeout in PHP-FPM pool files"

    for php_dir in /etc/php/*; do
        if [[ ! -d "$php_dir" ]]; then
            continue  # Skip if not a directory
        fi

        php_version=$(basename "$php_dir")
        pool_dir="${php_dir}/fpm/pool.d"

        if [[ -d "$pool_dir" ]]; then
            for pool_file in "$pool_dir"/*.conf; do
                if [[ -f "$pool_file" ]]; then
                    log_message "Updating request_terminate_timeout to $largest_input_time in $pool_file"
                    sudo sed -i "s/request_terminate_timeout\( *\)=.*/request_terminate_timeout = $largest_input_time/" "$pool_file"
                fi
            done
        fi
    done
}

# ----- MAIN EXECUTION -----

# Step 1: Clean up existing settings in nginx.conf
clean_nginx_conf

# Step 2: Find the largest settings across all PHP configurations
result=$(find_largest_settings)
IFS='|' read -r largest_filesize largest_filesize_file largest_input_time largest_input_time_file <<< "$result"

# Step 3: Display the found values
log_message "Summary of maximum values found:"
log_message "Largest upload_max_filesize: ${largest_filesize}M in ${largest_filesize_file}"
log_message "Largest max_input_time: ${largest_input_time} in ${largest_input_time_file}"

# Step 4: Update Nginx configuration files
update_nginx_conf "$largest_filesize" "$largest_filesize_file" "$largest_input_time" "$largest_input_time_file"

# Step 5: Update request_terminate_timeout in PHP-FPM pool files
update_pool_timeouts "$largest_input_time"

log_message "Configuration update completed successfully"
