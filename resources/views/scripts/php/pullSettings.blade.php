php_versions=()

# Get installed PHP packages
php_packages=$(dpkg -l | grep '^ii' | grep 'php' | awk '{print $2}')

# Extract PHP versions from the package names
for package in $php_packages; do
    # Use regex to extract version number
@if($server->stack->isNginx())
    if [[ $package =~ ([0-9]+\.[0-9]+) ]]; then
        php_version="${BASH_REMATCH[1]}"
@else
    if [[ $package =~ ([0-9][0-9]) ]]; then
        php_version="${BASH_REMATCH[1]}"
        php_version=$(echo "scale=1; $php_version / 10" | bc) # Convert 80 to 8.0
@endif
        # Add version to the array only if it's not already present
        if [[ ! " ${php_versions[@]} " =~ " $php_version " ]]; then
            php_versions+=("$php_version")
        fi
    fi
done

# Create JSON object
json_output="{"
for version in "${php_versions[@]}"; do
    # Check if php.ini file exists
@if($server->stack->isNginx())
    php_ini_file="/etc/php/$version/fpm/php.ini"
@else
    ls_php_version=$(printf "%.0f" $(echo "$version * 10" | bc));
    php_ini_file="/usr/local/lsws/lsphp${ls_php_version}/etc/php/${version}/litespeed/php.ini"
@endif

    if [[ -e $php_ini_file ]]; then
        json_output+="\"$version\": {"
        # Get max_execution_time and upload_max_filesize values
        max_execution_time=$(grep -E "^max_execution_time" "$php_ini_file" | awk '{print $3}')
        upload_max_filesize=$(grep -E "^upload_max_filesize" "$php_ini_file" | awk '{print $3}')
        memory_limit=$(grep -E "^memory_limit" "$php_ini_file" | awk '{print $3}')
        max_input_vars=$( grep -E "^;?\s*max_input_vars" "$php_ini_file" | awk -F '=' '{print $2}' | cut -d ' ' -f2)

        # Check if opcache is enabled - need to check both main php.ini and opcache.ini in mods-available
        @if($server->stack->isNginx())
            opcache_ini_file="/etc/php/$version/mods-available/opcache.ini"
        @else
            opcache_ini_file="/usr/local/lsws/lsphp${version/./}/etc/php/$version/mods-available/opcache.ini"
        @endif

        # Function to check if opcache is enabled in a file
        check_opcache_enabled() {
            local ini_file=$1
            if [ -f "$ini_file" ]; then
                # Check if zend_extension is uncommented and opcache.enable=1 (if present)
                if grep -q "^\s*zend_extension\s*=\s*opcache" "$ini_file" || grep -q "^\s*zend_extension\s*=\s*opcache.so" "$ini_file"; then
                    # If zend_extension is uncommented, check if opcache.enable exists and is set to 0
                    if grep -q "^\s*opcache.enable\s*=\s*0" "$ini_file"; then
                        return 1 # opcache is disabled
                    else
                        return 0 # opcache is enabled
                    fi
                else
                    return 1 # opcache is disabled (zend_extension is commented out)
                fi
            fi
            return 1 # file doesn't exist, assume disabled
        }

        # Check both files
        opcache_enabled="false"
        if check_opcache_enabled "$php_ini_file" || check_opcache_enabled "$opcache_ini_file"; then
            opcache_enabled="true"
        fi

        # if max_input_vars is not set, get the default value from ;max_input_vars
        if [ -z "$max_input_vars" ]; then
            max_input_vars=$(grep -E "^;max_input_vars" "$php_ini_file" | awk '{print $3}')
        fi

        max_input_time=$(grep -E "^\s*max_input_time" "$php_ini_file" | awk -F '=' '{gsub(/^[ \t]+|[ \t]+$/, "", $2); print $2}')
        if [ -z "$max_input_time" ]; then
            max_input_time=$(grep -E "^\s*;max_input_time" "$php_ini_file" | awk '{print $3}')
        fi

        session_gc_maxlifetime=$(grep -E "^\s*session.gc_maxlifetime" "$php_ini_file" | awk -F '=' '{gsub(/^[ \t]+|[ \t]+$/, "", $2); print $2}')
        if [ -z "$session_gc_maxlifetime" ]; then
            session_gc_maxlifetime=$(grep -E "^\s*;session.gc_maxlifetime" "$php_ini_file" | awk '{print $3}')
        fi

        allow_url_fopen=$(grep -E "^\s*allow_url_fopen" "$php_ini_file" | awk -F '=' '{gsub(/^[ \t]+|[ \t]+$/, "", $2); print $2}')
        if [ -z "$allow_url_fopen" ]; then
            allow_url_fopen=$(grep -E "^\s*;allow_url_fopen" "$php_ini_file" | awk '{print $3}')
        fi

        post_max_size=$( grep -E "^;?\s*post_max_size" "$php_ini_file" | awk -F '=' '{print $2}' | cut -d ' ' -f2)
        if [ -z "$post_max_size" ]; then
            post_max_size=$(grep -E "^;post_max_size" "$php_ini_file" | awk '{print $3}')
        fi

        json_output+="\"max_execution_time\": \"$max_execution_time\", "
        json_output+="\"upload_max_filesize\": \"$upload_max_filesize\", "
        json_output+="\"max_input_vars\": \"$max_input_vars\", "
        json_output+="\"max_input_time\": \"$max_input_time\", "
        json_output+="\"post_max_size\": \"$post_max_size\", "
        json_output+="\"session_gc_maxlifetime\": \"$session_gc_maxlifetime\", "
        json_output+="\"allow_url_fopen\": \"$allow_url_fopen\", "
        json_output+="\"memory_limit\": \"$memory_limit\", "
        json_output+="\"opcache_enabled\": $opcache_enabled"

        json_output+="},"
    fi
done
json_output="${json_output%,}"
json_output+="}"

echo  $json_output;
