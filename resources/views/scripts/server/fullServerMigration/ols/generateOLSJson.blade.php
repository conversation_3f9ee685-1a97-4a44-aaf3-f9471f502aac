# Function to parse the OLS configuration and add wp_config_path
generate_ols_json_array() {
    local parsed_data="$1"
    local base_path="$2"
    local output="["
    # Process each line of parsed data
    while IFS=',' read -r vh_root domain_name php_version; do

        # Get the WordPress configuration file path
        wp_config_file=$(get_wp_config_file "$base_path$vh_root")
        # Check if wp-config.php exists, skip if not found
        if [ ! -f "$wp_config_file" ]; then
            continue
        fi
        db_name=$(get_db_name "$wp_config_file")
        db_user=$(get_db_user "$wp_config_file")
        db_password=$(get_db_password "$wp_config_file")
        db_host=$(get_db_host "$wp_config_file")
        db_port=$(get_db_port "$wp_config_file")

        #if domain name is empty, set it to directory name after last slash
        if [ -z "domain" ]; then
            domain_name=$(echo "$base_path$vh_root" | rev | cut -d '/' -f 1 | rev)
        fi
        site_name=$(echo "$domain_name" | cut -d '.' -f 1)

        # Calculate storage usage in GB
        # Use -k instead of -sb for macOS compatibility, then convert KB to GB
        storage=$(du -sk "$base_path$vh_root" | awk '{usage = $1 / 1024 / 1024; printf "%.2f", usage}')

        # Get the database size
        db_size=$(get_database_size "$db_name" "$db_host" "$db_port" "$db_user" "$db_password")

        # Calculate total size
        # Fix awk syntax by ensuring db_size and storage are properly passed
        if [ -z "$db_size" ]; then
            db_size=0
        fi
        total_size=$(awk "BEGIN { total = $db_size + $storage; printf \"%.2f\", total }")

       # Construct the JSON object
        json_object=$(printf '{
            "site_name": "%s",
            "directory": "%s",
            "domain_name": "%s",
            "php_version": "%s",
            "wp_config_file": "%s",
            "database": {
                "name": "%s",
                "user": "%s",
                "password": "%s",
                "host": "%s",
                "port": "%s",
                "size": "%.2fGB"
            },
            "storage": "%.2fGB",
            "total_size": "%.2fGB"
        }' \
        "$site_name" "$vh_root" "$domain_name" "$php_version" "$wp_config_file" "$db_name" "$db_user" "$db_password" "$db_host" "$db_port" "$db_size" "$storage" "$total_size")

        if [ "$output" != "[" ]; then
            output+=",\n$json_object"
        else
            output+="$json_object"
        fi
    done <<< "$parsed_data"

    output+="]"
    echo -e "$output"
}
