# Check if fail2ban is running for sshd
if ! sudo fail2ban-client status sshd &>/dev/null; then
    echo "Fail2Ban is not enabled for SSHD. Enabling Fail2Ban for SSHD..."
    sudo systemctl restart fail2ban
    sudo systemctl enable fail2ban
fi

# Run fail2ban-client status sshd and extract banned IP addresses
banned_ips=$(sudo iptables -L -n | awk '$1=="REJECT" && $4!="0.0.0.0/0" {print $4}')

# Print the extracted banned IP addresses
echo "Banned IP addresses:"
echo "$banned_ips"
