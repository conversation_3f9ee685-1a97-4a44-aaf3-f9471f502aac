DOMAIN={{$site->name}}
TIMEOUT=30

response=$(curl -k -s -o /dev/null -w "%{http_code}" -I http://$DOMAIN)

if [[ "$response" -eq 301 || "$response" -eq 302 ]]; then
    # Get the final destination URL if redirected
    redirect_url=$(curl -k -s -o /dev/null -w "%{redirect_url}" -I http://$DOMAIN)

    # Extract the base domain from the redirect URL
    REDIRECT_DOMAIN=$(echo "$redirect_url" | awk -F/ '{print $3}')

    if [[ "$REDIRECT_DOMAIN" == "$DOMAIN" || -z "$REDIRECT_DOMAIN" ]]; then
        # Follow the redirect and get the final response code
        response=$(curl -k -s -o /dev/null -w "%{http_code}" -L http://$DOMAIN)
    fi
fi

if [ $response -eq 200 ] || [ $response -eq 201 ] || [ $response -eq 202 ] || [ $response -eq 204 ] || [ $response -eq 304 ] ; then
     # website up
        site_status_code=$response
        site_status_description="OK - ${DOMAIN} is up and running."
elif [ $response -eq 301 ] || [ $response -eq 302 ] || [ $response -eq 307 ]; then
        site_status_code=$response
        site_status_description="WARNING - ${DOMAIN} has been temporarily moved to a new location. Response code: ${response}."
  else
        site_status_code=$response
        site_status_description="CRITICAL - ${DOMAIN} is down. Response code: ${response}."
fi

#ssl check
DAYS=7;
expirationdate=0
if curl --head --silent --insecure "https://$DOMAIN" > /dev/null; then
    expirationdate=$(date -d "$(: | openssl s_client -connect $DOMAIN:443 -servername $DOMAIN 2>/dev/null \
                                  | openssl x509 -text \
                                  | grep 'Not After' \
                                  |awk '{print $4,$5,$7}')" '+%s');
fi
in7days=$(($(date +%s) + (86400*$DAYS)));
if [ $in7days -gt $expirationdate ]; then
    ssl_status="error"
    ssl_expiration_date="$expirationdate"
else
    ssl_status="success"
    ssl_expiration_date="$expirationdate"
fi;

#domain host check

IP={{ $site->server->ip }}
OUTPUT=$(dig +short ${DOMAIN} 2>&1)


if [[ $OUTPUT =~ .*${IP}.* ]]; then
    dns_status="success"
    dns_description="DNS Monitor for ${DOMAIN} is working fine."

else
    dns_status="error"
    dns_description="The DNS Monitor for ${DOMAIN} is having issues based on the ip ${IP} you set."

fi

declare site_integrity
@includeWhen($site->isWordpress(),'scripts.site.integrity.verify-checksums')

# if site_integrity is empty, set it to null
if [ -z "$site_integrity" ]; then
    site_integrity="null"
fi

DATA='{
    "site_status_code": "'"$site_status_code"'",
    "site_status_description": "'"$site_status_description"'",
    "ssl_status": "'"$ssl_status"'",
    "ssl_expiration_date": "'"$ssl_expiration_date"'",
    "dns_status": "'"$dns_status"'",
    "dns_description": "'"$dns_description"'",
    "site_integrity": '"$site_integrity"'
}'


# Result output: STDOUT or HTTP
if [ -z "$1" ]; then
  #  curl -X POST -H "Content-Type: application/json" -d "$JSON" "$3" as nocallback not exits
  curl -s -X POST -H 'Content-type: application/json' -d "$DATA" --insecure {!! callback_url('/api/callback/monitoring/server/'.hashid_encode($server->id).'/site/'.hashid_encode($site->id)) !!} >/dev/null
else
  echo "$DATA"
fi

#print current time to a new file with new line
echo "Callback sent at: $(date +%s)" >> /home/<USER>/.xcloud-monitoring/{{$site->id }}/{{$site->id}}.log
