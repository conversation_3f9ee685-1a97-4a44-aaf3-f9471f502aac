# Initialize the JSON array
siteStates="["

# Get information for each user
for user in $(awk -F: '$3 >= 1000 && $1 != "nobody" {print $1}' /etc/passwd); do
  # Find all directories owned by this user in /var/www folder
  directories=$(find /var/www -maxdepth 1 -type d -user "$user")

  # If no directories are found, continue to the next user
  if [ -z "$directories" ]; then
    continue
  fi

  # Iterate over each directory using process substitution to avoid subshell
  while read directory; do
    if [ -z "$directory" ]; then
      continue
    fi
    siteName=$(basename "$directory")

    # Get process IDs (PIDs) for processes owned by the user
    # Disabling exit on error, as if no process is running for a user, it will throw an error
    set +e
    pids=$(ps -u "$user" -o pid=)
    # Enabling exit on error
    set -e

    # Initialize variables to store total memory and CPU usage
    total_memory=0
    total_cpu=0

    # Iterate through each PID and accumulate memory and CPU usage
    for pid in $pids; do
      # Get memory usage for the process (in KB)
      memory=$(ps -p "$pid" -o rss=)

      # Get CPU usage for the process (as a percentage)
      cpu=$(ps -p "$pid" -o %cpu=)

      # Add memory and CPU usage to the totals
      total_memory=$((total_memory + memory))
      total_cpu=$(echo "$total_cpu + $cpu" | bc)
    done

    # Get disk usage for the directory
    disk=$(du -s "$directory" | cut -f1) # Disk usage in KB
    disk=$(awk "BEGIN {print $disk/1024}") # Convert to MB

    # Add the information for this user and site to the JSON array
    siteStates="$siteStates{\"site_user\":\"$user\",\"cpu\":\"$total_cpu\",\"ram\":\"$total_memory\",\"disk\":\"$disk\",\"site_name\":\"$siteName\"},"
  done < <(echo "$directories") # Process substitution
done

# Remove the last comma and close the JSON array
siteStatesJson="${siteStates%,}]"
