# Setup PM2 to start on system boot for the site user
echo "Setting up PM2 to start on system boot for user {{$site->site_user}}"

# Run pm2 startup as the site user to get the proper command
STARTUP_CMD=$(sudo -i -u {{$site->site_user}} bash -c "pm2 startup | grep sudo")

if [ -n "$STARTUP_CMD" ]; then
    echo "Executing PM2 startup command: $STARTUP_CMD"
    # Execute the startup command as root
    eval $STARTUP_CMD

    # Save the PM2 process list again as the site user
    sudo -i -u {{$site->site_user}} bash -c "pm2 save"

    # Enable and start the PM2 service for this user
    if systemctl list-unit-files | grep -q "pm2-{{$site->site_user}}"; then
        echo "Enabling and starting pm2-{{$site->site_user}} service"
        systemctl enable pm2-{{$site->site_user}}
        systemctl start pm2-{{$site->site_user}}
        systemctl status pm2-{{$site->site_user}}
    else
        echo "PM2 service for user {{$site->site_user}} not found"
        systemctl status pm2-root
    fi
else
    echo "Failed to get PM2 startup command for user {{$site->site_user}}"
    # Fallback to root PM2 service
    pm2 startup
    systemctl status pm2-root
fi
