@include('scripts.apt.apt-wait')

# Updated repository URL for OpenLiteSpeed
# https://docs.openlitespeed.org/installation/repo/
wget -O - https://repo.litespeed.sh | sudo bash

apt-get install openlitespeed -y

# Generate a new private key
openssl genrsa -out /usr/local/lsws/conf/cert/server.key 2048

# Generate a self-signed certificate
openssl req -new -x509 -key /usr/local/lsws/conf/cert/server.key -out /usr/local/lsws/conf/cert/server.crt -days 365 -subj "/C=US/ST=YourState/L=YourCity/O=YourOrganization/OU=YourDepartment/CN=localhost"

mkdir -p /usr/local/lsws/conf/vhosts/default
mkdir -p /etc/lsws
mkdir -p /var/log/lsws/
mkdir -p /root/.wp-cli/
mkdir -p /var/www/default/

cat > /var/www/default/index.html << 'EOF'
<h1>Welcome to OpenLiteSpeed!</h1>

EOF

chown -R xcloud:xcloud /var/log/lsws/
chown -R xcloud:xcloud /var/www/default

# Remove isolated group access to the default site
setfacl -m g:isolated:000 /var/www/default

# Disable the web console
touch /usr/local/lsws/conf/disablewebconsole

cat > /root/.wp-cli/config.yml << 'EOF'
apache_modules:
  - mod_rewrite
EOF

cat > /usr/local/lsws/conf/httpd_config.conf << 'XCLOUD_HTTPD_CONFIG_EOF'
{!! file_get_contents(resource_path('views/scripts/openlitespeed/xcloud_httpd_config.conf')) !!}
XCLOUD_HTTPD_CONFIG_EOF

# Replace lsphp74/bin/lsphp with server php
sed -i 's|lsphp74/bin/lsphp|lsphp{{ $server->ls_php_version }}/bin/lsphp|g' /usr/local/lsws/conf/httpd_config.conf

cat > /usr/local/lsws/conf/vhosts/default/vhconf.conf << 'EOF'
docRoot                   $VH_ROOT
vhDomain                  $VH_NAME
adminEmails               <EMAIL>
enableGzip                1
enableIpGeo               1

index  {
  useServer               0
  indexFiles index.html, index.php
}

module cache {
  ls_enabled              0
}
EOF

@include('scripts.openlitespeed.installHtaccessWatcher')

@include('scripts.lsphp.addLswsPathIntoEnv')
