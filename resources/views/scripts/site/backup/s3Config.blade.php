# Configure Backblaze B2 environment variables
export AWS_ACCESS_KEY_ID="{{$access_key_id}}"
export AWS_SECRET_ACCESS_KEY="{{$secret_key}}"
export AWS_DEFAULT_REGION="{{ $region }}"
export AWS_ENDPOINT_URL="{{ $endpoint }}"

# Backblaze B2 requires path-style addressing
export AWS_S3_USE_PATH_STYLE="true"
export AWS_S3_ADDRESSING_STYLE="path"

# Additional optimizations
export AWS_CLI_AUTO_PROMPT="off"
export AWS_RETRY_MODE="adaptive"
export AWS_MAX_ATTEMPTS="5"
export AWS_REQUEST_CHECKSUM_CALCULATION="when_required"
export AWS_RESPONSE_CHECKSUM_VALIDATION="when_required"
export PASSPHRASE="{{ $profile }}"
