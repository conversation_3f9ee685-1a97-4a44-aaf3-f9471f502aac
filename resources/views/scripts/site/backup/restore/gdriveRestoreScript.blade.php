#making backup directory
mkdir -p {{$site->backupFilesPath()}}/backup-remote/{{$site->backupDirName()}}
mkdir -p {{$site->backupFilesPath()}}/old

BACKUP_PATH={{$site->backupFilesPath()}}
DATETIME=$(date +"%Y%m%d%H%M%S")
log_file="$BACKUP_PATH/backup.log"
CALLBACK_URL="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
@if($file)
FILE_FULL_PATH={{$site->backupFileFullPath(path: $site->backupFilesPath(),file: $file,is_local: false)}}
@endif
@if($sqlFile)
SQL_FILE_FULL_PATH={{$site->backupFileFullPath(path: $site->backupFilesPath(),file: $sqlFile,is_local: false)}}
@endif
# Logging function
log_message() {
    echo "$(date +"%Y-%m-%d %H:%M:%S") $1" >> $log_file
}

# Send error callback
send_error_callback() {
    local error_message=$1
    curl -s -X POST -H 'Content-type: application/json' -d '{"error": "'"$error_message"'"}' --insecure "$CALLBACK_URL" >/dev/null
}

# Fetch a new access token
refresh_access_token() {
    local response
    response=$(curl -s -X POST -H "Content-Type: application/json" --insecure "{{ callback_url('api/google-drive/refresh-token/' . hashid_encode($storageProvider->id)) }}")
    if [ -z "$response" ]; then
        log_message "Failed to fetch access token."
        send_error_callback "Access token not found"
        exit 1
    fi

    echo "$response" | awk -F'"' '{print $4}'
}



#download file from google drive
download_file_from_gdrive() {
    local file_id=$1
    local target_path=$2
    local access_token=$3
    log_message "Downloading file from Google Drive"
    curl -s -X GET -H "Authorization: Bearer $access_token" "https://www.googleapis.com/drive/v3/files/$file_id?alt=media" > $target_path
}

required_space_in_mb={{$required_size}}
available_space_in_server=$(df -m / | tail -1 | awk '{print $4}')

#check required_space_in_mb is less than available_space_in_server or not
if [ $required_space_in_mb -gt $available_space_in_server ]; then
    send_error_callback "Not enough space to restore backup"
    exit 1
fi

# Fetch a new access token
ACCESS_TOKEN=$(refresh_access_token $CALLBACK_URL)

@if($fileID)
log_message "Downloading backup files from Google Drive"
download_file_from_gdrive {{$fileID}} $FILE_FULL_PATH $ACCESS_TOKEN
if [ $? -ne 0 ]; then
    send_error_callback "Backup file download failed"
    exit 1
fi
@endif
@if($sqlFileID)
download_file_from_gdrive {{$sqlFileID}} $SQL_FILE_FULL_PATH $ACCESS_TOKEN
if [ $? -ne 0 ]; then
    send_error_callback "Database file download failed and deleted backup file"
    rm -f $FILE_FULL_PATH
    exit 1
fi
@endif

@includeWhen($sqlFile,'scripts.site.backup.restore.restoreDatabase',[
    'server' => $server,
    'site' => $site,
    'full_path' => $site->backupFileFullPath(path: $site->backupFilesPath($backupDomain ?? null),file: $sqlFile, is_local: false),
    'should_delete' => true,
    'old_path' => $site->backupFilesPath().'/old'
])

@if($file)
    log_message "Extracting backup files"
    mv /var/www/{{ $site->name}} {{$site->backupFilesPath()}}/old/{{ $site->name}}_$DATETIME
    mkdir -p /var/www/{{ $site->name}}
    chown -R {{$site->site_user}}:{{$site->site_user}} /var/www/{{ $site->name}}
    @include('scripts.site.backup.restore.restoreFiles',[
        'site' => $site,
        'full_path' => $site->backupFileFullPath(path: $site->backupFilesPath($backupDomain),file: $file, is_local: false),
        'should_delete' => true,
        'backupDomain' =>$backupDomain
    ])
    rm -rf {{$site->backupFilesPath()}}/old
@endif

log_message "Backup restored successfully"

@includeWhen($site->isWordpress() && $site->hasFullPageCaching(), 'scripts.site.cache.purgeObjectCache', ['site' => $site])
@includeWhen($site->isWordpress() && $site->hasRedisObjectCaching(), 'scripts.site.cache.purgeRedisObjectCache', ['site' => $site,'server' => $server])
@includeWhen($site->isLaravel(), 'scripts.laravel.postBackupRestore', ['site' => $site,'server' => $server, 'file' => $file])

#send restore time to monitoring
url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
curl -s -X POST -H 'Content-type: application/json' -d '{"restore_time": "'$datetime'"}' --insecure "$url" >/dev/null

