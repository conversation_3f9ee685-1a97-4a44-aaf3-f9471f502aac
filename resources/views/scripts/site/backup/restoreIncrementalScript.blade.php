#making backup directory
mkdir -p {{$site->incrementalPath()}}/backup-remote/{{$site->backupDirName()}}
mkdir -p {{$site->incrementalPath()}}/old
#server time
datetime=$(date +"%Y%m%d%H%M%S")

log_file={{$site->incrementalPath()}}/backup.log
@if(!$is_local)
#check aws cli configuration is invalid
@include('scripts.site.backup.s3Config',[
    'profile' => $site->backupDirName(),
    'access_key_id' => $storageProvider->access_key_id,
    'secret_key' => $storageProvider->secret_key,
    'region' => $storageProvider->region,
    'endpoint' => $storageProvider->getEndPoint()
])

/usr/local/bin/aws configure set profile.{{ $site->backupDirName() }}.s3.signature_version s3
/usr/local/bin/aws configure set profile.{{ $site->backupDirName() }}.region {{ $storageProvider->region }}
/usr/local/bin/aws configure set profile.{{ $site->backupDirName() }}.endpoint_url {{ $storageProvider->getEndPoint() }}
/usr/local/bin/aws configure set profile.{{ $site->backupDirName() }}.response_checksum_validation when_required
/usr/local/bin/aws configure set profile.{{ $site->backupDirName() }}.request_checksum_calculation when_required
/usr/local/bin/aws configure set aws_access_key_id {{ $storageProvider->access_key_id }} --profile {{ $site->backupDirName() }}
/usr/local/bin/aws configure set aws_secret_access_key {{ $storageProvider->secret_key }} --profile {{ $site->backupDirName() }}
#Those are the environment variables that are used by the AWS CLI to authenticate with AWS S3
export AWS_ACCESS_KEY_ID={{ $storageProvider->access_key_id }}
export AWS_SECRET_ACCESS_KEY={{ $storageProvider->secret_key }}
export AWS_DEFAULT_REGION={{ $storageProvider->region }}
export PASSPHRASE="{{ hashid_encode($server->id) }}"
export AWS_REQUEST_CHECKSUM_CALCULATION=when_required
export AWS_RESPONSE_CHECKSUM_VALIDATION=when_required

# Get the installed Duplicity version
duplicity_version=$(duplicity --version | awk '{print $2}')

# Compare the Duplicity version directly with 2.1.4
if dpkg --compare-versions "$duplicity_version" ge "0.9"; then
# If version is higher than 0.9, execute this endpoint
endpoint="{{ $storageProvider->getEndPointWithS3Flag().DIRECTORY_SEPARATOR.$site->backupDirName() }}"
else
# Otherwise, execute this endpoint
endpoint="{{ $storageProvider->getS3EndPoint().DIRECTORY_SEPARATOR.$site->backupDirName() }}"
fi
@endif
@if($file && $restoreTime)
    #move current site to old directory
    mv /var/www/{{$site->name}} {{$site->incrementalPath()}}/old/{{$site->name}}_${datetime}
    duplicity restore -t {{$restoreTime}} --no-encryption @if($is_local) {{ str($filePath)->remove('~/')->prepend('file:///root/') }} @else $endpoint @endif /var/www/{{$site->name}}
    if [ $? -ne 0 ]; then
        #move old site back to original directory
        mv {{$site->incrementalPath()}}/old/{{$site->name}}_${datetime} /var/www/{{$site->name}}
        echo "Backup Restore failed" >> {{$site->incrementalPath()}}/backup.log
        chown -R {{$site->site_user}}:{{$site->site_user}} /var/www/{{$site->name}}
        #send call monitoring callback
        url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
        curl -s -X POST -H 'Content-type: application/json' -d '{"error": "Duplicity failed to restore files"}' --insecure "$url" >/dev/null
        echo "Backup Restore failed and old site restored"
        exit 1
    fi
    chown -R {{$site->site_user}}:{{$site->site_user}} /var/www/{{$site->name}}
    echo "Backup Restore completed" >> {{$site->incrementalPath()}}/backup.log
    rm -rf {{$site->incrementalPath()}}/old/{{$site->name}}_${datetime}
@endif
@if($sqlFile)
    @if(!$is_local)
        /usr/local/bin/aws s3api get-object --bucket {{ $storageProvider->bucket }} --key {{$site->backupDirName()}}/{{$sqlFile}} {{$site->incrementalPath()}}/backup-remote/{{$sqlFile}}  --profile {{ $site->backupDirName() }} --endpoint-url {{ $storageProvider->getEndPoint() }}
        if [ $? -ne 0 ]; then
            url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
            curl -s -X POST -H 'Content-type: application/json' -d '{"error": "Backup Database download failed"}' --insecure "$url" >/dev/null
            echo "Database download failed"
            exit 1
        fi
        @include('scripts.site.backup.restore.restoreDatabase',[
            'server' => $server,
            'site' => $site,
            'full_path' => $site->incrementalPath().'/backup-remote/'.$sqlFile,
            'old_path' => $site->incrementalPath().'/old',
            'should_delete' => !$is_local
        ])
    @else
        @include('scripts.site.backup.restore.restoreDatabase',[
           'server' => $server,
           'site' => $site,
           'full_path' => $filePath.DIRECTORY_SEPARATOR.$sqlFile,
           'old_path' => $site->incrementalPath().'/old',
           'should_delete' => !$is_local
       ])
    @endif
@endif

#remove  adminer*.php file from the site if exists
if [ -f /var/www/{{ $site->name }}/adminer*.php ]; then
    echo "Remove adminer*.php file from the site"
    rm -f /var/www/{{ $site->name }}/adminer*.php
fi
#remove  file-manager*.php file from the site if exists
if [ -f /var/www/{{ $site->name }}/file-manager-*.php ]; then
    echo "Remove file-manager*.php file from the site"
    rm -f /var/www/{{ $site->name }}/file-manager-*.php
fi

@if($site->isWordpress())
sudo -i -u {{ $site->site_user }} bash << 'UPDATE_URL'
# cd to the directory
cd /var/www/{{ $site->name }}
oldDomain={{ $site->wp_cli }} option get siteurl --skip-plugins --skip-themes | sed 's/^http[s]\?:\/\///'
if [ "$oldDomain" != "{{$site->name}}" ]; then
    {{ $site->wp_cli }} search-replace "$oldDomain" {{ $site->name }} --all-tables-with-prefix --verbose --skip-plugins --skip-themes > /dev/null 2>&1 || true
    {{ $site->wp_cli }} option update HOME "{{$site->site_url}}" --skip-plugins --skip-themes > /dev/null 2>&1
    {{ $site->wp_cli }} option update SITEURL "{{$site->site_url}}" --skip-plugins --skip-themes > /dev/null 2>&1

    {{ $site->wp_cli }} config set WP_HOME "{{$site->site_url}}" --skip-plugins --skip-themes > /dev/null 2>&1
    {{ $site->wp_cli }} config set WP_SITEURL "{{$site->site_url}}" --skip-plugins --skip-themes > /dev/null 2>&1
    @if($site->isMultiSite())
        {{ $site->wp_cli }} config set DOMAIN_CURRENT_SITE "{{$site->site_url}}" --skip-plugins --skip-themes > /dev/null 2>&1
    @endif

    @if($server->stack->isNginx())
    if {{ $site->wp_cli }} config --skip-plugins --skip-themes has WP_REDIS_PREFIX; then
    {{ $site->wp_cli }} config set WP_REDIS_PREFIX '{{ $site->redis_object_cache_key }}' --skip-plugins --skip-themes > /dev/null 2>&1 || echo "The redis prefix could not be set. Continuing..."
    fi
    @endif
    @if($server->stack->isOpenLiteSpeed())
    if {{ $site->wp_cli }} config --skip-plugins --skip-themes has LSOC_PREFIX; then
    {{ $site->wp_cli }} config set LSOC_PREFIX '{{ $site->redis_object_cache_key }}' --skip-plugins --skip-themes > /dev/null 2>&1 || echo "The redis prefix could not be set. Continuing..."
    fi
    @endif
fi
UPDATE_URL
if [ -f /var/www/{{$site->name}}/wp-content/object-cache.php ]; then
    @include('scripts.site.cache.installRedisObjectCache', ['site' => $site])
fi
@includeWhen($site->hasFullPageCaching(), 'scripts.site.cache.purgeObjectCache', ['site' => $site])
@includeWhen($site->hasRedisObjectCaching(), 'scripts.site.cache.purgeRedisObjectCache', ['site' => $site,'server' => $server])
@endif

@includeWhen($site->isLaravel(), 'scripts.laravel.postBackupRestore', ['site' => $site,'server' => $server, 'file' => $file])
