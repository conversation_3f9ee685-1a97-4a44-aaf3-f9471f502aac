# pCloud API Configuration and Helper Functions

# Get pCloud access token (pCloud uses long-lived tokens, no refresh needed)
get_pcloud_access_token() {
    # pCloud access tokens are long-lived and stored directly
    echo "{{$backupSetting->storageProvider->access_key_id}}"
}

# Check pCloud storage space
check_pcloud_space() {
    #$1 is the access token
    #$2 is the required space in bytes

    local storage_info quota used_quota available_space

    storage_info=$(curl -s -X GET -H "Authorization: Bearer $1" "https://api.pcloud.com/userinfo")

    # Check if the request was successful
    if [ "$(echo "$storage_info" | grep -o '"result": *[0-9]*' | sed -E 's/"result": *([0-9]*)/\1/')" != "0" ]; then
        log_message "Failed to get pCloud storage info: $storage_info"
        return 1
    fi

    quota=$(echo "$storage_info" | grep -o '"quota": *[0-9]*' | sed -E 's/"quota": *([0-9]*)/\1/')
    used_quota=$(echo "$storage_info" | grep -o '"usedquota": *[0-9]*' | sed -E 's/"usedquota": *([0-9]*)/\1/')
    available_space=$((quota - used_quota))

    log_message "pCloud available space: $available_space bytes"
    log_message "Required space: $2 bytes"

    if [ "$available_space" -lt "$2" ]; then
        log_message "pCloud has insufficient space"
        send_error_callback "pCloud has insufficient space"
        exit 1
    fi
}

# Upload file to pCloud
upload_to_pcloud() {
    #$1 file path
    #$2 access token
    #$3 folder id
    #$4 file type (for logging)

    local file_path="$1"
    local access_token="$2"
    local folder_id="$3"
    local file_type="$4"
    local file_name upload_response

    file_name=$(basename "$file_path")

    log_message "Starting upload of $file_type: $file_name to folder ID: $folder_id"
    log_message "Upload parameters - File: $file_path, Token: [HIDDEN], Folder ID: $folder_id"


    # Upload file directly to pCloud API
    upload_response=$(curl -s -X POST \
        -H "Authorization: Bearer $access_token" \
        -F "filename=$file_name" \
        -F "folderid=$folder_id" \
        -F "file=@$file_path" \
        "https://api.pcloud.com/uploadfile")

    local body=${upload_response%HTTPSTATUS:*}
    local status=${upload_response##*HTTPSTATUS:}

    log_message "Upload HTTP response code: $response"

    if [ "$status" -ne 200 ]; then
        log_message "Upload failed with response: $body"
        send_error_callback "Failed to upload $file_type to pCloud (Output $body)"
        return 1
    else
        log_message "Successfully uploaded $file_type: $file_name"
        echo "{\"status\": $status, \"body\": $body}"
        return 0
    fi

}
