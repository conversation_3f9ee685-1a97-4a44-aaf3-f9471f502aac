#!/bin/bash

# Constants
BACKUP_PATH={{$site->backupFilesPath()}}
LOG_FILE="$BACKUP_PATH/backup.log"
DATETIME=$(date +"%Y%m%d%H%M%S")
CALLBACK_URL="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
# Folder ID
FOLDER_ID="{{$folderId}}"

# Logging function
log_message() {
    echo "$(date +"%Y-%m-%d %H:%M:%S") $1" >> $LOG_FILE
}

@include('scripts.site.backup.pcloud.pcloudConfig',[
    'backupSetting' => $backupSetting
])

# Send error callback
send_error_callback() {
    local error_message=$1
    url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
    curl -s -X POST -H 'Content-type: application/json' -d '{"storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "server_datetime": "'"$DATETIME"'", "backup_settings_id": "'{{$backupSetting->id}}'", "error": "'"$error_message"'"}' --insecure "$url" >/dev/null
    # Remove the backup files if the backup fails
    rm -rf "$FULL_FILE_PATH" "$FULL_SQL_FILE_PATH"
}

# Backup Database
backup_database() {
    # $1 is the full path of the sql file
    log_message "Database backup started"
    mysqldump -u root -p{{ $server->database_password }} --single-transaction --skip-lock-tables {{ $site->database_name }} > $1 2>/dev/null
    if [ $? -ne 0 ]; then
        log_message "Database backup failed"
        send_error_callback "Database backup failed"
        exit 1
    fi
}

# Backup Site Files
backup_files() {
    # $1 is the full path of the tar file
    log_message "Site backup started"
    tar -czvf $1 -C /var/www --exclude={{$site->name}}/adminer*.php --exclude={{$site->name}}/file-manager-*.php --warning=no-file-changed @foreach($site->backupExcludesPaths($backupSetting) as $excludesPath) --exclude='{{$site->name}}/{{$excludesPath}}'  @endforeach {{$site->name}} > /dev/null
    if [ ! -f $1 ]; then
        log_message "tar file not created"
        send_error_callback "Site backup failed"
        exit 1
    fi
}

# Main script
# Ensure backup directory and log file exist before any logging
mkdir -p $BACKUP_PATH
touch $LOG_FILE
log_message "pCloud backup started"

@include('scripts.site.backup.checkStorageForBackup', [ 'backupSetting' => $backupSetting, 'site' => $site, 'server' => $server])

dir_size=$(($(du -s @foreach($site->backupExcludesPaths($backupSetting) as $excludesPath) --exclude='/var/www/{{$site->name}}/{{$excludesPath}}'  @endforeach /var/www/{{ $site->name }} | awk '{print $1}') * 1024 / 1000000))
FULL_SQL_FILE_PATH="$BACKUP_PATH/{{$site->name}}_s_${DATABASE_SIZE}_${DATETIME}.sql"
FULL_FILE_PATH="$BACKUP_PATH/{{$site->name}}_s_${dir_size}_${DATETIME}.tar.gz"

# Get pCloud access token
ACCESS_TOKEN=$(get_pcloud_access_token)
if [ -z "$ACCESS_TOKEN" ]; then
    send_error_callback "Failed to get pCloud access token."
    exit 1
fi

@if($backupSetting->files)
backup_files $FULL_FILE_PATH
@endif

@if($backupSetting->database)
backup_database $FULL_SQL_FILE_PATH
@endif

# Check if the files are created successfully
@if($backupSetting->files && $backupSetting->database)
if [ ! -f "$FULL_FILE_PATH" ] && [ ! -f "$FULL_SQL_FILE_PATH" ]; then
    log_message "Backup failed - no files created"
    send_error_callback "Backup failed"
    rm -rf "$FULL_FILE_PATH" "$FULL_SQL_FILE_PATH"
    exit 1
fi
@elseif($backupSetting->files)
if [ ! -f "$FULL_FILE_PATH" ]; then
    log_message "File backup failed - tar file not created"
    send_error_callback "File backup failed"
    rm -rf "$FULL_FILE_PATH"
    exit 1
fi
@elseif($backupSetting->database)
if [ ! -f "$FULL_SQL_FILE_PATH" ]; then
    log_message "Database backup failed - sql file not created"
    send_error_callback "Database backup failed"
    rm -rf "$FULL_SQL_FILE_PATH"
    exit 1
fi
@endif

# Get the size of each file in bytes
file_size=0
sql_file_size=0

if [ -f "$FULL_FILE_PATH" ]; then
    file_size=$(stat -c %s "$FULL_FILE_PATH")
fi

if [ -f "$FULL_SQL_FILE_PATH" ]; then
    sql_file_size=$(stat -c %s "$FULL_SQL_FILE_PATH")
fi

# Sum the sizes
total_size=$((file_size + sql_file_size))

# Check pCloud storage space
check_pcloud_space $ACCESS_TOKEN $total_size

# Upload files to pCloud
@if($backupSetting->files)
if [ -f "$FULL_FILE_PATH" ]; then
    upload_to_pcloud "$FULL_FILE_PATH" "$ACCESS_TOKEN" "$FOLDER_ID" 'File'
    rm -rf "$FULL_FILE_PATH"
fi
@endif

@if($backupSetting->database)
if [ -f "$FULL_SQL_FILE_PATH" ]; then
upload_result=$(upload_to_pcloud "$FULL_SQL_FILE_PATH" "$ACCESS_TOKEN" "$FOLDER_ID" 'Database')
    rm -rf "$FULL_SQL_FILE_PATH"
fi
@endif

# Convert file sizes to KB for callback
file_size=$(($file_size / 1024))
sql_file_size=$(($sql_file_size / 1024))

file_name=$(basename "$FULL_FILE_PATH")
database_name=$(basename "$FULL_SQL_FILE_PATH")

# Send callback with data
DATA='{"file": "'"$FOLDER_ID"'", "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'",  "file_size": "'"$file_size"'","taken_size": "'"$dir_size"'", "server_datetime": "'"$DATETIME"'", "db_file_size": "'"$sql_file_size"'", "is_remote": "true", "file_name" : "'"$file_name"'", "database": "'"$FOLDER_ID"'", "database_name" :  "'"$database_name"'"}'

user_id=${1:-0}  # Set user_id to $1 if provided, otherwise default to 0.
# Check if user_id is not 0
if [ $user_id -ne 0 ]; then
    DATA="${DATA%?}, \"user_id\": \"$user_id\"}"
fi

url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
curl -s -X POST -H 'Content-type: application/json' -d "$DATA" --insecure "$url" >/dev/null

log_message "pCloud backup completed"
exit 0
