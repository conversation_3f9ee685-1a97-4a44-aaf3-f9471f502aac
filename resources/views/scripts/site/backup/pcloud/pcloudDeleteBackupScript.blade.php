cat > {{$site->backupScriptsPath()}}/deleteBackup_remote.sh << 'SITE_BACKUP'
#!/bin/bash

# Define paths and constants
SCRIPT_DIR="{{ $site->backupScriptsPath() }}"
LOG_FILE="$SCRIPT_DIR/backup.log"
BACKUP_DIR_NAME="{{ $site->backupDirName() }}"
PARENT_DIR_ID="{{ $storageProvider->endpoint ?: 0 }}"
DELETE_AFTER_DAYS="{{ $backupSetting->delete_after_days }}"
CALLBACK_URL="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
DELETE_CALLBACK_URL="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/deleteBackup') }}"
API_URL="https://api.pcloud.com"

# Ensure log file exists
mkdir -p "$SCRIPT_DIR"
[ ! -f "$LOG_FILE" ] && touch "$LOG_FILE"

# Logging function
log_message() {
    echo "$(date +"%Y-%m-%d %H:%M:%S") $1" >> "$LOG_FILE"
}


# Delete files from pCloud
delete_pcloud_files() {
    local access_token="$1"
    local folder_id="$2"
    local delete_after_days="$3"
    local cutoff_date cutoff_timestamp response files_to_delete files_to_delete_names

    # Calculate cutoff timestamp - files older than this should be deleted
    # Use end of day for the cutoff date to ensure we don't delete files from the cutoff day itself
    cutoff_timestamp=$(date -u -d "-$delete_after_days days 23:59:59" +%s)
    cutoff_date=$(date -u -d "-$delete_after_days days 23:59:59" --iso-8601=seconds)

    log_message "Deleting files older than $cutoff_date (UTC)"

    response=$(curl -s -X GET -H "Authorization: Bearer $access_token" "${API_URL}/listfolder?folderid=$folder_id")

    # Check if API call was successful using jq
    if ! echo "$response" | jq -e '.result == 0' >/dev/null 2>&1; then
        log_message "Failed to list folder contents: $response"
        return 1
    fi

    # Test date parsing with cutoff timestamp
    cutoff_readable=$(date -u -d "@$cutoff_timestamp" '+%Y-%m-%d %H:%M:%S UTC' 2>/dev/null)
    log_message "Cutoff timestamp: $cutoff_timestamp ($cutoff_readable)"
    files_to_delete=""
    files_count=0
    echo "$response" | tr '\n' ' ' | sed 's/},[[:space:]]*{/\n/g' | grep '"fileid"' | while read -r block; do
        fileName=$(echo "$block" | grep -o '"name"\s*:\s*"[^"]*"' | sed 's/.*"name"\s*:\s*"\([^"]*\)".*/\1/')
        if [ "$fileName" = "{{ $site->backupDirName() }}" ]; then
            continue
        fi
        fileid=$(echo "$block" | egrep -o '"fileid"\s*:\s*[0-9]+' | grep -o '[0-9]\+')
        modified=$(echo "$block" | grep -o '"modified"\s*:\s*"[^"]*"' | sed 's/.*"modified"\s*:\s*"\([^"]*\)".*/\1/')
        if [ -n "$modified" ]; then
            modified_timestamp=$(date -u -d "$modified" +%s 2>/dev/null)
            if [ -z "$modified_timestamp" ]; then
                log_message "Failed to parse date for $modified"
            elif [ "$modified_timestamp" -lt "$cutoff_timestamp" ]; then
                files_to_delete="${files_to_delete}${fileid}|${fileName}\n"
                files_count=$((files_count + 1))
                echo  "File $fileName will be deleted (older than cutoff)"
            fi
        fi
    done

    if [ -z "$files_to_delete" ] || [ "$files_count" -eq 0 ]; then
        log_message "No files found older than $delete_after_days days (before $cutoff_date UTC)."
        return 0
    fi

    log_message "Files to delete: $files_count files"

    files_to_delete_names=""
    deleted_files_temp="/tmp/pcloud_deleted_files_$$"

    while IFS='|' read -r file_id file_name; do
        if [ -n "$file_id" ] && [ -n "$file_name" ]; then
            log_message "Deleting file: $file_name (ID: $file_id)"
            delete_response=$(curl -s -X POST -H "Authorization: Bearer $access_token" \
            "${API_URL}/deletefile" -d "fileid=$file_id")

            if ! echo "$delete_response" | jq -e '.result == 0' >/dev/null 2>&1; then
                log_message "Failed to delete file $file_name (ID: $file_id): $delete_response"
            else
                log_message "Successfully deleted file: $file_name"
                echo "$file_name" >> "$deleted_files_temp"
            fi
        fi
    done <<< "$(echo -e "$files_to_delete")"

    if [ -f "$deleted_files_temp" ] && [ -s "$deleted_files_temp" ]; then
        while IFS= read -r deleted_file; do
            if [ -z "$files_to_delete_names" ]; then
                files_to_delete_names="\"$deleted_file\""
            else
                files_to_delete_names="$files_to_delete_names, \"$deleted_file\""
            fi
        done < "$deleted_files_temp"

        rm -f "$deleted_files_temp"

        DATA="{\"files\": [$files_to_delete_names], \"backup_settings\": \"{{ $backupSetting->id }}\", \"storage_provider_id\": \"{{ $backupSetting->storage_provider_id }}\"}"
        curl -s -X POST -H "Content-Type: application/json" -d "$DATA" --insecure "$DELETE_CALLBACK_URL" >/dev/null
        log_message "Sent deletion callback for deleted files"
    else
        log_message "No files were successfully deleted"
        rm -f "$deleted_files_temp"
    fi
}

main() {
    ACCESS_TOKEN=$(get_pcloud_access_token)

    if [ -z "$ACCESS_TOKEN" ]; then
        log_message "Access token is empty."
        exit 1
    fi

    # Use PHP to get folder id (make sure your method returns a valid integer)
    FOLDER_ID={{ $storageProvider->getPcloudFolderId($site->backupDirName(), $storageProvider->endpoint) }}

    if [ -z "$FOLDER_ID" ] || [ "$FOLDER_ID" == "0" ]; then
        log_message "Backup folder not found."
        exit 0
    fi

    delete_pcloud_files "$ACCESS_TOKEN" "$FOLDER_ID" "$DELETE_AFTER_DAYS"
}

main

SITE_BACKUP

chmod +x {{$site->backupScriptsPath()}}/deleteBackup_remote.sh
