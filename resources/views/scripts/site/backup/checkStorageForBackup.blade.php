
## step 1: Check how much storage needs in tar file
@if($backupSetting->files)
    STORAGE_IN_USE=$(($(du -s  @foreach($site->backupExcludesPaths() as $excludesPath) --exclude=/var/www/{{$site->name}}/{{$excludesPath}}  @endforeach /var/www/{{ $site->name }} | awk '{print $1}') * 1024 / 1000000))
  #  echo "Storage in use: $STORAGE_IN_USE MB"
@endif
@if($backupSetting->database)
    DATABASE_SIZE=$(mysql -u {{$site->database_user}} -p{{ $site->database_password }} -e "SELECT table_schema AS DatabaseName, ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS SizeMB FROM information_schema.tables WHERE table_schema = '{{ $site->database_name }}' GROUP BY table_schema;" 2>/dev/null | awk '/{{ $site->database_name }}/ {print $2}')
@endif

## step 2: Check if the storage is enough for the backup
@if($backupSetting->files && $backupSetting->database)
   # echo "CHECKING STORAGE"
    FREE_STORAGE=$(echo "$(df -m /var/www | awk 'NR==2{print $4}') - $STORAGE_IN_USE - $DATABASE_SIZE" | bc)
@endif
@if($backupSetting->files && !$backupSetting->database)
    FREE_STORAGE=$(($(df -m /var/www | awk '{print $4}' | tail -n 1) - $STORAGE_IN_USE))
@endif
@if(!$backupSetting->files && $backupSetting->database)
    FREE_STORAGE=$(($(df -m /var/www | awk '{print $4}' | tail -n 1) - $DATABASE_SIZE))
@endif

if [ "$(echo "$FREE_STORAGE < {{\App\Models\BackupSetting::NEED_EXTERNAL_SPACE}}" | bc)" -eq 1 ]; then
    TOTAL_FREE_STORAGE=$(echo "$(df -m /var/www | awk 'NR==2{print $4}') / 1024" | bc)
    TOTAL_REQUIRED_STORAGE=$(echo "({{ \App\Models\BackupSetting::NEED_EXTERNAL_SPACE }} + $STORAGE_IN_USE + $DATABASE_SIZE) / 1024" | bc)
    ERROR_MESSAGE="Not enough space in the Server for backup. Required: $TOTAL_REQUIRED_STORAGE GB | Available: $TOTAL_FREE_STORAGE GB"
    url="{!! callback_url('/api/callback/monitoring/server/'.hashid_encode($server->id).'/site/'.hashid_encode($site->id)).'/backup' !!}"
    if [ -z "$datetime" ]; then
        datetime=$(date +"%Y%m%d%H%M%S")
    fi
    curl -s -X POST -H 'Content-type: application/json' -d '{"error": "'"$ERROR_MESSAGE"'", "server_datetime": "'"$datetime"'", "storage_in_use": "'$STORAGE_IN_USE'", "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "database_size": "'$DATABASE_SIZE'","free_storage": "'$FREE_STORAGE'"}' --insecure "$url" >/dev/null
    echo $ERROR_MESSAGE
    exit 1
fi

