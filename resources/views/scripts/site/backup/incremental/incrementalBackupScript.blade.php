#making backup directory
mkdir -p {{$site->incrementalPath()}}
#log file
touch {{$site->incrementalPath()}}/backup.log
#server time
datetime=$(date +"%Y%m%d%H%M%S")
@include('scripts.site.backup.checkStorageForBackup',[ 'backupSetting' => $backupSetting, 'site' => $site, 'server' => $server])
@if($isRemoteBackup)
@include('scripts.site.backup.installAWSCLI')
#check aws cli configuration is invalid
@include('scripts.site.backup.s3Config',[
    'profile' => $site->backupDirName(),
    'access_key_id' => $storageProvider->access_key_id,
    'secret_key' => $storageProvider->secret_key,
    'region' => $storageProvider->region,
    'endpoint' => $storageProvider->getEndPoint()
])

#check if s3 client work or not
/usr/local/bin/aws --profile {{ $site->backupDirName() }} s3 ls s3://{{ $storageProvider->bucket }}  --endpoint-url={{ $storageProvider->getEndPoint() }} >/dev/null
if [ $? -ne 0 ]; then
    url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
    curl -s -X POST -H 'Content-type: application/json' -d '{ "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "server_datetime": "'"$datetime"'", "error": "S3 client can not connect to s3 bucket"}' --insecure "$url" >/dev/null
    echo "S3 client not working" >> {{$site->incrementalPath()}}/backup.log
    echo { "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "error": "S3 client can not connect to s3 bucket"}
    exit 1
fi
@endif

@if($backupSetting->database)
    echo "Database backup started" >> {{$site->incrementalPath()}}/backup.log
    mysqldump --single-transaction --skip-lock-tables --quick -u root -p{{ $server->database_password }} {{ $site->database_name }} > {{$site->incrementalPath()}}/{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${DATABASE_SIZE}_${datetime}.sql 2>/dev/null
    if [ $? -ne 0 ]; then
    echo "Database backup failed" >> {{$site->incrementalPath()}}/backup.log
    url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
    curl -s -X POST -H 'Content-type: application/json' -d '{ "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "error": "Database backup failed"}' --insecure "$url" >/dev/null
    echo "Database backup failed" >> {{$site->incrementalPath()}}/backup.log
    echo { "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "error": "Database backup failed"}
    exit 1
    fi
    db_file_size=$(stat -c %s {{$site->incrementalPath()}}/{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${DATABASE_SIZE}_${datetime}.sql | awk '{print $1/1024}')
    echo "Database file name: {{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${DATABASE_SIZE}_${datetime}.sql" >> {{$site->incrementalPath()}}/backup.log
    echo "Database file size: $db_file_size" >> {{$site->incrementalPath()}}/backup.log
@endif

@if($backupSetting->files)
    dir_size=$(($(du -s @foreach($site->backupExcludesPaths($backupSetting) as $excludesPath) --exclude='/var/www/{{$site->name}}/{{$excludesPath}}'  @endforeach /var/www/{{ $site->name }} | awk '{print $1}') * 1024 / 1000000))

    #site backup
    echo "Site backup started" >> {{$site->incrementalPath()}}/backup.log
    tar -czvf {{$site->incrementalPath()}}/{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${dir_size}_${datetime}.tar.gz -C /var/www --exclude={{$site->name}}/adminer*.php --exclude={{$site->name}}/file-manager-*.php @foreach($site->backupExcludesPaths($backupSetting) as $excludesPath) --exclude='{{$site->name}}/{{$excludesPath}}'  @endforeach {{$site->name}} > /dev/null
    if [ ! -f {{$site->incrementalPath()}}/{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${dir_size}_${datetime}.tar.gz ]; then
        echo "tar file not created" >> {{$site->incrementalPath()}}/backup.log
        @if($backupSetting->database)
            rm -rf {{$site->incrementalPath()}}/{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${DATABASE_SIZE}_${datetime}.sql
        @endif
        url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"
        curl -s -X POST -H 'Content-type: application/json' -d '{ "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "error": "Site backup failed"}' --insecure "$url" >/dev/null
        echo { "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "error": "Site backup failed"}
        exit 1
    fi
    echo "Site backup completed" >> {{$site->incrementalPath()}}/backup.log
    #file size in KB
    file_size=$(stat -c %s {{$site->incrementalPath()}}/{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${dir_size}_${datetime}.tar.gz | awk '{print $1/1024}')
    echo "File name: {{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${dir_size}_${datetime}.tar.gz" >> {{$site->incrementalPath()}}/backup.log
    echo "File size: $file_size" >> {{$site->incrementalPath()}}/backup.log
@endif

@if($isRemoteBackup)
    @if($backupSetting->files)
    file_response=$(/usr/local/bin/aws s3 cp {{$site->incrementalPath()}}/{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${dir_size}_${datetime}.tar.gz s3://{{ $storageProvider->bucket }}/{{$site->backupDirName()}}/{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${dir_size}_${datetime}.tar.gz --metadata '{"SiteID": "{{$site->id}}","ServerID": "{{$site->server_id}}"}' --profile {{ $site->backupDirName() }} --endpoint-url {{ $storageProvider->getEndPoint() }})
    echo "File response: $file_response" >> {{$site->incrementalPath()}}/backup.log
    file_response=$(echo "$file_response" | grep -o '"ETag": ".*"' | sed 's/"ETag": "\(.*\)"/\1/' | tr -d '\\\"')
    @endif
    @if($backupSetting->database)
    sql_response=$(/usr/local/bin/aws s3 cp {{$site->backupFilesPath()}}/{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${DATABASE_SIZE}_${datetime}.sql s3://{{ $storageProvider->bucket }}/{{$site->backupDirName()}}/{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${DATABASE_SIZE}_${datetime}.sql --metadata '{"SiteID": "{{$site->id}}","ServerID": "{{$site->server_id}}"}' --profile {{ $site->backupDirName() }} --endpoint-url {{ $storageProvider->getEndPoint() }})
    sql_response=$(echo "$sql_response" | grep -o '"ETag": ".*"' | sed 's/"ETag": "\(.*\)"/\1/' | tr -d '\\\"')
    @endif
    #delete local backup
    rm -rf {{$site->incrementalPath()}}/{{$site->name}}_s_${dir_size}_${datetime}.tar.gz
    rm -rf {{$site->incrementalPath()}}/{{$site->name}}_s_${DATABASE_SIZE}_${datetime}.sql
@endif
#send response with s3 file path with json format with condition
# Initialize an empty JSON object

@if($backupSetting->files && $backupSetting->database)
    DATA='{"file": @if($isRemoteBackup) "{{$site->backupDirName()}}" @else  "{{$site->incrementalPath()}}" @endif, "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'",  "file_size": "'"$file_size"'","taken_size": "'"$dir_size"'", "server_datetime": "'"$datetime"'", "db_file_size": "'"$db_file_size"'", "is_remote": @if($isRemoteBackup) "true" @else "false" @endif, "file_name" :  "'"{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${dir_size}_${datetime}.tar.gz"'", "database": @if($isRemoteBackup) "{{$site->backupDirName()}}" @else "{{$site->incrementalPath()}}" @endif, "database_name" :  "'"{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${DATABASE_SIZE}_${datetime}.sql"'"}'
@elseif($backupSetting->files)
    DATA='{"file": @if($isRemoteBackup) "{{$site->backupDirName()}}" @else "{{$site->incrementalPath()}}" @endif, "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "file_size": "'"$file_size"'", "taken_size": "'"$dir_size"'", "server_datetime": "'"$datetime"'", "is_remote": @if($isRemoteBackup) "true" @else "false" @endif, "file_name" :  "'"{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${dir_size}_${datetime}.tar.gz"'"}'
@elseif($backupSetting->database)
    DATA='{"database": @if($isRemoteBackup) "{{$site->backupDirName()}}" @else "{{$site->incrementalPath()}}" @endif, "storage_provider_id": "'{{$backupSetting->storage_provider_id}}'", "backup_settings_id": "'{{$backupSetting->id}}'", "db_file_size": "'"$db_file_size"'", "server_datetime": "'"$datetime"'", "is_remote": @if($isRemoteBackup) "true" @else "false" @endif, "database_name" :  "'"{{$isRemoteBackup ? $site->name : "{$site->name}_local"}}_s_${db_file_size}_${datetime}.sql"'"}'
@endif

user_id=${1:-0}  # Set user_id to $1 if provided, otherwise default to 0.
#check if user_id is not 0
if [ $user_id -ne 0 ]; then
    DATA="${DATA%?}, \"user_id\": \"$user_id\"}"
fi

# Construct the callback URL
url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/backup') }}"

# Send callback
curl -s -X POST -H 'Content-type: application/json' -d "$DATA" --insecure "$url" >/dev/null
echo '{"server_datetime": "'"$datetime"'",  "backup_settings_id": "'{{$backupSetting->id}}'"}'
exit 0
