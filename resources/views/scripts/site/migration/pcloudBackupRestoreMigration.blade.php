@include('scripts.wordpress.createDir', ['server' => $site->server, 'site' => $site])

CALLBACK_URL="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
FILE_FULL_PATH={{$site->backupFileFullPath(path: $site->backupFilesPath(),file: $migration->getNonSqlFileName())}}
SQL_FILE_FULL_PATH={{$site->backupFileFullPath(path: $site->backupFilesPath(),file: $migration->getSqlFileName())}}
log_file={{$site->restoreLogPath()}}/migration-log.log;
datetime=$(date '+%Y-%m-%d-%H-%M-%S')

# Setup Directories and Logs
mkdir -p {{ $site->restoreLogPath() }}
mkdir -p {{ dirname($site->backupFileFullPath(path: $site->backupFilesPath(),file: $migration->getNonSqlFileName())) }}
mkdir -p {{ dirname($site->backupFileFullPath(path: $site->backupFilesPath(),file: $migration->getSqlFileName())) }}

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$log_file"
}

log_message "Starting pCloud migration for site: {{ $site->name }}"

# Get pCloud access token (pCloud uses long-lived tokens, no refresh needed)
get_pcloud_access_token() {
    echo "{{$storageProvider->access_key_id}}"
}

# Download file from pCloud
download_file_from_pcloud() {
    local file_id=$1
    local target_path=$2
    local access_token=$3
    local API_URL="https://api.pcloud.com"

    log_message "Downloading file from pCloud (File ID: $file_id)"

    # Get download link
    local download_response
    download_response=$(curl -s -X GET \
        -H "Authorization: Bearer $access_token" \
        "${API_URL}/getfilelink?fileid=$file_id")

    # Check if the request was successful
    if [ "$(echo "$download_response" | grep -o '"result": *[0-9]*' | sed -E 's/"result": *([0-9]*)/\1/')" != "0" ]; then
        log_message "Failed to get download link: $download_response"
        exit 1
    fi

    # Extract download URL
    local host path download_url
    host=$(echo "$download_response" | grep -o '"hosts": *\[[^]]*\]' | sed -E 's/"hosts": *\["([^"]*)".*\]/\1/')
    path=$(echo "$download_response" | grep -o '"path": *"[^"]*"' | sed -E 's/"path": *"([^"]*)"/\1/')
    download_url="https://${host}${path}"

    log_message "Download URL: $download_url"

    # Create target directory if it doesn't exist
    mkdir -p "$(dirname "$target_path")"

    # Download the file
    local download_http_code
    download_http_code=$(curl -s -w "%{http_code}" -o "$target_path" "$download_url")

    log_message "Download HTTP response code: $download_http_code"

    # Check if download was successful
    if [ "$download_http_code" -ne 200 ] || [ ! -f "$target_path" ]; then
        log_message "Failed to download file to $target_path (HTTP: $download_http_code)"
        exit 1
    fi

    # Check if file has content
    local file_size
    file_size=$(stat -c%s "$target_path" 2>/dev/null || echo "0")
    if [ "$file_size" -eq 0 ]; then
        log_message "Downloaded file is empty: $target_path"
        exit 1
    fi

    log_message "Downloaded file size: $file_size bytes"

    log_message "Successfully downloaded file to $target_path"
}

# Get pCloud access token
ACCESS_TOKEN=$(get_pcloud_access_token)
if [ -z "$ACCESS_TOKEN" ]; then
    log_message "Failed to get pCloud access token."
    exit 1
fi

# Download backup files from pCloud
download_file_from_pcloud {{$fileID}} $FILE_FULL_PATH $ACCESS_TOKEN
if [ $? -ne 0 ] || [ ! -f "$FILE_FULL_PATH" ]; then
    log_message "Backup file download failed"
    exit 1
fi

download_file_from_pcloud {{$sqlFileID}} $SQL_FILE_FULL_PATH $ACCESS_TOKEN
if [ $? -ne 0 ] || [ ! -f "$SQL_FILE_FULL_PATH" ]; then
    log_message "Database file download failed"
    rm -f $FILE_FULL_PATH
    exit 1
fi

log_message "Both files downloaded successfully"

# Variables for migration
siteDir="/var/www/{{ $site->name }}"
backupDomain="{{ $domain }}"
remoteFilesPath="$FILE_FULL_PATH"
remoteSqlPath="$SQL_FILE_FULL_PATH"

log() {
    log_message "$1"
}

# Create site directory
mkdir -p {{ $siteDir }}

log "Extracting files to backup directory"
tar -xzvf {{ $remoteFilesPath }} -C {{ $siteDir }} > /dev/null
@if($site->name !== $domain)
    log "Moving files from {{ $domain }} to {{ $siteDir }}"
    cp -r {{ $siteDir }}/{{ $domain }}/* {{ $siteDir }}/
    rm -r {{ $siteDir }}/{{ $domain }}
@endif

#Check if wp-config.php exists
if [ ! -f {{ $siteDir }}/wp-config.php ]; then
log "wp-config.php not found. Exiting..."
exit 1
fi

# Clean up and Set Permissions
log "Removing object-cache.php and setting permissions"
rm -rf {{ $siteDir }}/wp-content/object-cache.php
chown -R {{ $site->site_user }}:{{ $site->site_user }} {{ $siteDir }}

# Database restoration
log "Starting database restoration"
mysql -u root -p{{ $server->database_password }} {{ $site->database_name }} < {{ $remoteSqlPath }}
if [ $? -ne 0 ]; then
    log "Database restoration failed"
    exit 1
fi

log "Database restored successfully"

# Update WordPress URLs and configurations
log "Updating WordPress configuration"

# Update site URL in database
mysql -u root -p{{ $server->database_password }} {{ $site->database_name }} -e "UPDATE wp_options SET option_value = 'https://{{ $site->primary_domain }}' WHERE option_name = 'home';"
mysql -u root -p{{ $server->database_password }} {{ $site->database_name }} -e "UPDATE wp_options SET option_value = 'https://{{ $site->primary_domain }}' WHERE option_name = 'siteurl';"

# Clean up downloaded files
log "Cleaning up downloaded files"
rm -f {{ $remoteFilesPath }}
rm -f {{ $remoteSqlPath }}

@includeWhen($site->isWordpress() && $site->hasFullPageCaching(), 'scripts.site.cache.purgeObjectCache', ['site' => $site])
@includeWhen($site->isWordpress() && $site->hasRedisObjectCaching(), 'scripts.site.cache.purgeRedisObjectCache', ['site' => $site,'server' => $server])

log "pCloud migration completed successfully for site: {{ $site->name }}"

# Send completion callback
url="{{ callback_url('/api/callback/monitoring/server/' . hashid_encode($server->id) . '/site/' . hashid_encode($site->id) . '/restore') }}"
curl -s -X POST -H 'Content-type: application/json' -d '{"restore_time": "'$datetime'"}' --insecure "$url" >/dev/null
