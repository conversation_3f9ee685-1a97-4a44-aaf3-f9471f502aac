@include('scripts.wordpress.createDir', ['server' => $site->server, 'site' => $site])

# Variables
@php
    $siteDir = "/var/www/{$site->name}";
    $logFile = "{$site->restoreLogPath()}/log.log";
    $sshKeySlug = $migration->getSshKeyPair()?->getSlug();
    $sourceSite = $migration->getSourceSite();
    $sourceIP = $sourceSite?->server->public_ip;
    $remotePort = $sourceSite?->port();
    $incremental = $migration->hasIncrementalFiles();
    $backupDomain = $migration->getSourceDomainName();
    $remoteSqlPath = $migration->backupFileFullPath(isSql: true);
    $remoteFilesPath = $migration->backupFileFullPath(isSql: false);
    $dbUser = $site->database_user;
    $dbName = $site->database_name;
    $dbPassword = $site->database_password;
@endphp

# Setup Directories and Logs
mkdir -p {{ $site->restoreLogPath() }}
mkdir -p {{ dirname($remoteSqlPath) }}
mkdir -p {{ dirname($remoteFilesPath) }}
log_file={{ $logFile }}

log() {
echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$log_file"
}

log "Starting migration for site: {{ $site->name }}"

# Clean up Site Directory
log "Cleaning up site directory: {{ $siteDir }}"
rm -rf {{ $siteDir }}

@if($migration->isBucket() || $migration->isSourceBackupTypeRemote())
    log "Configuring environment for remote backup restore"
    @include('scripts.site.migration.configureForRemoteEnv', [
        'backupDirName' => $migration->getBackupDirName(),
        'server' => $server,
        'storageProvider' => $storageProvider,
        'incremental' => $incremental
    ])
@endif

@if($migration->isOtherServerLocalBackup() && $sourceSite)
    @if($incremental)
        @include('scripts.site.backup.incremental.installDuplicity')
        export PASSPHRASE="{{ hashid_encode($sourceSite->server_id) }}"
        log "Restoring incremental backup using duplicity"
        duplicity restore -t {{ $migration->incrementalRestoreDateTime() }} --ssh-options="-p {{ $remotePort }} -i /root/.ssh/{{ $sshKeySlug }}" {{ "scp://root@$sourceIP" }}/{{ dirname(expandPath($remoteFilesPath)) }} {{ $siteDir }}
    @else
        log "Copying full backup file from remote server"
        rsync -avze "ssh -p {{ $remotePort }} -o StrictHostKeyChecking=no -i /root/.ssh/{{ $sshKeySlug }}" {{"root@$sourceIP" }}:{{ $remoteFilesPath }} {{ $remoteFilesPath }}
    @endif
    log "Copying SQL backup file from remote server"
    rsync -avze "ssh -p {{ $remotePort }} -o StrictHostKeyChecking=no -i /root/.ssh/{{ $sshKeySlug }}" {{ "root@$sourceIP" }}:{{ $remoteSqlPath }} {{ $remoteSqlPath }}

    log "Remove the private key file"
    rm -f /root/.ssh/{{ $sshKeySlug }}
@endif

@if(($migration->isBucket() || $migration->isSourceBackupTypeRemote()) && $incremental)
    log "Restoring incremental backup from bucket"
    duplicity restore -t {{ $migration->incrementalRestoreDateTime() }} --no-encryption $endpoint {{ $siteDir }}
    log "Endpoint of duplicity: $endpoint"
@elseif($sourceSite?->server_id === $site->server_id && $incremental)
    log "Restoring local incremental backup"
    duplicity restore -t {{ $migration->incrementalRestoreDateTime() }} --no-encryption {{ $migration->isSourceBackupTypeRemote() ? '$endpoint' : str($file->file_path)->remove('~/')->prepend('file:///root/') }} {{ $siteDir }}
@elseif(!$incremental)
    @if($migration->isSourceBackupTypeRemote() || $migration->isBucket())
        log "Downloading full backup from S3 bucket"
        /usr/local/bin/aws s3api get-object --bucket {{ $storageProvider->bucket }} --key {{ $migration->getNonSqlFileName() }} {{ $remoteFilesPath }} --profile {{ $migration->getBackupDirName() }} --endpoint-url {{ $storageProvider->endpoint }}
    @endif
    mkdir -p {{ $siteDir }}
    log "Extracting files to backup directory"
    tar -xzvf {{ $remoteFilesPath }} -C {{ $siteDir }} > /dev/null
    @if($site->name !== $backupDomain)
        log "Moving files from {{ $backupDomain }} to {{ $siteDir }}"
        cp -r {{ $siteDir }}/{{ $backupDomain }}/* {{ $siteDir }}/
        rm -r {{ $siteDir }}/{{ $backupDomain }}
    @endif
@endif
@if($incremental)
    log "Removing duplicity cache files"
    rm -rf /root/.cache/duplicity
@endif

#Check if wp-config.php exists
if [ ! -f {{ $siteDir }}/wp-config.php ]; then
log "wp-config.php not found. Exiting..."
exit 1
fi

# Clean up and Set Permissions
log "Removing object-cache.php and setting permissions"
rm -rf {{ $siteDir }}/wp-content/object-cache.php
chown -R {{ $site->site_user }}:{{ $site->site_user }} {{ $siteDir }}

@if($migration->isSourceBackupTypeRemote() || $migration->isBucket())
    log "Downloading database backup from S3 bucket"
    /usr/local/bin/aws s3api get-object --bucket {{ $storageProvider->bucket }} --key {{ $migration->getSqlFileName() }} {{ $remoteSqlPath }} --profile {{ $migration->getBackupDirName() }} --endpoint-url {{ $storageProvider->endpoint }}
@endif

# Database Creation and Restoration
log "Creating database {{ $dbName }}"
mysql -u root -p{{ $server->database_password }} -e "CREATE DATABASE IF NOT EXISTS {{ $dbName }}" 2>/dev/null

log "Restoring database from SQL backup"
mysql -u root -p{{ $server->database_password }} {{ $dbName }} < {{ $remoteSqlPath }} 2>/dev/null

#remove downloaded files
rm -f /root/.backup/{{$site->name}}
rm -f /root/.backup/{{$site->id}}

# Update wp-config.php with Database Credentials
log "Updating wp-config.php with new database credentials"
sudo -i -u {{ $site->site_user }} bash << 'WP_RESTORING'
{{ $site->wp_cli }} config set DB_NAME {{ $dbName }} --path={{ $siteDir }} --skip-plugins --skip-themes > /dev/null
{{ $site->wp_cli }} config set DB_USER {{ $dbUser }} --path={{ $siteDir }} --skip-plugins --skip-themes > /dev/null
{{ $site->wp_cli }} config set DB_PASSWORD {{ $dbPassword }} --path={{ $siteDir }} --skip-plugins --skip-themes > /dev/null
{{ $site->wp_cli }} config set WP_HOME {{ $site->site_url }} --path={{ $siteDir }} --skip-plugins --skip-themes > /dev/null
{{ $site->wp_cli }} config set WP_SITEURL {{ $site->site_url }} --path={{ $siteDir }} --skip-plugins --skip-themes > /dev/null
{{ $site->wp_cli }} config shuffle-salts --path={{ $siteDir }} --skip-plugins --skip-themes > /dev/null
WP_RESTORING

# Search and Replace URL
log "Executing search and replace on URL"
@includeWhen($backupDomain !== $site->name, 'scripts.site.searchReplaceUrl', [
    'site' => $site,
    'newDomain' => $site->name,
    'oldDomain' => $backupDomain,
    'new_url' => $site->site_url
])

log "Migration completed successfully for site: {{ $site->name }}"
