@include('scripts.site.backup.installAWSCLI')

#check aws cli configuration is invalid
@include('scripts.site.backup.s3Config',[
    'profile' => $backupDirName,
    'access_key_id' => $storageProvider->access_key_id,
    'secret_key' => $storageProvider->secret_key,
    'region' => $storageProvider->region,
    'endpoint' => $storageProvider->getEndPoint()
])

@if($incremental)
@include('scripts.site.backup.incremental.installDuplicity')
@if($storageProvider->isBackBlaze())
    #instll backblaze b2 client if storage provider is backblaze
    #check b2sdk is installed or not

    dpkg -l | grep python3-b2sdk >/dev/null
    if [ $? -ne 0 ]; then
    sudo apt-get install python3-b2sdk -y
    pip install b2sdk
    fi

@endif

# Check if boto3 is installed
python3 -c "import boto" 2>/dev/null
# Capture the exit code of the previous command
if [ $? -ne 0 ]; then
# Install boto3 using pip if not installed
pip3 install boto
fi

@if($storageProvider->isCloudflareR2())
    # Check if boto3 is installed
    python3 -c "import boto3" 2>/dev/null

    # Capture the exit code of the previous command
    if [ $? -ne 0 ]; then
    # Install boto3 using pip if not installed
    pip3 install boto3
    fi
@endif

#Those are the environment variables that are used by the AWS CLI to authenticate with AWS S3
export AWS_ACCESS_KEY_ID={{ $storageProvider->access_key_id }}
export AWS_SECRET_ACCESS_KEY={{ $storageProvider->secret_key }}
export AWS_DEFAULT_REGION={{ $storageProvider->region }}
export PASSPHRASE="{{ hashid_encode($server->id) }}"
# Get the installed Duplicity version
duplicity_version=$(duplicity --version | awk '{print $2}')

# Compare the Duplicity version directly with 2.1.4
if dpkg --compare-versions "$duplicity_version" ge "0.9"; then
# If version is higher than 0.9, execute this endpoint
endpoint="{{ $storageProvider->getEndPointWithS3Flag().DIRECTORY_SEPARATOR.$backupDirName }}"
else
# Otherwise, execute this endpoint
endpoint="{{ $storageProvider->getS3EndPoint().DIRECTORY_SEPARATOR.$backupDirName }}"
fi
@endif
