# Generate WP Rocket Nginx Configuration
#
# This script uses Rocket-Nginx from SatelliteWP (https://github.com/SatelliteWP/rocket-nginx)
# Rocket-Nginx is a Nginx configuration for the WP Rocket WordPress plugin
# that enables browser caching and server-side caching features.
#
# Rocket-Nginx is maintained by SatelliteWP and is designed to work with the
# WP Rocket WordPress plugin to provide optimal caching performance.
# It includes configurations for browser caching, GZIP compression, and
# various optimizations to improve website loading speed.
#
# The configuration is regularly updated to ensure compatibility with the
# latest versions of WP Rocket and Nginx.
#
# Features:
# - Browser caching for static assets (CSS, JS, images)
# - GZIP compression for faster content delivery
# - Mobile device detection for responsive caching
# - HTTPS support for secure sites
# - Exclusion rules for dynamic content
# - Compatible with WP Rocket's cache clearing mechanisms
#
# Credits: SatelliteWP (https://www.satellitewp.com)
# Original author: <PERSON>e <PERSON>in
# License: MIT

@if(!empty($uploadedFiles))
# Successfully uploaded the following files:
@foreach($uploadedFiles as $file)
# - {{ $file }}
@endforeach
@endif

@if(!empty($errors))
# Encountered the following errors:
@foreach($errors as $error)
# - {{ $error }}
@endforeach
@endif

# Configure and generate the Nginx configuration
cd /etc/nginx
cd rocket-nginx

# Copy the disabled configuration file to the active one
cp rocket-nginx.ini.disabled rocket-nginx.ini

# Run the parser to generate the configuration
php rocket-parser.php

# Check if the configuration was generated successfully
if [ -f conf.d/default.conf ]; then
    echo "WP Rocket Nginx configuration generated successfully."
    exit 0
else
    echo "Failed to generate WP Rocket Nginx configuration."
    exit 1
fi
EOF
