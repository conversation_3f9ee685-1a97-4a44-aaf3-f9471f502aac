@if($site->server->stack->isNginx())
    rm -rf /etc/nginx/cache/{{ $site->name }}
    mkdir -p /etc/nginx/cache/{{ $site->name }}
    chown -R {{ $site->site_user }}:xcloud /etc/nginx/cache/{{ $site->name }}
    chmod -R 770 /etc/nginx/cache/{{ $site->name }}
@endif

@if($site->server->stack->isOpenLiteSpeed())
sudo -i -u {{$site->site_user}} bash << 'PURGE_LITESPEED_CACHE'
cd /var/www/{{ $site->name }}
{{ $site->wp_cli }} litespeed-purge all
PURGE_LITESPEED_CACHE
@endif
