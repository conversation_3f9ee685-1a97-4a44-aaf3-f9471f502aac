@include('scripts.wordpress.createDir', ['server' => $site->server,'site' => $site])
@includeWhen($site->isLaravel() && $site->server->isRedisSeven(),'scripts.redis.addUser',['site'=>$site])

mv {{ $site->getRSAKeyTempPath() }} {{ $site->getRSAKeyPath() }}
chmod 600 {{ $site->getRSAKeyPath() }}
chown -R {{ $site->site_user }}:{{ $site->site_user }} /home/<USER>/.ssh

sudo -i -u {{ $site->site_user }} bash <<'GIT_CLONE_SCRIPT'

# check if $site->name is not empty, if empty exit the script with an error message
if [ -z "{{ $site->name }}" ]; then
    echo "Error: Domain name is missing, please provide a domain name for the site to install the site."
    exit 1
fi

# cd to the directory and check wordpress files are ok using wp core verify-checksums
cd /var/www/{{ $site->name }}

# clone repository
git clone -c core.sshCommand="ssh -i {{ $site->getRSAKeyPath() }} -o 'StrictHostKeyChecking=no'" -b {{ $gitInfo['git_branch'] }} {{ $gitInfo['git_repository'] }} .

git config --global --add safe.directory /var/www/{{ $site->name }}

@if($createConfig && $site->isWordpress())
if [ ! -f /var/www/{{ $site->name }}/wp-config.php ]; then
    # also check if wp-config-sample.php exists and if it does, copy it to wp-config.php
    if [ ! -f /var/www/{{ $site->name }}/wp-config-sample.php ]; then
        echo "wp-config-sample.php does not exist"
        exit 0
    fi
    cp wp-config-sample.php wp-config.php
    {{ $site->wp_cli }} config set DB_NAME {{ $site->database_name }}
    {{ $site->wp_cli }} config set DB_USER {{ $site->database_user }}
    {{ $site->wp_cli }} config set DB_PASSWORD {{ $site->database_password }}
    {{ $site->wp_cli }} config set WP_HOME {{ $site->site_url }}
    {{ $site->wp_cli }} config set WP_SITEURL {{ $site->site_url }}
    {{ $site->wp_cli }} config shuffle-salts
    git add wp-config.php
    git commit -m "Add wp-config.php"
fi
@endif

@includeWhen($site->isLaravel(), 'scripts.laravel.updateEnv', ['site' => $site, 'createConfig' => $createConfig])

GIT_CLONE_SCRIPT
