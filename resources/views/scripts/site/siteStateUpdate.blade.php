@includeWhen($site->hasFullPageCaching(), 'scripts.site.cache.purgeObjectCache', ['site' => $site])
@includeWhen($site->hasRedisObjectCaching(), 'scripts.site.cache.purgeRedisObjectCache', ['site' => $site])
@include('scripts.site.SiteSFTPSettings', ['site' => $site])

@includeWhen($server?->stack->isNginx(),'scripts.site.reGenerateNginxConfig', ['site' => $site])
@includeWhen($server?->stack->isOpenLiteSpeed(),'scripts.site.reGenerateOlsConfig', ['site' => $site])

@if($site->isDisable())
mkdir -p /var/www/html/errors

cat > /var/www/html/errors/503_{{$site->id}}_error.html << 'EOF_{{$eof}}'
{!! $data['disable_html'] !!}
EOF_{{$eof}}

chmod -R 755 /var/www/html/errors

    @if(!empty($data['disable_site_cron']))
        rm -f /etc/cron.d/{{ $site->id }}-cron
        rm -f /etc/cron.d/{{ str_replace('.', '_', $site->name) }}-cron
    @endif
rm -f /etc/cron.d/{{ $site->id }}-monitoring
rm -f /etc/cron.d/{{ $site->name }}-monitoring
rm -f /etc/cron.d/{{ str_replace('.', '_', $site->name) }}-monitoring

@else
    rm -rf /var/www/html/errors/503_{{ $site->id }}_error.html
    @include('scripts.monitoring.site.GenerateCorn', ['minute' => 60, 'site' => $site])
    @includeWhen($site->isWordpress(),'scripts.site.installWpCron', [
        'site' => $site,
        'enable_wp_cron' => $site->getMeta('enable_wp_cron', \App\Models\Site::DEFAULT_WP_CRON),
        'wp_cron_interval' => $site->getMeta('wp_cron_interval', \App\Models\Site::DEFAULT_CRON_INTERVAL)
    ])
@endif
