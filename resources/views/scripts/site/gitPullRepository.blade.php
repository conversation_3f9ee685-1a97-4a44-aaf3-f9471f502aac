chmod 600 {{ $site->getRSAKeyPath() }}
chown -R {{ $site->site_user }}:{{ $site->site_user }} /home/<USER>/.ssh

sudo -i -u {{ $site->site_user }} bash << 'EOF'
cd /var/www/{{ $site->name }}
# Pull repository
# -c core.sshCommand="ssh -i {{ $site->getRSAKeyPath() }} -o 'StrictHostKeyChecking=no'" # no need to this

git config --global --add safe.directory /var/www/{{ $site->name }}
git reset --hard && git clean -df
git fetch origin {{$gitInfo['git_branch']}}
git checkout origin/{{$gitInfo['git_branch']}} -f
git pull origin {{ $gitInfo['git_branch'] }}
EOF

@includeWhen($site->shouldRunDeployScript(), 'scripts.site.deployScript', ['site' => $site, 'script' => $site->getMeta('deploy_script')] )
