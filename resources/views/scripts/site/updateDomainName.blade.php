# Move Site Directory

@include('scripts.site.updatePermission', ['site' => $site])

# check if $newDomain is not empty, if empty exit the script with an error message
if [ -z "{{ $newDomain  }}" ]; then
echo "Error: Domain name is missing, please provide a domain name for the site to update it properly."
exit 1
fi

mv /var/www/{{ $oldDomain }} /var/www/{{ $newDomain }}

#remove old backup script
rm -rf /home/<USER>/.xcloud-backup/{{ $oldDomain }}

# Change Nginx Config File Names
@if($site->server->stack->isNginx())
    mv /etc/nginx/sites-available/{{ $oldDomain }} /etc/nginx/sites-available/{{ $newDomain }}

    # Rename xCloud Conf Directory, if exists
    if [ -d /etc/nginx/xcloud-conf/{{ $oldDomain }} ]; then
        mv /etc/nginx/xcloud-conf/{{ $oldDomain }} /etc/nginx/xcloud-conf/{{ $newDomain }}
    fi

    # Change Nginx Links
    rm /etc/nginx/sites-enabled/{{ $oldDomain }}

    # Removing cache
    rm -rf /etc/nginx/cache/{{ $oldDomain }}
@else
    # Change OpenLiteSpeed Config File Names
    mv /etc/lsws/conf.d/{{ $oldDomain }}.conf /etc/lsws/conf.d/{{ $newDomain}}.conf
    mv /usr/local/lsws/conf/vhosts/{{ $oldDomain }} /usr/local/lsws/conf/vhosts/{{ $newDomain}}
@endif

@php
    $site_name = str_replace('.', '_', $oldDomain);
@endphp

# remove existing cron using site name
if [ -f /etc/cron.d/{{ $site_name }}-cron ]; then
    rm -rf /etc/cron.d/{{ $site_name }}-cron
fi

# remove existing monitoring using site name
if [ -f /etc/cron.d/{{ $site_name }}-monitoring ]; then
    rm -rf /etc/cron.d/{{ $site_name }}-monitoring
fi

# remove existing cron using site id
if [ -f /etc/cron.d/{{ $site->id }}-cron ]; then
    rm -rf /etc/cron.d/{{ $site->id }}-cron
fi

# remove existing monitoring using site id
if [ -f /etc/cron.d/{{ $site->id }}-monitoring ]; then
    rm -rf /etc/cron.d/{{ $site->id }}-monitoring
fi

# remove old backup script
if [ -d /home/<USER>/.xcloud-backup/{{ $oldDomain }} ]; then
    rm -rf /home/<USER>/.xcloud-backup/{{ $oldDomain }}
fi
@includeWhen($site->server->stack->isNginx(),'scripts.site.updating.nginx-search-replace',[
    'oldDomain' => $oldDomain,
    'newDomain' => $newDomain
])

# Updating wordpress table prefixes with new domain
@includeWhen($site->isWordpress(),'scripts.site.searchReplaceUrl', [
    'oldDomain' => $oldDomain,
    'newDomain' => $newDomain,
    'new_url' => $new_url
])
