@php
    use App\Services\Deleting\SiteDeleting;
@endphp

@include('scripts.site.deleteSite.init', ['site' => $site])

@include('scripts.site.deleteSite.ping', ['step' => SiteDeleting::DELETING_USER])

#remove site php-fpm
rm -f "/etc/php/{{ $site->php_version }}/fpm/pool.d/www-{{$site->site_user}}.conf"

@if($site->server->stack->isNginx() && $site->php_version)
# restart fpm
sudo service php{{ $site->php_version }}-fpm restart
@endif

@if($site->server->stack->isOpenLiteSpeed())
systemctl restart lsws
@endif

#stop all process by site user
sudo pkill -u {{ $site->site_user }}

#delete site user from server
sudo userdel -r {{ $site->site_user }}

@if($site->server->isRedisSeven())
#remove acl user from /etc/redis/users.acl file
if grep -q "user {{$site->site_user}}" /etc/redis/users.acl; then
sed -i "/user {{$site->site_user}}/d" /etc/redis/users.acl
service redis-server restart
fi
@endif


echo "Site user deleted: {{ $site->site_user }}"
