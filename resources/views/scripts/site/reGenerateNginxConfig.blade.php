@if($site->hasFullPageCaching() && $site->type !== \App\Enums\SiteType::N8N && $site->type !== \App\Enums\SiteType::UPTIME_KUMA)
mkdir -p /etc/nginx/cache/{{ $site->name }}
chown -R {{ $site->site_user }}:xcloud /etc/nginx/cache/{{ $site->name }}
chmod -R 770 /etc/nginx/cache/{{ $site->name }}
@endif

mkdir -p /etc/nginx/xcloud-conf
@includeWhen($site->has7gFirewallEnabled(), 'scripts.wordpress.7g-firewall.7g-firewall')

@includeWhen($site->has7gFirewallEnabled(), 'scripts.wordpress.7g-firewall.7g-conf')

@includeWhen($site->has8gFirewallEnabled(), 'scripts.wordpress.8g-firewall.8g-firewall')

@includeWhen($site->has8gFirewallEnabled(), 'scripts.wordpress.8g-firewall.8g-conf')

@includeWhen($site->getMeta('enable_ai_bot_blocker'), 'scripts.security.ai-bot-blocker.ai-bot-blocker-nginx')

@if($site->isDisable())
cat > /etc/nginx/sites-available/{{ $site->name }} << 'EOF'
@include('scripts.wordpress.nginxDisableConfig', ['site' => $site])
EOF
@else

mkdir -p /etc/nginx/xcloud-conf/{{ $site->name }}/before
mkdir -p /etc/nginx/xcloud-conf/{{ $site->name }}/server
mkdir -p /etc/nginx/xcloud-conf/{{ $site->name }}/after

cat > /etc/nginx/sites-available/{{ $site->name }} << 'EOF'
@if($site->type === \App\Enums\SiteType::N8N || $site->type === \App\Enums\SiteType::UPTIME_KUMA)
    @include('scripts.oneclick.n8n.nginxConfig', ['site' => $site])
@elseif($site->type === \App\Enums\SiteType::MAUTIC)
    @include('scripts.oneclick.mautic.nginxConfig', ['site' => $site])
@else
    @include('scripts.wordpress.nginxConfig', ['site' => $site])
@endif
EOF

@endif

@include('scripts.wordpress.symlink', ['site' => $site])

@if($site->isIPSite())
if [ ! -f /etc/nginx/sites-available/default ]; then
cat > /etc/nginx/sites-available/default << 'EOF'
@include('scripts.nginx.default_config')
EOF
ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/default
fi
# Configure Nginx Default Page
if [ ! -f /var/www/html/*.html ]; then
cat > /var/www/html/index.html << 'EOF'
<h1>Welcome to nginx!</h1>
<p>Configured by <a href="https://xcloud.host">xCloud</a></p>
EOF
fi
@endif

nginx -t
service nginx reload
@if($site->type !== \App\Enums\SiteType::N8N && $site->type !== \App\Enums\SiteType::UPTIME_KUMA)
service php{{$site->php_version}}-fpm restart
@endif

@if(isset($restartNginx) && $restartNginx)
sudo systemctl restart nginx
@endif
