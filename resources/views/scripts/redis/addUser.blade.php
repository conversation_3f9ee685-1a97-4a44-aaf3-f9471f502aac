# Remove any existing ACL for this user
sed -i "/user {{ $redis_user ?? $site->site_user }}/d" /etc/redis/users.acl

# Add a new ACL entry
@if($site?->isLaravel() ?? false)
echo "user {{ $redis_user ?? $site->site_user }} on >{{ $redis_password ?? $site->redis_password }} ~* +@all" >> /etc/redis/users.acl
@else
echo "user {{ $redis_user ?? $site->site_user }} on >{{ $redis_password ?? $site->redis_password }} \
    ~{{ $redis_object_cache_key ?? $site->redis_object_cache_key }}* \
    +@read +@write +select +get +flushdb +del +set +setex +info \
    +ping +eval +zadd +zrangebyscore +zremrangebyscore \
    +zcount +mget +hmset +dbsize +hdel"  >> /etc/redis/users.acl
@endif

# Restart the redis service
service redis-server restart
