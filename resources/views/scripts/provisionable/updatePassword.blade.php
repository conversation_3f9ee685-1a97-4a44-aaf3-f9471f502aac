@include('scripts.ssh.syncKeys',[
    'user' => $user,
    'keyPairs' => [],
    'removePasswordAuthentication' => true,
])
# User Password & Enable Password Authentication for user

PASSWORD=$(mkpasswd -m sha-512 {!! escapeshellcmd($password) !!})

usermod --password $PASSWORD {!! escapeshellcmd($user) !!}

# Create a backup of the SSH config
cp /etc/ssh/sshd_config /home/<USER>/sshd_config.backup

# Escape the username for use in sed pattern
ESCAPED_USER=$(echo "{{ $user }}" | sed 's/[\/&]/\\&/g')

# Remove any existing managed block for this user
sed -i "/# BEGIN XCLOUD MANAGED BLOCK ${ESCAPED_USER}$/,/# END XCLOUD MANAGED BLOCK ${ESCAPED_USER}$/d" /etc/ssh/sshd_config

# Add the new managed block
cat >> /etc/ssh/sshd_config << EOF
# BEGIN XCLOUD MANAGED BLOCK {{ $user }}
Match User {{ $user }}
PubkeyAuthentication no
PasswordAuthentication yes
# END XCLOUD MANAGED BLOCK {{ $user }}
EOF

# Verify the syntax of the SSH config before restarting
if sshd -t; then
    service ssh restart
else
    # If there's a syntax error, restore the backup
    echo "Error in SSH config. Restoring backup..."
    cp /home/<USER>/sshd_config.backup /etc/ssh/sshd_config
    service ssh restart
    exit 1
fi
