<template>
    <div class="bg-focused dark:bg-mode-light rounded-10px pt-0 flex flex-col">
        <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px
                  flex justify-between items-center gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px">
            <div class="flex items-center gap-3">
                <img :src="asset('img/phpmyadmin.svg')" alt="phpMyAdmin" class="w-8 h-8" />
                <h4 class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
                    {{ $t('phpMyAdmin') }}
                </h4>
            </div>
            <div class="flex gap-20px mobile:flex-wrap mobile:gap-10px">
                <btn
                    v-if="!phpMyAdminEnabled"
                    :loading="enablingPhpMyAdmin"
                    :disabled="enablingPhpMyAdmin"
                    icon="xcloud xc-add"
                    @click.prevent="enablePhpMyAdmin"
                    :class="{'cursor-not-allowed opacity-50' : enablingPhpMyAdmin}"
                >
                    {{ !enablingPhpMyAdmin ? $t('Enable phpMyAdmin') : $t('Enabling...') }}
                </btn>
                <div v-else class="flex gap-20px mobile:flex-wrap mobile:gap-10px">
                    <btn
                        :loading="disablingPhpMyAdmin || phpMyAdminSiteDeleting"
                        :disabled="isLoading"
                        icon="xcloud xc-delete"
                        :classes="'bg-secondary-full focus:outline-none hover:bg-secondary-full'"
                        @click.prevent="disablePhpMyAdmin"
                        :class="{
                            'cursor-not-allowed disabled:opacity-50' : isLoading,
                        }"
                    >
                        {{ (disablingPhpMyAdmin || phpMyAdminSiteDeleting) ? $t('Disabling...') : $t('Disable phpMyAdmin') }}
                    </btn>
                    <btn
                        icon="xcloud xc-logout rotate-180"
                        buttonStyle="success"
                        @click.prevent="openPhpMyAdmin"
                        :loading="phpMyAdminProvisioning"
                        :disabled="isLoading || !phpMyAdminSiteProvisioned"
                        :class="{'cursor-not-allowed disabled:opacity-50' : isLoading || !phpMyAdminSiteProvisioned}"
                    >
                        {{ phpMyAdminProvisioning ? $t('Provisioning...') : $t('Open phpMyAdmin') }}
                    </btn>
                </div>
            </div>
        </div>
        <div class="p-30px pt-0 text-base text-dark dark:text-white">
            <p v-if="!phpMyAdminEnabled">
                {{ $t('phpMyAdmin is a free software tool written in PHP, intended to handle the administration of MySQL over the Web.') }}
            </p>
            <p v-else-if="phpMyAdminProvisioning">
                {{ $t('phpMyAdmin is being provisioned. Please wait until the provisioning process completes.') }}
            </p>
            <p v-else-if="!phpMyAdminSiteProvisioned">
                {{ $t('phpMyAdmin is enabled but not yet fully provisioned. Please wait until it becomes available.') }}
            </p>
            <p v-else>
                {{ $t('phpMyAdmin is enabled on this server. Click the button above to open it in a new tab.') }}
            </p>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import Btn from "@/Shared/Btn.vue";
import axios from "axios";
import { useFlash } from "@/Composables/useFlash";
import { Inertia } from "@inertiajs/inertia";

const props = defineProps({
    server: {
        type: Object,
        required: true
    },
    phpMyAdminSite: {
        type: Object,
        default: null
    },
    site: {
        type: Object,
        default: null
    },
    useSiteSpecificLogin: {
        type: Boolean,
        default: false
    }
});

let phpMyAdminEnabled = ref(!!props.phpMyAdminSite);
let enablingPhpMyAdmin = ref(false);
let disablingPhpMyAdmin = ref(false);
let phpMyAdminProvisioning = ref(false);
let phpMyAdminSite = ref(props.phpMyAdminSite);

// Check if phpMyAdmin site is provisioned
const phpMyAdminSiteProvisioned = computed(() => {
    return phpMyAdminSite.value && phpMyAdminSite.value.status === 'provisioned';
});
// Check if phpMyAdmin site is provisioned
const phpMyAdminSiteProvisioning = computed(() => {
    return phpMyAdminSite.value && phpMyAdminSite.value.status === 'provisioning';
});

// Check if phpMyAdmin site is provisioned
const phpMyAdminSiteDeleting = computed(() => {
    return phpMyAdminSite.value && phpMyAdminSite.value.status === 'deleting';
});

// Check if phpMyAdmin site is provisioned
const isLoading = computed(() => {
    return disablingPhpMyAdmin.value || phpMyAdminSiteDeleting.value || phpMyAdminProvisioning.value || phpMyAdminSiteProvisioning.value
});

function enablePhpMyAdmin() {
    enablingPhpMyAdmin.value = true;

    axios.post(route('api.server.phpmyadmin.enable', props.server.id))
        .then(response => {
            phpMyAdminSite.value = response.data;
            phpMyAdminEnabled.value = true;
            phpMyAdminProvisioning.value = true;
            useFlash().success('phpMyAdmin has been enabled successfully. It will be available once provisioning completes.');
            joinPhpMyAdminSocket();
        })
        .catch(error => {
            console.error(error);
            useFlash().error('Failed to enable phpMyAdmin. Please try again.');
        })
        .finally(() => {
            enablingPhpMyAdmin.value = false;
        });
}

function joinPhpMyAdminSocket() {
    if (window.Echo && phpMyAdminSite.value) {
        console.log(
            "joining",
            'SiteStatusChanged',
            `site.status.${phpMyAdminSite.value.id}`
        );
        window.Echo.private(`site.status.${phpMyAdminSite.value.id}`).listen(
            "SiteStatusChanged",
            (e) => {
                console.log("SiteStatusChanged", e);
                if (e.status === "deleted" || e.status === "provisioned") {
                    refreshPhpMyAdminStatus();
                }
            }
        );
    }
}

function disablePhpMyAdmin() {
    disablingPhpMyAdmin.value = true;

    axios.post(route('api.server.phpmyadmin.disable', props.server.id))
        .then(() => {
            phpMyAdminSite.value.status = "deleting";
            useFlash().success('phpMyAdmin has been disabled successfully.');
            joinPhpMyAdminSocket();
        })
        .catch(error => {
            console.error(error);
            useFlash().error('Failed to disable phpMyAdmin. Please try again.');
        })
        .finally(() => {
            disablingPhpMyAdmin.value = false;
        });
}

function refreshPhpMyAdminStatus() {
    if (props.useSiteSpecificLogin && props.site) {
        Inertia.get(route('site.database', [props.server.id, props.site.id]));
    } else {
        Inertia.get(route('server.database', props.server.id));
    }
}

function openPhpMyAdmin() {
    if (phpMyAdminSite.value && phpMyAdminSiteProvisioned.value) {
        if (props.useSiteSpecificLogin && props.site) {
            window.open(route('site.phpmyadmin-login', props.site.id), '_blank');
        } else {
            window.open(route('server.phpmyadmin-login', props.server.id), '_blank');
        }
    } else if (phpMyAdminSite.value) {
        useFlash().info('phpMyAdmin is still being provisioned. Please wait until it becomes available.');
    }
}

onMounted(() => {
    joinPhpMyAdminSocket();
});

onUnmounted(() => {
    if (window.Echo && phpMyAdminSite.value) {
        console.log(
            "leaving",
            'SiteStatusChanged',
            `site.status.${phpMyAdminSite.value.id}`
        );
        window.Echo.private(`site.status.${phpMyAdminSite.value.id}`).stopListening('SiteStatusChanged');
    }
});
</script>
