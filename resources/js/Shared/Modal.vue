<template>
    <Teleport v-if="show" to="body">
        <Transition
            enter-from-class="opacity-0 scale-125"
            enter-to-class="opacity-100 scale-100"
            enter-active-class="transition duration-300"
            leave-active-class="transition duration-200"
            leave-from-class="opacity-100 scale-100"
            leave-to-class="opacity-0 scale-125"
        >
            <div class="fixed z-modal inset-0 overflow-y-auto scrollbar-thin dark:scrollbar-thin px-12 tablet:px-4 pt-8 small-laptop:pt-25 pb-8 small-laptop:pb-4 tablet:pt-20 flex">
                <div class="flex flex-col w-full transition-sidenote duration-75 linear" :class="helper.isOpen ? 'mr-100 small-laptop:mr-0' : null">
                    <div
                        class="w-full max-h-full transition m-auto flex flex-col z-1 pr-10" :class="widthClass">
                        <header class="relative">
                            <div v-if="enableHeading" class="bg-light dark:bg-mode-base px-8 tablet:px-6 py-6 tablet:py-4 rounded-t-lg ">
                                <div class="flex items-center w-full" v-if="title">
                                    <slot name="header">
                                        <h1 class="text-2xl tablet:text-xl leading-none text-dark dark:text-white">
                                            {{ title }}
                                        </h1>
                                    </slot>
                                </div>
                            </div>
                            
                            <button
                                v-if="closeable"
                                @click="close()"
                                class="absolute top-0 -right-10 text-dark dark:text-secondary-light hover:text-black dark:hover:text-white w-8 aspect-square inline-flex items-center justify-center">
                                <span
                                    class="text-2xl inline-flex items-center justify-center">
                                    <i class="xcloud xc-close-2"></i>
                                </span>
                            </button>
                        </header>
                        <main class="p-8 tablet:p-6 bg-white dark:bg-mode-light first:rounded-t-lg last:rounded-b-lg max-h-full custom-scrollbar "
                        :class="isOverflowHidden ? '' : 'overflow-auto'"
                        >
                            <slot></slot>
                        </main>
                        <footer class="bg-light dark:bg-mode-base p-4 tablet:p-2.5 last:rounded-b-lg" v-if="footerButton">
                            <slot name="footer">
                                <div
                                    :class="{ 'justify-end': rightButton }"
                                    class="flex items-center">
                                  <btn
                                      :loading="loading"
                                      :disabled="loading || hasError || disabled"
                                      @click.prevent="$emit('footerClick')"
                                      :class="{'cursor-not-allowed opacity-50' : loading || hasError}"
                                  >
                                    <span>{{ loading ? loadingText : footerButtonTitle }}</span>
                                  </btn>
                                </div>
                            </slot>
                        </footer>
                    </div>
                </div>
                <div class="fixed bg-secondary-light/90 dark:bg-black/90 inset-0" @click="closeable && close()"></div>
            </div>
        </Transition>
    </Teleport>


</template>

<script setup>
import {onMounted, onUnmounted} from "vue";
import {useHelpStore} from "@/stores/HelpStore";
import Btn from "@/Shared/Btn.vue";

let helper = useHelpStore();
const props = defineProps({
    show: Boolean,
    widthClass: {
        type: String,
        default: "max-w-100"
    },
    closeable: {
        type: Boolean,
        default: true
    },
    loading: {
        type: Boolean,
        default: false
    },
    title: String,
    footerButton: {
        type: Boolean,
        default: false,
    },
    footerButtonTitle: String,
    hasError: {
        type: Boolean,
        default: false
    },
    disabled: {
        type: Boolean,
        default: false
    },
    mainClass: String,
    loadingText: {
        type: String,
        default: 'Loading...'
    },
    isOverflowHidden: {
        type: Boolean,
        default: false
    },
    rightButton: {
        type: Boolean,
        default: false
    },
    enableHeading: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['close','footerClick'])

const close = () => {
    if (props.closeable) {
        emit('close')
    }
}

const closeOnEscape = (e) => {
    if (e.key === 'Escape' && props.show) {
        close()
    }
}


onMounted(() => document.addEventListener('keydown', closeOnEscape))
onUnmounted(() => {
    document.removeEventListener('keydown', closeOnEscape)
    document.body.style.overflow = null
})


</script>
