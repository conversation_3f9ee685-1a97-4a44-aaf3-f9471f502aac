<template>
        <div
            class="flex flex-col gap-50px p-50px wide-mobile:p-30px mobile:p-20px w-full" :class="tableParentClass">
            <div class="rounded-md flex flex-col">
                <div class="overflow-x-auto w-full">
                    <table class="w-full">
                        <thead class="bg-primary-light dark:bg-dark">
                        <tr class="divide-x divide-light dark:divide-dark">
                            <th
                                class="px-30px w-[50ch] py-20px text-lg font-normal text-white dark:text-white text-left">
                                {{ $t('Command') }}
                            </th>
                            <th
                                class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                {{ $t('Initiated By') }}
                            </th>
                            <th
                                class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                {{ $t('Date') }}
                            </th>
                            <th
                                class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white">
                                {{ $t('Status') }}
                            </th>
                            <th
                                class="px-30px py-20px text-center text-lg font-normal text-white dark:text-white">
                                {{ $t('Actions') }}
                            </th>
                        </tr>
                        </thead>
                        <tbody
                            class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                        <tr
                            class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                            v-for="command in commands.data" :key="command.id"
                        >
                            <td
                                class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white w-96 break-all">
                                <CopyAbleText :text="commandText(command?.command)"> {{ commandText(command?.command) }} </CopyAbleText>
                            </td>
                            <td
                                class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                <template v-if="command?.initiatedBy">
                                    {{ command?.initiatedBy?.name }}
                                </template>
                            </td>
                            <td
                                class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white ">
                                {{ command.created_at }}
                            </td>
                            <td
                                class="px-30px py-20px text-center text-base font-normal text-dark dark:text-white group-hover:text-white">
                                <span
                                    v-if="command.status === 'running'"
                                    class="capitalize text-primary-light group-hover:text-white">Inprogress</span>

                                <span
                                    v-else
                                    :class="{
                                        'text-danger': command.status === 'failed',
                                        'text-success-full': command.status === 'finished',
                                    }"
                                    class="capitalize group-hover:text-white">{{ command.status === 'finished' ? 'Completed' : command.status }}</span>
                            </td>
                            <td
                                class="px-30px py-20px text-center text-base font-normal text-secondary-full dark:text-white group-hover:text-white">
                                <button
                                    v-if="(command.status !== 'running' && $page.props?.user?.role === 'user') || ['admin','super_admin','support_level_1','support_level_2'].includes($page.props?.user?.role)"
                                    class="underline" @click.prevent="showSingleCommand(command)">{{ $t('View') }}</button>
                                <tooltip v-if="$inertia.page.props.auth_user?.role === 'super_admin'" title="Output" placement="top">
                                    <button
                                        @click.prevent="showOutput(command)"
                                        type="button"
                                        class="relative group cursor-pointer mt-1.5 ml-4">
                                        <i class="xcloud xc-analytics text-secondary-light"></i>
                                    </button>
                                </tooltip>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- pagination -->
            <Pagination v-if="hasPagination"
                        :links="commands.links"
            />
        </div>

    <Modal
        @close="openEventModal = false"
        :show="openEventModal"
        :footerButton="false"
        :widthClass="'max-w-850px'"
        :title="modalTask.name"
    >
        <div class="flex flex-col text-secondary-full dark:text-white leading-loose">
            <div v-if="modalTaskIsLoading">
                <!-- tailwind loading icon -->
                <div class="flex justify-center items-center h-100px">
                    <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                </div>
            </div>

            <div v-else>
                <pre class="text-left whitespace-pre-wrap break-words text-xs border-b-1 mb-4 pb-4"
                     v-if="modalTask.script">{{ modalTask.script }}</pre>
                <!-- Scroll buttons -->
                <div class="flex justify-end my-4">
                    <button @click="scrollToBottom">Scroll to end</button>
                </div>
                <pre ref="output" class="text-left whitespace-pre-wrap break-words text-xs">{{ modalTask.output }}</pre>
                <div class="flex justify-end my-4">
                    <button @click="scrollToTop">Scroll to top</button>
                </div>
            </div>
        </div>
    </Modal>
</template>

<script setup>
import {ref} from "vue";
import Pagination from "@/Shared/Pagination.vue";
import Modal from "@/Shared/Modal.vue";
import 'v-calendar/style.css';
import Tooltip from "@/Shared/Tooltip.vue";
import axios from "axios";
import CopyAbleText from "./CopyAbleText.vue";

let openEventModal = ref(false);
let modalTask = ref({});
let modalTaskIsLoading = ref(false);

const props = defineProps({
    title: {
        type: String,
        default: 'Events',
    },
    tableParentClass: {
        type: String,
        default: null,
    },
    commands: Object,
    teamMembers: Array,
    showSite: {
        type: Boolean,
        default: true,
    },
    showServer: {
        type: Boolean,
        default: true,
    },
    hasPagination: {
        type: Boolean,
        default: true,
    },
    showHeader: {
        type: Boolean,
        default: true,
    },
    member: {
        type: Number,
        default: 0,
    },
})

const commandText = (text, length = 300) => {
    return text?.length > length ? text.substring(0, length) + '...' : text;
};

const showSingleCommand = (task) => {
    openEventModal.value = true;
    modalTaskIsLoading.value = true;
    modalTask.value = task;
    axios.get(route('api.task.show', task.id)).then(response => {
        modalTaskIsLoading.value = false;
        modalTask.value = response.data;
    })
}

const showOutput = (task) => {
    openEventModal.value = true;
    modalTaskIsLoading.value = true;
    modalTask.value = task;
    axios.get(route('api.task.event.output', task.id))
    .then(response => {
        modalTaskIsLoading.value = false;
        modalTask.value = response.data;
    })
    .catch((error) => {
        modalTaskIsLoading.value = false;
    });
}

const output = ref(null);

function scrollToBottom() {

    output.value?.scrollIntoView({behavior: "smooth", block: "end"})
}

function scrollToTop() {
    output.value?.scrollIntoView({behavior: "smooth", block: "start"})
}
</script>
