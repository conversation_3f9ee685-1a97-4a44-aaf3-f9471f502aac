<template>
    <transition
    enter-active-class="transition-all duration-150 ease-out"
    leave-active-class="transition-all duration-150 ease-in"
    enter-from-class="opacity-0 translate-y-2 scale-95"
    leave-to-class="opacity-0 translate-y-2 scale-95"
  >
    <div class="absolute top-full z-[9999]">
      <div
          v-if="(sitesData && sitesData.length > 0) || (serversData && serversData.length > 0)"
          ref="megaMenuRef"
          @mouseleave="$emit('close', menuType)"
          :style="(menuType === 'sites' ? site_last_page > 1 : server_last_page > 1) ? {minHeight: '530px'} : {}"
          class="w-[963px] bg-[#FFFFFF] dark:bg-[#15182A] shadow-lg mx-auto text-dark dark:text-white border border-white dark:border-[#1169DB] rounded-[8px] overflow-hidden py-4 px-8 relative"
      >
         <p v-if="menuType === 'servers' && serversData && serversData.length > 0" class="text-sm text-[14px] px-4 dark:text-[#697586] pb-1">{{ $t('All ' + capitalize(menuType)) }}</p>
          <p v-else-if="menuType === 'sites' && sitesData && sitesData.length > 0 " class="text-sm text-[14px] px-4 dark:text-[#697586] pb-1">{{ $t('All ' + capitalize(menuType)) }}</p>
          <p v-else class="text-sm text-[14px] px-4 dark:text-[#697586] pb-1">{{ $t('There are no ' + capitalize(menuType)) }}</p>

          <!-- Skeleton Loader -->
          <div v-if="loadSkeleton" class="space-y-4">
<!--                <div v-for="n in numberOfRows" :key="n" class="flex items-center gap-4 p-4 animate-pulse">-->
<!--                    <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-[4px]"></div>-->
<!--                    <div class="flex-1 space-y-2">-->
<!--                        <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>-->
<!--                        <div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>-->
<!--                    </div>-->
<!--                </div>-->
          </div>

          <!-- Data Display for sites-->
          <div v-else-if="menuType === 'sites' && sitesData" class="grid grid-cols-3">

              <template v-for="item in sitesData" :key="item?.id">
                <Link :href="getHref(item)" @click="closeDropDown">
                  <div
                      class="flex justify-between items-center gap-3 p-4 hover:bg-[#EDF2F8] dark:hover:bg-[#191D32] rounded-lg transition-colors cursor-pointer group"
                      @mouseover="$emit('update:hoverId', item.id)"
                      @mouseleave="$emit('update:hoverId', null)"
                  >
                          <div class="flex items-center gap-4">
                              <div class="w-8 h-8 flex-shrink-0 bg-[#EDF2F8] group-hover:bg-[#FFFFFF] dark:bg-[#1D2239] rounded-[4px] p-[4px] transition-transform transform duration-300 ease-in-out">
                                  <div class="w-full h-full">
                                      <img
                                          :src="siteFavicon(item.name)"
                                          :alt="getItemName(item)"
                                          @load="handleImageLoad($event, item)"
                                          @error="handleImageError"
                                          class="w-full h-full object-cover group-hover:scale-110"
                                      />
                                  </div>
                              </div>
                              <div class="flex flex-col min-w-0 gap-1">
                                  <Tooltip v-if="getItemName(item)?.length > 23" :title="item?.name">
                                      <h3
                                          :class="[
                                          'text-dark hover:text-primary-dark dark:text-white text-[16px] font-medium whitespace-nowrap',
                                          getItemName(item)?.length > 23 ? 'truncate w-[200px] overflow-hidden' : ''
                                      ]"
                                      >
                                          {{ getItemName(item) }}
                                      </h3>
                                  </Tooltip>
                                  <h3 v-else class="text-dark hover:text-primary-dark dark:text-white text-[16px] font-medium whitespace-nowrap">
                                      {{ getItemName(item) }}
                                  </h3>
                                  <div class="flex items-center gap-2 text-secondary-full dark:text-[#697586] text-xs">
                                      <span class="whitespace-nowrap">{{ dayjs(item?.created_at).format('D[th] MMM, YYYY') }}</span>
                                      <span class="dark:text-[#4B5565] w-[8px] h-[8px] flex items-center justify-center text">
                                      <i class="xcloud xc-dot text-[4px]"></i>
                                  </span>
                                      <Tooltip v-if="menuType === 'sites' && item?.server?.name?.length > 16" :title="item.server.name">
                                          <div class="flex items-center truncate flex-row min-w-0">
                                              <img
                                                  :src="getProviderIcon(item?.server?.provider?.provider)"
                                                  :alt="item?.server?.provider?.defined_name"
                                                  class="w-4 h-4 object-contain mr-1.5"
                                              />
                                              <span
                                                  :class="[
                                                  'whitespace-nowrap',
                                                  item?.server?.name?.length > 16 ? 'truncate w-[100px] overflow-hidden' : ''
                                              ]"
                                              >
                                              {{ item?.server?.name }}
                                          </span>
                                          </div>
                                      </Tooltip>
                                      <span v-else-if="menuType === 'sites'" class="whitespace-nowrap">
                                      {{ item?.server?.name }}
                                  </span>
                                  </div>
                              </div>
                          </div>

                      <span class="w-[16px] h-[16px] flex items-center justify-center">
                      <a :href="getHref(item)" target="_blank" rel="noopener noreferrer" @click.stop>
                      <i
                          v-show="hoverId === item.id"
                          class="xcloud xc-maximize text-base text-secondary-full dark:text-secondary-light hover:text-primary-light dark:hover:text-primary-light cursor-pointer transition ease-in-out duration-75 ml-auto"
                      ></i>
                      </a>
                  </span>
                  </div>
                </Link>
              </template>
          </div>

          <!-- Data Display for servers -->
          <div v-else-if="menuType === 'servers' && serversData" class="grid grid-cols-3">
              <template v-for="item in serversData" :key="item?.id">
                  <Link :href="getHref(item)" @click="closeDropDown">
                  <div
                      class="flex justify-between items-center gap-3 p-4 hover:bg-[#EDF2F8] dark:hover:bg-[#191D32] rounded-lg transition-colors cursor-pointer group"
                      @mouseover="$emit('update:hoverId', item.id)"
                      @mouseleave="$emit('update:hoverId', null)"
                  >
                          <div class="flex items-center gap-4">
                              <div class="w-8 h-8 flex-shrink-0 bg-[#EDF2F8] group-hover:bg-[#FFFFFF] dark:bg-[#1D2239] rounded-[4px] p-[4px] transition-transform transform duration-300 ease-in-out">
                                  <div class="w-full h-full">
                                      <img
                                          :src="getIcon(item)"
                                          :alt="getItemName(item)"
                                          class="w-full h-full object-cover group-hover:scale-110"
                                      />
                                  </div>
                              </div>
                              <div class="flex flex-col min-w-0 gap-1">
                                  <Tooltip v-if="getItemName(item)?.length > 23" :title="getItemName(item)">
                                      <h3
                                          :class="[
                                          'text-dark hover:text-primary-dark dark:text-white text-[16px] font-medium whitespace-nowrap',
                                          getItemName(item)?.length > 23 ? 'truncate w-[200px] overflow-hidden' : ''
                                      ]"
                                      >
                                          {{ getItemName(item) }}
                                      </h3>
                                  </Tooltip>
                                  <h3 v-else class="text-dark hover:text-primary-dark dark:text-white text-[16px] font-medium whitespace-nowrap">
                                      {{ getItemName(item) }}
                                  </h3>
                                  <div class="flex items-center gap-2 text-secondary-full dark:text-[#697586] text-xs">
                                  <span class="text-[12px]">
                                      <CopyAbleText align="bottom" :text="item?.public_ip ? item?.public_ip : 'xxx.xxx.xxx.xx'" />
                                  </span>
                                      <span v-if="menuType === 'servers'" class="dark:text-[#4B5565] w-[8px] h-[8px] flex items-center justify-center text">
                                      <i class="xcloud xc-dot text-[4px]"></i>
                                  </span>
                                      <span v-if="menuType === 'servers'">{{ item?.total_site }} {{ $t('Sites') }}</span>
                                      <span v-if="menuType === 'sites'" class="whitespace-nowrap">{{ dayjs(item?.created_at).format('D[th] MMM, YYYY') }}</span>
                                      <span v-if="menuType === 'sites'" class="dark:text-[#4B5565] w-[8px] h-[8px] flex items-center justify-center text">
                                      <i class="xcloud xc-dot text-[4px]"></i>
                                  </span>
                                      <Tooltip v-if="menuType === 'sites' && item?.server?.name?.length > 16" :title="item.server.name">
                                          <div class="flex items-center truncate flex-row min-w-0">
                                              <img
                                                  :src="getProviderIcon(item?.server?.provider?.provider)"
                                                  :alt="item?.server?.provider?.defined_name"
                                                  class="w-4 h-4 object-contain mr-1.5"
                                              />
                                              <span
                                                  :class="[
                                                  'whitespace-nowrap',
                                                  item?.server?.name?.length > 16 ? 'truncate w-[100px] overflow-hidden' : ''
                                              ]"
                                              >
                                              {{ item?.server?.name }}
                                          </span>
                                          </div>
                                      </Tooltip>
                                      <span v-else-if="menuType === 'sites'" class="whitespace-nowrap">
                                      {{ item?.server?.name }}
                                  </span>
                                  </div>
                              </div>
                          </div>
                      <span class="w-[16px] h-[16px] flex items-center justify-center">
                      <a :href="getHref(item)" target="_blank" rel="noopener noreferrer" @click.stop>
                      <i
                          v-show="hoverId === item.id"
                          class="xcloud xc-maximize text-base text-secondary-full dark:text-secondary-light hover:text-primary-light dark:hover:text-primary-light cursor-pointer transition ease-in-out duration-75 ml-auto"
                      ></i>
                      </a>
                  </span>
                  </div>
                  </Link>
              </template>
          </div>
          <div class="absolute bottom-5 right-4 py-4 px-8 ">
          <!-- Pagination for sites -->
          <PaginationForMegaMenu
              v-if="menuType === 'sites'"
              :current-page="site_current_page"
              :last-page="site_last_page"
              @fetch-data="fetchSiteList"
          />
          <!-- Pagination for servers -->
          <PaginationForMegaMenu
              v-if="menuType === 'servers'"
              :current-page="server_current_page"
              :last-page="server_last_page"
              @fetch-data="fetchServerList"
          />
        </div>

      </div>
    </div>
  </transition>
</template>

<script setup>
import {Link, usePage} from "@inertiajs/inertia-vue3";
import CopyAbleText from "@/Shared/CopyAbleText.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import dayjs from "dayjs";
import {useCloudProviderIcon} from "@/Composables/useCloudProviderIcon";
import {computed, onMounted, ref} from "vue";
import {asset} from "laravel-vapor";
import {useMegaMenuStore} from "@/stores/MegaMenuStore.js";
import {userClickOutside} from "@/Shared/Events/userClickOutside";
import PaginationForMegaMenu from "@/Shared/Header/Components/PaginationForMegaMenu.vue";
import {useSiteTypeIcon} from "@/Composables/useSiteTypeIcon";
let megaMenuStorage = useMegaMenuStore();


const props = defineProps({
  hoverId: [Number, null],
  menuType: String,
});

const emit = defineEmits([
  'open',
  'close',
  'update:hoverId',
  'open-in-new-tab',
  'go-to-page'
]);

const loadSkeleton = ref(false);

const sitesData = ref();
const site_current_page = ref();
const site_last_page = ref();
const site_pages = ref();
const megaMenuRef = ref(null);

const serversData = ref();
const server_current_page = ref();
const server_last_page = ref();
const server_pages = ref();
const { user } = usePage().props.value;

onMounted(() => {
  megaMenuStorage.setTeamId(user.current_team_id)
  fetchSiteList();
  fetchServerList();
});

userClickOutside(megaMenuRef, () => {
  emit('close', props.menuType);
});

const fetchSiteList = (page = 1, perPage = 18) => {

  // if (!sitesData.value || sitesData.value.length === 0) {
  //     loadSkeleton.value = true; // Start loading only if there is no serversData
  // }

  let sites = megaMenuStorage.getSites(page);
  let latestSiteResponse = megaMenuStorage.getLatestSiteResponse;

  if(sites.length > 0){
      sitesData.value = sites;
      site_current_page.value = page;
      site_last_page.value = latestSiteResponse.last_page;
      site_pages.value = range(1, latestSiteResponse.last_page);
      return;
  }

  axios.get(route('api.site-list', {
      page: page,
      per_page: perPage,
  })).then((response) => {
      megaMenuStorage.saveSites(response.data.current_page, response.data.data)
      megaMenuStorage.saveLatestSiteResponse(response.data);

      site_current_page.value = response.data.current_page;
      site_last_page.value = response.data.last_page;
      site_pages.value = range(1, response.data.last_page);
      sitesData.value = response.data.data;
  }).catch((error) => {
      console.error("Error fetching site list:", error);
  }).finally(() => {
      loadSkeleton.value = false; // Stop loading
  });

};

const fetchServerList = (page = 1, perPage = 18) => {

  // if (!serversData.value || serversData.value.length === 0) {
  //     loadSkeleton.value = true;
  // }

  let servers = megaMenuStorage.getServers(page);
  let latestServerResponse = megaMenuStorage.getLatestServerResponse;

  if(servers.length > 0){
      serversData.value = servers;
      server_current_page.value = page;
      server_last_page.value = megaMenuStorage.getLatestServerResponse.last_page;
      server_pages.value = range(1, latestServerResponse.last_page);
      return;
  }

  axios.get(route('api.server-list', {
      page: page,
      per_page: perPage,
  })).then((response) => {
      megaMenuStorage.saveServers(response.data.current_page, response.data.data);
      megaMenuStorage.saveLatestServerResponse(response.data);

      server_current_page.value = response.data.current_page;
      server_last_page.value = response.data.last_page;
      server_pages.value = range(1, response.data.last_page);
      serversData.value = response.data.data;
  }).catch((error) => {
      console.error("Error fetching site list:", error);
  }).finally(() => {
      loadSkeleton.value = false; // Stop loading
  });
};

const range = (start, end) => {
  return Array.from({ length: end - start + 1 }, (_, i) => start + i);
};

const capitalize = (str = null) => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

const siteFavicon = (siteName) => {
  return `https://icons.duckduckgo.com/ip3/${siteName}.ico`;

  // img.onload = () => {
  //     if (img.naturalWidth <= 16 || img.naturalHeight <= 16) {
  //         img.src = asset('img/wordpress-blue.svg');
  //     }
  // };
  //
  // img.onerror = () => {
  //     img.src = asset('img/wordpress-blue.svg'); // Fallback in case of error
  // };
  //
  // return img.src;
};

function handleImageLoad(e, item) {
  let imgElement = e.target;
  if(imgElement){
      if (imgElement.src.includes('wordpress-blue.svg') || imgElement.src.includes('php_version.svg')) {
          return;
      }

      if(imgElement.naturalWidth <= 48 || imgElement.naturalHeight <= 48){
          imgElement.src = useSiteTypeIcon(item.type).siteTypeIcon.value;
      }
  }
}

function handleImageError() {
    console.log('Favicon not found, using custom icon.');
    if (siteFavicon.value.includes('wordpress-blue.svg') || siteFavicon.value.includes('php_version.svg')) {
        return;
    }
    siteFavicon.value = props.site?.is_wordpress ?  asset('img/wordpress-blue.svg') : asset('img/php_version.svg');
}


const getProviderIcon = (providerName) => {
  const { cloudProviderIcon } = useCloudProviderIcon(providerName);
  return cloudProviderIcon.value;
};

const getHref = (item) => {
  if (props.menuType === 'servers') {
      return item?.status !== 'deleting' ? '/server/' + item.id + '/sites' : '#';
  } else if (props.menuType === 'sites') {
      return  '/server/' + item.server.id + '/site/' + item.id + '/site-overview';
  }

  return "#";
};

const getIcon = (item) => {
  if (props.menuType === 'servers') {
      return getProviderIcon(item?.provider?.provider);
  } else if (props.menuType === 'sites') {
      return siteFavicon(item?.name);
  }
  return '';
};

const getItemName = (item) => {
  return props.menuType === 'servers' ? item?.name : (item?.name?.length > 23 ? item.name.slice(0, 23) + '...' : item?.name);
}

const numberOfRows = computed(() => {
  if (props.menuType === 'servers') {
      return Math.ceil(serversData.value?.length / 3);
  } else if (props.menuType === 'sites') {
      return Math.ceil(sitesData.value?.length / 3);
  }
  return 0;
});

const beforeEnter = (el) => {
  el.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
  el.style.transform = 'scale(0.9)';
  el.style.opacity = '0';
};

const enter = (el, done) => {
  el.style.transform = 'scale(1)';
  el.style.opacity = '1';
  el.addEventListener('transitionend', done);
};

const beforeLeave = (el) => {
  el.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
};

const leave = (el, done) => {
  el.style.transform = 'scale(0.9)';
  el.style.opacity = '0';
  el.addEventListener('transitionend', done);
};

</script>

<style scoped>
.animate-pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
.dropdown-enter-active, .dropdown-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.dropdown-enter-from, .dropdown-leave-to {
  transform: scale(0.9);
  opacity: 0;
}
</style>
