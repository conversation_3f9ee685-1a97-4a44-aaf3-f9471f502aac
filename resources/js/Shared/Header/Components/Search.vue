<template>
    <div class="relative inline-block text-left small-laptop:hidden w-52" ref="search">
        <form class="relative flex items-center group" @submit.prevent>
            <input type="text"
                   v-model="queryString"
                   @keydown.enter.prevent
                   class="flex-auto w-full appearance-none bg-light dark:bg-mode-base pl-4 pr-10 h-12 min-h-12 rounded-lg text-secondary-full dark:text-white text-sm placeholder-secondary-light dark:placeholder-secondary-full focus:outline-none font-normal border-none focus:ring-success-light"
                   :placeholder="$t('Find Servers or Sites')"/>
            <div
                class="icon flex absolute top-1/2 right-4 -translate-y-1/2 pointer-events-none text-lg" v-if="navigation.headers.search.length===0">
                <i class="xcloud xc-search text-secondary-light group-focus-within:text-success-light"></i>
            </div>
            <div
                class="icon flex absolute top-1/2 right-4 -translate-y-1/2 cursor-pointer text-sm" v-if="navigation.headers.search.length!==0"
                @click="setSearchEmpty()">
                <i class="xcloud xc-close1 text-danger"></i>
            </div>
        </form>
        <div
            v-if="navigation.headers.search.length > 0"
            class="origin-top-right top-full absolute right-1/2 translate-x-1/2 w-96 rounded-lg shadow-lg bg-light dark:bg-dark focus:outline-none z-dropdown mt-1"
            role="menu" aria-orientation="vertical" aria-labelledby="menu-button" tabindex="-1">
            <div class="px-5 py-3.5 flex items-center">
                <div class="text-base flex items-center text-dark dark:text-white mr-auto">
                    {{ $t('Search Results') }}
                </div>
            </div>
            <div class="rounded-b-lg py-1 bg-white dark:bg-mode-focus-dark divide-y divide-light dark:divide-mode-base max-h-100 overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                <div v-if="searchResult.sites.length > 0" class="px-5 py-3.5">
                    <label
                        class="text-base mb-2.5 text-dark dark:text-white flex leading-none">{{ $t('Sites') }}</label>
                    <div class="flex flex-col gap-2.5 max-h-36 overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                        <Link v-for="site in searchResult.sites" :key="site.id"
                              :href="route('site.show', {server: site.server_id, site: site.id})"
                              class="flex items-center" @click="setSearchEmpty">
                            <div v-if="site.server.is_connected" class="relative w-5 shrink-0">
                                <img :src="asset('img/wordpress-blue.svg')" alt=""/>
                            </div>
                            <div v-else class="relative w-5 shrink-0">
                                <img :src="asset('img/wordpress-blind.svg')" alt="" class="w-full"/>
                                <img :src="asset('img/important.svg')" alt=""
                                     class="w-3.5 absolute -right-1 -top-1"/>
                            </div>
                            <span
                                class="text-base leading-tight text-secondary-full dark:text-secondary-light ml-2">{{ site.name }}</span>
                        </Link>
                    </div>
                </div>
                <div v-if="searchResult.servers.length > 0" class="px-5 py-3.5">
                    <label
                        class="text-base mb-2.5 text-dark dark:text-white flex leading-none">{{ $t('Servers') }}</label>
                    <div class="flex flex-col gap-2.5 max-h-36 overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                        <Link v-for="server in searchResult.servers" :key="server.id"
                              :href="route('server.show', server.id)" class="flex items-center" @click="setSearchEmpty">
                            <div v-if="server.is_connected" class="relative w-5 shrink-0">
                                <img :src="getCloudProvider(server?.provider)" alt=""/>
                            </div>
                            <div v-else class="relative w-5 shrink-0">
                                <img :src="asset('img/failed.svg')" alt="" class="w-full"/>
                                <!--<img :src="asset('img/digitalocean-blind.svg')" alt="" class="w-full"/>-->
                            </div>
                            <span
                                class="text-base leading-tight text-secondary-full dark:text-secondary-light ml-2">{{ server.name }}</span>
                        </Link>
                    </div>
                </div>
                <div
                    v-if="navigation.headers.search.length > 0 && searchResult.servers.length === 0 && searchResult.sites.length === 0"
                    class="px-5 py-3.5 flex">
                    <p class="text-sm my-1 text-dark dark:text-white flex leading-none">{{ $t("Can't find what you're looking for?") }}</p>
                    <a href="#" class="text-sm text-red-500 pl-3 inline-flex items-center leading-none">{{ $t('Contact Support') }}</a>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useDebouncedRef } from "@/Composables/useDebouncedRef";
import { userClickOutside } from "@/Shared/Events/userClickOutside.js";
import { useNavigationStore } from "@/stores/NavigationStore.js";
import { Link } from "@inertiajs/inertia-vue3";
import axios from "axios";
import { ref, watchEffect } from "vue";
import {useCloudProviderIcon} from "@/Composables/useCloudProviderIcon";

let navigation = useNavigationStore();
const queryString = useDebouncedRef(navigation.headers.search, 750);
const search = ref(null);
const searchResult = ref({
    servers: [],
    sites: []
});

watchEffect(() => {
    navigation.updateSearchQuery(queryString.value);
    if (queryString.value.length > 0) {
        axios.get(route('api.search', {query: queryString.value})).then(response => {
            searchResult.value = response.data;
        });
    }
},[queryString.value]);

/**
 * Close the search list when user clicks outside
 */

const setSearchEmpty = () => {
    if (navigation.headers.search.length > 0) {
        setTimeout(() => {
            queryString.value = '';
            navigation.updateSearchQuery("");
        }, 300);
    }
};
const getCloudProvider = (provider) => {
    const {cloudProviderIcon} = useCloudProviderIcon(provider);
    return cloudProviderIcon.value;
};

userClickOutside(search, ()=>{
    setSearchEmpty();
});


</script>
