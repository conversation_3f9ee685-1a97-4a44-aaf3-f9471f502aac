<template>
    <div class="w-full block small-laptop:sticky small-laptop:-order-1 small-laptop:inset-x-0 small-laptop:top-0 small-laptop:z-header z-dropdown">
        <required-payment-method v-if="requires_active_payment_method" />
        <manage-white-label-team v-if="$page?.props?.whiteLabelManageTeam" />
        <header
            v-if="$page.props.auth_user?.name"
            class="header w-full h-20 bg-white dark:bg-mode-light tablet:h-15 small-laptop:z-header relative after:absolute after:inset-x-0 after:bottom-0 after:h-0.25 after:bg-focused dark:after:bg-mode-focus-light z-1 after:-z-1 after:pointer-events-none">
            <div class="xc-container !h-full flex !flex-row">
                <div class="h-full w-full flex items-stretch">
                    <Link v-if="currentWhiteLabel && currentWhiteLabel.brand_photo_url" :href="route('dashboard')" class="brand-logo self-center flex items-center h-9 tablet:h-8 shrink-0 mr-10">
                        <img :src="currentWhiteLabel.brand_photo_url" alt="" class="h-full dark:hidden"/>
                        <img :src="currentWhiteLabel.brand_photo_url" alt="" class="h-full hidden dark:block"/>
                    </Link>

                    <Link v-else :href="route('dashboard')" class="brand-logo self-center flex items-center h-9 tablet:h-8 shrink-0 mr-10">
                        <img :src="asset('img/x-cloud_blue.svg')" alt="" class="h-full dark:hidden"/>
                        <img :src="asset('img/x-cloud_dark.svg')" alt="" class="h-full hidden dark:block"/>
                    </Link>
                    <nav class="header-menu-area mr-5 flex flex-1 pc-version relative">
                        <ul class="header-menu flex gap-6">
                            <NavLink url="/dashboard" :active="$page.component ==='Dashboard'" icon="xcloud xc-dashboard-2">{{ $t('Dashboard') }}</NavLink>
                            <span class="inline-flex gap-6"
                                  @mouseover="openMegaMenu"
                                  @mouseleave="closeMegaMenu">
                                <NavLink
                                    @mouseenter="openDropdown('servers')"
                                    @mouseleave="closeDropdown('servers')"
                                    v-if="page?.props?.can_view_server" url="/server" :active="page.component ==='Server/Index'" icon="xcloud xc-server_2">{{ $t('Servers') }}

                                </NavLink>
                                <NavLink
                                    @mouseenter="openDropdown('sites')"
                                    @mouseleave="closeDropdown('sites')"
                                    url="/site" :active="page.component ==='Site/Index'" icon="xcloud xc-web">{{ $t('Sites') }}
                                </NavLink>
                            </span>

                            <template v-if="!$page?.props?.current_white_label">
                                <NavLink :url="route('white-label.onboarding.startup')" :active="isActiveWhiteLabel" icon="xcloud xc-whitelabel">
                                    {{ $t('White Label') }}
                                    <template v-slot:badge v-if="!$page?.props?.has_active_white_label_subscription">
                                        <img class="w-14 h-auto shrink-0 -mx-2.5" :src="asset('img/new.gif')" alt="new" />
                                    </template>
                                </NavLink>
                            </template>
                            <NavLink v-if="page?.props?.can_try_playground" url="/site/installWordPressToPlayground" :active="page.component ==='Site/New/InstallWordPressToPlayground'" icon="xcloud xc-playground">
                                {{ $t('Playground') }}
                            </NavLink>
                        </ul>
                        <!-- Dropdown for Servers and Sites -->
                        <MegaMenuDropDown
                            v-if="isDropdownOpen"
                            @mouseenter="handleMegaMenuOpen"
                            :hover-id="hoverId"
                            :menu-type="menuType"
                            @open="openMegaMenu"
                            @close="closeMegaMenu"
                            @update:hover-id="hoverId = $event"
                        />
                    </nav>

                    <div class="header-right mobile:relative flex items-center gap-6 ml-auto">
                        <Search />
                        <div class="flex items-center gap-4">
                            <template v-if="!$page?.props?.whiteLabelManageTeam">
                                <Events v-if="props.page.props.user" />
                                <Notification v-if="props.page.props.user && $page.props?.can_view_notifications" />
                            </template>
                            <MobileNavbar/>
                        </div>
                        <Profile v-if="props.page.props.user" >
                            <template v-slot:nightmode>
                                <NightMode/>
                            </template>
                        </Profile>
                        <div v-else><NightMode /></div>
                    </div>
                </div>
            </div>
        </header>
    </div>
</template>

<script setup>
import Profile from "@/Shared/Header/Components/Profile.vue";
import Search from "@/Shared/Header/Components/Search.vue";
import Events from "@/Shared/Header/Components/Events.vue";
import NightMode from "@/Shared/Header/Components/NightMode.vue";
import Notification from "@/Shared/Header/Components/Notification.vue";
import NavLink from "@/Shared/Header/Components/NavLink.vue";
import MobileNavbar from "@/Shared/Header/Components/Mobile/MobileNavbar.vue";
import {useNavigationStore} from "@/stores/NavigationStore.js";
import {Link, usePage} from "@inertiajs/inertia-vue3";
import RequiredPaymentMethod from "@/Shared/Header/Components/RequiredPaymentMethod.vue";
import {ref, computed, onMounted} from "vue";
import ManageWhiteLabelTeam from "@/Shared/Header/Components/ManageWhiteLabelTeam.vue";
import {asset} from "laravel-vapor";
import MegaMenuDropDown from "@/Shared/Header/Components/MegaMenuDropDown.vue";
import {useMegaMenuStore} from "@/stores/MegaMenuStore.js";
import {userClickOutside} from "@/Shared/Events/userClickOutside";
let megaMenuStorage = useMegaMenuStore();

onMounted(() => {
    axios.get(route('api.site-list', {
        page: 1,
        per_page: 18,
    })).then((response) => {
        megaMenuStorage.saveSites(response.data.current_page, response.data.data)
        megaMenuStorage.saveLatestSiteResponse(response.data);
    }).catch((error) => {
        console.error("Error fetching site list:", error);
    }).finally(() => {

    });

    axios.get(route('api.server-list', {
        page: 1,
        per_page: 18,
    })).then((response) => {
        megaMenuStorage.saveServers(response.data.current_page, response.data.data)
        megaMenuStorage.saveLatestServerResponse(response.data);
    }).catch((error) => {
        console.error("Error fetching server list:", error);
    }).finally(() => {

    });
});

const props = defineProps({
    page: Object,
    requires_active_payment_method: Boolean,
})

const isDropdownOpen = ref(false);
const hoverId = ref(null);
const menuType = ref(null);
const megaMenuOpened = ref(false);

const openMegaMenu = () => {
    isDropdownOpen.value = true;
};

const handleMegaMenuOpen = () => {
    megaMenuOpened.value = true;
};

const closeMegaMenu = () => {
    setTimeout(() => {
        if (!megaMenuOpened.value) {
            isDropdownOpen.value = false;
        } else {
            megaMenuOpened.value = false;
        }
    }, 300);
};

const openDropdown = (element) => {
    menuType.value = element;
    isDropdownOpen.value = true;
};

const closeDropdown = (element) => {
    if (element === menuType.value) {
        // isDropdownOpen.value = false;
    }
};

userClickOutside( () => {
    isDropdownOpen.value = false;
});


let navigation = useNavigationStore();

const currentWhiteLabel = ref(usePage().props.value?.current_white_label);

const isActiveWhiteLabel = computed(() => {
    const currentUrl = usePage().url;
    return currentUrl.value.includes('white-label');
});
</script>

<style>
.pc-version {
    @apply small-laptop:hidden;
}
</style>
