<template>
    <div
        class="relative grid grid-cols-12 gap-10px bg-white dark:bg-mode-light py-15px px-30px pr-15px rounded-10px"
    >
        <div
            class="text-secondary-full text-xl dark:text-mode-secondary-light group-hover:text-white flex items-center gap-20px col-span-4 wide-tablet:col-span-8 mobile:col-span-12 mobile:order-1"
        >
            <site-type-logo :site-type="site.type" class="w-40px" />

            <tooltip class="cursor-pointer" :title="site.name">
                <h6>
                    <Link v-if="!canVisit"
                        :href="`/site/${site.id}`"
                        class="text-xl text-dark hover:text-primary-dark dark:text-light dark:hover:text-white"
                    >
                        {{ $filters.textLimit(site.name, 30) }}
                    </Link>
                    <a v-else
                        class="text-xl text-dark hover:text-primary-dark dark:text-light dark:hover:text-white"
                        :href="'https://'+site.name" target="_blank" rel="noopener noreferrer"
                    >
                        {{ $filters.textLimit(site.name, 30) }}
                    </a>

                </h6>
            </tooltip>

            <span class="inline-flex items-center mobile:ml-auto mobile:mr-2">
                <StateToolTip :state="site.state" :title="site.status_readable" />
            </span>
        </div>
        <div
            class="flex items-center justify-start flex-wrap gap-x-30px tablet:gap-x-20px wide-mobile:gap-x-10px gap-y-10px col-span-5 wide-tablet:col-span-12 wide-tablet:order-1 mobile:col-span-12 mobile:order-2"
        >
            <h6
                class="flex items-center text-base tablet:text-sm text-secondary-full dark:text-mode-secondary-light"
            >
                Server:
                <span
                    class="px-10px py-1 border-1 border-light dark:border-mode-base group-hover:border-transparent dark:group-hover:bg-mode-focus-dark group-hover:bg-white/10 divide-x divide-light dark:divide-mode-base group-hover:divide-primary-dark dark:group-hover:divide-dark rounded-md ml-1.5"
                >
                    <span
                        class="text-secondary-full dark:text-mode-secondary-light group-hover:text-white flex items-center max-w-[150px] truncate"
                    >
                        <cloud-provider-logo
                            :provider-name="site.server.provider_name"
                            class="w-20px mr-2 group-hover:brightness-[100]"
                        />
                        {{ site.server.name }}
                    </span>
                </span>
            </h6>
            <h6
                class="flex items-center flex-wrap text-base tablet:text-sm text-secondary-full dark:text-mode-secondary-light"
            >
                PHP:
                <span
                    class="px-10px py-1 border-1 border-light dark:border-mode-base group-hover:border-transparent dark:group-hover:bg-mode-focus-dark group-hover:bg-white/10 divide-x divide-light dark:divide-mode-base group-hover:divide-primary-dark dark:group-hover:divide-dark rounded-md ml-1.5"
                >
                    {{ site.php_version }}
                </span>
            </h6>
            <div class="flex items-center" v-if="site.tags?.length > 0">
                <tooltip class="cursor-pointer" :title="site.tags[0]?.name">
                    <span
                        class="text-base text-secondary-full dark:text-mode-secondary-light rounded-md"
                    >
                        # {{ $filters.textLimit(site.tags[0]?.name) }}
                    </span>
                </tooltip>

                <tooltip
                    class="text-xs cursor-pointer ml-2"
                    :title="
                        site.tags
                            .slice(1)
                            .map((tag) => tag.name)
                            .join(', ')
                    "
                >
                    <h6
                        class="text-base tablet:text-sm text-primary-dark dark:text-primary-light"
                        v-if="site.tags.length > 1"
                    >
                        +{{ site.tags.length - 1 }}
                    </h6>
                </tooltip>
            </div>
        </div>

        <div
            class="col-span-3 wide-tablet:col-span-4 mobile:col-span-12 mobile:order-3 mobile:justify-center flex justify-end items-center gap-x-10px"
        >
            <Link
                v-if="site.state === 'Initial'"
                :href="`/site/${site.id}`"
                class="inline-flex items-center justify-center rounded-10px border-primary-light dark:border-dark shadow-none min-h-50px wide-tablet:min-h-40px px-20px bg-success-light bg-opacity-10 hover:bg-opacity-20 dark:bg-dark text-base tablet:text-sm font-medium text-success-light dark:text-white hover:bg-success-light dark:hover:bg-mode-focus-light focus:outline-0 mobile:mr-15px"
            >
                Continue Setup
            </Link>
            <span
                v-else-if="site.state !== 'Error'"
                class="w-50px tablet:w-40px wide-tablet:absolute wide-tablet:right-30px wide-tablet:top-20px"
            >
                <ProgressBar :percentage="percentage" />
            </span>
            <Link
                v-else
                :href="route('site.overview',{server:site.server.id,site:site.id})"
                class="inline-flex items-center justify-center rounded-10px border-primary-light dark:border-dark shadow-none min-h-50px py-4 px-20px bg-success-light bg-opacity-10 hover:bg-opacity-20 dark:bg-dark text-base tablet:text-sm font-medium text-success-light dark:text-white hover:bg-success-light dark:hover:bg-mode-focus-light focus:outline-0"
            >
                View Details
            </Link>
            <Link
                v-if="site.state === 'Success'"
                :href="route('site.overview',{server:site.server.id,site:site.id})"
                class="inline-flex items-center justify-center rounded-10px border-primary-light dark:border-dark shadow-none min-h-50px py-4 px-20px bg-green-500 bg-opacity-100 hover:bg-opacity-90 dark:bg-dark text-base tablet:text-sm font-medium text-white hover:bg-green-600 dark:hover:bg-mode-focus-light focus:outline-0"
            >
                View Site
            </Link>
            <div
                class="flex items-center justify-center server-toggle-button pr-10px"
            >
                <SiteActions
                    :site="site"
                    :server="site.server"
                    position="right"
                >
                    <template #selector>
                        <div>
                            <button
                                type="button"
                                class="inline-flex items-center justify-center min-h-40px border-1 border-primary-light dark:border-dark text-sm font-medium rounded-10px shadow-none text-white dark:text-white bg-primary-light dark:bg-dark group-hover:bg-light group-hover:text-primary-light group-hover:border-light hover:bg-white hover:border-white hover:text-dark hover:shadow-md hover:shadow-primary-dark/30 dark:hover:shadow-secondary-full/10 transition ease-in-out duration-300 focus:outline-none disabled:opacity-75 disabled:cursor-not-allowed px-15px"
                                id="menu-button"
                                aria-expanded="true"
                                aria-haspopup="true"
                            >
                                <span class="sr-only">Open options</span>
                                <!-- Heroicon name: mini/ellipsis-horizontal -->
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke-width="1.5"
                                    stroke="currentColor"
                                    :data-site="props.site.id"
                                    class="w-6 h-6"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M6.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM18.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"
                                    />
                                </svg>
                            </button>
                        </div>
                    </template>
                </SiteActions>
            </div>

        </div>
    </div>
</template>

<script setup>
import ProgressBar from "@/Shared/Icons/ProgressBar.vue";
import { computed, onMounted, onUnmounted, ref } from "vue";
import { Link } from "@inertiajs/inertia-vue3";
import CloudProviderLogo from "@/Shared/Icons/CloudProviderLogo.vue";
import StateToolTip from "@/Shared/StateToolTip.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import SiteTypeLogo from "@/Shared/Icons/SiteTypeLogo.vue";
import {Inertia} from "@inertiajs/inertia";
import SiteActions from "@/Pages/Site/Components/SiteActions.vue";

const props = defineProps({
    site: {
        type: Object,
        required: true,
    },
    canVisit : {
        type: Boolean,
        default: false
    }
});

const status = ref("");

const percentage = ref(
    Number(props.site?.provision_percentage ?? 0)
);
if (props.site.has_migration) {
    percentage.value = Number(props.site?.migration_percentage ?? 0);
}
let dots = ref("...");
let interval = null;

console.log("site id : " + props.site.id, "site state: " + props.site.state);

onMounted(() => {
    interval = setInterval(() => {
        if (dots.value === ".") {
            dots.value = "..";
        } else if (dots.value === "..") {
            dots.value = "...";
        } else if (dots.value === "...") {
            dots.value = "....";
        } else if (dots.value === "....") {
            dots.value = "";
        } else if (dots.value === "") {
            dots.value = ".";
        }
    }, 700);
    if (window.Echo) {

        joinWebSocket('SiteStatusChanged','site.status.' + props.site.id)
        if (props.site.has_migration){
            joinWebSocket('SiteMigrationStatusChanged','site.migration.' + props.site.id)
        }else{
            joinWebSocket('SiteProvisioningStatusChanged','site.' + props.site.id)
        }
    }
});

function joinWebSocket(socket,channel){
     console.log(
            "joining",
            channel,
            socket
        );
        window.Echo.private(channel).listen(
            socket,
            (e) => {
                console.log(socket, e);
                if (e.percentage) {
                    percentage.value = e.percentage;
                    if (e.percentage === 100) {
                        Inertia.reload();
                    }
                }

                if (e.status) {
                    status.value = e.status;
                     if (e.status === 1) {
                        Inertia.reload();
                    }
                }
                if (e.state) {
                    props.site.state = e.state;
                }
                if (e.site?.status_readable) {
                    props.site.status_readable = e.site.status_readable;
                }
            }
        );
}

function leaveWebSocket(socket, channel) {
    console.log(
        "leaving",
        channel,
        socket
    );
    window.Echo.private(channel).stopListening(
        socket
    );

}

onUnmounted(() => {
    if (window.Echo) {
        leaveWebSocket('SiteStatusChanged','site.status.' + props.site.id)
        if (props.site.has_migration){
            leaveWebSocket('SiteMigrationStatusChanged','site.migration.' + props.site.id)
        }else{
            leaveWebSocket('SiteProvisioningStatusChanged','site.' + props.site.id)
        }
    }
    if (interval) {
        clearInterval(interval);
    }
});
</script>
