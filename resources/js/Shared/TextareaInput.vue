<template>
    <div class="w-full"
         :class="{'cursor-not-allowed opacity-50' : disabled }"
    >
        <label
            class="block text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none mb-2.5"
        >
            {{ label }}<span v-if="requiredField" class="text-danger">*</span>
        </label>
        <textarea :id="id"
                  :disabled="disabled"
                  class="flex-1 block text-base font-normal min-h-60px py-2 pr-25px border-1 w-full
                        focus:outline-none min-w-0 rounded-md shadow-none focus:shadow-none focus:ring-0
                        outline-none appearance-none focus-within:border-success-full autofill:bg-light
                        dark:autofill:bg-mode-base placeholder-gray-400 dark:placeholder-gray-500
                        bg-white dark:bg-mode-light text-dark dark:text-white border-secondary-light
                        dark:border-mode-focus-light"
                  :value="modelValue" @input="$emit('update:modelValue', $event.target.value)"
                  :class="{'!border-danger !bg-danger/5': error}"
                  :rows="rows"
                  :placeholder="placeholder"
        >
        </textarea>
        <Error :error="error" />
    </div>
</template>

<script setup>
import { v4 as uuid } from 'uuid';
import Error from "@/Shared/Error.vue";

defineProps({
    id: {
        type: String,
        default() {
            return `textarea-input-${uuid()}`
        },
    },
    error: String,
    label: String,
    modelValue: String,
    rows: {
        type: Number,
        default: 5,
    },
    placeholder: {
        type: String,
        default: null,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    requiredField: {
        type: Boolean,
        default: false
    }
})

function focus() {
    this.$refs.input.focus()
}

function select() {
    this.$refs.input.select()
}

</script>
