import {computed} from "vue";
import {asset} from "laravel-vapor";

export function useSiteTypeIcon(site_type) {
    const siteTypeIcon = computed(() => {
        switch (site_type) {
            case 'wordpress':
                return asset('img/wordpress-blue.svg')
            case 'laravel':
                return asset('img/laravel.svg')
            case 'custom-php':
                return asset('img/php_version.svg')
            case 'Other Provider':
                return asset('img/other_providers.svg')
        }
    })

    return {siteTypeIcon}
}
