import { defineStore } from 'pinia'
import axios from "axios";

export const useMegaMenuStore = defineStore({
    id: 'mega-menu',
    state: () => ({
        sites: {}, // Store sites per page as key-value pairs
        servers: {}, // Store servers per page as key-value pairs
        latestSiteResponse: {},
        latestServerResponse: {},
        sitesLoaded: false,
        teamId: null
    }),
    getters: {
        getSites: (state) => (page) => {
            return state.sites[`${state.teamId}_${page}`] || [];
        },
        getServers: (state) => (page) => {
            return state.servers[`${state.teamId}_${page}`] || [];
        },
        getLatestSiteResponse: (state) => {
            return state.latestSiteResponse;
        },
        getLatestServerResponse: (state) => {
            return state.latestServerResponse;
        },
        isSiteLoaded: (state) => (page) => {
            return !!state.sites[`${state.teamId}_${page}`];
        },
        isServerLoaded: (state) => (page) => {
            return !!state.servers[`${state.teamId}_${page}`];
        }
    },
    actions: {
        saveLatestServerResponse(serverData) {
            this.latestServerResponse = serverData;
        },

        saveLatestSiteResponse(siteData) {
            this.latestSiteResponse = siteData;
        },

        saveSites(page, sites) {
            if (this.isSiteLoaded(page)) return; // Check if the sites for the page are already loaded
            this.sites[page] = sites;
        },
        setTeamId(teamId) {
            this.teamId = teamId;
        },
        saveServers(page, servers) {
            if (this.isServerLoaded(page)) return; // Check if the servers for the page are already loaded
            this.servers[page] = servers;
        }
    }
});
