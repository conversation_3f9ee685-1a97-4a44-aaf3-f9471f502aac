<template>
    <!-- Pay invoice modal - never show for playground team -->
    <Modal
        @close="handleCloseInvoicePaymentModal"
        :show="openInvoicePaymentModal && !isPlaygroundTeam"
        :title="$t('Pay Bills')"
        :footer-button="true"
        :widthClass="'max-w-590px'">

        <!-- Choose card -->
        <div>
            <p class="text-dark dark:text-light mb-2">
                {{ $t('Choose Your Payment Method') }}
            </p>
            <div class="w-full max-w-lg flex justify-between">
                <Multiselect
                    v-model="invoicePaymentForm.paymentMethodId"
                    :placeholder="$t('Choose Your Payment Method')"
                    :label="$t('Payment Methods')"
                    :options="paymentMethods()"
                >
                    <template v-slot:singlelabel="{ value }">
                        <div class="multiselect-single-label rounded-md">
                            <img class="character-label-icon h-6 w-6 mr-2" :src="value.icon" alt="provider">
                            {{ value.card_no }}
                        </div>
                    </template>

                    <template v-slot:option="{ option }">
                        <img class="character-option-icon h-6 w-6 mr-2" :src="option.icon"> {{ option.card_no }}
                        <!-- inactive card warning -->
                        <tooltip title="Inactive Card" v-if="option.inactiveCard">
                            <span v-html="inactiveCardWarning"></span>
                        </tooltip>
                    </template>
                </Multiselect>
            </div>
        </div>

        <!-- Summary -->
        <div class="py-5">
            <p class="text-dark dark:text-light">
                {{ $t('You are paying for following invoice') }}: <br><code class="text-red-400 font-bold">{{ invoicePaymentForm.invoice?.invoice_number}}</code>
            </p>
            <div class="text-sm text-dark dark:text-white leading-7">
                <ul class="grid grid-cols-4 gap-4 px-5 mt-3">
                    <li class="col-span-3 list-disc">
                        <p>{{ invoicePaymentForm.invoice?.title }}</p>
                    </li>
                    <li class="col-span-1">
                        <p class="text-right">${{ invoicePaymentForm.invoice?.amount }}</p>
                    </li>
                </ul>
                <hr>
                <div class="flex justify-between px-5">
                    <p class="text-dark dark:text-light">{{ $t('Total Amount') }}:</p>
                    <p class="text-right">${{ invoicePaymentForm.invoice?.amount }}</p>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="flex justify-end">
                <btn
                    :loading="invoicePaymentForm.processing"
                    :disabled="invoicePaymentForm.processing"
                    @click.prevent="takePayment()"
                    :class="{'cursor-not-allowed opacity-50' : invoicePaymentForm.processing}"
                >
                    <span class="whitespace-nowrap">{{ invoicePaymentForm.processing ? $t('Taking Payment...') : $t('Pay Now') }}</span>
                </btn>
            </div>
        </template>

    </Modal>
</template>

<script setup>
import Btn from "@/Shared/Btn.vue";
import Modal from "@/Shared/Modal.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import Multiselect from "@vueform/multiselect";
import { usePage } from "@inertiajs/inertia-vue3";
import { computed } from "vue";

const props = defineProps({
    openInvoicePaymentModal: Boolean,
    invoicePaymentForm: Object,
    handleCloseInvoicePaymentModal: Function,
    handleInvoicePaymentModal: Function,
    takePayment: Function,
    paymentMethods: Function,
    inactiveCardWarning: Function
});

// Check if current team is a playground team
const isPlaygroundTeam = computed(() => {
    const { current_team } = usePage().props.value;
    return current_team?.email === '<EMAIL>';
});

</script>


<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
