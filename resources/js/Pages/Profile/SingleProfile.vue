<template>
    <Head title="Settings"/>
    <single-layout>
        <template v-slot:sidebar>
            <template
                v-if="navigation.current_team">
                <div class="min-h-100px flex items-center py-20px px-30px small-laptop:px-10px">
                    <span
                        class="inline-flex justify-center items-center h-50px min-w-50px focus:outline-none focus:border-none text-base font-normal text-dark dark:text-white">
                        <template v-if="$page?.props?.current_white_label">
                            <span
                                class="w-40px h-40px min-w-40px shirnk-0 rounded-full mr-2 small-laptop:mr-0 flex items-center justify-center relative">
                                <img
                                    class="w-40px h-40px min-w-40px shrink-0 bg-light dark:bg-mode-base rounded-full mr-10px flex items-center justify-center object-cover"
                                    :src="page.user?.profile_photo_url"
                                    :alt="page.user?.name"/>
                            </span>
                            <span v-if="page.user?.name" class="small-laptop:hidden">{{page.user?.name}}</span>
                        </template>
                        <template v-else>
                            <span
                                class="w-40px h-40px min-w-40px shirnk-0 rounded-full mr-2 small-laptop:mr-0 flex items-center justify-center relative">
                                <img
                                    class="w-40px h-40px min-w-40px shrink-0 bg-light dark:bg-mode-base rounded-full mr-10px flex items-center justify-center object-cover"
                                    :src="navigation.current_team.team_photo_url"
                                    :alt="navigation.current_team.name"/>
                            </span>
                            <span v-if="navigation.current_team" class="small-laptop:hidden">{{navigation.current_team.name}}</span>
                        </template>
                    </span>
                </div>
                <Sidebar @updateSearch="handleSearch" :filteredItems="filteredMenuItems" @onSelected="sidebarItems.activeItem = $event" :items="sidebarItems"></Sidebar>
            </template>
        </template>

        <template v-slot:content>
            <slot></slot>
        </template>


    </single-layout>

</template>

<script setup>
import SingleLayout from "@/Layouts/SingleLayout.vue";
import Sidebar from "@/Shared/Sidebar.vue";
import { useNavigationStore } from "@/stores/NavigationStore.js";
import {reactive, ref, computed, watch} from "vue";
import {usePage} from "@inertiajs/inertia-vue3";
import Fuse from 'fuse.js';
import { useI18n } from "vue-i18n";
const { t } = useI18n();
let navigation = useNavigationStore();

const searchKeyword = ref('');

const props = defineProps({
    active: String,
    title: {
        type: String,
        default: "Profile"
    },
})

const emit = defineEmits(['update:filteredItems']);
const page = usePage().props.value;
const permissions = Object.values(page?.access_permissions ?? {});
const sidebarItems = reactive({
    activeItem: props.active,
    data: [
        {
            title: t("User Profile"),
            name: "User Profile",
            icon: "xcloud xc-user",
            url: "/user/profile",
            isShow: true,
            keyword: ["Profile Update"]
        },
        {
            title: t("Security"),
            name: "Security",
            icon: "xcloud xc-password",
            isShow: true,
            subItems: [
                {
                    title: t("Password"),
                    name: "Password",
                    icon: "xcloud xc-password",
                    url: "/user/password",
                    isShow: true,
                    keyword: ["Password", "Change Password"]
                },
                {
                    title: t("Authentication"),
                    name: "Authentication",
                    icon: "xcloud xc-shield",
                    url: "/user/authentication",
                    isShow: true,
                    keyword: ["Authentication", "Two Factor Authentication"]
                },
                {
                    title: t("Browser Sessions"),
                    name: "Browser Sessions",
                    icon: "xcloud xc-browser",
                    url: "/user/browser-sessions",
                    isShow: true,
                    keyword: ["Browser Sessions", "Manage Active Browser Sessions", "Logout of All Sessions"]
                },
            ]
        },
        {
            title: t("Team"),
            name: "Team",
            icon: "xcloud xc-team",
            isShow: page?.current_white_label==null,
            subItems: [
                {
                    title: t("Management"),
                    name: "Management",
                    icon: "xcloud xc-team",
                    isShow: page?.current_white_label==null,
                    url: "/user/team-management",
                    keyword: ["Team Management", "Add New Team", "Edit Team", "Team Language Settings"]
                },
                {
                    title: t("Customization"),
                    name: "Customization",
                    icon: "xcloud xc-team",
                    url: "/user/customization",
                    isShow: page?.current_white_label==null,
                    keyword: ["Customization", "Disable Adminer", "Disable File Manager"]
                },
                {
                    title: t("Export"),
                    name: "Export",
                    icon: "xcloud xc-team",
                    url: "/user/export",
                    isShow: page?.current_white_label==null && page?.auth_user?.id === page?.current_team?.user_id,
                    keyword: ["Customization", "Disable Adminer", "Disable File Manager", "Export Site", "Export Server",  "Export"]
                }
            ]
        },
        {
            title: t("Vulnerable Sites"),
            name: "Vulnerable Sites",
            url: "/user/vulnerable-sites",
            isShow: permissions.includes('site:vulnerability-scan'),
            keyword: ["Sites", "Vulnerability Scanner", "Site Vulnerability"],
            icon: "xcloud xc-bug"
        },
        {
            title: t("SSH Key"),
            name: "SSH Key",
            icon: "xcloud xc-keys",
            url: "/user/ssh-key",
            isShow: permissions.includes('account:manage-ssh') || permissions.includes('server:manage-access'),
            keyword: ["SSH Keys", "Add New SSH Key"]
        },

        {
            title: t("Integrations"),
            name: "Integrations",
            icon: "xc-settings",
            isShow:  permissions.includes('account:manage-integrations'),
            subItems: [
                {
                    title: t("Server Provider"),
                    name: "Server Provider",
                    icon: "xcloud xc-server_provider",
                    url: "/user/server-provider",
                    isShow: (permissions.includes('server:add-provider') || permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider')) && page?.current_white_label==null,
                    keyword: ["Server Provider", "Add New Provider"]
                },
                {
                    title: t("Cloudflare"),
                    name: "Cloudflare",
                    icon: "xc-settings",
                    url: "/user/integration/cloudflare",
                    isShow: true,
                    keyword: ["Cloudflare", "New Cloudflare Integration"]
                },
                {
                    title: t("Storage Provider"),
                    name: "Storage Provider",
                    icon: "xcloud xc-file",
                    url: "/user/storage-provider",
                    isShow: true,
                    keyword: ["Storage Provider", "Add New Provider"]
                },
                {
                    title: t("Email Provider"),
                    name: "Email Provider",
                    icon: "xcloud xc-server_provider",
                    url: "/user/email-provider",
                    isShow: permissions.includes('server:add-provider') || permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider'),
                    keyword: ["Email Provider", "Add new Provider"]
                },
                {
                    title: t("Notification"),
                    name: "Notification",
                    icon: "xcloud xc-file",
                    url: "/user/notification-integration",
                    isShow: page?.current_white_label==null,
                    keyword: ["Notification", "Slack Integration", "WhatsApp Integration"]
                },{
                    title: t("Others"),
                    name: "Others",
                    url: "/integration/others",
                    isShow: permissions.includes('site:manage-caching'),
                    keyword: [
                        "Others",
                        "Add New Item",
                        "Item Management",
                        "Item Settings",
                        "Item Configuration"
                    ]
                },
          ]
        },
        {
          title: "Add-ons",
          name: "Addons",
          icon: "xc-settings",
          isShow:  permissions.includes('account:manage-integrations'),
          subItems: [
            {
              title: t("SMTP Emails"),
              name: "SMTP Emails",
              icon: "xcloud xc-server_provider",
              url: "/user/email-provider",
              isShow: permissions.includes('server:add-provider') || permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider'),
              keyword: ["SMTP Emails" , "Email Provider", "Add new Provider"]
            },
            {
              title: t("Mailbox"),
              name: "Mailbox",
              icon: "xcloud xc-server_provider",
              url: "/addons/mailboxes",
              isShow: permissions.includes('server:add-provider') || permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider'),
              keyword: ["Mailbox" , "Email Inbox", "Add new Provider"]
            },
            {
              title: t("Storage Provider"),
              name: "xCloud Storage Provider",
              url: "/addons/s3-storage-provider",
              isShow: permissions.includes('server:add-provider') || permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider'),
              keyword: ["Storage Provider" , "Add new Provider"]
            }
          ]
        },
        {
            title: t("Billing"),
            name: "Billing",
            icon: "xc-invoice",
            isShow:  permissions.includes('account:manage-integrations'),
            subItems: [
                {
                    title: t("Bills & Payment"),
                    name: "Bills & Payment",
                    url: "/user/bills-payment",
                    isShow: permissions.includes('billing:create') || permissions.includes('billing:update'),
                    keyword: ["Bills and Payment", "Billing History", "Add a Payment Method"]
                },
                {
                    title: t("Invoices"),
                    name: "Invoices",
                    url: "/user/detailed-invoices",
                    isShow: permissions.includes('billing:create') || permissions.includes('billing:update'),
                    keyword: ["Detailed Invoices"]
                },
                {
                    title: t("Manual Invoices"),
                    name: "Manual Invoices",
                    url: "/user/manual-invoices",
                    isShow: page?.support_manual_billing && (permissions.includes('billing:create') || permissions.includes('billing:update')),
                    keyword: ["Manual Invoices"]
                }
            ]
        },
        {
          title: t("Subscriptions"),
          name: "Subscriptions",
          icon: "xc-invoice",
          isShow:  permissions.includes('account:manage-integrations'),
          subItems: [
            {
                title: t("Team Packages"),
                name: "Team Packages",
                icon: "xcloud xc-server_provider",
                url: "/user/team-packages",
                keyword: ["Team Packages"],
                isShow: page?.current_white_label==null,
            },
            {
                title: t("Team Products"),
                name: "Team Products",
                icon: "xc-settings",
                url: "/user/team-products",
                isShow: true,
                keyword: ["Team Products"],
            },
            {
                title: t("Email Subscriptions"),
                name: "Email Subscriptions",
                icon: "xc-settings",
                url: "/user/email-subscriptions",
                keyword: ["Email Subscriptions", "Billing History"],
                isShow: page?.current_white_label==null,
            },
          {
                title: t("Patchstack Subscriptions"),
                name: "Patchstack Subscriptions",
                icon: "xc-settings",
                url: "/user/patchstack-subscriptions",
                keyword: ["Patchstack Subscriptions"],
                isShow: page?.current_white_label==null,
            },
            {
              title: t("Whitelabel"),
              name: "Whitelabel",
              icon: "xc-settings",
              url: "/user/whitelabel-subscriptions",
              keyword: ["Whitelabel Subscriptions"],
              isShow: page?.current_white_label==null && page?.has_active_white_label_subscription,
            },
          ]
        },
        {
            title: t("Notifications"),
            name: "Notifications",
            icon: "xc-notifications",
            url: "/user/notifications",
            isShow: permissions.includes('account:manage-notifications'),
            keyword: ["Server Notifications", "Site Notifications", "Other Notifications"]
        },
        {
            title: t("All Events"),
            name: "All Events",
            icon: "xc-analytics",
            url: "/user/all-events",
            isShow: true,
            keyword: ["All Events"]
        },
        {
            title: t("My Blueprints"),
            name: "My Blueprints",
            icon: "xc-blueprint",
            url: "/blueprints",
            isShow: permissions.includes('site:blueprint-manage'),
            keyword: ["My Blueprints", "Create New Blueprint"]
        },
        /*{
            title: $t("Archive Servers"),
            name: "Archive Servers",
            icon: "xc-cloud",
            url: route('user.archive-servers'),
            isShow: permissions.includes('account:manage-archive-servers'),
            keyword: ["Archive Servers"]
        },*/
    ]

});

const handleSearch = (newSearch) => {
    searchKeyword.value = newSearch;
};

const itemsToSearch = sidebarItems.data.flatMap(item => {
    const combinedItem = {
        ...item,
        subItems: item.subItems || [],
    };

    const allItems = [combinedItem, ...combinedItem.subItems];

    return allItems.flatMap(currentItem => {
        const keywords = currentItem.keyword || [];
        return keywords.map(keyword => ({
            icon: currentItem.icon,
            isShow: currentItem.isShow,
            keyword: keyword,
            name: currentItem.title,
            title: keyword,
            url: `${currentItem.url}#:~:text=${encodeURIComponent(keyword)}`,
        }));
    });
});

const fuse = new Fuse(itemsToSearch, {
    keys: ["keyword"],
    includeScore: true,
    minMatchCharLength: 3,
});

const filteredMenuItems = computed(() => {
    if (!searchKeyword.value) return [];

    const results = fuse.search(searchKeyword.value);
    const items = results.map(result => result.item).filter(item => item.isShow);

    // Reconstruct the hierarchical structure
    const filteredMenu = [];
    items.forEach(matchedItem => {
        if (matchedItem?.subItems && matchedItem?.subItems?.length > 0) {
            matchedItem.subItems.forEach(subItem => {
                if (!filteredMenu.some(item => item.name === subItem.name)) {
                    filteredMenu.push({ ...subItem });
                }
            });
        } else {
            if (!filteredMenu.some(item => item.name === matchedItem.name)) {
                filteredMenu.push({ ...matchedItem });
            }
        }
    });

    return filteredMenu;
});

watch(filteredMenuItems, (newFilteredItems) => {
    emit('update:filteredItems', newFilteredItems);
});

</script>
