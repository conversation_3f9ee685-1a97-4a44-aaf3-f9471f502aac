<template>
  <single-profile active="Email Provider">
    <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
      <div class="flex justify-between items-center gap-10px p-30px w-full">
        <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{ $t('Email Provider') }}</h2>
      </div>
      <div class="flex flex-col gap-50px p-50px wide-mobile:p-30px mobile:p-20px w-full">
        <div class="flex justify-between items-center gap-15px w-full wide-mobile:flex-wrap">
          <div class="flex flex-col gap-10px">
            <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('List of Email Providers') }}</h3>
            <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
              {{ $t('Find all the email providers associated with your account here.') }}
            </p>
          </div>
          <button v-if="permissions.includes('server:add-provider')" @click.prevent="addEmailProviderModal = true"
                  class="inline-flex items-center justify-center min-h-50px tablet:min-h-50px border-1 border-primary-light
                            text-lg px-25px py-1 font-medium rounded-10px shadow-none text-primary-light bg-transparent
                            hover:bg-primary-light hover:text-white hover:shadow-lg hover:shadow-primary-light/30 transition
                            ease-in-out duration-300 focus:outline-none leading-tight flex-nowrap whitespace-nowrap">
            <span>{{ $t('Add New Provider') }}</span>
          </button>
        </div>
        <div class="flex flex-col gap-30px">
          <div class="rounded-md flex flex-col">
            <div class="overflow-x-auto w-full overflow-y-hidden">
              <table class="w-full">
                <thead class="bg-primary-light dark:bg-dark">
                <tr class="divide-x divide-light dark:divide-dark">
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Label') }}
                  </th>
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Provider') }}
                  </th>
                  <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Username/Domain') }}
                  </th>
                  <th v-if="permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider')"
                      class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                    {{ $t('Actions') }}
                  </th>
                </tr>
                </thead>
                <tbody class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                <tr v-for="provider in providers?.data"
                    :key="provider.id"
                    class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                >
                  <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                    {{ provider.label ?? '-'}}
                  </td>
                  <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                    {{ provider.provider }}
                  </td>
                  <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                      <span
                          class="text-secondary-full dark:text-mode-secondary-light group-hover:text-white flex items-center mb-10px">
                          {{ getUserNameOrDomain(provider) }}
                      </span>
                  </td>
                  <td v-if="permissions.includes('server:delete-provider') || permissions.includes('server:edit-provider')"
                      class="relative w-14 px-30px py-20px sm:w-16 sm:px-8"
                  >
                      <span class="flex gap-30px">
                        <button v-if="permissions.includes('server:edit-provider')
                                      && provider?.plan !== 'xcloud_100_emails'"
                                @click.prevent="edit(provider)"
                                class="relative group cursor-pointer mt-1.5">
                            <span class="text-sm flex text-secondary-light group-hover:text-white">
                              <i class="xcloud xc-edit"></i>
                            </span>
                        </button>
                        <Tooltip :title="provider?.provider === 'xCloud Managed Email Service' ? 'Cancel Subscription' : ''"
                                 color="danger"
                        >
                          <button v-if="permissions.includes('server:delete-provider')
                                      && provider?.plan !== 'xcloud_100_emails'"
                                  @click.prevent="destroy(provider)"
                                  class="relative group cursor-pointer mt-1.5"
                          >
                            <span class="text-sm flex text-secondary-light group-hover:text-white">
                              <i class="xcloud xc-delete"></i>
                            </span>
                          </button>
                        </Tooltip>
<!--                        <button v-if="permissions.includes('server:delete-provider')"-->
<!--                                @click.prevent="testEmailModal(provider)"-->
<!--                                class="relative group cursor-pointer mt-1.5"-->
<!--                                title="Send Test Email"-->
<!--                        >-->
<!--                          <span class="text-sm flex text-secondary-light group-hover:text-white">-->
<!--                            <i class="xcloud xc-email"></i>-->
<!--                          </span>-->
<!--                        </button>-->
                      </span>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
          <!-- pagination -->
          <pagination :links="providers?.links"/>
        </div>
      </div>
    </div>
  </single-profile>

  <!-- Add Provider Modal -->
  <Modal
      @footerClick="addEmailProvider"
        :footer-button="true"
      :footer-button-title="`${is_edit ? $t('Update') : (`${emailProviderForm.provider === 'xCloud Managed Email Service' ? $t('Buy & Add') : $t('Add')}`)} `+$t('Email Provider')"
      :closeable="true"
      :loading="emailProviderForm.processing"
      @close="addEmailProviderModal = false; is_edit = false; emailProviderForm.reset()"
      :show="addEmailProviderModal"
      :widthClass="'max-w-850px'"
      :title="`${is_edit? $t('Edit') : $t('Add')} `+$t('Email Provider')"
  >

    <div class="flex flex-col gap-30px">
      <Create :form="emailProviderForm" :x-cloud-email-provider-plans="xcloudEmailProviderPlans" :is-editing="is_edit"/>
    </div>
  </Modal>

  <!-- Send Email Modal-->
<!--  <Modal-->
<!--      @close="openEmailModal = false"-->
<!--      @footerClick="sendTestEmail"-->
<!--      footer-button-title="Send Test Email"-->
  <!--      :footer-button="true"
      :show="openEmailModal"-->
<!--      title="Send Test Email"-->
<!--      :widthClass="'max-w-850px'">-->
<!--    <TestEmail :form="emailTestForm"/>-->
<!--  </Modal>-->
</template>
<script setup>
import {ref} from "vue";
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import Modal from "@/Shared/Modal.vue"
import {useForm, usePage} from '@inertiajs/inertia-vue3';
import Pagination from "@/Shared/Pagination.vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import {useCloudProviderIcon} from "@/Composables/useCloudProviderIcon";
import Button from "@/Jetstream/Button.vue";
import Create from "@/Pages/Profile/EmailProvider/Create.vue";
import TestEmail from "@/Pages/Profile/EmailProvider/TestEmail.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
  team: Object,
  providers: {
    type: Object,
    required: true
  },
  uniqueProviders: {
    type: Object,
  },
  access_permissions: {
    type: Object,
    required: true
  },
  xcloudEmailProviderPlans: {
    type: Object,
    required: true
  },
  subAccountEmail: {
    type: String
  },
});
const permissions = Object.values(props?.access_permissions ?? {});
const {current_white_label} =  usePage().props?.value;
let openServerProviderModal = ref(false)
let addEmailProviderModal = ref(false)
const is_edit = ref(false);
const test_email_provider_id = ref(null);



const openEmailModal = ref(false);

const closeModal = () => {
  openServerProviderModal.value = false;
  is_edit.value = false;
}

const selectedPlan = ref('xcloud_1000_emails');

const emailProviderForm = useForm({
  provider_type: 'Own SMTP',
  provider: current_white_label ? 'Mailgun' : 'xCloud Managed Email Service',
  host: '',
  port: '',
  username: '',
  password: '',
  domain: '',
  api_key: '',
  label: '',
  selected_plan: selectedPlan.value,
});

const addEmailProvider = () => {
  if (is_edit.value) {
    emailProviderForm.put(route('team.email_provider.update', is_edit.value), {
      preserveScroll: true,
      onSuccess: () => {
        addEmailProviderModal.value = false;
      }
    });
  } else {
    emailProviderForm.put(route('team.email_provider.store'), {
      preserveScroll: true,
      onSuccess: () => {
        addEmailProviderModal.value = false;
      }
    });
  }

}

const edit = (provider) => {
  emailProviderForm.provider_type = provider.provider_type;
  emailProviderForm.provider = provider.provider;
  emailProviderForm.host = provider.host;
  emailProviderForm.port = provider.port;
  emailProviderForm.username = provider.username;
  emailProviderForm.password = provider.password;
  emailProviderForm.domain = provider.domain;
  emailProviderForm.api_key = provider.api_key;
  emailProviderForm.label = provider.label;
  is_edit.value = provider.id;

  emailProviderForm.selected_plan = provider.plan;

  addEmailProviderModal.value = true;
}
let items = () => {
  let providers = [];
  for (let key in props.providers.data) {
    const {cloudProviderIcon} = useCloudProviderIcon(props.providers.data[key].provider);
    providers.push({
      value: props.providers.data[key].id,
      name: props.providers.data[key].name,
      icon: cloudProviderIcon
    })
  }
  return providers;
}

const destroy = (provider) => {
  useFlash().deleteConfirmation({
    title: t('Are you sure?'),
    text: t("You won't be able to revert this!"),
    btn_text: t('Yes, Remove!'),
  }, () => {
    Inertia.delete(route('team.email_provider.destroy', [provider.id]), {
      preserveScroll: true,
      onSuccess: () => {
        useFlash().success('Email Provider Removed Successfully')
      }
    })
  });
}

const emailTestForm = useForm({
  from_email: '',
  email: '',
  provider: ''
});

function testEmailModal(provider) {
  openEmailModal.value = true;
  test_email_provider_id.value = provider.id;
  emailTestForm.from_email = provider.provider === 'xCloud Managed Email Service' ? props.subAccountEmail : '';
  emailTestForm.provider = provider;
}

function getUserNameOrDomain(provider) {
  if (provider.username) {
    return provider.username;
  } else {
    return provider.domain;
  }
}

function sendTestEmail() {
  emailTestForm.post(route('team.email_provider.send_test_email', test_email_provider_id.value), {
    onSuccess: (response) => {
      openEmailModal.value = false;
        if (response.props.jetstream?.flash?.type === 'error') {
            useFlash().error('Your credentials are incorrect.');
        } else {
            useFlash().success('Test Email Sent Successfully');
        }

      test_email_provider_id.value = null;
      emailTestForm.reset();
    },
  });
}

</script>


<style>
@import '@vueform/multiselect/themes/tailwind.css';

.multiselect.is-disabled {
    @apply bg-white dark:bg-mode-light;
}
</style>
