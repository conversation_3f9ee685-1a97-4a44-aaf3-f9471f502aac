<template>
    <single-profile :active="activeItem">
        <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
            <div class="flex justify-between items-center gap-10px p-30px w-full">
                <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">{{title}}</h2>
            </div>
            <div class="flex flex-col gap-30px p-50px wide-mobile:p-30px mobile:p-20px w-full">
                <div class="flex flex-wrap gap-x-5 gap-y-3 flex-row justify-between">
                  <h3 class="text-lg text-dark dark:text-white leading-none inline-flex items-center">
                    {{ $t('Find complete, downloadable receipts of your regular payments.') }}
                  </h3>
                  <div class="ml-auto flex flex-wrap justify-end items-center gap-10px w-30" >
                    <!-- GeneralInvoice date filter -->
                    <div class="border-1 border-primary-light dark:border-dark px-2 py-1">
                      <SelectorTitleDropdown :selected-item="invoiceDateFilter">
                        <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                          <div class="p-2px">
                            <SelectItem
                                title="All Invoices"
                                :is-active="invoiceDateFilter === 'All Invoices'"
                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                            />
                            <SelectItem
                                title="This Month"
                                :is-active="invoiceDateFilter === 'This Month'"
                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                            />
                            <SelectItem
                                title="Last Month"
                                :is-active="invoiceDateFilter === 'Last Month'"
                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                            />
                            <SelectItem
                                title="Last 6 Months"
                                :is-active="invoiceDateFilter === 'Last 6 Months'"
                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                            />
                            <SelectItem
                                title="Last 1 Year"
                                :is-active="invoiceDateFilter === 'Last 1 Year'"
                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                            />
                          </div>
                        </div>
                      </SelectorTitleDropdown>
                    </div>

                    <!-- GeneralInvoice status filter -->
                    <div class="border-1 border-primary-light dark:border-dark px-2 py-1">
                      <SelectorTitleDropdown :selected-item="invoiceStatusFilter">
                        <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                          <div class="p-2px">
                            <SelectItem
                                title="Paid Invoices"
                                :is-active="invoiceStatusFilter === 'Paid Invoices'"
                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                            />
                            <SelectItem
                                title="Unpaid Invoices"
                                :is-active="invoiceStatusFilter === 'Unpaid Invoices'"
                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                            />
                            <SelectItem
                                title="Failed Invoices"
                                :is-active="invoiceStatusFilter === 'Failed Invoices'"
                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                            />
                            <SelectItem
                                title="All Invoices"
                                :is-active="invoiceStatusFilter === 'All Invoices'"
                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                            />
                          </div>
                        </div>
                      </SelectorTitleDropdown>
                    </div>
                  </div>
                </div>
                <div class="flex flex-col gap-30px">
                    <div class="rounded-md flex flex-col">
                        <div class="overflow-x-auto w-full overflow-y-hidden">
                            <table class="w-full">
                                <thead class="bg-primary-light dark:bg-dark">
                                <tr class="divide-x divide-light dark:divide-dark">
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                      {{ $t('Invoice No') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Date') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Title') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Amount') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Status') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Due Date') }}
                                    </th>
                                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                                        {{ $t('Actions') }}
                                    </th>
                                </tr>
                                </thead>
                                <tbody class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                                <tr
                                    v-for="invoice in invoices?.data"
                                    :key="invoice?.id"
                                    class="divide-x divide-light dark:divide-dark group hover:bg-primary-light dark:hover:bg-dark"
                                >

                                      <td class="px-30px capitalize py-20px text-left text-base font-normal text-dark dark:text-white
                                        group-hover:text-white position-relative">
                                        {{ invoice?.invoice_number ?? invoice?.reference_no }}
                                          <CopyButton
                                              position-class=""
                                              :content="invoice?.invoice_number ?? invoice?.reference_number"
                                              align="top"
                                              color="primary"
                                              :hideCopyText="true"
                                          />
                                      </td>
                                    <td
                                        class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                        {{ invoice?.date }}
                                    </td>
                                    <td
                                        class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                        {{ invoice?.title }}
                                    </td>
                                    <td
                                        class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                        ${{ invoice?.amount }}
                                    </td>

                                    <td
                                        class="px-20px capitalize py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                                        <span v-if="invoice?.status === 'refunded' || invoice?.status === 'paid'"
                                              class="text-success-full group-hover:text-white px-6 py-1.5 rounded-3xl bg-success-full/10 group-hover:bg-success-full">
                                          {{ invoice?.status_readable }}
                                        </span>
                                        <span
                                            v-else-if="invoice?.status === 'cancelled' || invoice?.status === 'failed' || invoice?.status === 'payment_failed'"
                                            class="text-danger group-hover:text-white px-6 py-1.5 rounded-3xl bg-danger/10 group-hover:bg-danger">
                                          {{ invoice?.status_readable }}
                                        </span>
                                        <span v-else
                                              class="text-warning group-hover:text-white px-6 py-1.5 rounded-3xl bg-warning/10 group-hover:bg-warning">
                                          {{ invoice?.status_readable }}
                                        </span>
                                    </td>
                                    <td class="px-30px py-20px text-base font-normal text-dark dark:text-white group-hover:text-white text-center">
                                        {{ invoice?.due_date && invoice?.status !== 'paid' ? dayjs(invoice?.due_date).format('M/D/YY') : '' }}
<!--                                        <br v-if="invoice?.due_date">-->
                                        {{ invoice?.due_date && invoice?.status !== 'paid' ? ' - ' + dayjs(invoice?.due_date).fromNow() : (invoice?.status === 'paid' ? '-' : 'No Due Date') }}
                                    </td>

                                    <td class="py-4 whitespace-nowrap px-30px">
                                      <div class="grid grid-cols-2 gap-x-5">
                                        <button v-if="invoiceIsPayable(invoice)"
                                                @click.prevent="handleInvoicePayment(invoice)"
                                                class="relative group cursor-pointer mt-1.5 underline text-dark dark:text-white group-hover:text-white whitespace-nowrap">
                                          {{ $t('Pay Now') }}
                                        </button>

                                        <a v-if="invoice?.type === 'manual'" :href="route('user.invoice.manual.download',[invoice.invoice_number])"
                                           class="relative group cursor-pointer mt-1.5 underline text-dark dark:text-white group-hover:text-white">
                                          {{ $t('Download') }}
                                        </a>
                                        <a v-else :href="isStaging || isAdmin || isImpersonating ? route('user.invoice.preview-download', [invoice.invoice_number]) : route('user.invoice.download',[invoice.invoice_number])"
                                           class="relative group cursor-pointer mt-1.5 underline text-dark dark:text-white group-hover:text-white">
                                          {{ $t('Download') }}
                                        </a>

                                        <button v-if="invoiceIsCancellable(invoice)"
                                                @click.prevent="cancelInvoice(invoice)"
                                                class="relative group cursor-pointer mt-1.5 underline text-dark dark:text-white group-hover:text-white">
                                          {{ $t('Cancel') }}
                                        </button>

                                        <a v-if="isStaging || isAdmin || isImpersonating"
                                         target="_blank"
                                         :href="`/admin/resources/general-invoices/${invoice.id}`"
                                         class="relative group cursor-pointer mt-1.5 underline text-dark dark:text-white group-hover:text-white">
                                          {{ $t('View Nova') }}
                                        </a>
                                      </div>
                                    </td>
                                  </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- Pagination -->
                    <pagination
                        :links="invoices?.links"
                        :query-string="generateFilterQueryString()"
                    />
                </div>
            </div>
        </div>
    </single-profile>

    <!-- Invoice Payment Modal -->
    <pay-invoice-modal
        :openInvoicePaymentModal="openInvoicePaymentModal"
        :invoice-payment-form="invoicePaymentForm"
        :payment-methods="paymentMethods"
        :handle-invoice-payment-modal="handleInvoicePayment"
        @close="handleCloseInvoicePaymentModal"
        :take-payment="takeInvoicePayment">
    </pay-invoice-modal>
</template>
<script setup>
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime"
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import Button from "@/Jetstream/Button.vue";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import Pagination from "@/Shared/Pagination.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import SelectorTitleDropdown from "@/Shared/SelectorTitleDropdown.vue";
import {ref} from "vue";
import {Inertia} from "@inertiajs/inertia";
import CopyAbleText from "@/Shared/CopyAbleText.vue";
import Modal from "@/Shared/Modal.vue";
import Btn from "@/Shared/Btn.vue";
import {useFlash} from "@/Composables/useFlash";
import CopyButton from "@/Shared/CopyButton.vue";
import TextInput from "@/Shared/TextInput.vue";
import Multiselect from "@vueform/multiselect";
import Tooltip from "@/Shared/Tooltip.vue";
import {useCloudProviderIcon} from "@/Composables/useCloudProviderIcon";
import {asset} from "laravel-vapor";
import {useCreditCardIcon} from "@/Composables/useCreditCardIcon";
import PayInvoiceModal from "@/Pages/Profile/PayInvoiceModal.vue";

const form = useForm({
  invoice_id: null
});

const props = defineProps({
  bills: Array,
  invoices: [Array,Object],
  invoiceDateFilter:{
    type: String,
    default: 'All Invoices'
  },
  activeItem:{
    type: String,
    default: 'Invoices'
  },
  title:{
    type: String,
    default: 'Detailed Invoices'
  },
  invoiceStatusFilter:{
    type: String,
    default: 'Paid Invoices'
  },
  paymentMethods: {
    type: Object,
    default: () => {}
  },
  defaultCard: {
    type: Object,
    default: () => {}
  },
  isStaging: {
    type: Boolean,
    default: false
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  isImpersonating: {
     type: Boolean,
     default: false
  },
});


let paymentMethod = ref(props.defaultCard?.id)
const invoiceDateFilter = ref(props?.invoiceDateFilter);
const invoiceStatusFilter = ref(props?.invoiceStatusFilter);
let openInvoicePaymentModal = ref(false);

const invoicePaymentForm = useForm({
  invoice: null,
  paymentMethodId: props.defaultCard?.id,
});

let paymentMethods = () => {
  let paymentMethods = [];
  for (let key in props.paymentMethods) {
    paymentMethods.push({
      value: props.paymentMethods[key].id,
      card_no: props.paymentMethods[key].card_no,
      expires_at: props.paymentMethods[key].expiry_month + '/' + props.paymentMethods[key].expiry_year,
      brand: props.paymentMethods[key].brand,
      icon: creditCardIcon(props.paymentMethods[key].brand),
      disabled: !(props.paymentMethods[key].status === 'active'),
      inactiveCard: !(props.paymentMethods[key].status === 'active')
    })
  }
  return paymentMethods;
}

const creditCardIcon = (brand) => {
  const {creditCardIcon} =  useCreditCardIcon(brand);
  return creditCardIcon.value;
}

const inactiveCardWarning = () => {
  return '<img src="' + asset('img/warning.svg') + '" class="ml-1.5 h-4 w-4" alt="inactive card warning" />';
};

const onInvoiceDateFilterChanged = (filter) => {
    invoiceDateFilter.value = filter;
    loadFilteredData();
}

const onInvoiceStatusFilterChanged = (filter) => {
  invoiceStatusFilter.value = filter;
  loadFilteredData();
}

function loadFilteredData(type = 'created_at'){
  Inertia.get(route("user.detailedInvoices"), {
    invoiceDateFilter: invoiceDateFilter.value,
    invoiceStatusFilter: invoiceStatusFilter.value,
    filterType: type,
  });
}

const generateFilterQueryString = () => {
    let queryString = '';

    if (invoiceDateFilter.value) {
        queryString += `&invoiceDateFilter=${invoiceDateFilter.value}&filterType=created_at`;
    }

    if (invoiceStatusFilter.value) {
        queryString += `&invoiceStatusFilter=${invoiceStatusFilter.value}&filterType=created_at`;
    }

    return queryString;
}

const invoiceIsPayable = (invoice) => {
  return invoice?.status !== 'paid' && invoice?.status !== 'cancelled' && invoice?.status !== 'refunded';
}

const invoiceIsCancellable = (invoice) => {
  return invoice?.status !== 'paid' && invoice?.status !== 'cancelled' && invoice?.status !== 'refunded';
}

const downloadInvoice = (invoice) => {
    form.invoice_id = invoice.invoice_number;
    form.post(route('user.invoice.download', [invoice.invoice_number]));
}

const handleCloseInvoicePaymentModal = () => {
  openInvoicePaymentModal.value = false;
}

const handleInvoicePayment = (invoice) => {
  invoicePaymentForm.invoice = invoice;
  openInvoicePaymentModal.value = true;
}

const takeInvoicePayment = () => {
  if(invoicePaymentForm.invoice?.status === 'paid') {
    useFlash().warning('This invoice is already paid.')
    return;
  }

  invoicePaymentForm.post(route('api.invoice.pay', [invoicePaymentForm.invoice?.invoice_number]),{
    preserveScroll: true,
    onSuccess: (response) => {
      const downloadInvoiceRoute = route('user.invoice.download', [invoicePaymentForm.invoice?.invoice_number]);
      // console.log(response.props.jetstream.flash)

      if(response.props.jetstream.flash.success || response.props.jetstream.flash.error){
          useFlash().swal().fire({
            icon: response.props.jetstream.flash.error ? 'error' : "success",
            title: response.props.jetstream.flash.error
                ? 'Failed to process payment. Please check if your card is valid'
                : "Your payment has been processed successfully.",
            html: response.props.jetstream.flash.error === '' ?
                `
                  <p class="text-sm text-dark dark:text-white leading-tight">
                    You can download the invoice from
                    <a href="${downloadInvoiceRoute}" class="underline text-secondary-full dark:text-secondary-light">here</a>
                  </p>
                `
                : null
            ,
            showCancelButton: false,
            showConfirmButton: false,
            showCloseButton: true,
          });

          openInvoicePaymentModal.value = false;
          loadFilteredData();
      }
    },
  });
}

const cancelInvoice = (invoice) => {
  if(invoice.status === 'paid') {
    useFlash().warning('You can not cancel a paid invoice.')
    return;
  }

  useFlash().swal().fire({
    title: 'Are you sure?',
    text: "You won't be able to revert this!",
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Yes, cancel it!',
    cancelButtonText: 'No, keep it'
  }).then((result) => {
    if (result.isConfirmed) {
      axios.post(route('api.invoice.cancel', [invoice.id]))
          .then((response) => {
            console.log(response)
            useFlash().swal().fire({
              icon: response.data.status === 'error' ? 'error' : "success",
              title: response.data.status === 'error'
                  ? response.data.message
                  : "Your invoice has been cancelled successfully.",
              showCancelButton: false,
              showConfirmButton: false,
              showCloseButton: true,
            });
            loadFilteredData();
          })
          .catch((error) => {
            useFlash().swal().fire({
              icon: 'error',
              title: 'Oops...',
              text: 'Something went wrong!'
            })
          })
    }
  })
}

</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
