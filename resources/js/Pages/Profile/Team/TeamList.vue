<template>
    <div
        class="grid grid-cols-3 small-laptop:grid-cols-2 tablet:grid-cols-1 gap-30px mobile:gap-15px mobile:grid-cols-1"
    >
        <div
            class="p-30px mobile:py-10 w-full rounded-10px border-2 border-light dark:border-mode-base bg-white dark:bg-mode-base flex flex-col hover:drop-shadow-grid-box dark:hover:bg-dark"
            v-for="team in teamList"
            :key="team.id"
        >
            <div
                class="flex justify-between items-center gap-10px mobile:gap-2 mobile:flex-col mobile:items-start mobile:relative"
                :class="team.tags > 0 ? 'mb-3' : 'mb-30px mobile:mb-3'"
            >
                <div
                    class="inline-flex justify-center items-center text-base font-normal text-dark dark:text-white group mobile:w-full"
                >
                    <span
                        class="w-50px h-50px wide-mobile:w-35px wide-mobile:h-35px mobile:w-8 mobile:h-8 shrink-0 rounded-full mr-10px wide-mobile:mr-1.5 mobile:mr-2 flex items-center justify-center"
                    >
                          <Link v-if="team.can_view" :href="route('user.team.show', team.id)">
                            <img
                                :alt="team.name"
                                :src="team.team_photo_url"
                                class="w-50px h-50px shrink-0 wide-mobile:w-35px wide-mobile:h-35px mobile:w-8 mobile:h-8 bg-light dark:bg-mode-base rounded-full mr-10px flex items-center justify-center object-cover"
                            />
                          </Link>
                         <img
                             v-else
                             :alt="team.name"
                             :src="team.team_photo_url"
                             class="w-50px h-50px shrink-0 wide-mobile:w-35px wide-mobile:h-35px mobile:w-8 mobile:h-8 bg-light dark:bg-mode-base rounded-full mr-10px flex items-center justify-center object-cover"
                         />
                    </span>
                    <div class="flex flex-col mobile:w-full">
                        <div class="text-2xl small-laptop:text-xl wide-mobile:text-lg mobile:text-base font-semibold flex items-center flex-wrap mobile:flex-nowrap mobile:gap-1">
                            <Link :href="route('user.team.show', team.id)" class="mobile:text-base">{{team.name}}</Link>
                            <span v-if="user.current_team_id === team.id" class="ml-2 shrink-0 text-lg mobile:text-base text-success-full flex justify-center items-center no-underline">
                                <i class="xcloud xc-tick-o"></i>
                            </span>
                            <p class="ml-2 text-primary-light bg-primary-light/10 text-sm mobile:text-xs px-4 py-1.5 leading-none text-center font-normal rounded-3xl inline-flex" v-if="team?.personal_team && (team.user_id === user.id)">Default</p>
                        </div>
                        <span class="text-base mobile:text-xs text-secondary-full dark:text-mode-secondary-light break-all">
                            {{ team?.email || team?.owner?.email }}
                        </span>
                    </div>
                </div>
                <div class="relative mobile:absolute mobile:right-2 mobile:top-2">
                    <vertical-menu
                    @delete-team="deleteTeam"
                    :teams_has_admin_role="teams_has_admin_role"
                    :team="team"
                    />
                </div>
            </div>
            <div v-if="team.tags" class="flex items-center mb-30px mobile:mb-2">
                <span
                    class="text-base tablet:text-sm mobile:text-xs text-secondary-full dark:text-mode-secondary-light rounded-md"
                >
                    # {{ team.tags[0]?.name }}
                </span>

                <tooltip
                    v-if="team.tags.length > 1"
                    class="text-xs cursor-pointer ml-2"
                    :title="
                        team.tags
                            .slice(1)
                            .map((tag) => tag.name)
                            .join(', ')
                    "
                >
                    <h6
                        class="text-base mobile:text-xs text-primary-dark dark:text-primary-light"
                    >
                        +{{ team.tags.length - 1 }}
                    </h6>
                </tooltip>
            </div>
            <div
                v-if="team.memberships"
                class="flex items-center gap-10px mobile:gap-2 mt-auto mobile:mt-2"
            >
                <div class="flex items-center mr-10px mobile:mr-2">
                    <img
                        v-if="team?.owner"
                        alt="Owner Image"
                        class="w-40px h-40px min-w-40px shrink-0 bg-light dark:bg-mode-base rounded-full -mr-10px mobile:-mr-2 flex items-center justify-center object-cover"
                        :src="getAvatar(team?.owner)"
                    />

                    <img
                        alt="Member Image"
                        v-for="membership in team.memberships"
                        class="w-40px h-40px min-w-40px shrink-0 bg-light dark:bg-mode-base rounded-full -mr-10px mobile:-mr-2 flex items-center justify-center object-cover"
                        :src="getAvatar(membership.user)"
                    />
                    <span
                        v-if="team.memberships && team.memberships_count - team.memberships.length > 0"
                        class="w-40px h-40px min-w-40px shrink-0 bg-light rounded-full -mr-10px mobile:-mr-2 flex items-center justify-center"
                    >
                        <span class="text-sm mobile:text-xs text-dark">+{{team.memberships_count - team.memberships.length}}</span>
                    </span>
                </div>
                <span
                    class="text-secondary-full dark:text-mode-secondary-light ml-auto mobile:text-xs"
                    >{{ $t('Server') }}: {{ team.servers_count }}</span
                >
                <span class="text-secondary-full dark:text-mode-secondary-light mobile:text-xs"
                    >{{ $t('Site') }}: {{ team.sites_count }}</span
                >
            </div>
        </div>
    </div>
    <div class="flex justify-between gap-x-10px">
        <pagination :links="teams.links" />
    </div>
    <template v-if="deletableTeam">
    <DeleteTeam
        @close="closeDeleteModal"
        :show-delete-modal="showDeleteModal"
        :team="deletableTeam"/>
    </template>
</template>

<script setup>
import Pagination from "@/Shared/Pagination.vue";
import VerticalMenu from "@/Pages/Profile/Team/VerticalMenu.vue";
import { usePage } from "@inertiajs/inertia-vue3";
import { useNavigationStore } from "@/stores/NavigationStore.js";
import {onMounted, onUnmounted, ref} from "vue";
import Tooltip from "@/Shared/Tooltip.vue";
import DeleteTeam from "@/Pages/Teams/Partials/DeleteTeam.vue";

const navigationStore = useNavigationStore();

const { user } = usePage().props.value;
const props = defineProps({
    team_name: {
        type: String,
        default: "Team",
    },
    teams: {
        type: Object,
        required: true,
    },
    teams_has_admin_role: {
        type: Array,
        required: true,
    },
    teams_has_site_admin_role: {
        type: Array,
        required: true,
    },
});
const teamList = [
    ...props.teams.data.filter((team) => team.id === user.current_team_id),
    ...props.teams.data.filter((team) => team.id !== user.current_team_id)
];

onMounted(() => {
    if (user) {
        if (window.Echo) {
            window.Echo.private(`App.Models.User.${user.id}`).listen(
                "UserSwitchTeam",
                (e) => {
                    // add team to store
                    if (props.teams.data.find((t) => t.id === e.team.id)) {
                        user.current_team_id = e.team.id;
                    }
                }
            );
        }
    }
});

onUnmounted(() => {
    if (window.Echo) {
        window.Echo.leave(`App.Models.User.${user.id}`);
    }
});

const deletableTeam = ref(null);
const showDeleteModal = ref(false);

const getAvatar = ({ name, profile_photo_path, profile_photo_url }) => {
    if (profile_photo_url) {
        return profile_photo_url;
    }
    if (!navigationStore.nightMode) {
        return profile_photo_path
            ? "/" + profile_photo_path
            : `https://ui-avatars.com/api/?name=${name[0]}&background=247AFF&color=fff`;
    }
    return profile_photo_path
        ? "/" + profile_photo_path
        : "https://ui-avatars.com/api/?name=" + name[0];
};
const deleteTeam = (team) =>{
    deletableTeam.value = team;
    showDeleteModal.value =true;

}
const closeDeleteModal= () => {
    showDeleteModal.value =false;
    deletableTeam.value = null;
}

</script>
