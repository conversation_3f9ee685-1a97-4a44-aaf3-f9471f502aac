<template>
    <single-profile
        active="Vulnerable Sites">
        <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
            <div class="flex justify-between items-center gap-10px p-30px w-full">
                <div class="flex flex-col gap-2">
                    <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">
                        {{ $t('Vulnerable Sites') }}
                    </h2>
                </div>
            </div>
            <div class="flex flex-col grow gap-30px p-50px wide-mobile:p-30px mobile:p-20px w-full">
                <div class="flex flex-col gap-4">
                    <div class="flex flex-wrap gap-3 justify-between">
                        <h4 class="text-2xl font-medium text-dark dark:text-white leading-none">
                            {{ $t('All Sites') }}
                        </h4>
                        <div class="flex flex-row gap-2">
                          <button @click.prevent="reFreshVulnerability()" type="button"  :class="{ 'cursor-not-allowed opacity-70': refreshing }"
                            class="inline-flex items-center rounded-3xl shadow-none px-2 py-1 pr-10px bg-primary-light/10 text-sm text-primary-light focus:outline-0 gap-1">
                            <span class="rounded-full bg-primary-light h-25px w-25px shrink-0 flex justify-center items-center"><i
                                :class="{'animate-spin': refreshing}" class="xcloud xc-verify_dns text-white pt-[0.02rem]"></i>
                            </span>
                            <span>{{ $t('Refresh') }}</span>
                          </button>

                          <!-- Filter Type Dropdown -->
                          <div>
                            <SelectorTitleDropdown
                                :selected-item="getFilterLabel(filter_type)"
                                custom-class="!w-32 border"
                            >
                                <div
                                    class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
                                >
                                    <div class="p-2px">
                                        <SelectItem
                                            v-for="option in filter_options"
                                            :key="option.value"
                                            :title="option.label"
                                            :is-active="option.value === filter_type"
                                            @onItemSelected="changeFilterType(option.value)"
                                        />
                                    </div>
                                </div>
                            </SelectorTitleDropdown>
                          </div>

                          <!-- Server Dropdown -->
                          <div>
                          <SelectorTitleDropdown
                              :selected-item="selected_server ? servers[selected_server] : 'All Servers'"
                              custom-class="!w-52 border"
                          >
                              <div
                                  class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
                              >
                                  <div class="p-2px">
                                      <SelectItem
                                          title="All Servers"
                                          :is-active="null === selected_server"
                                          @onItemSelected="changeServer(null)"
                                      />
                                      <SelectItem
                                          v-for="(server,server_id) in servers"
                                          :title="server"
                                          :is-active="server_id === selected_server"
                                          @onItemSelected="changeServer(server_id)"
                                      />
                                  </div>
                              </div>
                          </SelectorTitleDropdown>
                          </div>
                        </div>
                    </div>
                    <div v-if="sites?.total > 0" class="w-full h-full flex flex-col gap-2 table-responsive">
                        <template
                            v-for="(site,index) in sites.data"
                            :key="site?.id">
                        <div
                            class="group relative flex tablet:flex-wrap items-center gap-x-20px gap-y-10px hover:shadow-sm bg-light dark:bg-mode-base p-5 rounded-10px duration-75 ease-in-out"
                        >
                            <div class="flex items-center grow">
                                <div class="flex items-center gap-20px wide-mobile:gap-10px">
                                    <span class="w-100px wide-tablet:w-20 wide-mobile:w-60px shrink-0">
                                       <img
                                           v-if="site.status === 'provisioned'"
                                           :src="`https://s.wordpress.com/mshots/v1/${site.site_url}/?w=200`"
                                           alt="h-full"
                                       />
                                        <img v-else :src="asset('img/site_placeholder.gif')" alt="">
                                    </span>
                                    <div class="flex flex-col">
                                        <h2 class="inline-flex items-center">
                                            <StateToolTip
                                                :align="index === 0 ? 'bottom' : 'top'"
                                                class="mr-3"
                                                :state="site.state"
                                                :title="site.status_readable"
                                                :blinking="site?.has_vulnerability_scan && site?.vulnerabilities_count > 0"
                                                :blinking_title="site?.vulnerabilities_count + ' Vulnerabilities Found'"
                                            />
                                            <tooltip
                                                :align="index === 0 ? 'bottom' : 'top'"
                                                class="cursor-pointer"
                                                :title="site.name"
                                            >
                                                <Link
                                                    :href="'/site/' + site.id"
                                                    class="inline-flex items-center text-dark dark:text-light text-3xl wide-tablet:text-2xl tablet:text-xl !leading-none hover:text-primary-dark dark:hover:text-white transition ease-in-out duration-75"
                                                >
                                                    {{ $filters.textLimit(site.name, 20) }}
                                                </Link>
                                            </tooltip>
                                            <tooltip
                                                :align="index === 0 ? 'bottom' : 'top'"
                                                v-if="site.state === 'Success'"
                                                class="cursor-pointer ml-3"
                                                title="View Site"
                                            >
                                                <a
                                                    :href="site.site_url"
                                                    target="_blank"
                                                    class="inline-flex items-center"
                                                >
                                                    <i class="xcloud xc-maximize text-base text-secondary-full dark:text-secondary-light hover:text-primary-light
                          dark:hover:text-primary-light cursor-pointer transition ease-in-out duration-75">
                                                    </i>
                                                </a>
                                            </tooltip>
                                        </h2>
                                        <div class="flex items-center flex-wrap gap-x-1 gap-y-1 mt-1">
                                            <span class="text-base text-dark dark:text-light leading-none">{{ site.created_at_readable }}</span>
                                            <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm"
                                                v-if="site.environment"
                                            >
                                              <span
                                                  class="px-10px py-1 wide-mobile:p-1
                                                    rounded-md transition ease-in-out duration-150 inline-flex items-center gap-1.5"
                                                  :class="{
                                                      'text-success-light bg-success-light/10' : site.environment === 'production',
                                                      'text-[#9AA4B2] bg-[#9AA4B2]/10' : site.environment === 'staging' || site.environment === 'staging_with_own_domain',
                                                      'text-[#007EFD] bg-[#007EFD]/10' : site.environment === 'demo'
                                                  }"
                                              >
                                                  <span>{{ getSiteEnvironmentReadable(site.environment) }}</span>
                                              </span>
                                                </h6>

                                                <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm text-secondary-full
                                dark:text-mode-secondary-light"
                                                    v-if="site.wordpress_version"
                                                >
                                              <span class="px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base rounded-md transition ease-in-out
                                                          duration-150 inline-flex items-center gap-1.5"
                                              >
                                                  <i class="xcloud xc-wordpress text-primary-light"></i>
                                                  <span>{{ site.wordpress_version }}</span>
                                              </span>
                                            </h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-wrap items-center justify-end gap-10px tablet:w-full">
                                <button
                                     v-if="!site.is_patchstack"
                                    @click="openUpgradeModal(site)"
                                    class="h-7 inline-flex items-center justify-center px-4 py-1
                                           bg-transparent dark:bg-[#2A325C]
                                           text-[#32BA7C] dark:text-white
                                           rounded-lg text-[12px] font-normal
                                           border border-[#32BA7C] dark:border-none">
                                    <i class="xcloud xc-privacy_bolt text-base text-[32BA7C] dark:text-[#AFE614] mr-[4px]"></i>
                                    {{ $t('Enable Shield') }}
                                </button>
                                <button
                                    v-else
                                    class="h-7 inline-flex items-center justify-center px-4 py-1
                                           bg-[#32BA7C] dark:bg-[#32BA7C]/10
                                           text-white dark:text-[#32BA7C]
                                           rounded-lg text-[12px] font-normal cursor-not-allowed">
                                    <i class="xcloud xc-privacy_bolt text-base text-white  dark:text-[#A0D854] mr-[4px]"></i>
                                    {{ $t('Shield Enabled') }}
                                </button>
                                <Link v-if="site?.vulnerabilities_count>0"
                                  :href="route('site.vulnerability-scan',{
                                    server: site.server,
                                    site: site.id
                                })"
                                class="h-7 inline-flex items-center justify-center gap-1 px-4 py-2
                                        bg-transparent dark:bg-[#2E2233]
                                       text-[#FF674F] border border-[#FF674F] dark:border-none
                                       rounded-lg text-[12px] font-normal">
                                    <i class="xcloud xc-bug_2 text-base"></i>
                                      {{site?.vulnerabilities_count}} {{site?.vulnerabilities_count === 1 ? 'Issue' : 'Issues'}}
                                </Link>
                                <!-- Upgrade to Pro Button - Only show when site.is_patchstack is false -->

                                <Link
                                    :href="route('site.vulnerability-scan',{
                                        server: site.server,
                                        site: site.id
                                    })"
                                    class="h-10 inline-flex items-center justify-center px-4 py-1 border-1 border-solid border-primary-light text-primary-light rounded-lg text-sm font-medium hover:text-white hover:bg-primary-light transition duration-75 ease-in-out">
                                    {{ $t('Check Details') }}
                                </Link>
                                <div class="flex items-center justify-center server-toggle-button pr-10px">
                                    <SiteActions
                                        :site="site"
                                        :server="site?.server"
                                        position="right"
                                        @onChangeDropDown="onChangeDropDown"
                                    >
                                        <template #selector>
                                            <div>
                                                <button
                                                    type="button"
                                                    class="inline-flex items-center justify-center min-h-40px border-1 border-primary-light dark:border-dark text-sm font-medium rounded-10px shadow-none text-white dark:text-white bg-primary-light dark:bg-dark hover:bg-white hover:border-white hover:text-dark hover:shadow-md hover:shadow-primary-dark/30 dark:hover:shadow-secondary-full/10 transition ease-in-out duration-300 focus:outline-none disabled:opacity-75 disabled:cursor-not-allowed px-15px"
                                                    id="menu-button"
                                                    aria-expanded="true"
                                                    aria-haspopup="true"
                                                >
                                                    <span class="sr-only">{{ $t('Open options') }}</span>
                                                    <!-- Heroicon name: mini/ellipsis-horizontal -->
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                        stroke-width="1.5"
                                                        stroke="currentColor"
                                                        :data-site="props.site.id"
                                                        class="w-6 h-6"
                                                    >
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            d="M6.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM18.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"
                                                        />
                                                    </svg>
                                                </button>
                                            </div>
                                        </template>
                                    </SiteActions>
                                </div>
                            </div>
                        </div>
                        </template>
                        <pagination :links="sites.links"></pagination>
                    </div>

                    <div class="xc-container" v-else>
                        <Empty
                            :documentation="$t('No vulnerabilities were found on your sites. To setup vulnerability scanner please check this')+' '+`<a target='_blank' class='underline' href='https://xcloud.host/docs/vulnerability-checker-in-xcloud/'>`+$t('documentation')+`.</a>`"
                            :canPerformAction="false"
                            type="site"
                        />
                    </div>
                </div>
            </div>
        </div>
    </single-profile>

    <!-- Vulnerability Shield Pro Upgrade Component -->
    <VulnerabilityShieldProUpgrade
        ref="vulnerabilityShieldUpgradeRef"
        :server-id="selectedSiteServerId"
        :site-id="selectedSiteId"
        :payment-methods="paymentMethods"
        :default-card="defaultCard"
        :product-price="productPrice"
        @success="handleUpgradeSuccess"
        @hidden="handleHiddenBanner"
    />
</template>
<script setup>
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import Pagination from "@/Shared/Pagination.vue";
import SelectorDropdown from "@/Shared/SelectorDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import {Link, usePage} from "@inertiajs/inertia-vue3";
import StateToolTip from "@/Shared/StateToolTip.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import SiteActions from "@/Pages/Site/Components/SiteActions.vue";
import {onMounted, onUnmounted, ref} from "vue";
import Empty from "@/Shared/Empty.vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import SelectorTitleDropdown from "@/Shared/SelectorTitleDropdown.vue";
import VulnerabilityShieldProUpgrade from "@/Shared/VulnerabilityShieldProUpgrade.vue";
const props = defineProps({
    sites: Object,
    servers: Object,
    can_create_site: Boolean,
    selected_server: {
        type: [Number,String],
        default: null
    },
    filter_type: {
        type: String,
        default: 'all'
    },
    filter_options: {
        type: Array,
        default: () => []
    },
    paymentMethods: {
        type: Object,
    },
    defaultCard: {
        type: Object,
        default: () => {}
    },
    productPrice: {
        type: Number,
        default: null
    }
})
const showDropDown = ref(false);
const refreshing = ref(false);
const vulnerabilityShieldUpgradeRef = ref(null);
const selectedSiteId = ref('');
const selectedSiteServerId = ref('');

const team_id = usePage().props.value?.user?.current_team_id;
onMounted(() => {
  if (window.Echo && team_id) {
    // console.log(
    //     "joining",
    //     "sites.update." +team_id,
    //     "TeamSitesUpdates"
    // );
    window.Echo.private("sites.update." + team_id).listen(
        "TeamSitesUpdates",
        (e) => {
          if (e?.team_id && team_id){
            //refresh the page
            Inertia.visit(window.location.href);
          }
        }
    );
  }
});

onUnmounted(() => {
  if (window.Echo && team_id) {
    window.Echo.leave("sites.update." + team_id);
  }
});


const onChangeDropDown = (value) => {
    showDropDown.value = value;
};
const reFreshVulnerability = () => {
  refreshing.value = true;
  axios.post(route('api.user.sites.vulnerability-scan')).then(() => {
    useFlash().success('Vulnerability scan started successfully');
  }).catch(() => {
    useFlash().error('Failed to start vulnerability scan');
    refreshing.value = false;
  });
}

const getSiteEnvironmentReadable = (environment) => {
    if(environment === 'production') {
        return 'Production';
    } else if(environment === 'demo') {
        return 'Demo';
    } else if(environment === 'staging_with_own_domain' || environment === 'staging') {
        return 'Staging';
    }
}

const getFilterLabel = (value) => {
    const option = props.filter_options.find(opt => opt.value === value);
    return option ? option.label : 'All';
}

const changeFilterType = (value) => {
    let params = {};

    if (props.selected_server) {
        params.server = props.selected_server;
    }

    params.filter_type = value;
    Inertia.visit(route('user.vulnerability_scanner', params));
}

const changeServer = (serverId) => {
    let params = {};

    if (serverId !== null) {
        params.server = serverId;
    }

    params.filter_type = props.filter_type;
    Inertia.visit(route('user.vulnerability_scanner', params));
}

// Open the upgrade modal for a specific site
const openUpgradeModal = (site) => {
    selectedSiteId.value = site.id;
    selectedSiteServerId.value = site.server.id;
    vulnerabilityShieldUpgradeRef.value.openUpgradeModal();
}

// Handle successful upgrade
const handleUpgradeSuccess = () => {
    Inertia.visit(route('site.vulnerability-scan', {
        server: selectedSiteServerId.value,
        site: selectedSiteId.value
    }));
}

const handleHiddenBanner = (data) => {

}
</script>
