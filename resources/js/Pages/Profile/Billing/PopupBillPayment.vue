<template>
    <!-- Add payment method modal -->
    <Modal
        @close="openCardModal = false"
        :show="openCardModal"
        :title="$t('Add A New Payment Method')"
        :footerButton="false"
        widthClass="max-w-590px">

        <div class="flex flex-col gap-20px">
            <div class="flex items-center gap-4 bg-transparent dark:bg-mode-base border-2 border-light hover:border-primary-light
            dark:border-mode-base dark:hover:border-primary-light rounded-md p-5 wide-mobile:p-3 wide-mobile:gap-2.5 w-full
            duration-100 ease-linear group cursor-pointer">
                <div class="h-14 w-14 wide-mobile:h-10 wide-mobile:w-10 inline-flex shrink-0 justify-center items-center border-1
              border-light dark:border-mode-focus-light rounded p-2">
                    <img :src="asset('img/stripe icon.svg')" alt="" />
                </div>
                <a :href="route('api.payment.add',['stripe'])"
                   id="payButton" class="stripe-button inline-flex flex-col gap-2.5 wide-mobile:gap-1 items-start justify-center">
                    <h3 class="text-lg wide-mobile:text-base font-normal text-dark dark:text-white leading-none tracking-tight">
                        {{ $t('Link a Credit/Debit Card') }}
                    </h3>
                    <p class="text-sm font-normal text-secondary-full dark:text-mode-secondary-light leading-relaxed tracking-tight">
                        {{ $t('We welcome Visa, Mastercard, American Express, Discover, Diners Club along with China UnionPay (CUP), Japan Credit Bureau (JCB)') }}
                    </p>
                </a>
                <span class="text-secondary-light group-hover:text-primary-light text-sm ml-auto">
            <i class="xcloud xc-angle_right"></i>
        </span>
            </div>
        </div>
    </Modal>

    <!-- Invoice Payment Modal -->
    <pay-invoice-modal
        :openInvoicePaymentModal="openInvoicePaymentModal"
        :invoice-payment-form="invoicePaymentForm"
        :payment-methods="paymentMethodLists"
        :handle-invoice-payment-modal="handleInvoicePayment"
        @close="handleCloseInvoicePaymentModal"
        :take-payment="takeInvoicePayment">
    </pay-invoice-modal>

    <!-- Bill details modal -->
    <Modal
        @close="closeDetailsModal()"
        :show="true"
        :title="$t('Bill')+'#' + bill?.id + ' - ' + bill?.title"
        :footerButton="false"
        :widthClass="'max-w-850px'"
    >
        <div class="flex flex-col gap-20px">
            <ul class="flex flex-col items-start gap-20px">

                <li class="text-dark dark:text-white text-base font-normal">
                    <b class="font-medium">{{ $t('Service') }}:</b> &nbsp;

                    <a class="underline cursor-pointer"
                       :href="bill?.service_is_active ? bill?.generator_info?.url : '#'" target="_blank">
                        {{ bill?.generator_info?.type ? bill?.generator_info.type + ' - ' + bill?.generator_info?.name : bill?.generator_info?.name }}
                    </a>
                </li>

                <li class="text-dark dark:text-white text-base font-normal">
                    <b class="font-medium">{{ $t('Description') }}:</b>&nbsp;
                    {{ bill?.description }}
                </li>

                <li class="text-dark dark:text-white text-base font-normal">
                    <b class="font-medium">{{ $t('Date') }}:</b>&nbsp;
                    {{ bill?.date }}
                </li>

                <li class="text-dark dark:text-white text-base font-normal">
                    <b class="font-medium">{{ $t('Plan') }}:</b>&nbsp;
                    {{ bill?.title }}
                </li>

                <li v-if="!bill?.is_lifetime" class="text-dark dark:text-white text-base font-normal">
                    <b class="font-medium">{{ $t('Billing Amount') }}:</b>&nbsp; ${{ bill?.billing_amount }}
                </li>

                <li v-if="!bill?.is_lifetime" class="text-dark dark:text-white text-base font-normal">
                    <b v-if="bill?.status === 'paid'" class="font-medium">{{ $t('Amount Paid') }}:</b>
                    <b v-else class="font-medium">{{ $t('Amount Payable') }}:</b>

                    ${{ bill?.amount_to_pay }}
                </li>

                <li v-if="!bill?.is_lifetime" class="text-dark dark:text-white text-base font-normal">
                    <b class="font-medium">
                        {{ $t('Bill From') }}: </b> {{ dayjs(bill?.bill_from).format('DD-MMM-YYYY') }}
                    <template v-if="bill?.next_billing_date">
                        {{ $t('to') }} {{ dayjs(bill?.next_billing_date).subtract(1, 'day').format('D-MMM-YYYY') }}
                    </template>
                    <template v-else-if="!bill?.service_is_active">
                        {{ $t('discontinued on') }} {{ dayjs(bill?.service_deactivated_from).format('h:mm A DD-MMM-YYYY') }}
                    </template>
                    <template v-else>
                        {{ $t('to lifetime') }}
                    </template>
                </li>

                <li v-if="bill?.next_billing_date" class="text-dark dark:text-white text-base font-normal">
                    <b class="font-medium">Next Billing:</b> {{ bill?.next_billing }}
                </li>

                <li v-if="bill?.adjusted_amount_comment && bill?.service_is_active" class="text-dark dark:text-white text-base font-normal">
                    <b class="font-medium">Next Billing Amount: </b> {{ bill?.next_billing_amount_comment }}
                </li>

                <li v-if="bill?.adjusted_amount_comment && bill?.service_is_active" class="text-dark dark:text-white text-base font-normal">
                    <b class="font-medium">Amount Adjusted:</b> {{ bill?.adjusted_amount_comment }}
                </li>

            </ul>

            <div class="flex items-center gap-4 justify-end" v-if="$page?.props?.current_team?.active_plan?.name !== 'free'">

                <!--Check box declaration-->
                <div class="flex items-center gap-4" v-if="bill?.has_offer && bill?.invoice_id === null">
                    <input type="checkbox" id="terms" v-model="confirmationOfFreeBilling" name="terms" class="form-checkbox h-5 w-5 text-primary-light border-2 border-primary-light rounded-md">
                    <label for="terms" class="text-dark dark:text-white text-base font-normal">
                        {{ $t('I want to convert this bill to monthly and unlock full features') }}
                    </label>
                </div>

                <div v-if="bill?.invoice_id && bill?.status !== 'paid'" class="justify-end wide-mobile:px-30px wide-mobile:py-20px">
                    <btn
                        :disabled="bill?.status === 'paid'"
                        @click.prevent="handleInvoicePayment(bill.invoice_id)"
                        :class="{'cursor-not-allowed opacity-50' : bill?.status === 'paid'}"
                    >
                        <span v-if="bill?.status === 'paid'">Paid</span>
                        <span v-else>{{ $t('Pay') }}</span>
                    </btn>
                </div>
                <div v-else-if="bill?.renewal_period !== 'lifetime' && bill?.billing_amount >= 0.50 && bill?.status !== 'paid'" class="justify-end wide-mobile:px-30px wide-mobile:py-20px">
                    <!--Generate invoice-->
                    <btn class="bg-success-dark hover:bg-success-light"
                         :class="{'cursor-not-allowed opacity-50' : !confirmationOfFreeBilling && bill?.has_offer}"
                         :disabled="!confirmationOfFreeBilling && bill?.has_offer"
                         @click.prevent="generateInvoiceFromBills(bill.id)">
                        <span>{{ $t('Generate Invoice') }}</span>
                    </btn>
                </div>
            </div>
            <div v-else class="flex items-center gap-4 justify-end">
                <!--Please add card to pay-->
                <p class="text-dark dark:text-white text-base font-normal">
                    {{ $t('Please add a') }} <Link class="font-bold" :href="route('user.bills-payment')">{{ $t('payment') }}</Link> {{ $t('method unlock more features.') }}
                </p>
            </div>
        </div>
    </Modal>
</template>

<script setup>
import { ref } from "vue";
import Modal from "@/Shared/Modal.vue";
import {Inertia} from "@inertiajs/inertia";
import {useFlash} from "@/Composables/useFlash";
import {useCreditCardIcon} from "@/Composables/useCreditCardIcon";
import {asset} from "laravel-vapor";
import dayjs from "dayjs";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import Btn from "@/Shared/Btn.vue";
import PayInvoiceModal from "@/Pages/Profile/PayInvoiceModal.vue";
import axios from "axios";
import * as url from "node:url";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
    bill: {
        type: Object,
        default: () => {}
    },
    paymentMethods: {
        type: Object,
    },
    previousRoute: {
        type: String,
        default: ''
    },
    defaultCard: {
        type: Object,
        default: () => {}
    }
});

let openCardModal = ref(false);
let openDetailsModal = ref(false);
let openBillPaymentModal = ref(false);
let bill = ref(props.bill);

const confirmationOfFreeBilling = ref(false);

let payableBills = ref([])
let totalPayableAmount = ref(0)

const billPaymentForm = useForm({
    payableBills: payableBills,
    amount: totalPayableAmount,
    bill: bill
});

let openInvoicePaymentModal = ref(false);

const generateInvoiceFromBills = (billId) => {
    axios.post(route('api.bill.invoice.generate', {
        confirmed_to_convert_free_bills: confirmationOfFreeBilling.value,
        bills: billId,
    })).then(response => {
        let invoiceNumber = response?.data?.invoice_number;
        if (invoiceNumber) {
            invoiceNumber = 'Invoice#' + invoiceNumber;
            useFlash().success('Invoice generated successfully. ' + invoiceNumber);
            bill.value.invoice_id = response.data.id;
        } else {
            useFlash().error('Failed to generate invoice, ' + response?.data?.message + ' Please contact support.');
        }
    })
        .catch(error => {
            useFlash().error('Failed to generate invoice, please contact support. ' + error.message);
        });
}

const handleInvoicePayment = (invoice) => {
    openDetailsModal.value = false;
    axios.get(route('api.invoice.get', [invoice]))
        .then(response => {
            invoicePaymentForm.invoice = response.data;
            setTimeout(() => {
                openInvoicePaymentModal.value = true;
            }, 150);
        })
        .catch(error => {
            useFlash().error('Failed to process payment, please contact support. ' + error.message);
        });
}
const handleCloseInvoicePaymentModal = () => {
    openInvoicePaymentModal.value = false;
}

const invoicePaymentForm = useForm({
    invoice: null,
    paymentMethodId: props.defaultCard?.id,
});

let paymentMethodLists = () => {
    let paymentMethods = [];
    for (let key in props.paymentMethods) {
        paymentMethods.push({
            value: props.paymentMethods[key].id,
            card_no: props.paymentMethods[key].card_no,
            expires_at: props.paymentMethods[key].expiry_month + '/' + props.paymentMethods[key].expiry_year,
            brand: props.paymentMethods[key].brand,
            icon: creditCardIcon(props.paymentMethods[key].brand),
            disabled: !(props.paymentMethods[key].status === 'active'),
            inactiveCard: !(props.paymentMethods[key].status === 'active')
        })
    }
    return paymentMethods;
}

const takeInvoicePayment = () => {
    if(invoicePaymentForm.invoice?.status === 'paid') {
        useFlash().warning('This invoice is already paid.')
        return;
    }

    invoicePaymentForm.post(route('api.invoice.pay', [invoicePaymentForm.invoice?.id]),{
        preserveScroll: true,
        onSuccess: (response) => {
            const downloadInvoiceRoute = route('user.invoice.download', [invoicePaymentForm.invoice?.id]);
            // console.log(response.props.jetstream.flash)

            if(response.props.jetstream.flash.success || response.props.jetstream.flash.error){
                useFlash().swal().fire({
                    icon: response.props.jetstream.flash.error ? 'error' : "success",
                    title: response.props.jetstream.flash.error
                        ? 'Failed to process payment. Please check if your card is valid'
                        : "Your payment has been processed successfully.",
                    html: response.props.jetstream.flash.error === '' ?
                        `
                  <p class="text-sm text-dark dark:text-white leading-tight">
                    You can download the invoice from
                    <a href="${downloadInvoiceRoute}" class="underline text-secondary-full dark:text-secondary-light">here</a>
                  </p>
                `
                        : null
                    ,
                    showCancelButton: false,
                    showConfirmButton: false,
                    showCloseButton: true,
                });

                openInvoicePaymentModal.value = false;
            }
        },
    });
}
const takePayment = () => {
    billPaymentForm.post(route('api.bill.pay'), {
        preserveScroll: true,
        onSuccess: () => {
            const downloadInvoiceRoute = route('user.invoice.download', [bill?.value.invoice_id]);
            useFlash().swal().fire({
                icon: "success",
                title: "Your payment has been processed successfully.",
                html: `
            <p class="text-sm text-dark dark:text-white leading-tight">
              You can download the invoice from
              <a href="${downloadInvoiceRoute}" class="underline text-secondary-full dark:text-secondary-light">here</a>
            </p>
          `,
                showCancelButton: false,
                showConfirmButton: false,
                showCloseButton: true,
            });
            openBillPaymentModal.value = false;
        },
    });
}

const creditCardIcon = (brand) => {
    const {creditCardIcon} =  useCreditCardIcon(brand);
    return creditCardIcon.value;
}

function closeDetailsModal() {
    Inertia.visit(props.previousRoute);
}

const setAsDefault = (paymentMethod) => {
    Inertia.post(route('api.payment-method.set-as-default', [paymentMethod]), '', {
        preserveScroll: true,
        onSuccess: () => {
            useFlash().success('Card is set to as default')
        },
    });
}

const formatTitle = (text = '') => {

    if (!text) return text;

    return text.replace(/[_-]/g, ' ')
        .replace(/\b\w/g, firstChar => firstChar.toUpperCase())
        .replace(/xcloud/gi, 'xCloud');
}

const deleteCard = (paymentMethod) => {
    useFlash().deleteConfirmation({
        title: t('Are you sure you want to delete this card?'),
        text: t('You can add another card.'),
        btn_text: t('Yes, Remove!'),
    }, () => {
        Inertia.delete(route('api.payment-method.delete', [paymentMethod]), {
            preserveScroll: true,
            onSuccess: () => {
                useFlash().success('Your card has been deleted.')
            }
        })
    });
}
</script>
