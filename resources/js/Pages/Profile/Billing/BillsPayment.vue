<template>
  <single-profile active="Bills & Payment">
    <div class="grow flex flex-col divide-y-2 divide-light dark:divide-mode-base bg-white dark:bg-mode-light">
      <div class="flex justify-between items-center gap-10px p-30px w-full">
        <h2 class="text-3xl mobile:text-2xls text-dark dark:text-white">
          {{ $t('Bills & Payment') }}
        </h2>
      </div>
      <div class="flex flex-col gap-30px p-50px wide-mobile:p-30px mobile:p-20px w-full">

          <!-- Warning for expired billing -->
          <expired-billing v-if="navigation.billingComment" />

          <!-- Free Billing Warning -->
          <template v-if="!$page?.props?.current_white_label">
              <div v-if="!billingActive && $page?.props?.requires_active_payment_method ||
                   !billingActive && $page?.props?.current_team?.active_plan?.name === 'free' ||
                   !$page?.props?.current_team?.active_plan"
                   class="warning flex items-center bg-warning/20 px-6 py-4 rounded-md">

              <img :src="asset('img/warning.svg')"
                   alt="warning_img"
                   class="w-6"/>
                <p v-if="$page?.props?.current_team?.active_plan?.name === 'free'"
                   class="text-sm text-dark dark:text-white leading-7 pl-3.5">
                  {{ $t('You’re on the') }} <span v-if="$page?.props?.current_white_label">{{$page?.props?.current_white_label?.branding?.brand_name}}</span><span v-else>{{ $t('xCloud') }}</span> {{ $t('Free plan which includes 1 server and 10 website with Self Hosting.') }}
                  {{ $t('Activate your') }} {{ $page?.props?.current_team?.name }} {{ $t('team by adding payment method today.') }}
                </p>
                <p v-else class="text-sm text-dark dark:text-white leading-7 pl-3.5">
                    {{ $t('Activate your') }} {{ $page?.props?.current_team?.name }} {{ $t('team by adding payment method.') }}
                </p>
            </div>
          </template>

        <div class="grid grid-cols-2 wide-tablet:grid-cols-1 gap-50px small-laptop:gap-40px tablet:gap-25px wide-mobile:gap-20px">
          <div class="flex border-2 border-light dark:border-mode-base rounded-md flex-col divide-y-2 divide-light dark:divide-mode-base">
            <!-- Estimated Cost -->
            <div class="flex flex-col gap-10px p-30px wide-mobile:p-20px mobile:p-15px w-full">
              <h3 class="text-lg text-dark dark:text-white leading-none">
                {{ $t('Estimated Cost') }}
              </h3>
              <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
                {{ $t('This is an estimate of the amount based on your current month-to-date') }}
              </p>
              <div class="flex justify-start items-center flex-wrap gap-x-30px wide-mobile:gap-x-20px gap-y-3 mt-20px">
                <a class="inline-flex flex-col gap-2 items-start justify-center bg-transparent hover:bg-light
                          dark:hover:bg-mode-base border-2 border-light dark:border-mode-base rounded-md p-4 pr-8
                          cursor-pointer duration-100 ease-linear">
                  <span class="text-base font-bold text-dark dark:text-white leading-none tracking-tight">
                      ${{  monthlyBillingAmount }}
                  </span>
                  <span class="text-sm font-normal text-secondary-full dark:text-mode-secondary-light leading-none tracking-tight">
                      {{ $t('Cost This Month') }}
                  </span>
                </a>
                <a class="inline-flex flex-col gap-2 items-start justify-center bg-transparent hover:bg-light
                          dark:hover:bg-mode-base border-2 border-light dark:border-mode-base rounded-md p-4 pr-8
                          cursor-pointer duration-100 ease-linear">
                  <span class="text-base font-bold text-dark dark:text-white leading-none tracking-tight">
                      ${{ nextMonthBillingAmount }}
                  </span>
                  <span class="text-sm font-normal text-secondary-full dark:text-mode-secondary-light leading-none tracking-tight">
                      {{ $t('Cost Next Month') }}
                  </span>
                </a>
                <a v-if="overUsedBillingAmount" class="inline-flex flex-col gap-2 items-start justify-center bg-transparent hover:bg-light
                          dark:hover:bg-mode-base border-2 border-light dark:border-mode-base rounded-md p-4 pr-8
                          cursor-pointer duration-100 ease-linear">
                  <span class="text-base font-bold text-dark dark:text-white leading-none tracking-tight">
                      ${{ overUsedBillingAmount }}
                  </span>
                  <span class="text-sm font-normal text-secondary-full dark:text-mode-secondary-light leading-none tracking-tight">
                      {{ $t('Overused Amount After 28th') }} {{ currentMonthShort }}
                  </span>
                </a>
              </div>
              <h4 class="mt-1.5 text-sm text-dark dark:text-white leading-none">
                {{ $t('Billing Period Monthly') }} ({{ $t(renewTime) }} {{ currentMonth }} {{ currentYear }}).
                  <span v-if="$page?.props?.current_white_label"></span>
                  <span v-else>
                      <a target="_blank" href="https://xcloud.host/docs/how-billing-works-in-xcloud/" class="underline">{{ $t('Read how billing works.') }}</a>
                  </span>
              </h4>
            </div>

            <!-- Subscriptions -->
            <div v-if="serviceCounts.length" class="flex flex-col gap-10px p-30px wide-mobile:p-20px mobile:p-15px w-full">
              <h3 class="text-lg text-dark dark:text-white leading-none">
                {{ $t('Subscriptions') }}
              </h3>
              <div class="mt-1.5 grid grid-cols-2 small-laptop:grid-cols-1 wide-tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-x-30px gap-y-3">
                <a v-for="serviceCount in serviceCounts" class="inline-flex flex-col gap-2 items-start justify-center bg-transparent hover:bg-light
                          dark:hover:bg-mode-base border-2 border-light dark:border-mode-base rounded-md p-4 cursor-pointer
                          duration-100 ease-linear">
                  <span class="text-base inline-flex items-center gap-y-1 font-medium text-dark dark:text-white leading-none tracking-tight">
                      <span class="mr-2.5" v-if="serviceCount.service === 'email_provider'">
                        {{ $t('xCloud Managed Email Provider') }}
                      </span>
                      <span class="mr-2.5" v-else>
                         {{ formatTitle(serviceCount?.service) }}
                      </span>
                      <span v-if="serviceCount.service === 'self_managed_hosting'" class="inline-flex items-center justify-center gap-1 text-xxs text-white bg-pro px-2 py-1 rounded-2xl uppercase">
                        <i class="xcloud xc-crown inline-flex"></i>
                        <span class="inline-flex items-center capitalize">{{ currentPlan }}</span>
                      </span>
                  </span>
                  <span class="text-sm font-normal text-secondary-full dark:text-mode-secondary-light leading-none tracking-tight">
                      {{ serviceCount.count ? serviceCount.count : 'No' }} {{ $t('Services') }}
                  </span>
                </a>
              </div>
            </div>
          </div>

          <!-- Payment Methods -->
          <div class="flex border-2 border-light dark:border-mode-base rounded-md flex-col divide-y-2 divide-light dark:divide-mode-base">
            <div class="flex flex-col gap-10px p-30px wide-mobile:p-20px mobile:p-15px w-full">
              <h3 class="text-lg text-dark dark:text-white leading-none">
                {{ $t('Payment Methods') }}
              </h3>
              <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
                {{ $t('You can add Credit/Debit Card as your payment method') }}
              </p>
              <div class="flex flex-col items-start justify-center gap-4 mt-20px">
                <div v-if="paymentMethods.length > 0" v-for="paymentMethod in paymentMethods" :key="paymentMethod.id"
                     :class="paymentMethod.status === 'active' ? 'border-light dark:border-mode-base' : 'bg-danger/20 border-danger/20'"
                    class="flex items-center border-2 rounded-md p-5
                           wide-mobile:p-3 gap-4 wide-mobile:gap-2.5 w-full">
                  <div :class="paymentMethod.status === 'active' ? 'border-1 border-light dark:border-mode-focus-light' : 'border-1 border-danger/20'"
                      class="h-14 w-14 wide-mobile:h-10 wide-mobile:w-10 inline-flex shrink-0 justify-center items-center
                             rounded p-2">
                    <img
                        :src="creditCardIcon(paymentMethod.brand)"
                        :alt="paymentMethod.brand"
                    />
                  </div>
                  <span class="inline-flex flex-col gap-2.5 wide-mobile:gap-1 items-start justify-center">
                    <h3 class="text-lg wide-mobile:text-base font-normal text-dark dark:text-white leading-none tracking-tight">
                        <span class="capitalize">{{ paymentMethod.brand }}</span> {{ paymentMethod.card_no }}
                    </h3>
                    <p class="text-sm font-normal text-secondary-full dark:text-mode-secondary-light leading-relaxed tracking-tight">
                        {{ $t('Expires at') }} {{ paymentMethod.expiry_month }}/{{paymentMethod.expiry_year}}
                    </p>
                    <span class="hidden mobile:flex items-center gap-4 wide-mobile:gap-2.5">
                        <span class="ml-auto inline-flex items-center justify-center text-xxs font-medium leading-none px-2.5 py-1.5
                            border-1 border-primary-dark text-primary-dark rounded-full">
                            {{ $t('Default') }}
                        </span>
                        <button class="text-xs text-secondary-full dark:text-mode-secondary-light p-0.5 cursor-pointer hover:text-danger
                              duration-75 ease-in-out">
                            <i class="xcloud xc-delete"></i>
                        </button>
                    </span>
                  </span>
                    <button v-if="paymentMethod.status !== 'active'"
                            title="We are unable to use this card for payment. If you think this is a mistake, please try adding the same card again or add a new card. For more information, please contact support."
                            class="ml-auto inline-flex mobile:hidden items-center justify-center text-xxs font-medium leading-none px-2.5 py-1.5
                            border-1 border-danger text-red-500 rounded-full cursor-pointer">
                        {{ paymentMethod.status.toUpperCase() }}
                    </button>
                  <span v-if="paymentMethod.default_card && paymentMethod.status === 'active'"
                      class="ml-auto inline-flex mobile:hidden items-center justify-center text-xxs font-medium leading-none px-2.5 py-1.5
                        border-1 border-primary-dark text-primary-dark rounded-full">
                      {{ $t('Default') }}
                  </span>
                  <button v-else-if="paymentMethod.status === 'active'"
                      class="ml-auto inline-flex mobile:hidden items-center justify-center text-xxs font-medium leading-none px-2.5 py-1.5
                            border-1 border-light dark:border-mode-focus-light text-secondary-full dark:text-mode-secondary-light
                            rounded-full cursor-pointer hover:border-primary-dark hover:text-primary-dark
                            dark:hover:border-primary-dark dark:hover:text-primary-dark"
                          @click.prevent="setAsDefault(paymentMethod)"
                  >
                    {{ $t('Set As Default') }}
                  </button>
                  <tooltip :title="$t('Delete')">
                    <button v-if="!paymentMethod.default_card"
                            @click.prevent="deleteCard(paymentMethod.id)"
                            class="mobile:hidden text-xs text-secondary-full dark:text-mode-secondary-light p-0.5 cursor-pointer hover:text-danger
                        duration-75 ease-in-out">
                      <i class="xcloud xc-delete"></i>
                    </button>
                  </tooltip>
                </div>

                <button
                    @click="openCardModal = true"
                    class="flex justify-center items-center gap-4 border-2 border-dashed border-light dark:border-mode-focus-dark p-5 mobile:px-2
                          rounded-md w-full cursor-pointer hover:border-secondary-light dark:hover:border-mode-focus-light duration-150 ease-in-out"
                >
                  <span class="text-2xl text-secondary-full dark:text-mode-secondary-light inline-flex items-center">
                      <i class="xcloud xc-credit-card inline-flex items-center"></i>
                  </span>
                  <span class="text-base font-normal leading-1 tracking-tight text-dark dark:text-white">
                    {{ $t('Add A Payment Method') }}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-col gap-15px">

          <div class="flex flex-row justify-between items-center gap-15px">

            <div class="text-lg text-dark dark:text-white leading-none w-52">
              {{ $t('Billing History') }}
            </div>

            <div class="flex flex-wrap justify-end gap-10px items-center">

              <!-- Date Filter -->
              <div class="border-1 border-primary-light dark:border-dark px-2 py-1">
                <SelectorTitleDropdown :selected-item="billsDateFilter">
                  <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                    <div class="p-2px">
                      <SelectItem
                          title="All Bills"
                          :is-active="billsDateFilter === 'All Bills'"
                          @onItemSelected="onBillsDateFilterChanged($event)"
                      />
                      <SelectItem
                          title="This Month"
                          :is-active="billsDateFilter === 'This Month'"
                          @onItemSelected="onBillsDateFilterChanged($event)"
                      />
                      <SelectItem
                          title="Last Month"
                          :is-active="billsDateFilter === 'Last Month'"
                          @onItemSelected="onBillsDateFilterChanged($event)"
                      />
                      <SelectItem
                          title="Last 6 Months"
                          :is-active="billsDateFilter === 'Last 6 Months'"
                          @onItemSelected="onBillsDateFilterChanged($event)"
                      />
                      <SelectItem
                          title="Last 1 Year"
                          :is-active="billsDateFilter === 'Last 1 Year'"
                          @onItemSelected="onBillsDateFilterChanged($event)"
                      />
                    </div>
                  </div>
                </SelectorTitleDropdown>
              </div>

              <!-- Payment Status Filter-->
              <div class="border-1 border-primary-light dark:border-dark px-2 py-1">
                <SelectorTitleDropdown :selected-item="billsPaymentStatusFilter">
                  <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                    <div class="p-2px">
                      <SelectItem
                          title="Payment Status"
                          :is-active="billsPaymentStatusFilter === 'Payment Status'"
                          @onItemSelected="onBillsPaymentStatusChanged($event)"
                      />
                      <SelectItem
                          title="Paid"
                          :is-active="billsPaymentStatusFilter === 'Paid'"
                          @onItemSelected="onBillsPaymentStatusChanged($event)"
                      />
                      <SelectItem
                          title="Unpaid"
                          :is-active="billsPaymentStatusFilter === 'Unpaid'"
                          @onItemSelected="onBillsPaymentStatusChanged($event)"
                      />
                    </div>
                  </div>
                </SelectorTitleDropdown>
              </div>

              <!-- Service Status Filter-->
              <div class="border-1 border-primary-light dark:border-dark px-2 py-1">
                <SelectorTitleDropdown :selected-item="billsServiceStatusFilter">
                  <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                    <div class="p-2px">
                      <SelectItem
                          title="Active Billing"
                          :is-active="billsServiceStatusFilter === 'Active Billing'"
                          @onItemSelected="onBillsServiceStatusFilterChanged($event)"
                      />
                      <SelectItem
                          title="Inactive Billing"
                          :is-active="billsServiceStatusFilter === 'Inactive Billing'"
                          @onItemSelected="onBillsServiceStatusFilterChanged($event)"
                      />
                      <SelectItem
                          title="All Billing"
                          :is-active="billsServiceStatusFilter === 'All Billing'"
                          @onItemSelected="onBillsServiceStatusFilterChanged($event)"
                      />
                    </div>
                  </div>
                </SelectorTitleDropdown>
              </div>

            </div>
          </div>

          <div class="flex flex-col gap-30px">
            <div class="rounded-md flex flex-col">
              <div class="overflow-x-auto w-full overflow-y-hidden">
                <table class="w-full">
                  <thead class="bg-primary-light dark:bg-dark">
                  <tr class="divide-x divide-light dark:divide-dark">
                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                      {{ $t('Description') }}
                    </th>
                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                      {{ $t('Date') }}
                    </th>
                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                      {{ $t('Service Status') }}
                    </th>
                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                      {{ $t('Amount') }}
                    </th>
                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                      {{ $t('Payment Status') }}
                    </th>
                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                      {{ $t('Next Billing') }}
                    </th>
                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                      {{ $t('Renewal Period') }}
                    </th>
                    <th class="px-30px py-20px text-left text-lg font-normal text-white dark:text-white">
                      {{ $t('Actions') }}
                    </th>
                  </tr>
                  </thead>
                  <tbody class="divide-y divide-light bg-white dark:bg-mode-light border-1 border-light dark:divide-dark dark:border-dark">
                    <tr v-for="bill in bills.data" :key="bill.id"
                        class="divide-x divide-light dark:divide-dark">
                      <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                        <span class="inline-flex items-center gap-4">
                          <i class="xcloud xc-pdf text-xl text-secondary-full dark:text-light"></i>
                          <span class="inline-flex flex-col gap-0.5">
                            <h4 class="font-semibold">
                                {{ bill?.title }}
                            </h4>
                              <a v-if="bill?.service_is_active" class="cursor-pointer leading-tight hover:text-primary-light duration-75 ease-in"
                               :href="bill?.generator_info?.url"
                               target="_blank">
                              {{ bill?.generator_info?.type ? bill?.generator_info.type + ' - ' + bill?.generator_info?.name : bill?.generator_info?.name }}
                              </a>
                              <span v-else>
                                  {{ bill?.generator_info?.type ? bill?.generator_info.type + ' - ' + bill?.generator_info?.name : bill?.generator_info?.name }}
                              </span>
                          </span>
                        </span>
                      </td>
                      <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                        <span class="text-dark capitalize dark:text-white group-hover:text-white flex items-center mb-10px">
                          {{ dayjs(bill?.bill_from).format('DD MMM YYYY') }}
                        </span>
                      </td>

                      <td class="px-30px py-20px text-center text-base font-normal text-dark dark:text-white group-hover:text-white">
                          <span v-if="bill?.service_is_active"
                              class="text-success-full group-hover:text-white px-6 py-1.5 rounded-3xl bg-success-full/10 group-hover:bg-success-full capitalize">
                              Active
                          </span>
                          <span v-else class="text-danger group-hover:text-white px-6 py-1.5 rounded-3xl bg-danger/10
                              group-hover:bg-danger capitalize">
                              Inactive
                          </span>
                      </td>

                      <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                        ${{ bill?.is_lifetime ? 0 : bill?.amount_to_pay }}
                      </td>

                      <td class="px-30px py-20px text-left text-base font-normal text-dark dark:text-white group-hover:text-white">
                        <span v-if="bill?.has_offer"
                              class="text-primary-light group-hover:text-white px-6 py-1.5 rounded-3xl bg-primary-light/10 group-hover:bg-primary-dark capitalize">
                          Free
                        </span>
                        <span v-else-if="bill?.status === 'paid'"
                              class="text-success-full group-hover:text-white px-6 py-1.5 rounded-3xl bg-success-full/10 group-hover:bg-success-full capitalize">
                          {{bill?.status}}
                        </span>
                        <span v-else-if="bill?.status === 'refundable'"
                              class="text-slate-400 group-hover:text-white px-6 py-1.5 rounded-3xl bg-slate-400/10 group-hover:bg-slate-400 capitalize">
                          {{bill?.status}}
                        </span>
                        <span v-else-if="bill?.status === 'refunded'"
                              class="text-indigo-400 group-hover:text-white px-6 py-1.5 rounded-3xl bg-indigo-400/10 group-hover:bg-indigo-400 capitalize">
                          {{bill?.status}}
                        </span>
                        <span v-else-if="bill?.status === 'cancelled' || bill?.status === 'failed'"
                              class="text-danger group-hover:text-white px-6 py-1.5 rounded-3xl bg-danger/10 group-hover:bg-danger capitalize">
                              {{bill?.status}}
                        </span>
                        <span v-else class="text-warning group-hover:text-white px-6 py-1.5 rounded-3xl bg-warning/10
                                group-hover:bg-warning capitalize">
                          {{bill?.status}}
                        </span>
                      </td>

                      <td class="px-30px py-20px text-base font-normal text-dark dark:text-white group-hover:text-white text-center">
                          {{ bill?.next_billing }}
                      </td>

                      <td class="px-30px py-20px text-base font-normal text-dark dark:text-white group-hover:text-white text-center">
                          <span v-if="bill?.renewal_period"
                                class="text-success-full group-hover:text-white px-6 py-1.5 rounded-3xl bg-success-full/10 group-hover:bg-success-full capitalize">
                              {{ bill?.renewal_period }}
                          </span>
                          <span v-else-if="bill?.service_is_active"
                                class="text-success-full group-hover:text-white px-6 py-1.5 rounded-3xl bg-success-full/10 group-hover:bg-success-full capitalize">
                              Lifetime
                          </span>
                          <span v-else class="text-danger group-hover:text-white px-6 py-1.5 rounded-3xl bg-danger/10
                              group-hover:bg-danger capitalize">
                              Discontinued
                          </span>
                      </td>

                      <td class="py-4 whitespace-nowrap px-30px ">
                        <div class="flex items-center space-x-5">
<!--                          <button v-if="bill?.status !== 'paid'"-->
<!--                              @click="handleBillPayment(bill)"-->
<!--                             class="relative group cursor-pointer mt-1.5 underline text-dark dark:text-white group-hover:text-white"-->
<!--                          >-->
<!--                            Pay Now-->
<!--                          </button>-->

<!--                          <a v-if="bill?.invoice_id"-->
<!--                             :href="route('user.invoice.download',[bill?.invoice_id])"-->
<!--                             class="relative group cursor-pointer mt-1.5 underline text-dark dark:text-white group-hover:text-white">-->
<!--                            Invoice-->
<!--                          </a>-->
                          <button
                              @click="openDetailsModal = true; selectedBill = bill"
                              class="relative group cursor-pointer mt-1.5 underline text-dark dark:text-white group-hover:text-white"
                          >
                            {{ $t('Details') }}
                          </button>
<!--                          <span  v-if="$page.props.impersonating" class="flex items-center flex-wrap text-base text-dark dark:text-white mt-1 underline">-->
<!--                            <CopyAbleText :text="`/admin/resources/bills/${bill?.id}`" :title="'Copy Nova URL'"/>-->
<!--                          </span>-->
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <!-- pagination -->
             <pagination :links="bills.links" :query-string="generateFilterQueryString()"/>
          </div>
        </div>
      </div>
    </div>
  </single-profile>

  <!-- Add payment method modal -->
  <Modal
      @close="openCardModal = false"
      :show="openCardModal"
      :title="$t('Add A New Payment Method')"
      :footerButton="false"
      :widthClass="'max-w-590px'">

<!--    <p v-if="payment_amount_to_upgrade" class="mb-50px text-dark dark:text-light">-->
<!--      You'll be charged <strong>${{payment_amount_to_upgrade}} </strong> when you add payment method and upgraded to the <strong>Starter</strong> plan.-->
<!--    </p>-->

<!--    <p v-if="!payment_amount_to_upgrade && eligible_to_upgrade" class="mb-50px text-dark dark:text-light">-->
<!--      Please check the pricing of <strong>{{ eligible_to_upgrade.toUpperCase() }}</strong> plan before adding payment method. All of your active bills will be adjusted to the new plan.-->
<!--    </p>-->

    <div class="flex flex-col gap-20px">
      <div class="flex items-center gap-4 bg-transparent dark:bg-mode-base border-2 border-light hover:border-primary-light
            dark:border-mode-base dark:hover:border-primary-light rounded-md p-5 wide-mobile:p-3 wide-mobile:gap-2.5 w-full
            duration-100 ease-linear group cursor-pointer">
        <div class="h-14 w-14 wide-mobile:h-10 wide-mobile:w-10 inline-flex shrink-0 justify-center items-center border-1
              border-light dark:border-mode-focus-light rounded p-2">
          <img :src="asset('img/stripe icon.svg')" alt="" />
        </div>
        <a :href="route('api.payment.add',['stripe'])"
            id="payButton" class="stripe-button inline-flex flex-col gap-2.5 wide-mobile:gap-1 items-start justify-center">
          <h3 class="text-lg wide-mobile:text-base font-normal text-dark dark:text-white leading-none tracking-tight">
              {{ $t('Link a Credit/Debit Card') }}
          </h3>
          <p class="text-sm font-normal text-secondary-full dark:text-mode-secondary-light leading-relaxed tracking-tight">
              {{ $t('We welcome Visa, Mastercard, American Express, Discover, Diners Club along with China UnionPay (CUP), Japan Credit Bureau (JCB)') }}
          </p>
        </a>
        <span class="text-secondary-light group-hover:text-primary-light text-sm ml-auto">
            <i class="xcloud xc-angle_right"></i>
        </span>
      </div>
<!--      <div class="flex items-center gap-4 bg-transparent dark:bg-mode-base border-2 border-light hover:border-primary-light dark:border-mode-base-->
<!--        dark:hover:border-primary-light rounded-md p-5 wide-mobile:p-3 wide-mobile:gap-2.5 w-full duration-100 ease-linear group cursor-pointer">-->
<!--        <div class="h-14 w-14 wide-mobile:h-10 wide-mobile:w-10 inline-flex shrink-0 justify-center items-center border-1 border-light-->
<!--              dark:border-mode-focus-light rounded p-2">-->
<!--          <img :src="asset('img/paypal icon.svg')" alt="" />-->
<!--        </div>-->
<!--        <div class="inline-flex flex-col gap-2.5 wide-mobile:gap-1 items-start justify-center cursor-not-allowed opacity-50">-->
<!--          <h3 class="text-lg wide-mobile:text-base font-normal text-dark dark:text-white leading-none tracking-tight">-->
<!--              Link PayPal (Coming soon..)-->
<!--          </h3>-->
<!--          <p class="text-sm font-normal text-secondary-full dark:text-mode-secondary-light leading-relaxed tracking-tight">-->
<!--              Establish a connection with your PayPal account-->
<!--          </p>-->
<!--        </div>-->
<!--        <span class="text-secondary-light group-hover:text-primary-light text-sm ml-auto">-->
<!--            <i class="xcloud xc-angle_right"></i>-->
<!--        </span>-->
<!--      </div>-->
    </div>
  </Modal>

    <!-- Invoice Payment Modal -->
    <pay-invoice-modal
        :openInvoicePaymentModal="openInvoicePaymentModal"
        :invoice-payment-form="invoicePaymentForm"
        :payment-methods="paymentMethodLists"
        :handle-invoice-payment-modal="handleInvoicePayment"
        @close="handleCloseInvoicePaymentModal"
        :take-payment="takeInvoicePayment">
    </pay-invoice-modal>

  <!-- Bill details modal -->
  <Modal
      @close="openDetailsModal = false"
      :show="openDetailsModal"
      :title="'Bill#' + selectedBill?.id + ' - ' + selectedBill?.title"
      :footerButton="false"
      :widthClass="'max-w-850px'"
  >
    <div class="flex flex-col gap-20px">
      <ul class="flex flex-col items-start gap-20px">

        <li class="text-dark dark:text-white text-base font-normal">
          <b class="font-medium">{{ $t('Service') }}:</b> &nbsp;
          <a class="underline cursor-pointer"
             :href="selectedBill?.service_is_active ? selectedBill?.generator_info?.url : '#'" target="_blank">
            {{ selectedBill?.generator_info?.type ? selectedBill?.generator_info.type + ' - ' + selectedBill?.generator_info?.name : selectedBill?.generator_info?.name }}
          </a>
        </li>

        <li class="text-dark dark:text-white text-base font-normal">
          <b class="font-medium">{{ $t('Description') }}:</b>&nbsp;
          {{ selectedBill?.description }}
        </li>

        <li class="text-dark dark:text-white text-base font-normal">
          <b class="font-medium">{{ $t('Date') }}:</b>&nbsp;
          {{ selectedBill?.date }}
        </li>

        <li class="text-dark dark:text-white text-base font-normal">
          <b class="font-medium">{{ $t('Plan') }}:</b>&nbsp;
          {{ selectedBill?.title }}
        </li>

        <li v-if="!selectedBill?.is_lifetime" class="text-dark dark:text-white text-base font-normal">
          <b class="font-medium">{{ $t('Billing Amount') }}:</b>&nbsp; ${{ selectedBill?.billing_amount }}
        </li>

        <li v-if="!selectedBill?.is_lifetime" class="text-dark dark:text-white text-base font-normal">
          <b v-if="selectedBill?.status === 'paid'" class="font-medium">Amount Paid:</b>
          <b v-else class="font-medium">{{ $t('Amount Payable') }}:</b>

            ${{ selectedBill?.amount_to_pay }}
        </li>

        <li v-if="!selectedBill?.is_lifetime" class="text-dark dark:text-white text-base font-normal">
          <b class="font-medium">
              {{ $t('Bill From') }}: </b> {{ dayjs(selectedBill?.bill_from).format('DD-MMM-YYYY') }}
              <template v-if="selectedBill?.next_billing_date">
                  {{ $t('to') }} {{ dayjs(selectedBill?.next_billing_date).format('D-MMM-YYYY') }}
              </template>
            <template v-else-if="!selectedBill?.service_is_active">
                {{ $t('discontinued on') }} {{ dayjs(selectedBill?.service_deactivated_from).format('h:mm A DD-MMM-YYYY') }}
            </template>
            <template v-else>
                {{ $t('to lifetime') }}
            </template>
        </li>

        <li v-if="selectedBill?.next_billing_date" class="text-dark dark:text-white text-base font-normal">
          <b class="font-medium">Next Billing:</b> {{ selectedBill?.next_billing }} {{ selectedBill?.next_billing_amount_comment }}
        </li>

        <li v-if="selectedBill?.adjusted_amount_comment && selectedBill?.service_is_active" class="text-dark dark:text-white text-base font-normal">
          <b class="font-medium">Amount Adjusted:</b> {{ selectedBill?.adjusted_amount_comment }}
        </li>

      <li v-if="selectedBill?.due_on && selectedBill?.status!== 'paid'" class="text-dark font-bold dark:text-white text-base">
        <b class="font-medium">Due On:</b> {{ dayjs(selectedBill?.due_on).format('M/D/YY') +' - '+ dayjs(selectedBill?.due_on).fromNow() }}
      </li>

      </ul>

        <div class="flex items-center gap-4 justify-end" v-if="$page?.props?.current_team?.active_plan?.name !== 'free'">

            <!--Check box declaration-->
            <div class="flex items-center gap-4" v-if="selectedBill?.has_offer && selectedBill?.invoice_id === null">
                <input type="checkbox" id="terms" v-model="confirmationOfFreeBilling" name="terms" class="form-checkbox h-5 w-5 text-primary-light border-2 border-primary-light rounded-md">
                <label for="terms" class="text-dark dark:text-white text-base font-normal">
                    {{ $t('I want to convert this bill to monthly and unlock full features') }}
                </label>
            </div>

            <div v-if="selectedBill.invoice_id && selectedBill?.status !== 'paid'" class="justify-end wide-mobile:px-30px wide-mobile:py-20px">
                <btn
                    :disabled="selectedBill?.status === 'paid'"
                    @click.prevent="handleInvoicePayment(selectedBill.invoice_id)"
                    :class="{'cursor-not-allowed opacity-50' : selectedBill?.status === 'paid'}"
                >
                  <span v-if="selectedBill?.status === 'paid'">Paid</span>
                  <span v-else>Pay</span>
                </btn>
            </div>
            <div v-else-if="selectedBill?.renewal_period !== 'lifetime' && selectedBill?.billing_amount >= 0.50 && selectedBill?.status !== 'paid'" class="justify-end wide-mobile:px-30px wide-mobile:py-20px">
                <!--Generate invoice-->
                <btn class="bg-success-dark hover:bg-success-light"
                     :class="{'cursor-not-allowed opacity-50' : !confirmationOfFreeBilling && selectedBill?.has_offer}"
                     :disabled="!confirmationOfFreeBilling && selectedBill?.has_offer"
                     @click.prevent="generateInvoiceFromBills(selectedBill.id)">
                  <span>{{ $t('Generate Invoice') }}</span>
                </btn>
            </div>
        </div>
        <div v-else class="flex items-center gap-4 justify-end">
            <!--Please add card to pay-->
            <p class="text-dark dark:text-white text-base font-normal">
                {{ $t('Please add a') }} <Link class="font-bold" :href="route('user.bills-payment')">{{ $t('payment') }}</Link> {{ $t('method unlock more features.') }}
            </p>
        </div>
    </div>
  </Modal>
</template>

<script setup>
import SingleProfile from "@/Pages/Profile/SingleProfile.vue";
import { ref } from "vue";
import Modal from "@/Shared/Modal.vue";
import {Inertia} from "@inertiajs/inertia";
import {useFlash} from "@/Composables/useFlash";
import Tooltip from "@/Shared/Tooltip.vue";
import Pagination from "@/Shared/Pagination.vue";
import {useCreditCardIcon} from "@/Composables/useCreditCardIcon";
import {asset} from "laravel-vapor";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime"
import {useNavigationStore} from "@/stores/NavigationStore";
import SelectorTitleDropdown from "@/Shared/SelectorTitleDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import CopyAbleText from "@/Shared/CopyAbleText.vue";
import Button from "@/Jetstream/Button.vue";
import Btn from "@/Shared/Btn.vue";
import ExpiredBilling from "@/Shared/Header/Components/ExpiredBilling.vue";
import PayInvoiceModal from "@/Pages/Profile/PayInvoiceModal.vue";
import axios from "axios";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
  serviceCounts: {
    type: Object
  },
  billingActive: {
    type: Boolean,
    default: false,
  },
  monthlyBillingAmount: {
    type: Number,
    default: 0,
  },
  nextMonthBillingAmount: {
    type: Number,
    default: 0,
  },
  overUsedBillingAmount: {
    type: Number,
    default: 0,
  },
  currentMonth: {
    type: String,
  },
  currentMonthShort: {
    type: String,
  },
  currentYear: {
    type: String,
  },
  paymentMethods: {
    type: Object,
  },
  currentPlan: {
    type: [Object,String],
  },
  selfManagedServers: Number,
  xCloudManagedHosting: Number,
  bills: Object,
  payment_amount_to_upgrade: Number,
  billsDateFilter: String,
  billsPaymentStatusFilter: String,
  billsServiceStatusFilter: String,
  eligible_to_upgrade: String,
  defaultCard: {
    type: Object,
    default: () => {}
  },
  renewTime: {
    type: String,
    default: ''
  }
});

let navigation = useNavigationStore();

let openCardModal = ref(false);
let openDetailsModal = ref(false);
let openBillPaymentModal = ref(false);

let selectedBill = ref(null);

const billsDateFilter = ref(props?.billsDateFilter);
const billsPaymentStatusFilter = ref(props?.billsPaymentStatusFilter);
const billsServiceStatusFilter = ref(props?.billsServiceStatusFilter);
const confirmationOfFreeBilling = ref(false);

let payableBills = ref([])
let totalPayableAmount = ref(0)

const billPaymentForm = useForm({
  payableBills: payableBills,
  amount: totalPayableAmount,
  selectedBill: selectedBill
});

let openInvoicePaymentModal = ref(false);

const generateInvoiceFromBills = (billId) => {
    axios.post(route('api.bill.invoice.generate', {
        confirmed_to_convert_free_bills: confirmationOfFreeBilling.value,
        bills: billId,
    })).then(response => {
        let invoiceNumber = response?.data?.invoice_number;
        if (invoiceNumber) {
            invoiceNumber = 'Invoice#' + invoiceNumber;
            useFlash().success('Invoice generated successfully. ' + invoiceNumber);
            selectedBill.value.invoice_id = response.data.id;
        } else {
            useFlash().error('Failed to generate invoice, ' + response?.data?.message + ' Please contact support.');
        }
    })
    .catch(error => {
        useFlash().error('Failed to generate invoice, please contact support. ' + error.message);
    });
}

const handleInvoicePayment = (invoice) => {
    openDetailsModal.value = false;
    axios.get(route('api.invoice.get', [invoice]))
        .then(response => {
            invoicePaymentForm.invoice = response.data;
            setTimeout(() => {
                openInvoicePaymentModal.value = true;
            }, 150);
        })
        .catch(error => {
            useFlash().error('Failed to process payment, please contact support. ' + error.message);
        });
}
const handleCloseInvoicePaymentModal = () => {
    openInvoicePaymentModal.value = false;
}

const invoicePaymentForm = useForm({
    invoice: null,
    paymentMethodId: props.defaultCard?.id,
});

let paymentMethodLists = () => {
    let paymentMethods = [];
    for (let key in props.paymentMethods) {
        paymentMethods.push({
            value: props.paymentMethods[key].id,
            card_no: props.paymentMethods[key].card_no,
            expires_at: props.paymentMethods[key].expiry_month + '/' + props.paymentMethods[key].expiry_year,
            brand: props.paymentMethods[key].brand,
            icon: creditCardIcon(props.paymentMethods[key].brand),
            disabled: !(props.paymentMethods[key].status === 'active'),
            inactiveCard: !(props.paymentMethods[key].status === 'active')
        })
    }
    return paymentMethods;
}

const onBillsDateFilterChanged = (filter) => {
  billsDateFilter.value = filter;
  loadFilteredData('bill_date');
}

const onBillsPaymentStatusChanged = (filter) => {
  billsPaymentStatusFilter.value = filter;
  loadFilteredData('payment_status');
}

const onBillsServiceStatusFilterChanged = (filter) => {
  billsServiceStatusFilter.value = filter;
  loadFilteredData('service_status');
}

const handleCloseBillPaymentModal = () => {
  openBillPaymentModal.value = false;
  payableBills.value = [];
  totalPayableAmount.value = 0;
  billPaymentForm.reset();
}

const handleBillPayment = (bill) => {
  selectedBill.value = bill;
  let invoiceId = bill.invoice_id;
  // console.log('invoiceId', invoiceId);
  if(invoiceId){
    // filter all bills with invoiceId on props.bills.data
    payableBills.value = props.bills.data.filter(bill => bill.invoice_id === invoiceId && bill.status !== 'paid');

    // calculate total payable amount
    totalPayableAmount.value = payableBills.value.reduce((total, bill) => total + bill.billing_amount, 0);
    openBillPaymentModal.value = true;
  }else{
    if(selectedBill?.status === 'paid') {
      useFlash().warning('This bill is already paid.')
    }else{
      payableBills.value.push(selectedBill.value);
      totalPayableAmount.value = selectedBill?.value.billing_amount;
      openBillPaymentModal.value = true;
    }
  }
}


const takeInvoicePayment = () => {
    if(invoicePaymentForm.invoice?.status === 'paid') {
        useFlash().warning('This invoice is already paid.')
        return;
    }

    invoicePaymentForm.post(route('api.invoice.pay.old', [invoicePaymentForm.invoice?.invoice_number]),{
        preserveScroll: true,
        onSuccess: (response) => {
            const downloadInvoiceRoute = route('user.invoice.download', [invoicePaymentForm.invoice?.invoice_number]);
            // console.log(response.props.jetstream.flash)

            if(response.props.jetstream.flash.success || response.props.jetstream.flash.error){
                useFlash().swal().fire({
                    icon: response.props.jetstream.flash.error ? 'error' : "success",
                    title: response.props.jetstream.flash.error
                        ? 'Failed to process payment. Please check if your card is valid'
                        : "Your payment has been processed successfully.",
                    html: response.props.jetstream.flash.error === '' ?
                        `
                  <p class="text-sm text-dark dark:text-white leading-tight">
                    You can download the invoice from
                    <a href="${downloadInvoiceRoute}" class="underline text-secondary-full dark:text-secondary-light">here</a>
                  </p>
                `
                        : null
                    ,
                    showCancelButton: false,
                    showConfirmButton: false,
                    showCloseButton: true,
                });

                openInvoicePaymentModal.value = false;
                loadFilteredData();
            }
        },
    });
}
const takePayment = () => {
  billPaymentForm.post(route('api.bill.pay'), {
    preserveScroll: true,
    onSuccess: () => {
      const downloadInvoiceRoute = route('user.invoice.download', [selectedBill?.value?.invoice?.invoice_number]);
      useFlash().swal().fire({
          icon: "success",
          title: "Your payment has been processed successfully.",
          html: `
            <p class="text-sm text-dark dark:text-white leading-tight">
              You can download the invoice from
              <a href="${downloadInvoiceRoute}" class="underline text-secondary-full dark:text-secondary-light">here</a>
            </p>
          `,
        showCancelButton: false,
        showConfirmButton: false,
        showCloseButton: true,
      });
      openBillPaymentModal.value = false;
      loadFilteredData();
    },
  });
}

function loadFilteredData(type = 'created_at'){
  Inertia.get(route("user.bills-payment"), {
    billsDateFilter: billsDateFilter.value,
    billsPaymentStatusFilter: billsPaymentStatusFilter.value,
    billsServiceStatusFilter: billsServiceStatusFilter.value,
    filterType: type,
  });
}

const generateFilterQueryString = () => {
    let queryString = '';

    if (billsDateFilter.value) {
        queryString += `&billsDateFilter=${billsDateFilter.value}&filterType=bill_date`;
    }

    if (billsPaymentStatusFilter.value) {
        queryString += `&billsPaymentStatusFilter=${billsPaymentStatusFilter.value}&filterType=payment_status`;
    }

    if (billsServiceStatusFilter.value) {
        queryString += `&billsServiceStatusFilter=${billsServiceStatusFilter.value}&filterType=service_status`;
    }

    return queryString;
}

const creditCardIcon = (brand) => {
  const {creditCardIcon} =  useCreditCardIcon(brand);
  return creditCardIcon.value;
}

function closeDetailsModal() {
  openDetailsModal.value = false;
}

const setAsDefault = (paymentMethod) => {
  Inertia.post(route('api.payment-method.set-as-default', [paymentMethod]), '', {
    preserveScroll: true,
    onSuccess: () => {
      useFlash().success('Card is set to as default')
    },
  });
}

const formatTitle = (text = '') => {

    if (!text) return text;

    return text.replace(/[_-]/g, ' ')
        .replace(/\b\w/g, firstChar => firstChar.toUpperCase())
        .replace(/xcloud/gi, 'xCloud');
}

const deleteCard = (paymentMethod) => {
  useFlash().deleteConfirmation({
    title: t('Are you sure you want to delete this card?'),
    text: t('You can add another card.'),
    btn_text: t('Yes, Remove!'),
  }, () => {
    Inertia.delete(route('api.payment-method.delete', [paymentMethod]), {
      preserveScroll: true,
      onSuccess: () => {
        useFlash().success('Your card has been deleted.')
      }
    })
  });
}
</script>
