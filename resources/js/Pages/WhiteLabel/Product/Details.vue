<template>
    <single-white-label active="Products">
        <Head title="Product Details"/>
        <div class="h-full flex flex-col w-full divide-y-1 divide-light dark:divide-mode-base">
            <div class="flex items-center px-8 wide-mobile:px-5 py-6 wide-mobile:py-4 bg-white dark:bg-mode-light rounded-t-lg gap-x-2 wide-mobile:gap-x-1">
                <Link :href="route('white-label.products.index')" class="flex items-center gap-x-2">
                    <span class="h-7 aspect-square shrink-0 rounded inline-flex items-center justify-center bg-light dark:bg-mode-base text-xxs text-dark dark:text-white">
                        <i class="xcloud xc-angle_left"></i>
                    </span>
                    <h4 class="text-2xl wide-mobile:text-xl font-medium leading-tight text-secondary-full dark:text-white">
                        {{ $t('Product Details') }}
                    </h4>
                </Link>
            </div>
            <div class="flex items-center p-8 wide-mobile:p-5 bg-white dark:bg-mode-light rounded-b-lg">
                <div class="flex flex-col text-dark dark:text-white gap-8 wide-mobile:gap-5 w-full">
                    <div class="flex flex-col gap-4 wide-mobile:gap-2 w-full">
                        <div class="flex items-center flex-wrap gap-2 wide-mobile:gap-1">
                            <div class="flex flex-col gap-2 wide-mobile:gap-1">
                                <h4 class="text-lg font-medium leading-none">{{ $t('Product Information') }}</h4>
                                <p class="text-xs leading-none text-secondary-full dark:text-mode-secondary-dark">{{ $t('Some basic information is shared over here') }}</p>
                            </div>
                            <div class="ml-auto flex flex-wrap items-center gap-x-4 gap-y-2 wide-mobile:gap-x-2 wide-mobile:gap-y-1">
                                <button @click.prevent="editProduct(product)" class="inline-flex items-center justify-center rounded gap-2 border border-primary-light shadow-none min-h-10 px-4 bg-transparent text-sm font-medium text-primary-light focus:outline-0">
                                    <span class="inline-flex items-center text-xs">
                                        <i class="xcloud xc-edit"></i>
                                    </span>
                                    {{ $t('Edit Product') }}
                                </button>
                            </div>
                        </div>
                        <div class="flex flex-col bg-light dark:bg-mode-base">
                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Plan Name') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ product?.title }}</h4>
                            </div>
                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Type') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ product?.type }}</h4>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Renewal Type') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ product?.renewal_type }}</h4>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('SKU') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ product?.sku }}</h4>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Price') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">${{ product?.price }}</h4>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Checkout URL') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">
                                    <a :href="product?.checkout_url" target="_blank" class="underline">
                                        {{ product?.checkout_url }}
                                    </a>
                                </h4>
                                <div class="ml-3">
                                    <CopyButton position-class="left" :content="product?.checkout_url"></CopyButton>
                                </div>
                            </div>

                            <div class="flex border-b border-solid border-white dark:border-dark px-6 py-4">
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Status') }}</h4>
                                <h4 class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">
                                    <span v-if="product?.is_active" class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-success-full/10 text-xs leading-none text-success-full focus:outline-0">
                                        <span>Active</span>
                                    </span>
                                    <span v-else class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-danger text-xs leading-none text-white focus:outline-0">
                                        <span>Inactive</span>
                                    </span>
                                </h4>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col gap-4 wide-mobile:gap-2 w-full">
                        <div class="flex items-center flex-wrap gap-2 wide-mobile:gap-1">
                            <div class="flex flex-col gap-2 wide-mobile:gap-1">
                                <h4 class="text-lg font-medium leading-none">{{ $t('Invoices of Product') }}</h4>
                                <p class="text-xs leading-none text-secondary-full dark:text-mode-secondary-dark">{{ $t('Here you can check all your previous invoices') }}</p>
                            </div>
                            <div class="ml-auto flex flex-wrap items-center gap-x-4 gap-y-2 wide-mobile:gap-x-2 wide-mobile:gap-y-1">
                                <SelectorTitleDropdown :selected-item="invoiceDateFilter">
                                    <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                                        <div class="p-2px">
                                            <SelectItem
                                                title="All Invoices"
                                                :is-active="invoiceDateFilter === 'All Invoices'"
                                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="This Month"
                                                :is-active="invoiceDateFilter === 'This Month'"
                                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="Last Month"
                                                :is-active="invoiceDateFilter === 'Last Month'"
                                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="Last 6 Months"
                                                :is-active="invoiceDateFilter === 'Last 6 Months'"
                                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="Last 1 Year"
                                                :is-active="invoiceDateFilter === 'Last 1 Year'"
                                                @onItemSelected="onInvoiceDateFilterChanged($event)"
                                            />
                                        </div>
                                    </div>
                                </SelectorTitleDropdown>

                                <SelectorTitleDropdown :selected-item="invoiceStatusFilter" custom-class="!w-52">
                                    <div class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                                        <div class="p-2px">
                                            <SelectItem
                                                title="Paid Invoices"
                                                :is-active="invoiceStatusFilter === 'Paid Invoices'"
                                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="Unpaid Invoices"
                                                :is-active="invoiceStatusFilter === 'Unpaid Invoices'"
                                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="Failed Invoices"
                                                :is-active="invoiceStatusFilter === 'Failed Invoices'"
                                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                            />
                                            <SelectItem
                                                title="All Invoices"
                                                :is-active="invoiceStatusFilter === 'All Invoices'"
                                                @onItemSelected="onInvoiceStatusFilterChanged($event)"
                                            />
                                        </div>
                                    </div>
                                </SelectorTitleDropdown>
                            </div>
                        </div>
                        <div class="flex flex-col">
                            <div class="inline-block min-w-full">
                                <table class="min-w-full divide-y divide-secondary-light dark:divide-mode-focus-light">
                                    <thead>
                                    <tr>
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Date') }}</th>
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Invoice No') }}</th>
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Amount') }}</th>
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Title') }}</th>
                                        <!--<th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">Server</th>-->
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Status') }}</th>
                                        <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Actions') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-mode-base">
                                    <tr v-for="invoice in invoices?.data" :key="invoice?.id" class="even:bg-light dark:even:bg-slate-900 border-l-1 border-r-1 border-b-1 border-secondary-light/30 dark:border-dark/30">
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">{{ invoice?.date }}</td>
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">
                                            {{ invoice?.invoice_number ?? invoice?.reference_no }}
                                            <CopyButton
                                                position-class=""
                                                :content="invoice?.invoice_number ?? invoice?.reference_number"
                                                align="top"
                                                color="primary"
                                                :hideCopyText="true"
                                            />
                                        </td>
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">${{ invoice?.amount }}</td>
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">{{ invoice?.title }}</td>
                                        <!--<td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark"></td>-->
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                            <span v-if="invoice?.status === 'refunded' || invoice?.status === 'paid'"
                                                  class="text-success-full group-hover:text-white px-6 py-1.5 rounded-3xl bg-success-full/10 group-hover:bg-success-full">
                                              {{ invoice?.status_readable }}
                                            </span>
                                            <span
                                                v-else-if="invoice?.status === 'cancelled' || invoice?.status === 'failed' || invoice?.status === 'payment_failed'"
                                                class="text-danger group-hover:text-white px-6 py-1.5 rounded-3xl bg-danger/10 group-hover:bg-danger">
                                                  {{ invoice?.status_readable }}
                                            </span>
                                            <span v-else
                                                  class="text-warning group-hover:text-white px-6 py-1.5 rounded-3xl bg-warning/10 group-hover:bg-warning">
                                                  {{ invoice?.status_readable }}
                                            </span>
                                        </td>
                                        <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                            <div class="flex items-center justify-center">
                                                <InvoiceActions
                                                    :invoice="invoice"
                                                    :isStaging="isStaging"
                                                    :isAdmin="isAdmin"
                                                    :isImpersonating="isImpersonating"
                                                    position="right"
                                                    :show_border="true"
                                                    @onChangeDropDown="() => {}"
                                                />
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <Pagination :links="invoices?.links"/>
                    </div>
                </div>
            </div>
        </div>
    </single-white-label>

    <Modal
        @close="closeModalTwo"
        :show="addStepTwoProductModal"
        :footerButton="true"
        :title="$t('Edit Product')"
        :widthClass="'max-w-244'"
    >
        <div class="flex flex-col gap-6">
            <div
                class="flex flex-col gap-3"
            >
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Source Product') }}
                    </h3>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ selectedPlan?.source?.title }}</h4>
                        <tooltip :title="selectedPlan?.source?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                        <span class="ml-auto text-sm text-success-light inline-flex items-center">
                            <i class="xcloud xc-tick-o"></i>
                        </span>
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ selectedPlan?.source?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">{{ $t('month') }}</small></h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                {{ $t('Includes Up to RAM') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.memory }}</span>,
                                {{ $t('SSD') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.disk }}</span>,
                                {{ $t('vCPU') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.cpu }}</span>
                                {{ $t('Bandwidth') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>

            <div
                class="flex flex-col gap-3"
            >
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Customize Package') }}
                    </h3>
                </div>
                <div class="grid grid-cols-2 tablet:grid-cols-1 gap-6">
                    <text-input
                        v-model="form.plan_name"
                        id="plan_name"
                        :placeholder="$t('My Server')"
                        :label="$t('Plan Name')"/>
                    <text-input
                        type="number"
                        v-model="form.custom_price"
                        id="custom_price"
                        :placeholder="minPrice.toFixed(2)"
                        :error="form.errors.custom_price"
                        :label="$t('Price/Month')"/>
                    <text-input
                        v-model="form.sku"
                        id="sku"
                        :placeholder="$t('XCPU110')"
                        :error="form.errors.sku"
                        :label="$t('SKU')"
                        @input="changeSku"/>
                </div>
            </div>

            <div>
                <suggestion
                    type="slot"
                    :light-mode="false"
                    class="mb-0 pl-2"
                >
                    <a href="https://stripe.com/pricing" target="_blank" class="underline cursor-pointer">{{ $t('Stripe') }}</a>
                    {{ $t('will deduct a 3%-7% fee per sale. Your approximate profit for this sale is') }} ${{ calculateProfit(basePrice, form.custom_price, 3) }}-${{ calculateProfit(basePrice, form.custom_price, 7) }}
                </suggestion>
            </div>

            <div
                class="flex gap-6 items-center justify-start"
            >
                <div class="flex gap-1 items-center">
                    <h5 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Active Package') }}
                    </h5>
                    <tooltip title="Toggle on to active the package">
                        <button
                            title="Info Here"
                            class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-dark dark:text-white text-sm"
                        >
                            <i class="xcloud xc-info"></i>
                        </button>
                    </tooltip>
                </div>
                <label class="inline-flex outline-none">
                    <input
                        v-model="form.is_active"
                        :checked="form.is_active"
                        class="hidden peer"
                        type="checkbox"
                        :disabled="false"
                    />
                    <span
                        class="flex w-11 h-6 shrink-0 bg-secondary-light rounded-25px relative before:w-4 before:shrink-0
                        before:h-4 before:bg-white before:rounded-full ease-in-out transition duration-75 outline-none
                        before:transition-switch before:ease-in-out before:duration-75 before:absolute before:top-1
                        before:left-1 peer-checked:bg-success-full peer-checked:before:left-6 peer-checked:before:bg-white
                        cursor-pointer"
                    >
                    </span>
                </label>
            </div>

            <div class="flex flex-col gap-3">
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        {{ $t('Preview Plan') }}
                    </h3>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ form.plan_name || selectedPlan?.title }}</h4>
                        <tooltip :title="form.plan_name || selectedPlan?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ form.custom_price || selectedPlan?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">month</small></h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                {{ $t('Includes Up to RAM') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.memory }}</span>,
                                {{ $t('SSD') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.disk }}</span>,
                                {{ $t('vCPU') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.cpu }}</span>
                                {{ $t('Bandwidth') }} - <span class="text-dark dark:text-white">{{ selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-4 ml-auto">
                    <button @click.prevent="saveProduct" :disabled="isPublishRunning" class="inline-flex items-center justify-center rounded-lg border-success-light shadow-none gap-2 min-h-10 px-6 bg-success-light text-base font-medium text-white focus:outline-0">
                        <i v-if="isPublishRunning"
                           class="xcloud xc-verify_dns text-white animate-spin mr-1">
                        </i>{{ $t('Save and Publish') }}
                    </button>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script setup>
import SingleWhiteLabel from "@/Pages/WhiteLabel/SingleWhiteLabel.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import Modal from "@/Shared/Modal.vue";
import {computed, ref, watch} from "vue";
import TextInput from "@/Pages/WhiteLabel/Components/TextInput.vue";
import {Inertia} from "@inertiajs/inertia";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import Button from "@/Jetstream/Button.vue";
import Pagination from "@/Shared/Pagination.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import CopyButton from "@/Shared/CopyButton.vue";
import SelectorTitleDropdown from "@/Pages/WhiteLabel/Components/SelectorTitleDropdown.vue";
import InvoiceActions from "@/Pages/WhiteLabel/Components/InvoiceActions.vue";

const props = defineProps({
    product: Object,
    invoices: Object,
    invoiceDateFilter:{
        type: String,
        default: 'All Invoices'
    },
    invoiceStatusFilter:{
        type: String,
        default: 'Paid Invoices'
    },
    isStaging: {
        type: Boolean,
        default: false
    },
    isAdmin: {
        type: Boolean,
        default: false
    },
    isImpersonating: {
        type: Boolean,
        default: false
    },
});

const addStepTwoProductModal = ref(false);
const selectedPlan = ref(props?.product);
const productId = ref(null)
const isSuggestion = ref(false);
const priceError = ref(false);
const isPublishRunning = ref(false);
const basePrice = ref(0);
const serverType = ref('general');
const product_id = computed(() => selectedPlan.value?.id || null);
const minPrice = ref(0);
const invoiceDateFilter = ref(props?.invoiceDateFilter);
const invoiceStatusFilter = ref(props?.invoiceStatusFilter);

let form = useForm({
    plan_name: "",
    custom_price: null,
    sku: "",
    server_type: props?.product?.type,
    product_id: product_id.value,
    is_active: true
});

const saveProduct = () => {
    if (priceError.value) {
        return;
    }

    isPublishRunning.value = true;

    form.post(route('white-label.products.update', {product: productId.value}), {
        preserveScroll: true,
        onSuccess: () => {
            addStepTwoProductModal.value = false;
            serverType.value = 'general';
            isPublishRunning.value = false;
            resetForm();
            Inertia.reload();
        },
        onError: (errors) => {
            isPublishRunning.value = false;
        }
    });
}

const editProduct = (product) => {
    if (product.description){
        const descriptions = product.description.trim().split('\n');
        descriptions.forEach(description => {
            if (description.includes('RAM')) {
                product.memory = description.replace('RAM -', '').trim();
            } else if (description.includes('SSD')) {
                product.disk = description.replace('SSD -', '').trim();
            } else if (description.includes('vCPU')) {
                product.cpu = description.replace('vCPU -', '').trim();
            } else if (description.includes('Bandwidth')) {
                product.bandwidth = description.replace('Bandwidth -', '').trim();
            }
        });
    }

    productId.value = product.id;
    selectedPlan.value = product;
    serverType.value = product.type
    form.plan_name = product.title;
    form.custom_price = product.price;
    form.is_active = product.is_active;
    form.sku = product.sku;
    basePrice.value = product?.source?.price;
    let minimumPrice = basePrice.value * 1.1;
    minPrice.value = parseFloat(minimumPrice.toFixed(2));
    addStepTwoProductModal.value = true;
}

function calculateProfit(previousPrice, newPrice, feePercentage) {
    const currentPrice = newPrice * (1 - feePercentage / 100);
    return (currentPrice - previousPrice).toFixed(2);
}

const resetForm = () => {
    form.plan_name = "";
    form.custom_price = null;
    form.sku = "";
}

const changeSku = () => {
    form.errors.sku = ""
}

const closeModalTwo = () => {
    addStepTwoProductModal.value = false;
    resetForm();
}

const onInvoiceDateFilterChanged = (filter) => {
    invoiceDateFilter.value = filter;
    loadFilteredData();
}

const onInvoiceStatusFilterChanged = (filter) => {
    invoiceStatusFilter.value = filter;
    loadFilteredData();
}

function loadFilteredData(type = 'created_at'){
    Inertia.get(route("white-label.products.details", props?.product?.id), {
        invoiceDateFilter: invoiceDateFilter.value,
        invoiceStatusFilter: invoiceStatusFilter.value,
        filterType: type,
    });
}

watch(selectedPlan, (newVal) => {
    isSuggestion.value = newVal && newVal?.memory === '1 GB';

    form.product_id = newVal?.id || null;
});

watch(() => form.custom_price, (newPrice) => {
    if (newPrice && parseFloat(newPrice) < minPrice.value) {
        priceError.value = true;
        form.errors.custom_price = `Price must be at least $${minPrice.value}`;
    } else {
        priceError.value = false;
        form.errors.custom_price = '';
    }
});
</script>

<style scoped>

</style>
