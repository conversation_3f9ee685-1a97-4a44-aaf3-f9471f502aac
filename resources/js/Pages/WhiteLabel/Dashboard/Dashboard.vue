<template>
    <single-white-label active="Overview" :white-label="whiteLabel">
        <Head title="Overview"/>
        <div class="flex flex-col gap-4 wide-tablet:gap-3 mobile:gap-2 grow">
            <div class="grid grid-cols-4 small-laptop:grid-cols-2 wide-mobile:grid-cols-1 gap-4 wide-tablet:gap-3 mobile:gap-2">
                <stat
                    :title="$t('Total Clients')"
                    :count="totalClients"
                    status="down"
                    :percentage="10.5"
                    icon="xc-users"
                />
                <stat
                    :title="$t('Total Products')"
                    :count="totalProducts"
                    status="down"
                    :percentage="10.5"
                    icon="xc-products"
                />
                <stat
                    :title="$t('Total Servers')"
                    :count="totalServers"
                    status="up"
                    :percentage="12.5"
                    icon="xc-server_2"
                />
                <stat
                    :title="$t('Total Sites')"
                    :count="totalSites"
                    status="up"
                    :percentage="16.5"
                    icon="xc-site"
                />
            </div>
            <div class="grid grid-cols-2 wide-tablet:grid-cols-1 gap-4 wide-tablet:gap-3 mobile:gap-2 w-full">
                <div class="flex flex-col p-6 gap-3 bg-white dark:bg-mode-light w-full rounded-lg">
                    <div class="inline-flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                        <h4 class="text-lg font-semibold leading-none font-manrope">{{ $t('Servers') }}</h4>
                        <Link
                            :href="route('white-label.all-servers')"
                            class="ml-auto text-sm leading-tight text-primary-light hover:underline underline-offset-2"
                        >
                            {{ $t('View All') }}
                        </Link>
                    </div>
                    <div class="flex flex-col gap-0.5" v-if="servers?.length > 0">
                        <div
                            v-for="server in servers"
                            :key="server.id"
                            class="relative grid grid-cols-12 gap-x-1.5 gap-y-1 bg-light dark:bg-mode-base border-none px-2 py-2 rounded-sm"
                        >
                            <div
                                class="flex items-center col-span-4 small-laptop:col-span-12 small-laptop:pr-3"
                            >
                                <div class="flex items-center gap-2">
                                    <Tooltip :title="server.provider_readable">
                                        <span class="w-6 aspect-square shrink-0 rounded-sm bg-white dark:bg-mode-light inline-flex items-center justify-center text-dark dark:text-white text-base">
                                            <img class="h-3.5 w-auto" :src="getCloudProvider(server.provider_name)" alt="h-full" />
                                        </span>
                                    </Tooltip>
                                    <div class="flex flex-col gap-1">
                                        <h2 class="inline-flex items-center gap-0.5">
                                            <Link
                                                class="text-sm leading-none text-dark hover:text-primary-dark dark:text-white dark:hover:text-white cursor-pointer"
                                                :href="server.status !== 'deleting'
                                                ? '/server/' + server.id + '/sites'
                                                : '#'"
                                            >
                                                {{ $filters.textLimit(server.name) }}
                                            </Link>
                                            <StateToolTip
                                                class="ml-1.5"
                                                :isLow="server?.monitoring?.disk?.isLow"
                                                :state="server.state"
                                                :title="server.status_readable"
                                            />
                                        </h2>
                                        <div class="flex items-center flex-wrap gap-x-2 gap-y-1">
                                            <h6
                                                class="flex items-center flex-wrap text-xs leading-none text-secondary-full"
                                            >
                                                <CopyAbleText
                                                    align="bottom"
                                                    :text="server?.public_ip"
                                                />
                                            </h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="flex flex-wrap items-center gap-1.5 col-span-7 small-laptop:order-1 small-laptop:col-span-12"
                            >
                                <div class="inline-flex items-center gap-1">
                                    <img :src="server?.user?.profile_photo_url"  alt="avatar" class="rounded-full h-5 aspect-square object-cover" />
                                    <p class="text-xs leading-none text-secondary-full">{{ server?.user?.name }}</p>
                                </div>
                                <div class="inline-flex items-center gap-1">
                                    <span v-if="server.region" class="flex items-center text-xs px-10px py-1 text-secondary-full">
                                        <RegionFlag :region="server.region_flag" />
                                    </span>
                                </div>
                                <div v-if="server.stack" class="inline-flex items-center gap-1">
                                    <Tooltip
                                        :title="`${server.stack_readable} Web Server`"
                                        align="top"
                                    >
                                        <div class="flex flex-row items-center gap-1">
                                            <img
                                                :src="{ 'openlitespeed': asset('img/openlitespeed.svg'),'nginx': asset('img/nginx-logo.svg')}[server.stack]"
                                                :alt="server.stack"
                                                class="h-4 w-auto"
                                            />
                                            <h6 class="text-secondary-full leading-none text-xs">
                                                {{ server.stack_readable }}
                                            </h6>
                                        </div>
                                    </Tooltip>
                                </div>
                                <div class="inline-flex items-center gap-1">
                                    <p class="text-xs leading-none text-secondary-full">
                                        <span v-if="server.sites_count > 0">
                                            {{
                                                server.sites_count > 1
                                                    ? server.sites_count + " "+$t('sites')
                                                    : server.sites_count + " "+$t('site')
                                            }}
                                        </span>
                                        <span v-else>{{ $t('No sites') }}</span>
                                    </p>
                                </div>
                            </div>
                            <div
                                class="flex items-center justify-end gap-1 col-span-1 small-laptop:absolute small-laptop:right-4 small-laptop:top-4"
                            >
                                <div
                                    class="flex items-center justify-center"
                                >
                                    <ServerActions
                                        :server="server"
                                        position="right"
                                        :show_border="true"
                                        :can_view_server="true"
                                        @onChangeDropDown="() => {}"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="bg-light dark:bg-mode-base px-4 py-6 min-h-60 rounded-sm flex items-center justify-center text-center">
                        <div class="flex flex-col gap-2">
                            <img v-if="navigations.nightMode" :src="asset('img/white-label/Frame3.svg')" alt="Frame3.svg" class="h-25 w-auto" />
                            <img v-else :src="asset('img/white-label/empty-server-white.svg')" alt="Frame3.svg" class="h-25 w-auto" />
                            <div class="flex flex-col gap-3 items-center">
                                <p class="text-sm text-secondary-light dark:text-mode-secondary-dark">{{ $t('You don’t have any server yet') }}</p>
                                <!--<button
                                    type="button"
                                    class="inline-flex items-center border-1 border-primary-light justify-center h-10 p-2 px-4 rounded shadow-none text-sm text-center text-white font-normal bg-primary-light focus:outline-none hover:bg-primary-dark hover:border-primary-dark hover:text-white ease-in-out transition duration-200 hover:shadow-lg hover:shadow-primary-dark/30"
                                >
                                    <span class="inline-flex justify-center items-center text-xs mr-1.5">
                                        <i class="xcloud xc-add"></i>
                                    </span>
                                    <span class="drop-shadow-button">Add Product</span>
                                </button>-->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col p-6 gap-3 bg-white dark:bg-mode-light w-full rounded-lg">
                    <div class="inline-flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                        <h4 class="text-lg font-semibold leading-none font-manrope">{{ $t('Sites') }}</h4>
                        <Link
                            :href="route('white-label.all-sites')"
                            class="ml-auto text-sm leading-tight text-primary-light hover:underline underline-offset-2"
                        >
                            {{ $t('View All') }}
                        </Link>
                    </div>
                    <div class="flex flex-col gap-0.5" v-if="sites?.length > 0">
                        <div
                            v-for="site in sites"
                            :key="site.id"
                            class="relative grid grid-cols-12 gap-x-1.5 gap-y-1 bg-light dark:bg-mode-base border-none px-2 py-2 rounded-sm"
                        >
                            <div
                                class="flex items-center col-span-4 small-laptop:col-span-12 small-laptop:pr-3"
                            >
                                <div class="flex items-center gap-2">
                                    <span class="w-6 aspect-square shrink-0 rounded-sm bg-white dark:bg-mode-light inline-flex items-center justify-center text-primary-light text-base">
                                        <i class="xcloud xc-wordpress"></i>
                                    </span>
                                    <div class="flex flex-col gap-1">
                                        <h2 class="inline-flex items-center gap-0.5">
                                            <Link
                                                class="text-sm leading-none text-dark hover:text-primary-dark dark:text-white dark:hover:text-white cursor-pointer"
                                                :href="'/site/' + site.id"
                                            >
                                                {{ $filters.textLimit(site.name, 20) }}
                                            </Link>
                                        </h2>
                                        <div class="flex items-center flex-wrap gap-x-2 gap-y-1">
                                            <h6
                                                class="flex items-center flex-wrap text-xs leading-none text-secondary-full"
                                            >
                                                {{ site.created_at_readable }}
                                                <!--<CopyAbleText
                                                    align="bottom"
                                                    :text="site.created_at_readable"
                                                />-->
                                            </h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div
                                class="flex flex-wrap items-center gap-1.5 col-span-7 small-laptop:order-1 small-laptop:col-span-12"
                            >
                                <div v-if="site.wordpress_version" class="inline-flex items-center gap-1 px-1 h-5 border-1 border-secondary-light dark:border-dark rounded-sm text-secondary-full dark:text-mode-secondary-dark">
                                    <span class="inline-flex items-center text-xs">
                                        <i class="xcloud xc-wordpress"></i>
                                    </span>
                                    <p class="text-xs leading-none">{{ site.wordpress_version }}</p>
                                </div>
                                <div class="inline-flex items-center gap-1 px-1 h-5 border-1 border-secondary-light dark:border-dark rounded-sm text-secondary-full dark:text-mode-secondary-dark">
                                    <span class="inline-flex items-center text-xs">
                                        <i class="xcloud xc-database"></i>
                                    </span>
                                    <p class="text-xs leading-none">
                                        {{
                                            site?.site_size
                                                ? diskSize(Math.ceil(site?.site_size.disk))
                                                : "0MB"
                                        }}
                                    </p>
                                </div>
                            </div>
                            <div
                                class="flex items-center justify-end gap-1 col-span-1 small-laptop:absolute small-laptop:right-4 small-laptop:top-4"
                            >
                                <div
                                    class="flex items-center justify-center"
                                >
                                    <SiteActions
                                        :site="site"
                                        position="right"
                                        :show_border="true"
                                        :can_view_site="true"
                                        @onChangeDropDown="() => {}"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-else class="bg-light dark:bg-mode-base px-4 py-6 min-h-60 rounded-sm flex items-center justify-center text-center">
                        <div class="flex flex-col gap-2">
                            <img v-if="navigations.nightMode" :src="asset('img/white-label/Frame4.svg')" alt="Frame4.svg" class="h-25 w-auto" />
                            <img v-else :src="asset('img/white-label/empty-site-white.svg')" alt="Frame4.svg" class="h-25 w-auto" />
                            <div class="flex flex-col gap-3 items-center">
                                <p class="text-sm text-secondary-light dark:text-mode-secondary-dark">{{ $t('You don’t have any site yet') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col p-6 gap-3 bg-white dark:bg-mode-light w-full rounded-lg">
                    <div class="inline-flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                        <h4 class="text-lg text-dark dark:text-white font-semibold leading-none font-manrope">{{ $t('Clients Overview') }}</h4>
                    </div>
                    <div class="flex flex-col bg-light dark:bg-mode-base px-3 rounded-sm divide-y divide-focused dark:divide-mode-focus-light" v-if="totalClients > 0">
                        <div class="flex py-3">
                            <div class="flex gap-3 items-center">
                                <span class="w-6 aspect-square shrink-0 rounded bg-white dark:bg-mode-light inline-flex items-center justify-center text-secondary-full dark:text-secondary-light text-base">
                                    <i class="xcloud xc-users"></i>
                                </span>
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Total Clients') }}</h4>
                            </div>
                            <div class="ml-auto flex items-center gap-3">
                                <p class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ totalClients }}</p>
                                <button class="w-6 aspect-square shrink-0 bg-light dark:bg-[#15182A] rounded-sm inline-flex items-center justify-center text-secondary-full dark:text-secondary-light hover:text-dark dark:hover:text-white text-sm cursor-pointer">
                                    <Link :href="route('white-label.clients.index')">
                                        <i class="xcloud xc-maximize"></i>
                                    </Link>
                                </button>
                            </div>
                        </div>
                        <div class="flex py-3">
                            <div class="flex gap-3 items-center">
                                <span class="w-6 aspect-square shrink-0 rounded bg-white dark:bg-mode-light inline-flex items-center justify-center text-secondary-full dark:text-secondary-light text-base">
                                    <i class="xcloud xc-users"></i>
                                </span>
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Total Active Clients') }}</h4>
                            </div>
                            <div class="ml-auto flex items-center gap-3">
                                <p class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ totalActiveClients }}</p>
                                <button class="w-6 aspect-square shrink-0 bg-light dark:bg-[#15182A] rounded-sm inline-flex items-center justify-center text-secondary-full dark:text-secondary-light hover:text-dark dark:hover:text-white text-sm cursor-pointer">
                                    <Link :href="route('white-label.clients.index')">
                                        <i class="xcloud xc-maximize"></i>
                                    </Link>
                                </button>
                            </div>
                        </div>
                        <div class="flex py-3">
                            <div class="flex gap-3 items-center">
                                <span class="w-6 aspect-square shrink-0 rounded bg-white dark:bg-mode-light inline-flex items-center justify-center text-secondary-full dark:text-secondary-light text-base">
                                    <i class="xcloud xc-users"></i>
                                </span>
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Total Inactive Clients') }}</h4>
                            </div>
                            <div class="ml-auto flex items-center gap-3">
                                <p class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ totalInactiveClients }}</p>
                                <button class="w-6 aspect-square shrink-0 bg-light dark:bg-[#15182A] rounded-sm inline-flex items-center justify-center text-secondary-full dark:text-secondary-light hover:text-dark dark:hover:text-white text-sm cursor-pointer">
                                    <Link :href="route('white-label.clients.index')">
                                        <i class="xcloud xc-maximize"></i>
                                    </Link>
                                </button>
                            </div>
                        </div>
                        <div class="flex py-3">
                            <div class="flex gap-3 items-center">
                                <span class="w-6 aspect-square shrink-0 rounded bg-white dark:bg-mode-light inline-flex items-center justify-center text-secondary-full dark:text-secondary-light text-base">
                                    <i class="xcloud xc-wallet"></i>
                                </span>
                                <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ $t('Total Invoices') }}</h4>
                            </div>
                            <div class="ml-auto flex items-center gap-3">
                                <p class="ml-auto text-sm leading-none font-medium text-dark dark:text-white font-manrope">{{ totalInvoices }}</p>
                                <button class="w-6 aspect-square shrink-0 bg-light dark:bg-[#15182A] rounded-sm inline-flex items-center justify-center text-secondary-full dark:text-secondary-light hover:text-dark dark:hover:text-white text-sm cursor-pointer">
                                    <Link :href="route('white-label.billing.index')">
                                        <i class="xcloud xc-maximize"></i>
                                    </Link>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div v-else class="bg-light dark:bg-mode-base px-4 py-6 min-h-60 rounded-sm flex items-center justify-center text-center">
                        <div class="flex flex-col gap-2">
                            <img v-if="navigations.nightMode" :src="asset('img/white-label/Frame1.svg')" alt="Frame1.svg" class="h-25 w-auto" />
                            <img v-else :src="asset('img/white-label/empty-client-white.svg')" alt="Frame1.svg" class="h-25 w-auto" />
                            <div class="flex flex-col gap-3 items-center">
                                <p class="text-sm text-secondary-light dark:text-mode-secondary-dark">{{ $t('You don’t have any client yet') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col p-6 gap-3 bg-white dark:bg-mode-light w-full rounded-lg">
                    <div class="inline-flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                        <h4 class="text-lg text-dark dark:text-white font-semibold leading-none font-manrope">{{ $t('Access Dashboard & Website') }}</h4>
                        <Link
                            :href="route('white-label.domain.settings')"
                            as="a"
                            class="text-secondary-full dark:text-mode-secondary-light hover:text-dark dark:hover:text-white ml-auto text-sm leading-tight hover:underline underline-offset-2"
                        >
                            {{ $t('Domain Settings') }}
                        </Link>
                    </div>
                    <div class="flex flex-col gap-0.5">
                        <div class="flex bg-light dark:bg-mode-base p-3 rounded-sm">
                            <div class="flex gap-3 items-center">
                                <span class="w-8 aspect-square shrink-0 rounded bg-white dark:bg-mode-light inline-flex items-center justify-center text-secondary-full dark:text-secondary-light text-xl">
                                    <i class="xcloud xc-web"></i>
                                </span>
                                <div class="flex flex-col gap-1">
                                    <p class="text-xs font-regular text-secondary-full dark:text-secondary-light">{{ $t('Visit Landing Page') }}</p>
                                    <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">
                                        <a :href="whiteLabel.url"
                                           class="text-primary-light dark:text-primary-dark hover:text-primary-light dark:hover:text-primary-dark"
                                           target="_blank">
                                            {{ whiteLabel.url }}
                                        </a>
                                    </h4>
                                </div>
                            </div>
                            <div class="ml-auto flex items-center gap-3">
                                <button class="w-8 aspect-square shrink-0 bg-light dark:bg-[#15182A] rounded-sm inline-flex items-center justify-center text-secondary-full dark:text-secondary-light hover:text-dark dark:hover:text-white text-sm cursor-pointer">
                                    <a :href="whiteLabel.url" target="_blank">
                                        <i class="xcloud xc-maximize"></i>
                                    </a>
                                </button>
                            </div>
                        </div>

                        <div class="flex bg-light dark:bg-mode-base px-4 py-3 rounded-sm">
                            <div class="flex gap-3 items-center">
                                <span class="w-8 aspect-square shrink-0 rounded bg-white dark:bg-mode-light inline-flex items-center justify-center text-secondary-full dark:text-secondary-light text-xl">
                                    <i class="xcloud xc-dashboard-2"></i>
                                </span>
                                <div class="flex flex-col gap-1">
                                    <p class="text-xs font-regular text-secondary-full dark:text-secondary-light">{{ $t('Dashboard') }}</p>
                                    <h4 class="text-sm leading-none font-medium text-dark dark:text-white font-manrope">
                                        <a
                                            class="text-primary-light dark:text-primary-dark hover:text-primary-light dark:hover:text-primary-dark"
                                            target="_blank" :href="whiteLabel.url + '/dashboard'">
                                            {{ whiteLabel.url + '/dashboard' }}
                                        </a>
                                    </h4>
                                </div>
                            </div>
                            <div class="ml-auto flex items-center gap-3">
                                <button class="w-8 aspect-square shrink-0 bg-light dark:bg-[#15182A] rounded-sm inline-flex items-center justify-center text-secondary-full dark:text-secondary-light hover:text-dark dark:hover:text-white text-sm cursor-pointer">
                                    <a
                                        target="_blank" :href="whiteLabel.url + '/dashboard'">
                                        <i class="xcloud xc-maximize"></i>
                                    </a>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </single-white-label>
</template>

<script setup>
import SingleWhiteLabel from "@/Pages/WhiteLabel/SingleWhiteLabel.vue";
import StateToolTip from "../Components/StateToolTip.vue";
import CopyAbleText from "@/Shared/CopyAbleText.vue";
import ServerActions from "@/Pages/WhiteLabel/Components/ServerActions.vue";
import Stat from "@/Pages/WhiteLabel/Dashboard/Stat.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {useCloudProviderIcon} from "@/Composables/useCloudProviderIcon";
import RegionFlag from "@/Shared/Icons/RegionFlag.vue";
import "flag-icons/css/flag-icons.min.css";
import {asset} from "laravel-vapor";
import SiteActions from "@/Pages/WhiteLabel/Components/SiteActions.vue";
import {useNavigationStore} from "@/stores/NavigationStore";

const navigations = useNavigationStore();

const props = defineProps({
    totalClients: Number,
    totalServers: Number,
    totalSites: Number,
    totalProducts: Number,
    totalActiveClients: Number,
    totalInactiveClients: Number,
    totalInvoices: Number,
    servers: Object,
    sites: Object,
    whiteLabel: Object
});

const getCloudProvider = (provider) => {
    const {cloudProviderIcon} = useCloudProviderIcon(provider);
    return cloudProviderIcon.value;
};

function diskSize(value) {
    if(!value) return 0 + 'MB';
    if(value > 1024){
        return (value / 1024).toFixed(2) + 'GB';
    }else{
        return value + 'MB';
    }
}
</script>

<style scoped>

</style>
