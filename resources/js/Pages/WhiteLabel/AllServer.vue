<template>
  <single-white-label active="All Servers">
    <Head title="All Servers"/>
    <div class="flex flex-col p-6 gap-3 bg-white dark:bg-mode-light w-full rounded-lg">
      <div class="inline-flex items-end gap-2 text-secondary-full dark:text-white">
        <h4 class="text-2xl font-medium leading-none font-manrope">{{ $t('All Servers') }}</h4>
        <div class="ml-auto flex flex-wrap items-center gap-x-4 gap-y-2 wide-mobile:gap-x-2 wide-mobile:gap-y-1">
          <form class="relative flex items-center group">
            <input type="text"
                   v-model="search"
                   @keydown="disableSearchEnterKey"
                   class="flex-auto w-full appearance-none bg-light dark:bg-mode-base px-10 h-10 rounded text-secondary-full dark:text-white text-sm placeholder-secondary-light dark:placeholder-secondary-full focus:outline-none font-normal border-transparent focus:border-success-light !ring-0"
                   :placeholder="$t('Press / to search')"/>
            <div
                class="icon flex absolute top-1/2 left-4 -translate-y-1/2 pointer-events-none text-sm">
              <i class="xcloud xc-search text-secondary-light group-focus-within:text-success-light"></i>
            </div>
            <div
                v-if="search?.length > 0"
                @click.prevent="resetSearch"
                class="icon flex absolute top-1/2 right-4 -translate-y-1/2 cursor-pointer text-xs">
              <i class="xcloud xc-close1 text-danger"></i>
            </div>
          </form>
          <SelectorTitleDropdown
              :selected-item="sortBy"
              :custom-class="'!w-52'"
          >
            <div
                class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
            >
              <div class="p-2px">
                <SelectItem
                    title="Name"
                    :is-active="sortBy === 'Name'"
                    @onItemSelected="onSortByChanged($event)"
                />
                <SelectItem
                    title="PHP Version"
                    :is-active="sortBy === 'PHP Version'"
                    @onItemSelected="onSortByChanged($event)"
                />
                <SelectItem
                    title="Created At"
                    :is-active="sortBy === 'Created At'"
                    @onItemSelected="onSortByChanged($event)"
                />
              </div>
            </div>
          </SelectorTitleDropdown>
          <SelectorTitleDropdown
              :selected-item="serverFilter"
              :custom-class="'!w-52'"
          >
            <div
                class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
            >
              <div class="p-2px">
                <SelectItem
                    title="All Servers"
                    :is-active="serverFilter === 'All Servers'"
                    @onItemSelected="onFilterChanged($event)"
                />
                <SelectItem
                    title="Provisioned"
                    :is-active="serverFilter === 'Provisioned'"
                    @onItemSelected="onFilterChanged($event)"
                />
                <SelectItem
                    title="Provisioning"
                    :is-active="serverFilter === 'Provisioning'"
                    @onItemSelected="onFilterChanged($event)"
                />
              </div>
              <div class="p-2px">
                <SelectItem
                    v-for="tag in tags"
                    :title="tag"
                    icon="xc-price-tag"
                    :is-active="serverFilter === tag"
                    @onItemSelected="
                        onFilterChanged($event, 'tag')
                    "
                />
              </div>
            </div>
          </SelectorTitleDropdown>

        <SelectorTitleDropdown v-if="Object.keys(clients).length" :selected-item="clientFilter == 0 ? 'All Clients' : clients[clientFilter]" :custom-class="'!w-52'">
            <span></span>
            <div
                class="rounded-10px bg-white dark:bg-mode-focus-dark divide-y-2 divide-light dark:divide-mode-base max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin">
                <div class="p-2px">
                    <SelectItem
                        title="All Clients"
                        :is-active="0 === clientFilter"
                        @onItemSelected="onFilterClientChanged(0)"
                    />
                    <SelectItem
                        v-for="(user, index) in clients"
                        :title="user"
                        :is-active="index !== 0 && index === clientFilter"
                        @onItemSelected="onFilterClientChanged(index)"
                    />
                </div>
            </div>
        </SelectorTitleDropdown>
        </div>
      </div>
      <div class="flex flex-col gap-2" v-if="true">
        <div
            v-for="server in servers.data"
            :key="server.id"
            class="relative grid grid-cols-12 gap-x-2 gap-y-1.5 bg-light dark:bg-mode-base border-none px-4 py-4 rounded-lg"
        >
          <div
              class="flex items-center col-span-6 small-laptop:col-span-12 small-laptop:pr-3"
          >
            <div class="flex items-center gap-2">
              <Tooltip :title="server.provider_readable">
                <span class="w-12 aspect-square shrink-0 rounded bg-white dark:bg-mode-light inline-flex items-center
                        justify-center text-[#89909D] text-2xl"
                >
                    <img class="h-8 w-auto" :src="getCloudProvider(server.provider_name)" alt="h-full" />
                </span>
              </Tooltip>
              <div class="flex flex-col gap-1">
                <h2 class="inline-flex items-center gap-0.5">
                  <Link
                      class="text-2xl leading-none text-dark hover:text-primary-dark dark:text-white dark:hover:text-white cursor-pointer"
                      :href="
                          server.status !== 'deleting'
                              ? '/server/' + server.id + '/sites'
                              : '#'
                      "
                  >
                    {{ $filters.textLimit(server.name) }}
                  </Link>
                  <StateToolTip
                      class="ml-1.5"
                      :state="server.state"
                      :title="server.status_readable"
                  />
                </h2>
                <div class="inline-flex flex-wrap items-center gap-x-2 gap-y-0.5">
                  <span class="text-sm text-secondary-full dark:text-mode-secondary-light whitespace-nowrap wide-mobile:whitespace-normal">{{ $t('Created') }}: <span class="text-dark dark:text-white">{{server?.created_at_readable}}</span></span>
                    <span class="text-sm text-secondary-full dark:text-mode-secondary-light whitespace-nowrap wide-mobile:whitespace-normal">{{ $t('IP') }}: <span class="text-dark dark:text-white">
                        <CopyAbleText
                            align="bottom"
                            :text="server?.public_ip"
                        />
                    </span>
                    </span>
                    <span class="text-sm text-secondary-full dark:text-mode-secondary-light whitespace-nowrap wide-mobile:whitespace-normal">{{ $t('Package') }}: <span class="text-dark dark:text-white">
                        <CopyAbleText
                            align="bottom"
                            :text="server?.plan_title"
                        />
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="flex flex-wrap items-center gap-x-4 gap-y-2 col-span-4 small-laptop:order-1 small-laptop:col-span-12">
            <div class="inline-flex items-center gap-1">
              <img :src="server?.user?.profile_photo_url"  alt="avatar" class="rounded-full h-6 aspect-square object-cover" />
              <p class="text-sm leading-none text-secondary-full">{{ server?.user?.name }}</p>
            </div>
            <div class="inline-flex items-center gap-1">
              <span v-if="server.region" class="flex items-center text-sm px-10px py-1 text-secondary-full">
                  <RegionFlag :region="server.region_flag" />
              </span>
            </div>
            <div v-if="server.stack" class="inline-flex items-center gap-1">
              <Tooltip
                  :title="`${server.stack_readable} Web Server`"
                  align="top"
              >
                <div class="flex flex-row items-center gap-1">
                  <img
                      :src="{ 'openlitespeed': asset('img/openlitespeed.svg'),'nginx': asset('img/nginx-logo.svg')}[server.stack]"
                      :alt="server.stack"
                      class="h-4 w-auto"
                  />
                  <h6 class="text-secondary-full leading-none text-sm">
                    {{ server.stack_readable }}
                  </h6>
                </div>
              </Tooltip>
            </div>
            <div class="inline-flex items-center gap-1">
              <p class="text-sm leading-none text-secondary-full">
                <span v-if="server.sites_count > 0">
                    {{
                    server.sites_count > 1
                        ? server.sites_count + " "+$t('sites')
                        : server.sites_count + " "+$t('site')
                  }}
                </span>
                <span v-else>{{ $t('No sites') }}</span>
              </p>
            </div>
          </div>
          <div class="flex items-center justify-end gap-1 col-span-2 small-laptop:absolute small-laptop:right-4 small-laptop:top-4">
            <div class="flex items-center justify-center gap-4">
              <a class="min-h-10 inline-flex justify-center items-center border-1 border-primary-light bg-transparent px-4 py-1
                        rounded-lg text-primary-light text-sm font-medium whitespace-nowrap translate duration-75 ease-in-out
                        wide-tablet:hidden hover:bg-primary-light hover:text-white"
                 :href="
                          server.status !== 'deleting'
                              ? '/server/' + server.id + '/sites'
                              : '#'
                      ">
                {{ $t('Manage') }}
              </a>
              <!--<ServerActions
                  server="server"
                  position="right"
                  :show_border="true"
                  :can_add_site="true"
                  :can_archive_server="true"
                  :can_restart_server="true"
                  :can_restart_nginx_server="true"
                  :can_restart_mysql_server="true"
                  :can-delete-server="true"
                  @onChangeDropDown="() => {}"
              />-->
            </div>
          </div>
        </div>

        <div class="flex justify-end mt-2">
          <pagination :links="servers.links"/>
        </div>

      </div>
      <div v-else class="bg-light dark:bg-mode-base px-4 py-6 min-h-60 rounded-sm flex items-center justify-center text-center">
        <div class="flex flex-col gap-2">
          <img :src="asset('img/white-label/Frame1.svg')" alt="Frame1.svg" class="h-25 w-auto" />
          <!-- <img :src="asset('img/white-label/Frame2.svg')" alt="Frame2.svg" class="h-25 w-auto" />
          <img :src="asset('img/white-label/Frame3.svg')" alt="Frame3.svg" class="h-25 w-auto" />
          <img :src="asset('img/white-label/Frame4.svg')" alt="Frame4.svg" class="h-25 w-auto" /> -->
          <div class="flex flex-col gap-3 items-center">
            <p class="text-sm text-secondary-light dark:text-mode-secondary-dark">{{ $t('You don’t have any Product yet') }}</p>
            <button
                type="button"
                class="inline-flex items-center border-1 border-primary-light justify-center h-10 p-2 px-4 rounded shadow-none text-sm text-center text-white font-normal bg-primary-light focus:outline-none hover:bg-primary-dark hover:border-primary-dark hover:text-white ease-in-out transition duration-200 hover:shadow-lg hover:shadow-primary-dark/30"
            >
              <span class="inline-flex justify-center items-center text-xs mr-1.5">
                  <i class="xcloud xc-add"></i>
              </span>
              <span class="drop-shadow-button">{{ $t('Add Product') }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </single-white-label>
</template>

<script setup>
import SingleWhiteLabel from "@/Pages/WhiteLabel/SingleWhiteLabel.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import StateToolTip from "@/Pages/WhiteLabel/Components/StateToolTip.vue";
import ServerActions from "@/Pages/WhiteLabel/Components/ServerActions.vue";
import CopyAbleText from "@/Shared/CopyAbleText.vue";
import {Link} from "@inertiajs/inertia-vue3";
import RegionFlag from "@/Shared/Icons/RegionFlag.vue";
import {asset} from "laravel-vapor";
import {useCloudProviderIcon} from "@/Composables/useCloudProviderIcon";
import "flag-icons/css/flag-icons.min.css";
import SelectorTitleDropdown from "@/Pages/WhiteLabel/Components/SelectorTitleDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import Pagination from "@/Shared/Pagination.vue";
import {ref, watch} from "vue";
import {Inertia} from "@inertiajs/inertia";

const props = defineProps({
  servers: Object,
  clients: Object,
  tags: Object,
  filter: String,
  sortBy: String,
  sortByWebServer: String,
  sortOrder: {
    type: String,
    default: "desc",
  },
    client: {
        type: Number,
        default: 0,
    },
});

const search = ref(props.search ?? '')
const serverFilter = ref(props.filter);
const sortBy = ref(props.sortBy);
const sortOrder = ref(props.sortOrder);
let searchTimeout = null;

const clientFilter = ref(props?.client);

const getCloudProvider = (provider) => {
  const {cloudProviderIcon} = useCloudProviderIcon(provider);
  return cloudProviderIcon.value;
};

function onSortByChanged(sort) {
  if (sortBy.value === sort) {
    sortOrder.value = sortOrder.value === "asc" ? "desc" : "asc";
  } else {
    sortBy.value = sort;
    sortOrder.value = "desc"; // Reset to default descending order
  }

  // load servers based on sortBy
  Inertia.get(route("white-label.all-servers"), {
    sortBy: sortBy.value,
    sortOrder: sortOrder.value,
  });
}

function onFilterChanged(filter, type = "status") {
  serverFilter.value = filter;
  //load servers based on filter
  Inertia.get(route("white-label.all-servers"), {
    filter: serverFilter.value,
    filterType: type,
  });
}

function onFilterClientChanged(client) {
    clientFilter.value = client;
    Inertia.get(route("white-label.all-servers"), {
        client: clientFilter.value,
    });
}

const updateSearch = () => {
  Inertia.get(route("white-label.all-servers"), {
    search: search.value
  });
};

const disableSearchEnterKey = (event) => {
  if (event.key === 'Enter') {
    event.preventDefault();
  }
};

const resetSearch = () => {
  Inertia.get(route("white-label.all-servers"));
}

watch(search, () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    updateSearch();
  }, 750);
});

</script>

<style scoped>

</style>
