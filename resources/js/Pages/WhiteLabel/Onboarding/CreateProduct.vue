<template>
    <div class="xc-container">
        <div class="max-w-300 w-full mx-auto flex flex-col grow">
            <div class="flex tablet:flex-col gap-2 grow">
                <Sidebar :step="3" :current-step="currentStep" />
                <div class="grow max-w-[calc(theme(width.full)-theme(spacing.96)-theme(spacing.2))] wide-tablet:max-w-[calc(theme(width.full)-theme(spacing.60)-theme(spacing.2))] tablet:max-w-full flex flex-col gap-2">
                    <div class="flex flex-col w-full divide-y-1 divide-light dark:divide-mode-base h-full">
                        <div class="flex items-center px-8 wide-mobile:px-5 py-6 wide-mobile:py-4 bg-white dark:bg-mode-light rounded-t-lg gap-x-2 wide-mobile:gap-x-1">
                            <h4 class="text-2xl wide-mobile:text-xl font-medium leading-tight text-secondary-full dark:text-white">
                                {{ $t('Create Product') }} <span class="text-xs">({{ $t('Optional') }})</span>
                            </h4>
                            <button @click.prevent="skipCreateProduct" class="ml-auto text-base text-secondary-full dark:text-mode-secondary-dark">{{ $t('Skip') }}</button>
                        </div>

                        <div class="flex flex-col p-8 wide-mobile:p-5 gap-8 wide-mobile:gap-5 bg-white dark:bg-mode-light rounded-b-lg h-full">
                            <div v-if="createdProducts && createdProducts?.length > 0" class="flex justify-end">
                                <button @click.prevent="addNewProduct" class="w-60 inline-flex items-center justify-center rounded-lg border-primary-light shadow-none gap-2 h-12 px-8 bg-primary-light text-base font-medium text-white focus:outline-0 btn-sm">
                                    {{ $t('Create Hosting Plan') }}
                                </button>
                            </div>
                            <div v-if="createdProducts && createdProducts?.length > 0" class="overflow-x-auto flex flex-col">
                                <div class="inline-block min-w-full">
                                    <table class="min-w-full divide-y divide-secondary-light dark:divide-mode-focus-light">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Plan Name') }}</th>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Type') }}</th>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Renewal Type') }}</th>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('SKU') }}</th>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Price') }}</th>
                                            <th scope="col" class="py-3 px-6 text-left text-sm font-medium text-dark dark:text-white bg-focused dark:bg-dark">{{ $t('Status') }}</th>
                                        </tr>
                                        </thead>
                                        <tbody class="bg-white dark:bg-mode-base">
                                        <tr v-for="product in createdProducts" :key="product.id"
                                            class="even:bg-light dark:even:bg-slate-900 border-l-1 border-r-1 border-b-1 border-secondary-light/30 dark:border-dark/30">
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white">
                                                {{ product?.title }}
                                            </td>
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white capitalize">
                                                {{ product?.type }}
                                            </td>
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-medium text-dark dark:text-white capitalize">
                                                {{ product?.renewal_type }}
                                            </td>
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                                {{ product?.sku }}
                                            </td>
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                                ${{ product?.price }}
                                            </td>
                                            <td class="whitespace-nowrap py-3 px-6 text-left text-sm leading-loose font-normal text-secondary-full dark:text-mode-secondary-dark">
                                                <span v-if="product?.is_active" class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-success-full/10 text-xs leading-none text-success-full focus:outline-0">
                                                    <span>Active</span>
                                                </span>
                                                <span v-else class="inline-flex items-center rounded-3xl shadow-none px-4 py-1.5 bg-danger text-xs leading-none text-white focus:outline-0">
                                                    <span>Inactive</span>
                                                </span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div v-else class="flex flex-col text-dark dark:text-white items-center text-center gap-3 wide-mobile:gap-2 mobile:gap-1">
                                <div class="inline-flex items-center justify-center">
                                    <img v-if="navigations.nightMode" :src="asset('img/white-label/onboarding/Empty.svg')" alt="Empty" class="w-56 wide-mobile:w-44 mobile:w-36 h-auto"/>
                                    <img v-else :src="asset('img/white-label/empty-product-white.svg')" alt="Empty" class="w-56 wide-mobile:w-44 mobile:w-36 h-auto"/>
                                </div>
                                <div class="flex flex-col items-center gap-4">
                                    <h3 class="text-2xl wide-mobile:text-xl mobile:text-lg font-semibold leading-tight">{{ $t('Setup Products & Start Selling') }}</h3>
                                </div>
                                <button @click.prevent="addNewProduct" class="mt-1 inline-flex items-center justify-center rounded-lg border-primary-light shadow-none gap-2 h-12 px-8 bg-primary-light text-base font-medium text-white focus:outline-0">
                                    {{ $t('Create Hosting Plan') }}
                                </button>
                            </div>

                            <div class="flex items-center gap-4 mt-auto">
                                <Link href="/white-label/onboarding/payment-setup" class="inline-flex items-center justify-center rounded-lg border-1 border-primary-light shadow-none gap-2 min-h-12 px-6 bg-transparent text-base font-semibold text-primary-light hover:bg-primary-light hover:border-primary-light hover:text-white transition duration-75 ease-in-out focus:outline-0">
                                    {{ $t('Back') }}
                                </Link>
                                <div class="flex items-center gap-4 ml-auto">
                                    <button @click.prevent="submit" class="inline-flex items-center justify-center rounded-lg border-primary-light shadow-none min-h-12 px-6 bg-primary-light text-base font-semibold text-white focus:outline-0 gap-1.5">
                                        <span>{{ $t('Next') }}</span>
                                        <span class="text-xs inline-flex items-center">
                                            <i class="xcloud xc-angle_right"></i>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <Modal
        @close="closeModalOne"
        :show="addProductModal"
        :footerButton="true"
        title="Create New Product"
        :widthClass="'max-w-244'"
    >

        <div class="flex flex-col gap-5">
            <div class="flex flex-col gap-3">
                <h3 class="text-lg font-normal text-dark dark:text-white leading-none">
                    Select Server Plan at xCloud
                </h3>
                <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-4">
                    <label class="w-full">
                        <input type="radio" v-model="serverType" name="product-type" id="general" value="general" class="peer hidden">
                        <span
                            class="bg-light dark:bg-mode-base flex gap-6 items-start w-full rounded-lg border-1 border-solid border-transparent peer-checked:border-primary-light p-6 cursor-pointer relative after:absolute after:top-3 after:right-3 after:h-4 after:aspect-square after:shrink-0 after:rounded-full after:inline-flex after:justify-center after:items-center after:font-xc after:content-['\e927'] after:text-xxxs after:text-transparent after:border-2 after:border-secondary-light dark:after:border-dark peer-checked:after:bg-primary-light peer-checked:after:border-primary-light peer-checked:after:text-white dark:peer-checked:after:text-mode-base"
                        >
                            <span class="w-16 aspect-square shrink-0 rounded-lg bg-white dark:bg-mode-light inline-flex items-center justify-center">
                                <img
                                    :src="asset('img/png/cloud-storage 1.png')"
                                    alt="xcloud_logo"
                                    class="w-12 h-auto"
                                />
                            </span>
                            <span class="flex flex-col gap-2">
                                <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                    General
                                </h5>
                                <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                    {{ $t('Cost-effective servers powered by Intel CPUs and regular SSDs.') }}
                                </p>
                            </span>
                        </span>
                    </label>
                    <label class="w-full">
                        <input type="radio" v-model="serverType" name="product-type" id="premium" value="premium" class="peer hidden">
                        <span
                            class="bg-light dark:bg-mode-base flex gap-6 items-start w-full rounded-lg border-1 border-solid border-transparent peer-checked:border-primary-light p-6 cursor-pointer relative after:absolute after:top-3 after:right-3 after:h-4 after:aspect-square after:shrink-0 after:rounded-full after:inline-flex after:justify-center after:items-center after:font-xc after:content-['\e927'] after:text-xxxs after:text-transparent after:border-2 after:border-secondary-light dark:after:border-dark peer-checked:after:bg-primary-light peer-checked:after:border-primary-light peer-checked:after:text-white dark:peer-checked:after:text-mode-base"
                        >
                            <span class="w-16 aspect-square shrink-0 rounded-lg bg-white dark:bg-mode-light inline-flex items-center justify-center">
                                <img
                                    :src="asset('img/png/cloud-server 2.png')"
                                    alt="xcloud_logo"
                                    class="w-12 h-auto"
                                />
                            </span>
                            <span class="flex flex-col gap-2">
                                <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                    Premium
                                </h5>
                                <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                    {{ $t('Blazing-fast servers with quicker 3GHz+ Intel Xeon CPUs, speedy memory, and NVMe storage.') }}
                                </p>
                            </span>
                        </span>
                    </label>
                </div>
            </div>
            <div class="flex flex-col gap-3">
                <suggestion
                    v-if="isSuggestion"
                    class="mb-0 pl-2"
                    message="We recommend choosing the 2 GB server type based on your requirements."
                    :light-mode="false"
                />

                <h3 class="text-lg font-normal text-dark dark:text-white leading-none">
                    Select Server Size:
                </h3>
                <div class="grid grid-cols-auto-56 gap-4">
                    <label
                        v-for="(product, key) in whiteLabelAllProducts"
                        :key="key"
                        class="w-full"
                    >
                        <input
                            type="radio"
                            v-model="selectedPlan"
                            :id="'basic' + key"
                            class="hidden peer"
                            :value="product"
                        />
                        <div
                            class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light peer-checked:!border-primary-light"
                        >
                            <div class="flex items-center gap-2 px-4 py-3 justify-start bg-light dark:bg-dark">
                                <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ product?.title }}</h4>
                                <tooltip v-if="product?.tooltip" :title="product?.tooltip" align="bottom">
                                    <button
                                        title="Info Here"
                                        class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                                    >
                                        <i class="xcloud xc-info"></i>
                                    </button>
                                </tooltip>
                                <span class="ml-auto text-base text-primary-light" v-if="selectedPlan?.id === product.id">
                                    <i class="xcloud xc-checkbox"></i>
                                </span>
                            </div>
                            <div class="px-4 py-3">
                                <ul class="list-none marker:text-light flex flex-col gap-2 text-sm">
                                    <li class="text-secondary-full dark:text-mode-secondary-light"> RAM -
                                        <span class="text-dark dark:text-white">
                                            {{ product?.memory }}
                                        </span>
                                    </li>
                                    <li class="text-secondary-full dark:text-mode-secondary-light"> SSD -
                                        <span class="text-dark dark:text-white">{{ product?.disk }} GB </span>
                                    </li>
                                    <li class="text-secondary-full dark:text-mode-secondary-light"> vCPU -
                                        <span class="text-dark dark:text-white">{{ product?.cpu }}</span>
                                    </li>
                                    <li class="text-secondary-full dark:text-mode-secondary-light"> Bandwidth -
                                        <span class="text-dark dark:text-white">{{ product?.bandwidth }}</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="flex items-center gap-3 px-4 py-3 justify-start">
                                <h4 class="text-base leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ product?.price }}/ <small class="text-secondary-full dark:text-mode-secondary-light">month</small></h4>
                            </div>
                        </div>
                    </label>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex justify-end">
                <button @click.prevent="addNewProductStepTwo" class="inline-flex items-center justify-center rounded-lg border-primary-light shadow-none gap-2 min-h-10 px-6 bg-primary-light text-base font-medium text-white focus:outline-0">
                    Next
                </button>
            </div>
        </template>
    </Modal>

    <Modal
        @close="closeModalTwo"
        :show="addStepTwoProductModal"
        :footerButton="true"
        title="Create New Product"
        :widthClass="'max-w-244'"
    >
        <div class="flex flex-col gap-6">
            <div class="flex flex-col gap-3">
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        Selected Plan
                    </h3>
                    <button
                        @click.prevent="addNewProduct"
                        class="ml-auto text-xs leading-tight text-primary-light hover:underline underline-offset-2"
                    >
                        Change Plan
                    </button>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ selectedPlan?.title }}</h4>
                        <tooltip :title="selectedPlan?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                        <!--<span class="ml-auto text-sm text-success-light inline-flex items-center">
                            <i class="xcloud xc-tick-o"></i>
                        </span>-->
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ selectedPlan?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">month</small></h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                Includes Up to RAM - <span class="text-dark dark:text-white">{{ selectedPlan?.memory }}</span>,
                                SSD - <span class="text-dark dark:text-white">{{ selectedPlan?.disk }}</span>,
                                vCPU - <span class="text-dark dark:text-white">{{ selectedPlan?.cpu }}</span>
                                Bandwidth - <span class="text-dark dark:text-white">{{ selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>

            <div class="flex flex-col gap-3">
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        Customize Package
                    </h3>
                </div>

                <div class="grid grid-cols-2 tablet:grid-cols-1 gap-6">
                    <text-input
                        v-model="form.plan_name"
                        id="plan_name"
                        placeholder="My Server"
                        label="Plan Name"/>
                    <text-input
                        type="number"
                        v-model="form.custom_price"
                        id="custom_price"
                        :placeholder="minPrice.toFixed(2)"
                        :error="form.errors.custom_price"
                        label="Price/Month"/>
                    <text-input
                        v-model="form.sku"
                        id="sku"
                        placeholder=""
                        :error="form.errors.sku"
                        label="SKU"
                        @input="changeSku"/>
                    <!--<text-input
                        id="site_limit"
                        placeholder=""
                        label="Site Limit"/>
                    <text-input
                        id="additional_info"
                        placeholder=""
                        label="Additional Info"/>
                    <div>
                        <label
                            class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none">
                            Coupon Code
                        </label>
                        <label
                            class="flex gap-4 items-center w-full rounded border-1 border-solid border-secondary-light dark:border-mode-focus-light pr-1.5 pl-4 min-h-12">
                            <input
                                class="p-0 bg-transparent border-none grow !shadow-none focus:!shadow-none !ring-0 focus:!ring-0 text-dark dark:text-white"
                                placeholder="Enter Code Here..."
                                type="text"
                            />
                            <button
                                class="inline-flex text-sm bg-primary-light text-white border-none rounded drop-shadow-button h-full px-4 py-2 ml-auto">
                                Apply
                            </button>
                        </label>
                    </div>-->
                </div>
            </div>

            <div>
                <suggestion
                    type="slot"
                    :light-mode="false"
                    class="mb-0 pl-2"
                >
                    <a href="https://stripe.com/pricing" target="_blank" class="underline cursor-pointer">Stripe</a>
                    will deduct a 3%-7% fee per sale. Your approximate profit for this sale is ${{ calculateProfit(selectedPlan.price, form.custom_price, 3) }}-${{ calculateProfit(selectedPlan.price, form.custom_price, 7) }}
                </suggestion>
            </div>

            <div
                class="flex gap-6 items-center justify-start"
            >
                <div class="flex gap-1 items-center">
                    <h5 class="text-base leading-snug font-medium text-dark dark:text-white">
                        Active Package
                    </h5>
                    <tooltip title="Toggle on to active the package">
                        <button
                            title="Info Here"
                            class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-dark dark:text-white text-sm"
                        >
                            <i class="xcloud xc-info"></i>
                        </button>
                    </tooltip>
                </div>

                <label class="inline-flex outline-none">
                    <input
                        v-model="form.is_active"
                        :checked="form.is_active"
                        class="hidden peer"
                        type="checkbox"
                        :disabled="false"
                    />
                    <span
                        class="flex w-11 h-6 shrink-0 bg-secondary-light rounded-25px relative before:w-4 before:shrink-0
                        before:h-4 before:bg-white before:rounded-full ease-in-out transition duration-75 outline-none
                        before:transition-switch before:ease-in-out before:duration-75 before:absolute before:top-1
                        before:left-1 peer-checked:bg-success-full peer-checked:before:left-6 peer-checked:before:bg-white
                        cursor-pointer"
                    >
                    </span>
                </label>
            </div>

            <div class="flex flex-col gap-3">
                <div class="flex items-center gap-1 text-secondary-full dark:text-mode-secondary-light">
                    <h3 class="text-base leading-snug font-medium text-dark dark:text-white">
                        Preview Plan
                    </h3>
                </div>

                <div
                    class="cursor-pointer block w-full rounded-lg border-1 border-solid divide-y-1 divide-light dark:divide-dark overflow-hidden border-light dark:border-mode-focus-light"
                >
                    <div class="flex items-center gap-2 px-6 py-4 justify-start bg-light dark:bg-dark">
                        <h4 class="text-base leading-snug font-medium text-dark dark:text-white">{{ form.plan_name || selectedPlan?.title }}</h4>
                        <tooltip :title="form.plan_name || selectedPlan?.title" align="bottom">
                            <button
                                title="Info Here"
                                class="inline-flex items-center justify-center w-4 aspect-square shrink-0 text-sm text-secondary-full"
                            >
                                <i class="xcloud xc-info"></i>
                            </button>
                        </tooltip>
                        <!--<span class="ml-auto text-sm text-success-light inline-flex items-center">
                            <i class="xcloud xc-tick-o"></i>
                        </span>-->
                    </div>
                    <div class="flex items-center gap-1.5 p-6 justify-between wide-mobile:flex-col">
                        <div class="flex flex-col gap-1 wide-mobile:items-center wide-mobile:text-center">
                            <h4 class="text-2xl leading-none font-medium tracking-tighter text-dark dark:text-white"> ${{ form.custom_price || selectedPlan?.price }}/<small class="text-secondary-full dark:text-mode-secondary-light text-base">month</small></h4>
                            <p class="text-secondary-full dark:text-mode-secondary-light text-sm">
                                Includes Up to RAM - <span class="text-dark dark:text-white">{{ selectedPlan?.memory }}</span>,
                                SSD - <span class="text-dark dark:text-white">{{ selectedPlan?.disk }}</span>,
                                vCPU - <span class="text-dark dark:text-white">{{ selectedPlan?.cpu }}</span>
                                Bandwidth - <span class="text-dark dark:text-white">{{ selectedPlan?.bandwidth }}</span>
                            </p>
                        </div>
                        <span class="text-dark dark:text-white text-sm pl-3.5 relative before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-1.5 before:aspect-square before:bg-primary-light before:rounded-full">{{ $t('Billed Monthly') }}</span>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="flex items-center gap-4">
                <button @click.prevent="addNewProduct" class="inline-flex items-center justify-center rounded-lg border-1 border-primary-light shadow-none gap-2 min-h-10 px-6 bg-transparent text-base font-medium text-primary-light hover:bg-primary-light hover:text-white transition duration-75 ease-in-out focus:outline-0">
                    Back
                </button>
                <div class="flex items-center gap-4 ml-auto">
                    <!--<button class="inline-flex items-center justify-center rounded-lg border-1 border-success-light shadow-none gap-2 min-h-10 px-6 bg-transparent text-base font-medium text-success-light hover:bg-success-light hover:text-white transition duration-75 ease-in-out focus:outline-0">
                        Save
                    </button>-->
                    <button @click.prevent="saveProduct" :disabled="isPublishRunning" class="inline-flex items-center justify-center rounded-lg border-success-light shadow-none gap-2 min-h-10 px-6 bg-success-light text-base font-medium text-white focus:outline-0">
                        <i v-if="isPublishRunning"
                           class="xcloud xc-verify_dns text-white animate-spin mr-1">
                        </i>Save and Publish
                    </button>
                </div>
            </div>
        </template>
    </Modal>
</template>

<script setup>
import {Inertia} from "@inertiajs/inertia";
import Sidebar from "@/Pages/WhiteLabel/Onboarding/Sidebar.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import Modal from "@/Shared/Modal.vue";
import TextInput from "@/Pages/WhiteLabel/Components/TextInput.vue";
import {computed, onMounted, ref, watch} from "vue";
import {useFlash} from "@/Composables/useFlash";
import Button from "@/Jetstream/Button.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import SelectorTitleDropdown from "@/Pages/WhiteLabel/Components/SelectorTitleDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import {useNavigationStore} from "@/stores/NavigationStore";
const addProductModal = ref(false);
const addStepTwoProductModal = ref(false);
const currentStep = ref(3);
const selectedPlan = ref(null);
const isSuggestion = ref(false);
const priceError = ref(false);
const isPublishRunning = ref(false);

const navigations = useNavigationStore();

const props = defineProps({
    products: Object,
    createdProducts: Object,
    skuPrefix: String
});

let serverType = ref("general");
const whiteLabelAllProducts = computed(() => {
    return props.products[serverType.value];
});

const product_id = computed(() => selectedPlan.value?.id || null);
const minPrice = ref(0);

let form = useForm({
    plan_name: "",
    custom_price: null,
    sku: "",
    server_type: serverType.value,
    product_id: product_id.value,
    is_active: true
});

const generateSku = computed(() => {
    if (!selectedPlan.value) {
        return '';
    }

    const parts = selectedPlan.value?.sku.split('-');
    parts[0] = props.skuPrefix;
    return parts.join('-');
});

const submit = () => {
    Inertia.visit('/white-label/onboarding/domain-setup')
}

const skipCreateProduct = () => {
    Inertia.visit('/white-label/onboarding/domain-setup')
}

const addNewProduct = () => {
    form.custom_price = null;
    addStepTwoProductModal.value = false;
    addProductModal.value = true;
}

const addNewProductStepTwo = () => {
    if (selectedPlan.value) {
        let minimumPrice = selectedPlan.value?.price * 1.1;
        minPrice.value = parseFloat(minimumPrice.toFixed(2));
        addProductModal.value = false;
        addStepTwoProductModal.value = true;
    } else {
        useFlash().error('Please select a Hosting Plan');
    }
}

const saveProduct = () => {
    if (priceError.value) {
        return;
    }

    isPublishRunning.value = true;

    form.post('/white-label/products/store', {
        preserveScroll: true,
        onSuccess: () => {
            form.plan_name = '';
            addProductModal.value = false;
            addStepTwoProductModal.value = false;
            isPublishRunning.value = false;
            resetForm();
            Inertia.reload();
            //Inertia.visit('/white-label/onboarding/domain-setup')
        },
        onError: (errors) => {
            isPublishRunning.value = false;
        }
    });
}

onMounted(() => {
    const selectedProduct = whiteLabelAllProducts.value && whiteLabelAllProducts.value.find(
        (product) => product.memory === '2 GB'
    );

    if (selectedProduct) {
        selectedPlan.value = selectedProduct;
    }
});

function calculateProfit(previousPrice, newPrice, feePercentage) {
    const currentPrice = newPrice * (1 - feePercentage / 100);
    return (currentPrice - previousPrice).toFixed(2);
}

const changeSku = () => {
    form.errors.sku = ""
}

const closeModalOne = () => {
    addProductModal.value = false;
    resetForm();
}

const closeModalTwo = () => {
    addStepTwoProductModal.value = false;
    resetForm();
}

const resetForm = () => {
    form.plan_name = "";
    form.custom_price = null;
    form.sku = generateSku.value;
}

watch(selectedPlan, (newVal) => {
    isSuggestion.value = newVal && newVal?.memory === '1 GB';

    form.product_id = newVal?.id || null;
});

watch(generateSku, (newVal) => {
    form.sku = newVal || null;
});

watch(() => form.custom_price, (newPrice) => {
    if (newPrice && parseFloat(newPrice) < minPrice.value) {
        priceError.value = true;
        form.errors.custom_price = `Price must be at least $${minPrice.value}`;
    } else {
        priceError.value = false;
        form.errors.custom_price = '';
    }
});
</script>
