<template>
    <Head title="Registration"/>
    <div class="xc-container">
        <div class="flex-1 items-center flex flex-col justify-center"
             :class="navigation.isOpenHelpbar(helper.isOpen) ? 'mr-400px small-laptop:mr-0' : 'mr-0'"
        >

            <span v-if="currentWhiteLabel && currentWhiteLabel.brand_photo_url">
                <img :src="currentWhiteLabel.brand_photo_url" alt="" class="w-32 mx-auto mobile:mb-2">
            </span>

            <span v-else>
                <img :src="asset('img/x-cloud_blue.svg')" alt="" class="h-20 w-60 mx-auto mobile:mb-2">
            </span>

            <div class="auth-wrapper max-w-590px w-full bg-white dark:bg-mode-light rounded-10px p-60px tablet:p-50px
                        wide-mobile:p-40px mobile:p-25px mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <h2 v-if="currentWhiteLabel" class="auth-title text-4xl font-medium leading-none text-dark dark:text-white mb-30px -tracking-wide mobile:text-3xl text-center">
                    {{ $t('Create Your Account') }}
                    <span class="block mt-2 text-base font-normal text-secondary-full dark:text-mode-secondary-dark text-center">{{ $t('Enter your credentials to sign up.') }}</span>
                </h2>
                <h2 v-else class="auth-title text-4xl font-medium leading-none text-dark dark:text-white mb-30px -tracking-wide mobile:text-3xl text-center">
                    {{ $t('Get Started For Free') }}
                    <span class="block mt-2 text-base font-normal text-secondary-full dark:text-mode-secondary-dark text-center">{{ $t('Access 1 server and 10 sites at $0 cost. No credit card required.') }}</span>
                </h2>

                <form @submit.prevent="submit">
                    <div class="flex flex-col gap-30px">
                        <text-input v-model="form.name"
                                    :error="form.errors.name"
                                    id="name"
                                    icon="xcloud xc-user"
                                    :placeholder="$t('Enter your name')"
                                    :label="$t('Name')"
                                    :autofocus="autofocus"/>

                        <text-input v-model="form.email"
                                    :error="form.errors.email"
                                    id="email"
                                    icon="xcloud xc-email"
                                    type="email"
                                    :placeholder="$t('Enter your email')"
                                    :label="$t('Email')"/>
                        <password-input v-model="form.password"
                                        :error="form.errors.password"
                                        id="password"
                                        icon="xcloud xc-pass_lock"
                                        type="password"
                                        placeholder="********"
                                        :note="$t('Please choose a strong password. Use at least 8 characters, including a mix of letters, numbers and symbols.')"
                                        :label="$t('Password')"/>

                        <password-input v-model="form.password_confirmation"
                                        :error="form.errors.password_confirmation"
                                        id="password_confirmation"
                                        icon="xcloud xc-pass_lock"
                                        type="password"
                                        placeholder="********"
                                        :label="$t('Retype Password')"/>

                    </div>
                    <label class="mt-30px inline-flex">
                        <input v-model="form.terms" type="checkbox" class="hidden peer"/>
                        <span class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent
                                        before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded-full
                                        before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center
                                        before:text-transparent before:outline-none before:transition before:duration-200
                                        peer-checked:before:border-success-full peer-checked:before:bg-success-full
                                        peer-checked:before:text-white">
                              <span class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark">
                                  {{ $t('By creating an account, you agree to our') }}
                                  <span v-if="$page?.props?.current_white_label">
                                      <template v-if="$page?.props?.current_white_label?.settings?.tos_page?.tos_type === 'custom'">
                                          <a target="_blank" :href="$page?.props?.current_white_label?.settings?.tos_page?.tos_url" class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light
                                              dark:hover:text-primary-light ">
                                            {{ $t('Terms of Service') }}
                                          </a>
                                      </template>
                                      <template v-else>
                                          <a target="_blank" href="/terms-and-services" class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light
                                              dark:hover:text-primary-light">
                                           {{ $t('Terms of Service') }}
                                          </a>
                                      </template>

                                      <span class="mx-2"> {{ $t('and') }} </span>

                                      <template v-if="$page?.props?.current_white_label?.settings?.privacy_policy?.privacy_policy_type === 'custom'">
                                          <a target="_blank" :href="$page?.props?.current_white_label?.settings?.privacy_policy?.privacy_policy_url" class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light
                                                      dark:hover:text-primary-light">
                                           {{ $t('Privacy Policy') }}
                                          </a>
                                      </template>

                                      <template v-else>
                                          <a target="_blank" href="/privacy-policy" class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light
                                                      dark:hover:text-primary-light">
                                           {{ $t('Privacy Policy') }}
                                          </a>
                                      </template>
                                  </span>

                                  <span v-if="!$page?.props?.current_white_label">
                                      <a target="_blank" href="https://xcloud.host/terms-and-conditions/" class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light
                                        dark:hover:text-primary-light">
                                        {{ $t('Terms of Service') }}
                                      </a>
                                        {{ $t('and') }}
                                      <a target="_blank" href="https://xcloud.host/privacy-policy/" class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light
                                              dark:hover:text-primary-light">
                                          {{ $t('Privacy Policy') }}
                                      </a>
                                  </span>
                              </span>
                        </span>
                    </label>
                    <div v-if="form.errors.terms" class="text-danger">{{ form.errors.terms }}</div>
                    <button type="submit"
                            :disabled="!form.terms"
                            class="flex items-center justify-center min-h-60px p-2 px-30px mt-30px rounded-10px shadow-none text-lg
                                    text-center text-white font-medium bg-primary-light w-full focus:outline-none hover:bg-primary-light
                                    ease-in-out transition duration-200 hover:shadow-lg hover:shadow-primary-dark/30"
                            :class="{'opacity-50 cursor-not-allowed' : !form.terms}"
                    >
                        <span class="drop-shadow-button">{{ $t('Sign Up') }}</span>
                    </button>
                </form>

                <span class="block mt-25px text-base font-normal text-secondary-full dark:text-mode-secondary-dark">
                      {{ $t('Already have an account?') }}
                      <Link class="text-dark dark:text-white font-medium hover:underline hover:text-primary-light dark:hover:text-primary-light"
                          href="/login">
                          {{ $t('Log In') }}
                      </Link>
                </span>
            </div>
        </div>
    </div>
</template>

<script setup>
import {useForm, usePage} from '@inertiajs/inertia-vue3';
import TextInput from '@/Shared/TextInput.vue';
import PasswordInput from '@/Shared/PasswordInput.vue';
import {useHelpStore} from "@/stores/HelpStore";
import {onMounted, ref, watch} from "vue";
import {useNavigationStore} from "@/stores/NavigationStore";

let helper = useHelpStore();
const navigation = useNavigationStore();
const currentWhiteLabel = ref(usePage().props.value?.current_white_label);
const autofocus = ref(false);
onMounted(async () => {
    autofocus.value = true;

    // Automatically get the user's time zone
    form.time_zone = Intl.DateTimeFormat().resolvedOptions().timeZone;  // Set the timezone in the form data
})

const form = useForm({
    name: '',
    email: '',
    password: '',
    password_confirmation: '',
    terms: false,
    time_zone: '',
})

function submit() {
    form.post(route('register'), {
        onFinish: () => form.reset('password', 'password_confirmation'),
    })
}
</script>
