<template>
    <single-site :server="server" :site="site" active="Git">
        <div
            class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px flex-1">
            <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                <div
                    class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col
                    items-center wide-mobile:px-15px gap-20px mobile:gap-1 justify-between">
                    <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                       {{ $t('Git Settings') }}
                    </h4>
                </div>
                <div
                    class="p-30px h-full bg-white dark:bg-mode-base rounded-md flex flex-col items-start tablet:grid
                        tablet:grid-cols-1 gap-30px tablet:gap-10px">
                    <form @submit.prevent="submit" class="w-full">
                        <div class="grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px">
                            <text-input
                                :model-value="git_info?.git_repository"
                                :disabled="true"
                                :readonly="true"
                                id="git_repository"
                                placeholder="****************:user/repository.git"
                                :label="$t('Git Repository')"
                            />
                            <text-input
                                v-model="form.git_branch"
                                :error="form.errors.git_branch"
                                id="git_branch"
                                :placeholder="$t('master')"
                                :label="$t('Git Branch')"
                            />
                        </div>
                        <div class="grid grid-cols-1 wide-mobile:grid-cols-1 gap-20px mt-5">
                            <Switch
                                @click.prevent="form.enable_push_deploy = !form.enable_push_deploy"
                                :checked="form.enable_push_deploy"
                            >
                                {{ $t('Enable push to deploy') }}
                            </Switch>
                            <div v-if="form.enable_push_deploy" class="relative block mt-5 border-1 border-secondary-light dark:border-mode-focus-light rounded-md p-20px mobile:px-10px mobile:py-15px">
                                <text-input
                                    v-model="form.git_deployment_url"
                                    :error="form.errors.git_deployment_url"
                                    id="deployment_url"
                                    :label="$t('Deployment URL')"
                                    readonly
                                />
                                <CopyButton
                                    :content="form.git_deployment_url"
                                    align="top"
                                    color="primary"
                                />
                            </div>
                        </div>

                        <div class="grid grid-cols-1 wide-mobile:grid-cols-1 gap-20px my-5">
                            <textarea-input
                                v-model="form.deploy_script"
                                :error="form.errors.deploy_script"
                                id="deploy_script"
                                :placeholder="$t('npm run deploy')"
                                :label="$t('Deploy Script')"
                            />
                            <Switch
                                @click.prevent="form.run_after_deployment = !form.run_after_deployment"
                                :checked="form.run_after_deployment"
                            >
                                {{ $t('Run this script after every site deployment') }}
                            </Switch>

                            <Switch
                                @click.prevent="form.pull_now = !form.pull_now"
                                :checked="form.pull_now"
                            >
                                {{ $t('Pull and deploy now') }}
                            </Switch>
                        </div>
                        <!-- Submit Button -->
                        <div class="flex justify-start mt-50px">
                            <btn
                                type="submit"
                                class="btn btn-primary"
                                processing="true"
                                :disabled="form.processing"
                            >
                                {{ $t('Save') }}
                            </btn>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </single-site>

</template>

<!-- script -->
<script setup>

import SingleSite from "@/Pages/Site/SingleSite.vue";
import {useForm} from "@inertiajs/inertia-vue3";
import Btn from "@/Shared/Btn.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import CopyButton from "@/Shared/CopyButton.vue";
import TextInput from "@/Shared/TextInput.vue";
import TextareaInput from "@/Shared/TextareaInput.vue";
import {useFlash} from "@/Composables/useFlash";
import Switch from "@/Shared/Switch.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";

const {git_info,deploy_script,git_deployment_url,site,server} = defineProps({
    site: Object,
    server: Object,
    git_info: Object,
    deploy_script:{
        type: String,
        default: null
    },
    git_deployment_url:{
        type: String,
        default: null
    }
});

let form = useForm({
    deploy_script: deploy_script,
    git_branch: git_info?.git_branch,
    enable_push_deploy: git_info?.enable_push_deploy,
    git_deployment_url: git_deployment_url,
    run_after_deployment: git_info?.run_after_deployment,
    pull_now: true,
});
const submit = () => {
    form.post(route('api.site.git.update', {server:server.id,site: site.id}), {
        preserveScroll: true,
        onSuccess: () => {
            useFlash().success('Git settings updated successfully.');
        }
    });
};
</script>
