<template>
    <single-site :server="server" :site="site" active="Vulnerability Scan">
        <template v-if="!site?.has_vulnerability_scan">
            <div class="bg-white dark:bg-mode-light rounded-r-lg flex flex-col gap-6 p-6">
                <div class="flex flex-col gap-30px wide-mobile:p-30px mobile:p-20px w-full items-center">
                    <div class="flex flex-col gap-30px items-center">
                        <Empty
                            :can-perform-action="false"
                            type="site"
                        />
                        <p class="text-secondary-full text-lg dark:text-mode-secondary-light">
                            {{ $t('Vulnerability detection is currently disabled for this site. To enable it, please visit Server>Security Settings or') }}
                            <Link :href="route('server.vulnerability-settings', server.id)" class="text-primary-light underline">{{ $t('click here') }}</Link>
                        </p>
                    </div>
                </div>
            </div>
        </template>
        <template v-else>
            <div v-if="isBannerOpen && !$page?.props?.current_white_label">
                <div v-if="!patchstackVulnerability" class="flex flex-col md:flex-row items-center justify-between gap-4 ml-30px mt-30px bg-gradient-to-r from-[#FFFFFF] dark:from-[#2B384C] to-[#F9FFF0] dark:to-[#233D00] border border-[#E3EDF6] dark:border-[#324251] dark:border-r-[#366013] text-dark dark:text-white mobile:mt-20px mobile:ml-20px mobile:gap-3 rounded-[4px] px-5 py-4">
                    <div class="flex flex-col md:flex-row items-center gap-4 mx-auto">
                        <div class="flex flex-col md:flex-row items-center gap-4 font-medium text-base">
                            <span class="font-medium text-[16px] text-dark dark:text-white text-center md:text-left">
                                {{ $t('Upgrade to Vulnerability Shield Pro for automated vulnerability detection & virtual patching powered by Patchstack') }}
                            </span>
                            <button @click="vulnerabilityShieldUpgradeRef.openUpgradeModal()" class="bg-[#AFE614] font-semibold text-[#182902] px-[14px] py-[7px] rounded-[4px] whitespace-nowrap">
                                {{ $t('Go PRO for') }} ${{ productPrice }}/{{ $t('mo') }} {{ $t('Only') }}
                            </button>
                        </div>
                    </div>

                    <div class="flex items-center justify-center min-w-[24px] h-[24px] bg-[#FFFFFF] dark:bg-[#344c15] rounded-[4px] ml-2">
                        <button @click="closeBanner" class="text-dark dark:text-white flex items-center justify-center">
                            <i class="xcloud xc-close1"></i>
                        </button>
                    </div>
                </div>
                <div v-else class="flex flex-row items-center px-30px py-5px justify-start text-dark dark:text-white rounded-[4px] mt-5">
                    <div class="flex tablet:flex-col sm:flex-row items-center justify-center gap-2 rounded-lg p-2">
                        <span class="flex items-center gap-2">
                            <i class="xcloud xc-shield text-2xl text-success-full"></i>
                        </span>
                        <span class="text-[16px] font-[16px]">
                            {{ $t('Vulnerability Shield Pro is active!') }}
                        </span>
                        <span class="text-secondary-full dark:text-[#A8ACBC] text-[14px] font-normal">
                            {{ $t("Vulnerability Shield Pro will now deliver faster alerts to keep you ahead of threats.") }}
                        </span>
                    </div>
                </div>
            </div>

            <div class="gap-30px ml-30px mt-30px dark:bg-mode-light  mobile:mt-20px mobile:ml-20px mobile:gap-20px bg-white rounded-10px px-20px py-15px flex justify-between items-center">
                <button @click.prevent="reScan()" type="button"
                        :disabled="site.is_disabled"
                        :class="{
                            'cursor-not-allowed opacity-70':site.is_disabled || refreshing
                        }"
                        class="inline-flex items-center rounded-3xl shadow-none px-1 py-1 pr-10px bg-primary-light/10 text-sm text-primary-light focus:outline-0 gap-1">
                    <span class="rounded-full bg-primary-light h-25px w-25px shrink-0 flex justify-center items-center"><i
                        :class="{'animate-spin': refreshing}" class="xcloud xc-verify_dns text-white pt-[0.02rem]"></i></span><span>{{ $t('Refresh') }}</span>
                </button>

                <!-- Filter Options -->
                <div class="flex items-center gap-2">
                    <div class="flex-row flex gap-2 items-center">
                        <SelectorDropdown
                            :title="filter"
                            position="center"
                        >
                            <template #items>
                                <SelectItem
                                    title="All"
                                    :isActive="filter === 'all'"
                                    @onItemSelected="filter = 'all'"
                                />
                                <SelectItem
                                    title="Insecure"
                                    :isActive="filter === 'insecure'"
                                    @onItemSelected="filter = 'insecure'"
                                />
                                <SelectItem
                                    title="Secure"
                                    :isActive="filter === 'secure'"
                                    @onItemSelected="filter = 'secure'"
                                />
                            </template>
                        </SelectorDropdown>
                    </div>
                    <span class="text-sm text-secondary-full dark:text-secondary-light leading-tight">{{ $t('Last Checked') }} {{ last_update_check }}</span>
                </div>
            </div>

            <box>
                <template #header>
                    {{ $t('WordPress Core') }}
                </template>
                <template #datetime>
                    <div class="inline-flex items-center gap-1">
                        <span>{{ $t('Last Scanned') }}: {{last_update_check}}</span>
                    </div>
                </template>

                <template #body>
                    <div class="flex flex-row">
                        <div class="flex flex-col grow gap-30px wide-mobile:p-30px mobile:p-20px w-full">
                        <div v-if="scan_list?.wp_core && (Array.isArray(scan_list?.wp_core) && scan_list?.wp_core.length > 0)"
                             v-for="wp_core in scan_list?.wp_core"
                             :key="wp_core"
                             class="bg-transparent -mt-3 dark:bg-mode-light border-2 border-light dark:border-mode-base rounded-10px flex flex-col">
                            <div class="overflow-x-auto w-full overflow-y-hidden">
                                <div
                                    class="border-2 border-light dark:border-mode-base p-30px flex gap-15px items-center flex-wrap rounded-md">
                                    <i class="xcloud xc-wordpress text-dark dark:text-white text-4xl"></i>
                                    <div class="flex flex-col gap-10px">
                                        <h3 class="text-lg text-dark dark:text-white leading-none">{{ $t('An Updated Version of WordPress is Available') }} <span class="capitalize">({{ wp_core?.update_type }})</span></h3>
                                        <p class="text-sm text-secondary-full dark:text-secondary-light leading-tight">
                                            {{ $t('Your current version is') }} {{ scan_list?.wp_version }}
                                        </p>
                                    </div>
                                    <div class="flex items-center flex-wrap gap-20px ml-auto">
                                        <button v-if="scan_list?.updating?.core.find(core_version=> core_version?.version === wp_core?.version)" type="button"
                                                class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1.5 ml-2 pr-3.5 bg-primary-light/10 text-sm text-primary-light focus:outline-0 gap-1.5"
                                                aria-expanded="true" aria-haspopup="true">
                                                <span
                                                    class="rounded-full bg-primary-light h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                    <i class="xcloud xc-spinner text-white animate-spin text-xxs" v-if="updating_core.includes(wp_core?.update_type)"></i>
                                                    <i v-else class="xcloud xc-angle_right text-white text-xxs"></i>
                                                </span>
                                            <span>Updating to {{ wp_core?.version }} </span>
                                        </button>
                                        <button v-else type="button"
                                                @click="updateWordPress(wp_core)"
                                                class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1.5 ml-2 pr-3.5 bg-primary-light/10 text-sm text-primary-light focus:outline-0 gap-1.5"
                                                aria-expanded="true" aria-haspopup="true">
                                                <span
                                                    class="rounded-full bg-primary-light h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                    <i class="xcloud xc-spinner text-white animate-spin text-xxs" v-if="updating_core.includes(wp_core?.update_type)"></i>
                                                    <i v-else class="xcloud xc-angle_right text-white text-xxs"></i>
                                                </span>
                                            <span>{{ wp_core?.version }} </span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else
                             class="flex gap-15px items-center flex-wrap rounded-md">
                            <i class="xcloud xc-wordpress text-dark dark:text-white text-4xl"></i>
                            <h4 class="dark:text-secondary-light text-xl">
                                {{ $t('Your WordPress') }} <span v-if="scan_list?.wp_version">({{ scan_list?.wp_version }})</span> {{ $t('is up to date.') }}
                            </h4>
                        </div>
                    </div>
                    </div>
                    <div v-if="scan_list?.wordpress?.length > 0" class="flex flex-row gap-2 items-center border rounded p-2 py-5">
                            <span class="rounded-full bg-warning h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                <i class="xcloud inline-flex xc-close-o text-white text-xxs"></i>
                            </span>
                        <span class="text-success-lite dark:text-white">{{ $t('Insecure') }}</span>
                        <!--view details-->
                        <button @click.prevent="showDetails(wordpress_vulnerabilities)" type="button" class="bg-info text-white text-sm px-2 py-1 rounded-3xl disabled:opacity-50 cursor-pointer">Details</button>
                    </div>
                </template>
            </box>
            <box>
                <template #header>
                    {{ $t('Plugins') }}
                </template>
                <template #datetime>
                    <div class="inline-flex items-center gap-1">
                        <span>{{ $t('Last Scanned') }}: {{last_update_check}}</span>
                    </div>
                </template>
                <template #body>
                    <div class="flex flex-col grow gap-30px wide-mobile:p-30px mobile:p-20px w-full">
                        <div
                            class="bg-transparent dark:bg-mode-light border-2 border-light dark:border-mode-base rounded-10px flex flex-col">
                            <div class="overflow-x-auto w-full overflow-y-hidden">
                                <skeleton class="mb-0" v-if="refreshing" :columns="5" :rows="Math.max(scan_list?.plugins?.length,5)"/>
                                <div
                                    v-else-if="filter_plugins?.length === 0"
                                    class="my-2"
                                >
                                    <empty
                                        :documentation="`<div class='text-center flex flex-col gap-2'>
                                        <p class='text-[24px] font-medium text-[#1F2328] dark:text-white'>${$t('Excellent! Your plugins are fully secure!')}</p>
                                        <p class='text-[#40464F] dark:text-[#919DB9] text-[14px] font-normal'>${$t('No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $')+productPrice+$t('/mo for advanced vulnerability detection and automated patching powered by Patchstack.')}</p>
                                        </div>`"
                                        :can-perform-action="false"
                                        :type="emptyComponentType"
                                        :no-vulnerabilities="true"
                                    />
                                </div>
                                <table v-else class="w-full divide-y-1 divide-light dark:divide-mode-light">
                                    <thead class="bg-primary-dark dark:bg-mode-focus-dark text-white">
                                    <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Name') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Status') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('CVSS Score') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Action') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-mode-base divide-y-1 divide-light  dark:divide-mode-light text-dark dark:text-white">
                                    <tr v-if="scan_list?.plugins" v-for="plugin in filter_plugins" class="divide-x-1 divide-light dark:divide-mode-light">
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                            <div
                                                class="inline-flex items-center text-base font-normal text-dark dark:text-white group">
                                                <img
                                                    :src="`https://ps.w.org/${plugin.name}/assets/icon-256x256.png`"
                                                    @error="(e) => {
                                                        if(e.errorCount){
                                                            return;
                                                        }
                                                        e.target.src = defaultLogo(plugin.name)
                                                         e.errorCount = true;
                                                    }"
                                                    class="w-40px h-40px min-w-40px shrink-0 bg-light dark:bg-mode-base rounded-full mr-10px flex items-center justify-center object-cover" :alt="plugin?.slug"/>
                                                <span class="flex flex-col">
                                                    <span class="text-lg min-w-[15rem]">{{ getTitle(plugin) }}</span>
                                                     <span class="text-xs">{{ $t('Current Version') }}: {{ plugin.version }}</span>
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                            <div class="flex flex-row gap-2 items-center" v-if="plugin?.vulnerabilities?.length">
                                                <span
                                                    class="rounded-full bg-warning h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                        <i class="xcloud inline-flex xc-close-o text-white text-xxs"></i>
                                                    </span>
                                                <span class="text-success-lite">Insecure</span>
                                                <!--view details-->
                                                <button @click.prevent="showDetails(highestScoredVulnerability(plugin?.vulnerabilities))" type="button" class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white underline rounded capitalize">{{ $t('Details') }}</button>
                                            </div>
                                            <div v-else class="flex flex-row gap-2 items-center">
                                             <span
                                                class="rounded-full bg-success-full h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                    <i class="xcloud inline-flex xc-done text-white text-xxs"></i>
                                                </span>
                                                <span class="text-success-lite">{{ $t('Secure') }}</span>
                                            </div>
                                        </td>
                                        <td class="px-40px py-2 text-left text-base font-normal h-60px">
                                            <template v-if="plugin?.vulnerabilities?.length">
                                                <tooltip
                                                    :html="true"
                                                    :title="'<b>Remediation</b> - '+highestScoredVulnerability(plugin?.vulnerabilities)?.remediation">
                                                <span
                                                    :class="{
                                                        'bg-danger': JSON.parse(highestScoredVulnerability(plugin?.vulnerabilities)?.cvss)?.rating === 'Critical',
                                                        'bg-danger/10 text-danger': JSON.parse(highestScoredVulnerability(plugin?.vulnerabilities)?.cvss)?.rating === 'High',
                                                        'bg-warning/10 text-warning': JSON.parse(highestScoredVulnerability(plugin?.vulnerabilities)?.cvss)?.rating === 'Medium',
                                                        'text-primary-light bg-primary-light/10': JSON.parse(highestScoredVulnerability(plugin?.vulnerabilities)?.cvss)?.rating === 'Low'
                                                    }"
                                                    style="text-wrap: nowrap"
                                                    class="text-white text-sm px-2 py-1 rounded-3xl">
                                                    {{JSON.parse(highestScoredVulnerability(plugin?.vulnerabilities)?.cvss)?.score }}
                                                    <b>[{{JSON.parse(highestScoredVulnerability(plugin?.vulnerabilities)?.cvss)?.rating}}]</b>
                                                </span>
                                                </tooltip>
                                            </template>
                                        </td>
                                        <td
                                            class="px-30px flex flex-row py-2 text-left text-base font-normal h-60px gap-2">
                                            <div
                                                v-if="Array.isArray(plugin?.vulnerabilities) && highestScoredVulnerability(plugin?.vulnerabilities)?.remediation"
                                                class="flex gap-2">
                                                <button v-if="plugin?.update_version && !plugin.hasOwnProperty('update_action')"
                                                        @click.prevent="updatePlugin(plugin)"
                                                        class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white underline rounded capitalize"
                                                >
                                                    {{ $t('Update') }}
                                                </button>
                                            </div>
                                            <div
                                                v-if="Array.isArray(plugin?.vulnerabilities) && highestScoredVulnerability(plugin?.vulnerabilities)?.remediation"
                                                class="flex gap-2">
                                                <button
                                                        @click.prevent="ignoreVulnerability(plugin,ignoreList.includes(plugin?.name),'plugin')"
                                                        class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white rounded capitalize"
                                                >
                                                    {{ ignoreList.includes(plugin?.name) ? $t('Ignored') : $t('Ignore') }}
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </template>
            </box>
            <box>
                <template #header>
                    {{ $t('Themes') }}
                </template>
                <template #datetime>
                    <div class="inline-flex items-center gap-1">
                        <span>{{ $t('Last Scanned') }}: {{last_update_check}}</span>
                    </div>
                </template>
                <template #body>
                    <div class="flex flex-col grow gap-30px wide-mobile:p-30px mobile:p-20px w-full">
                        <div
                            class="bg-transparent dark:bg-mode-light border-2 border-light dark:border-mode-base rounded-10px flex flex-col">
                            <div class="overflow-x-auto w-full overflow-y-hidden">
                                <skeleton class="mb-0" v-if="refreshing" :columns="5" :rows="Math.max(scan_list?.themes?.length,4)"/>
                                <div
                                    v-else-if="filter_themes.length === 0"
                                    class="my-2"
                                >
                                    <empty
                                        :documentation="`<div class='text-center flex flex-col gap-2'>
                                        <p class='text-[24px] font-medium text-[#1F2328] dark:text-white'>${$t('Excellent! Your theme is fully secure!')}</p>
                                        <p class='text-[#40464F] dark:text-[#919DB9] text-[14px] font-normal'>${$t('No issues were detected by the Vulnerability Scanner. You’ll be notified immediately if any issues are found. Upgrade to Pro for just $')+ productPrice +$t('/mo for advanced vulnerability detection and automated patching powered by Patchstack.')}</p>
                                        </div>`"
                                        :can-perform-action="false"
                                        :type="emptyComponentType"
                                        :no-vulnerabilities="true"
                                    />
                                </div>
                                <table v-else class="w-full divide-y-1 divide-light dark:divide-mode-light">
                                    <thead class="bg-primary-dark dark:bg-mode-focus-dark text-white">
                                    <tr class="divide-x-1 divide-light dark:divide-mode-light">

                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Name') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Status') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('CVSS Score') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Action') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-mode-base divide-y-1 divide-light  dark:divide-mode-light text-dark dark:text-white">
                                        <tr v-if="scan_list?.themes" v-for="theme in filter_themes"
                                            class="divide-x-1 divide-light dark:divide-mode-light">
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                            <div
                                                class="inline-flex justify-center items-center text-base font-normal text-dark dark:text-white group">
                                                <img
                                                    :alt="theme.name"
                                                    :src="`https://ts.w.org/wp-content/themes/${theme.name}/screenshot.png?ver=${theme.version}`"
                                                    @error="(e) => {
                                                        if(e.errorCount){
                                                            return;
                                                        }
                                                       e.target.src = defaultLogo(theme.name)  //increment the error count
                                                        e.errorCount = true;
                                                    }"
                                                    class="w-40px h-40px min-w-40px shrink-0 bg-light dark:bg-mode-base rounded-full mr-10px flex items-center justify-center object-cover"/>

                                                <span class="flex flex-col">
                                                    <span class="text-lg">{{ getTitle(theme) }}</span>
                                                    <span class="text-xs">Current Version: {{ theme.version }}</span>
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                            <div class="flex flex-row gap-2 items-center" v-if="theme?.vulnerabilities?.length">
                                                <span
                                                    class="rounded-full bg-warning h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                        <i class="xcloud inline-flex xc-close-o text-white text-xxs"></i>
                                                    </span>
                                                <span class="text-success-lite">Insecure</span>
                                                <!--view details-->
                                                <button @click.prevent="showDetails(highestScoredVulnerability(theme?.vulnerabilities))" type="button" class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white underline rounded capitalize">Details</button>
                                            </div>
                                            <div v-else class="flex flex-row gap-2 items-center">
                                             <span
                                                class="rounded-full bg-success-full h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                    <i class="xcloud inline-flex xc-done text-white text-xxs"></i>
                                                </span>
                                                <span class="text-success-lite">{{ $t('Secure') }}</span>
                                            </div>
                                        </td>
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">

                                            <template v-if="theme?.vulnerabilities?.length">
                                                <tooltip
                                                    :html="true"
                                                    :title="'<b>Remediation</b> - '+highestScoredVulnerability(theme?.vulnerabilities)?.remediation"
                                                    >
                                                <span
                                                    :class="{
                                                        'bg-danger': JSON.parse(highestScoredVulnerability(theme?.vulnerabilities)?.cvss)?.rating === 'Critical',
                                                        'bg-danger/10 text-danger': JSON.parse(highestScoredVulnerability(theme?.vulnerabilities)?.cvss)?.rating === 'High',
                                                        'bg-warning/10 text-warning': JSON.parse(highestScoredVulnerability(theme?.vulnerabilities)?.cvss)?.rating === 'Medium',
                                                        'text-primary-light bg-primary-light/10': JSON.parse(highestScoredVulnerability(theme?.vulnerabilities)?.cvss)?.rating === 'Low'
                                                    }"
                                                    style="text-wrap: nowrap"
                                                    class="text-white text-sm px-2 py-1 rounded-3xl">
                                                    {{JSON.parse(highestScoredVulnerability(theme?.vulnerabilities)?.cvss)?.score }}
                                                    <b>[{{JSON.parse(highestScoredVulnerability(theme?.vulnerabilities)?.cvss)?.rating}}]</b>
                                                </span>
                                                </tooltip>
                                            </template>
                                        </td>
                                        <td
                                            class="px-30px py-2 text-left text-base font-normal flex gap-2 h-60px">
                                            <div
                                                v-if="Array.isArray(theme?.vulnerabilities) && highestScoredVulnerability(theme?.vulnerabilities)?.remediation"
                                                class="flex gap-2">
                                                <button
                                                    v-if="theme?.update_version && !theme.hasOwnProperty('update_action')"
                                                    @click.prevent="updateTheme(theme)"
                                                    class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white underline rounded capitalize"
                                                >
                                                   {{ $t('Update') }}
                                                </button>
                                            </div>
                                            <div
                                                v-if="Array.isArray(theme?.vulnerabilities) && highestScoredVulnerability(theme?.vulnerabilities)?.remediation"
                                                class="flex gap-2">
                                                <button
                                                    @click.prevent="ignoreVulnerability(theme,ignoreList.includes(theme?.name),'theme')"
                                                    class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white rounded capitalize"
                                                >
                                                    {{ ignoreList.includes(theme?.name) ? $t('Ignored') : $t('Ignore') }}
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </template>
            </box>
            <template
            v-if="vulnerability!=null"
        >
        <Modal
            @close="vulnerability = null"
            :footer-button-title="$t('Close')"
            :footer-button="true"
            :show="vulnerability!=null"
            :title="vulnerability[0]?.name"
            :widthClass="'max-w-850px'">
            <div class="flex flex-col gap-30px">
                <div class="warning flex items-center bg-danger/20 px-6 py-4 rounded-md">
                    <img :src="asset('img/warning.svg')" alt="warning_img"/>
                    <p class="text-sm text-dark dark:text-white leading-7 pl-4">
                        <b>{{ $t('Warning') }}:</b> {{ vulnerability?.title }}
                    </p>
                </div>

                <div class="flex flex-col gap-30px">
                    <!-- Vulnerability Details -->
                    <p>
                        <span class="text-dark dark:text-white">{{ vulnerability?.description }}</span>
                    </p>
                    <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                        <tbody class="bg-white border dark:bg-mode-base divide-y-1 divide-light dark:divide-mode-light text-dark dark:text-white">
                        <tr class="divide-x-1 divide-light dark:divide-mode-light">
                            <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('ID') }}</th>
                            <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.uuid }}</td>
                        </tr>
                        <tr class="divide-x-1 divide-light dark:divide-mode-light">
                            <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Patched') }}</th>
                            <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.patched ? 'Yes' : 'No' }}</td>
                        </tr>
                        <tr v-if="vulnerability?.patched" class="divide-x-1 divide-light dark:divide-mode-light">
                            <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Fixed in') }}</th>
                            <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.patched_versions[0] }}</td>
                        </tr>
                        <tr class="divide-x-1 divide-light dark:divide-mode-light">
                            <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('CVSS Score') }}</th>
                            <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ JSON.parse(vulnerability?.cvss)?.score }} ({{JSON.parse(vulnerability?.cvss)?.rating}})</td>
                        </tr>
                        <tr v-if="vulnerability?.remediation" class="divide-x-1 divide-light dark:divide-mode-light">
                            <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Remediation') }}</th>
                            <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.remediation }}</td>
                        </tr>
                        <tr class="divide-x-1 divide-light dark:divide-mode-light">
                            <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Direct URL') }}</th>
                            <td class="px-30px py 2 text-left text-base font-normal h-60px">
                                <a
                                    class="text-primary-light underline text-sm"
                                    target="_blank" :href="`https://www.wordfence.com/threat-intel/vulnerabilities/id/${vulnerability?.uuid}?source=api-prod`">https://www.wordfence.com/threat-intel/vulnerabilities/id/{{vulnerability?.uuid}}?source=api-prod</a>
                            </td>
                        </tr>
                        <tr class="divide-x-1 divide-light dark:divide-mode-light">
                            <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('First Published') }}</th>
                            <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.created_at }}</td>
                        </tr>
                        <tr class="divide-x-1 divide-light dark:divide-mode-light">
                            <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Last Update') }}</th>
                            <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.updated_at }}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <template #footer>
                    <button
                        @click.prevent="vulnerability = null"
                        class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                        aria-expanded="true" aria-haspopup="true">
                        <span>{{ $t('Close') }}</span>
                    </button>
            </template>
        </Modal>
        </template>
        </template>
    </single-site>

    <!-- Vulnerability Shield Upgrade Flow Component -->
    <VulnerabilityShieldProUpgrade
        ref="vulnerabilityShieldUpgradeRef"
        :server-id="server.id"
        :site-id="site.id"
        :payment-methods="paymentMethods"
        :default-card="defaultCard"
        :product-price="productPrice"
        @success="handleUpgradeSuccess"
        @hidden="handleHiddenBanner"
    />
</template>

<!-- script -->
<script setup>
import { useFlash } from "@/Composables/useFlash";
import SingleSite from "@/Pages/Site/SingleSite.vue";
import Box from '@/Shared/Box.vue';
import { computed, ref } from "vue";
import Modal from "@/Shared/Modal.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Empty from "@/Shared/Empty.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import SelectorDropdown from "@/Shared/SelectorDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import { useI18n } from "vue-i18n";
import axios from "axios";
import VulnerabilityShieldProUpgrade from "@/Shared/VulnerabilityShieldProUpgrade.vue";
import { useForm } from "@inertiajs/inertia-vue3";
import { useNavigationStore } from "@/stores/NavigationStore";

const { t } = useI18n();

const navigation = useNavigationStore();

const nightMode = computed(() => navigation.nightMode);

const props = defineProps({
    site: Object,
    server: Object,
    scan_result: Object,
    wordpress_vulnerabilities: Object,
    ignored_vulnerabilities: Object,
    patchstack_vulnerability: Object,
    paymentMethods: {
        type: Object,
    },
    defaultCard: {
        type: Object,
        default: () => {}
    },
    productPrice: {
        type: Number,
        default: 5.0
    }
})
const vulnerability = ref(null);
const scan_list = ref(props.scan_result);
const refreshing = ref(false);
const filter = ref('insecure');
const ignoreList = ref(Object.values(props.ignored_vulnerabilities));
const last_update_check = ref(props.site.last_update_check);
const isBannerOpen = ref(!props.site?.meta?.hide_shield_pro_banner);
const vulnerabilityShieldUpgradeRef = ref(null);

const patchstackVulnerability = ref(props.patchstack_vulnerability);

const emptyComponentType = computed(() => {
    return nightMode.value ? 'vulnerability_free_dark' : 'vulnerability_free_light';
})

let form = useForm({
    enable_patchstack: true
});

const closeBanner = () => {
    isBannerOpen.value = !isBannerOpen.value;
}

const filter_plugins = computed(() => {
    const plugins = scan_list.value?.plugins || [];

    if (!plugins.length) return [];

    return plugins.filter((sitePlugin) => {
        switch (filter.value) {
            case 'all':
                return true;
            case 'secure':
                return sitePlugin.vulnerabilities?.length === 0;
            case 'insecure':
                return sitePlugin.vulnerabilities?.length > 0;
            default:
                return 'xcloud-auto-login' !== sitePlugin.name;
        }
    });
});

const filter_themes = computed(() => {
    const themes = scan_list.value?.themes || [];

    if (!themes.length) return [];

    return themes.filter((siteTheme) => {
        switch (filter.value) {
            case 'insecure':
                return siteTheme?.vulnerabilities && siteTheme?.vulnerabilities.length > 0;
            case 'secure':
                return !(siteTheme?.vulnerabilities && siteTheme?.vulnerabilities.length > 0);
            default:
                return true;
        }
    });
});

const showDetails = (vulnerabilities) => {
    vulnerability.value = vulnerabilities;
}

const getTitle = (item) => {
   if(Array.isArray(item.vulnerabilities) && item.vulnerabilities.length > 0){
     return highestScoredVulnerability(item.vulnerabilities)?.title;
   }
   return  item.title
}

const highestScoredVulnerability = (vulnerabilities) => {
    return vulnerabilities.sort((a, b) => {
        const scoreA = JSON.parse(a.cvss).score;
        const scoreB = JSON.parse(b.cvss).score;
        return scoreB - scoreA;
    })[0];
}

const updating_core = computed(() => {
    return scan_list.value?.updating?.core ?? [];
});

const updateWordPress = (wp_core) => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        useFlash().deleteConfirmation({
            'title': t('Are you sure you want to update WordPress?'),
            'text': t('Update WordPress Core'),
            'btn_text': t('Update')
        }, () => {
            axios.post(`/api/server/${props.server.id}/site/${props.site.id}/updateWordPress`, wp_core)
                .then(({data}) => {
                    useFlash().success('WordPress update started');
                })
                .catch(() => {
                    useFlash().error('WordPress update failed');
                })
        })
    }

}
const updatePlugin = (plugin) => {
    // console.log('plugin: ',plugin)
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        const plugins =  scan_list.value.plugins.filter((item) => item.name === plugin.name);
        useFlash().deleteConfirmation({
            'title': t('Are you sure you want to update the plugin?'),
            'text': t('Plugin will be updated to')+` ${plugin.update_version}`,
            'btn_text': t('Update')
        }, () => {
            axios.post(`/api/server/${props.server.id}/site/${props.site.id}/updatePlugin`, {plugins: plugins})
                .then(({data}) => {
                  useFlash().success( "Plugin update started");
                })
                .catch(() => {
                    useFlash().error('Plugin update failed');
                })
        })
    }
}

const ignoreVulnerability = (vulnerability,ignored,type) => {
    useFlash().deleteConfirmation({
        'title': t('Are you sure you want to')+` ${ignored ? t('not ignore') : t('ignore') } `+t('this vulnerability?'),
        'text': ``,
        'btn_text': t('Ignore')
    }, () => {
        axios.post(`/api/server/${props.server.id}/site/${props.site.id}/ignore-vulnerability`, {slug: vulnerability?.name, is_ignored: !ignored})
            .then(({data}) => {
                useFlash().success(`Vulnerability ${ignored ? 'not ignored' : 'ignored'}`);
                ignoreList.value = ignored ? ignoreList.value.filter((item) => item !== vulnerability?.name) : [...ignoreList.value, vulnerability?.name];
            })
            .catch(() => {
                useFlash().error('Vulnerability ignore failed');
            })
    });
}

const updateTheme = (theme) => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        const themes = scan_list.value.themes.filter((item) => theme.name === item.name && item?.update_version && !item.hasOwnProperty('update_action'));
        useFlash().deleteConfirmation({
            'title': t('Are you sure you want to update the theme?'),
            'text': t('You have select')+` ${theme.name} `+t('theme'),
            'btn_text': t('Update')
        }, () => {
            axios.post(`/api/server/${props.server.id}/site/${props.site.id}/updateTheme`, {themes: themes})
                .then(({data}) => {
                  useFlash().success( "Theme update started");
                })
                .catch(() => {
                    useFlash().error('Theme update failed');
                })
        })
    }
}

const defaultLogo = (name) => {
    return `https://ui-avatars.com/api/?name=${name}&color=7F9CF5&background=EBF4FF`;
}

const reScan = () => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else{
        if (!refreshing.value) {
            refreshing.value = true
            axios.post(route('api.site.scan-vulnerability', [props.server.id, props.site.id]))
                .then(({data}) => {
                    scan_list.value = data?.scan_result;
                    last_update_check.value = data?.last_update_check;
                    useFlash().success('Site vulnerability scan successful');
                })
                .catch(() => {
                    useFlash().error('Site vulnerability scan failed');
                }).finally(() => {  refreshing.value = false })
        } else {
            useFlash().info('Vulnerability scan already running');
        }
    }
}

// Handle successful upgrade
const handleUpgradeSuccess = (data) => {
    patchstackVulnerability.value = data;
    isBannerOpen.value = true;
}

const handleHiddenBanner = (data) => {
    isBannerOpen.value = !data;
}
</script>
<style>
.swal2-checkbox{
    @apply dark:bg-dark;
}
</style>
