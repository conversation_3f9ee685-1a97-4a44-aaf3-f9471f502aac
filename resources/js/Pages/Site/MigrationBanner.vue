<template>
    <div class="xc-container">
        <div class="flex-1 flex items-center mr-0">
            <div class="max-w-1050px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <h2
                    class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark dark:text-white wide-mobile:text-28px mobile:text-2xl mb-50px tablet:mb-40px wide-mobile:mb-30px">

                    <template v-if="site.type === 'laravel'">
                        {{ $t('Laravel application deployed successfully!') }}
                    </template>
                    <template v-else-if="site.type === 'custom-php'">
                        {{ $t('Custom PHP application deployed successfully!') }}
                    </template>
                    <template v-else>
                        {{ $t('Your Site Has Been Successfully Migrated') }}
                    </template>

                    <img :src="asset('img/png/success.png')" alt="xcloud_logo"
                         class="w-40px max-h-40px inline-block ml-20px mobile:ml-10px mobile:w-30px" />
                </h2>
                <div class="bg-white dark:bg-mode-light rounded-10px w-full">
                    <div
                        class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                        <div class="overflow-x-auto overflow-y-hidden">
                            <table class="min-w-full border-separate border-spacing-20px mobile:border-spacing-15px">
                                <tr>
                                  <td class="text-secondary-full dark:text-mode-secondary-light p-0">
                                      <span v-if="site.domain_parking_method && site.domain_parking_method === 'staging'">
                                          {{ $t('Staging URL') }}:
                                      </span>
                                      <span v-else>
                                          {{ $t('Domain Name') }}:
                                      </span>
                                  </td>
                                  <td class="text-dark dark:text-white p-0">
                                    <a :href="site.site_url" target="_blank" class="inline-flex items-center">
                                      {{site.name}}
                                      <a :href="site.site_url" target="_blank" class="inline-flex text-dark dark:text-white text-lg ml-2">
                                        <i class="xcloud xc-maximize"></i>
                                      </a>
                                    </a>
                                  </td>
                                </tr>
                                <tr>
                                    <td class="text-secondary-full dark:text-mode-secondary-light p-0">{{ $t('HTTPS') }}</td>
                                    <td class="text-dark dark:text-white p-0">
                                        <span v-if="has_ssl_certificate" class="inline-flex items-center"><i class="xcloud xc-tick-o text-lg mr-3 text-success-full"></i>Enabled</span>
                                        <span v-else class="inline-flex items-center"><i class="xcloud xc-close-o text-lg mr-3 text-danger"></i>Disabled</span>
                                    </td>
                                </tr>
                                <tr v-if="site.wordpress_version">
                                    <td class="text-secondary-full dark:text-mode-secondary-light p-0">
                                        {{ $t('WordPress Version') }}
                                    </td>
                                    <td class="text-dark dark:text-white p-0">{{site.wordpress_version}}</td>
                                </tr>
                                <tr>
                                    <td class="text-secondary-full dark:text-mode-secondary-light p-0">
                                        {{ $t('PHP Version') }}
                                    </td>
                                    <td class="text-dark dark:text-white p-0">{{site.php_version}}</td>
                                </tr>
                                <tr v-if="site?.is_wordpress">
                                    <td class="text-secondary-full dark:text-mode-secondary-light p-0">
                                        {{ $t('Full Page Cache') }}
                                    </td>
                                    <td class="text-dark dark:text-white p-0">
                                        <span v-if="has_full_page_caching" class="inline-flex items-center"><i class="xcloud xc-tick-o text-lg mr-3 text-success-full"></i>Enabled</span>
                                        <span v-else class="inline-flex items-center"><i class="xcloud xc-close-o text-lg mr-3 text-danger"></i>Disabled</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-secondary-full dark:text-mode-secondary-light p-0">
                                        {{ $t('Database Type') }}
                                    </td>
                                    <td class="text-dark dark:text-white p-0 capitalize">
                                        <p v-if="site.has_database">
                                        {{dbProvider}}
                                        </p>
                                        <p v-else>
                                            {{ $t('No Database') }}
                                        </p>
                                    </td>
                                </tr>
                                <tr v-if="site.database_name">
                                    <td class="text-secondary-full dark:text-mode-secondary-light p-0">
                                        {{ $t('Database') }}
                                    </td>
                                    <td class="text-dark dark:text-white p-0">{{site.database_name}}</td>
                                </tr>
                                <tr v-if="site.admin_user">
                                    <td class="text-secondary-full dark:text-mode-secondary-light p-0">
                                        {{ $t('Admin User Name') }}
                                    </td>
                                    <td class="text-dark dark:text-white p-0">{{site.admin_user}}</td>
                                </tr>
                            </table>
                        </div>

                      <div class="flex flex-col md:flex-row gap-20px mt-5">
                        <h5 class="text-dark dark:text-white pt-30px pl-20px pb-15px tablet:pt-20px mobile:pl-15px mobile:pt-20px">
                          <button @click.prevent="disappearBanner" class="underline">Site Dashboard</button>
                        </h5>
                        <template v-if="site.is_wordpress">
                        <h5 class="text-dark dark:text-white pt-30px pl-20px pb-15px tablet:pt-20px mobile:pl-15px mobile:pt-20px">
                            <MagicLoginButton
                            :site="site"
                            :external="true"
                            :buttonClass="'whitespace-nowrap underline'"
                            :showLogoutIcon="true"
                            />
                        </h5>

                          <h5 class="text-dark dark:text-white pt-30px pl-20px pb-15px tablet:pt-20px mobile:pl-15px mobile:pt-20px">
                          <a v-if="!$page?.props?.current_white_label" target="_blank"
                              href="https://xcloud.host/docs/setting-up-site-emails-for-wordpress-on-xcloud/"
                              class="whitespace-nowrap underline"
                          >
                            {{ $t('Setup Email for this site') }}
                          </a>
                        </h5>
                        </template>
                      </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<!-- script -->
<script setup>
import MagicLoginButton from "@/Shared/MagicLoginButton.vue";
import {Inertia} from "@inertiajs/inertia";
import {computed} from "vue";

const props = defineProps({
    site: Object,
    server: Object,
    additional_domains: Array,
    has_full_page_caching: Boolean,
    has_ssl_certificate: Boolean,
    has_basic_auth_enabled: Boolean,
    taskEvents: Object,
    database_provider: String,
})

const disappearBanner = () => {
    axios.post(route('api.site.disableMigrationBanner',{server:props.server,site:props.site})).then((response) => {
       //redirect to site overview
        Inertia.visit(route('site.overview',{server:props.server,site:props.site}));
    }).catch((error) => {
        console.log(error);
    });
}

const dbProvider = computed(() => {
    if(props.database_provider){
        return props.database_provider.replace(/_/g, ' ')
    }
    return 'Unknown';
});

</script>

<!-- style -->
<style scoped>

</style>
