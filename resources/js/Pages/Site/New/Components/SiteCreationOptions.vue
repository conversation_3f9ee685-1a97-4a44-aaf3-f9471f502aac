<template>
  <fieldset class="flex flex-wrap justify-center -mx-15px -mb-30px min-h-[500px]">
    <SiteCreationOption
      v-if="selectedType === 'wordpress'"
      :title="$t('Install New WordPress Website')"
      :description="$t('Select this option if you want to a create a fresh new WordPress website')"
      :icon="asset('img/wordpress-blue.svg')"
      :routeName="route('site.create.wordpress', {
          server: server.id,
          type: default_app_type
      })"
    />

    <SiteCreationOption
        v-if="selectedType !== 'oneclick'"
        :title="$t('Clone a Git Repository')"
        :description="$t('Clone a git repository to create a new website')"
        :icon="asset('img/git.svg')"
        :routeName="route('site.migrate.git.domains', {server:server.id, siteMigration: 'new', 'siteType': selectedType})"
    />

    <SiteCreationOption
      v-if="!($page.props.whiteLabelManageTeam || $page?.props?.current_white_label) && selectedType === 'wordpress'"
      :title="$t('Migrate An Existing WordPress Website')"
      :description="$t('Have an existing website already? Select this option to migrate it with ')+brandName"
      :icon="asset('img/wordpress_web.svg')"
      :beta="true"
      :routeName="route('site.migrate.auto.domains', {server:server.id, siteMigration: 'new'})"
    />
    <SiteCreationOption
        v-if="selectedType === 'wordpress' || selectedType === 'custom-php'"
      :title="$t('Manually Upload Website')"
      :description="$t('Upload a zipped file of your existing website (max size: 500 MB)')"
      :icon="asset('img/wordpress_upload.svg')"
      :routeName="route('site.migrate.manual.domains', {server:server.id, siteMigration:'new','siteType': selectedType})"
    />

  <SiteCreationOption
      v-if="selectedType === 'wordpress'"
      :title="$t('Migrate Full Server')"
      :description="$t('Migrate all WordPress sites from Ubuntu servers with a few clicks')"
      :icon="asset('img/server_migration.svg')"
      :routeName="route('server.migrate.source', {server:server.id, serverMigration:'new'})"
  />

  <SiteCreationOption
      v-if="selectedType === 'wordpress'"
      :title="$t('Recreate Site from Backup')"
      :description="$t('Restore site backup from local or remote storage easily to create a site')"
      :icon="asset('img/migrate_from_backups.svg')"
      :routeName="route('site.migrate.backup-restore', {server:server.id, siteMigration:'new'})"
  />

  <!-- One Click Apps Section -->
  <template v-for="app in oneClickApps" :key="app.id" v-if="selectedType === 'oneclick'">
    <SiteCreationOption
       v-if="app.is_active"
      :title="app.name"
      :description="app.description"
      :icon="asset(app.icon)"
      :routeName="route('site.create.oneclick', [server.id, {app_slug: app.slug}])"
    />
  </template>
  </fieldset>
</template>

<script setup>
import SiteCreationOption from "@/Pages/Site/New/Components/SiteCreationOption.vue";
import { defineProps, ref } from 'vue'
import {usePage} from "@inertiajs/inertia-vue3";

const { server } = defineProps({
  server: Object,
  default_app_type: {
    type: String,
    default: 'wordpress'
  },
  selectedType:{
    type: String,
    default: 'wordpress'
}
})
let brandName = usePage().props.value?.current_white_label ? usePage().props.value?.current_white_label?.branding?.brand_name : 'xCloud';

// Get one-click apps from props
const oneClickApps = usePage().props.value.oneClickApps || [];
</script>
