<template>
    <div>
        <div v-if="form.additional_domains && form.additional_domains.length > 0">
            <text-input
                :disabled="is_disable"
                v-model="form.additional_domains[index].value"
                v-for="(item, index) in form.additional_domains"
                :placeholder="$t('Additional Domain Name')"
                :key="item.id"
                class="mb-2"
                id="another_domain"
                :label="$t('Additional Domain (Optional)')"
            >
                <div class="absolute top-1/2 -translate-y-1/2 right-50px flex"
                     :class="{'hidden': !showRemove }"
                >
                    <tooltip title="Remove">
                        <button
                            title="Remove"
                            @click.prevent="
                                form.additional_domains.splice(index, 1)
                            "
                            class="flex text-danger text-xl"
                        >
                            <i class="xcloud xc-close-sq"></i>
                        </button>
                    </tooltip>
                </div>
                <div class="absolute top-1/2 -translate-y-1/2 right-5 flex">
                    <tooltip title="Redirect this domain to the primary domain">
                        <button
                            @click.prevent="
                                form.additional_domains[index].is_redirect =
                                    !form.additional_domains[index].is_redirect
                            "
                            class="flex text-secondary-light dark:text-white hover:text-dark text-base"
                        >
                            <i class="xcloud xc-maximize"
                                :class="{
                                    'text-success-full':
                                        form.additional_domains[index]
                                            .is_redirect,
                                }"
                            ></i>
                        </button>
                    </tooltip>
                </div>
            </text-input>
        </div>
        <button
            :disabled="is_disable"
            @click.prevent="form.additional_domains.push({ value: '', is_redirect: false })"
            type="button"
            :class="{
                 'cursor-not-allowed opacity-70':is_disable
            }"
            class="inline-flex items-center border-1 border-secondary-light dark:border-dark justify-center
                      min-h-40px p-2 px-20px pl-15px mt-5px rounded-md shadow-none text-base text-center
                      text-dark dark:text-white font-normal bg-white dark:bg-transparent focus:outline-none
                      hover:bg-primary-light dark:hover:bg-mode-focus-light hover:border-primary-light
                      dark:hover:border-mode-focus-light hover:text-white ease-in-out transition duration-200
                      hover:shadow-lg hover:shadow-primary-dark/30 dark:hover:shadow-mode-focus-light/25 group"
        >
            <span class="inline-flex justify-center items-center text-xs mr-2.5 text-secondary-light dark:text-mode-secondary-light group-hover:text-white dark:group-hover:text-white ease-in-out transition duration-200">
                <i class="xcloud xc-add"></i>
            </span>
            <span class="group-hover:drop-shadow-button">{{ $t('Add Domain') }}</span>
        </button>

        <div class="mt-2 text-sm text-red-600"
            v-if="form?.errors?.additional_domains"
        >
            {{ form?.errors?.additional_domains }}
        </div>
    </div>
</template>

<script setup>
import TextInput from "@/Shared/TextInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {computed, onMounted, watch} from "vue";
import OptionInput from "@/Shared/OptionInput.vue";

const props = defineProps({
    form: Object,
    is_disable: {
        type: Boolean,
        default: false,
    }
});

const showRemove = computed(() => {
    return props.form.additional_domains.length > 1
})

onMounted(async () => {
    if(props.form.additional_domains.length === 0){
        props.form.additional_domains.push(
            {
                value: '',
                is_redirect: false
            }
        )
    }
})

watch(
    () => props.form.additional_domains,
    (newVal, oldVal) => {
      for(let i = 0; i < props.form.additional_domains.length; i++){
        if(props.form.additional_domains[i].value !== ''){
          props.form.additional_domains[i].value = props.form.additional_domains[i].value.toLowerCase();
        }
      }
    },
    { deep: true } // Use the deep option to watch for changes in array contents
);

</script>
