<template>
    <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px wide-mobile:gap-25px tablet:gap-25px wide-tablet:gap-30px laptop:gap-30px monitor:gap-40px large-monitor:gap-50px">
        <div class="bg-white dark:bg-mode-light px-30px flex flex-col rounded-[4px] mobile:px-15px wide-mobile:px-20px tablet:px-25px">
            <!-- Header -->
            <div
                class="min-h-[80px] wide-mobile:min-h-60px mobile:min-h-50px flex mobile:flex-col items-center wide-mobile:px-10px gap-20px mobile:gap-10px tablet:gap-15px justify-between py-15px mobile:py-10px">
                <h4
                    class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:text-base mobile:text-sm tablet:text-base laptop:text-lg monitor:text-28px large-monitor:text-3xl">
                    {{ title }}
                </h4>
            </div>

            <!-- Content -->
            <div class="pb-[24px] mobile:pb-15px wide-mobile:pb-20px">
                <skeleton class="mb-0" v-if="isLoading" :columns="1" :rows="3" />
                <div v-else>
                    <!-- Has Issues -->
                    <div
                        v-if="hasIssues"
                        class="px-[16px] mx-auto wide-mobile:px-10px wide-mobile:py-5px bg-[#F7FBFF] dark:bg-mode-base rounded-[4px]"
                        :class="isExpanded ? 'pb-[24px]' : ''">
                        <!-- Header -->
                        <div
                            class="py-[16px] mx-auto wide-mobile:py-5px flex mobile:flex-col items-center gap-20px mobile:gap-10px tablet:gap-15px justify-between">
                            <div class="flex wide-mobile:flex-col gap-15px mobile:gap-10px items-center">
                                <i
                                    :class="{
                                        'text-success-full': isChecksumSuccess,
                                        'text-danger': !isChecksumSuccess,
                                    }"
                                    class="xcloud xc-warning1 text-xl mobile:text-base wide-mobile:text-lg tablet:text-xl monitor:text-28px large-monitor:text-40px  shrink-0"
                                ></i>
                                <h4
                                    class="text-dark dark:text-mode-secondary-dark text-[16px] mobile:text-sm wide-mobile:text-base tablet:text-[16px] monitor:text-28px large-monitor:text-3xl leading-tight">
                                    {{ result?.output }}
                                </h4>
                            </div>
                            <div
                                v-if="issuesCount"
                                class="flex wide-mobile:flex-col items-center gap-3 mobile:gap-2 tablet:gap-4">
                                <p
                                    class="text-sm mobile:text-xxs wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px text-secondary-full dark:text-secondary-light leading-tight">
                                    {{ issuesCount }} {{ $t('Plugins Found') }}
                                </p>
                                <div
                                    @click="toggleExpand"
                                    class="bg-white dark:bg-mode-light rounded-full w-[24px] h-[24px] mobile:w-[20px] mobile:h-[20px] tablet:w-[28px] tablet:h-[28px] monitor:w-[32px] monitor:h-[32px] flex items-center justify-center p-4 cursor-pointer">
                                    <i
                                        class="xcloud text-secondary-full dark:text-white text-[16px] mobile:text-[12px] tablet:text-[18px] monitor:text-[20px]"
                                        :class="isExpanded ? 'xc-angle_up' : 'xc-angle_down'"
                                    ></i>
                                </div>
                            </div>
                        </div>

                        <transition
                            enter-active-class="transition-all duration-200 ease-out"
                            leave-active-class="transition-all duration-200 ease-in"
                            enter-from-class="translate-y-2"
                            leave-to-class="translate-y-2"
                            @before-enter="beforeEnter"
                            @enter="enter"
                            @leave="leave">
                            <div
                                v-if="isExpanded"
                                class="mt-5 rounded-[4px] bg-white dark:bg-mode-base border border-[#E3EDF6] dark:border-[#29304D] overflow-x-auto overflow-y-hidden">
                                <table class="w-full text-sm wide-mobile:text-xs table-auto mobile:min-w-[600px]">
                                    <colgroup>
                                        <col class="mobile:w-[15%] wide-mobile:w-[20%]" />
                                        <col class="mobile:w-[40%] wide-mobile:w-[50%]" />
                                        <col class="mobile:w-[30%] wide-mobile:w-[20%]" />
                                        <col class="mobile:w-[15%] wide-mobile:w-[10%]" />
                                    </colgroup>
                                    <thead
                                        class="dark:bg-mode-light text-dark dark:text-white border-b border-[#E3EDF6] dark:border-[#29304D]">
                                    <tr class="text-[14px] font-[400] mobile:text-xxs wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px">
                                        <th class="text-left py-3 px-5 mobile:px-3">{{ $t('Status') }}</th>
                                        <th class="text-left py-3 px-5 mobile:px-3">{{ $t('Message') }}</th>
                                        <th class="text-left py-3 px-5 mobile:px-3">{{ $t('Plugin') }}</th>
                                        <th class="text-left py-3 pr-5 mobile:px-3">{{ $t('Action') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <!-- Plugin Issues -->
                                    <tr
                                        v-for="(group, groupIndex) in groupedByPlugin"
                                        :key="'group-' + groupIndex">
                                        <td
                                            class="px-5 py-3 mobile:px-3 text-dark dark:text-white text-[14px] wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px">
                                                <span
                                                    v-for="(status, statusIndex) in group.statuses"
                                                    :key="'status-' + statusIndex"
                                                    :class="getStatusBadgeClass(status)"
                                                >
                                                    {{ status }}
                                                </span>
                                        </td>
                                        <td
                                            class="px-5 py-3 mobile:px-3 text-dark dark:text-white text-[14px] wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px truncate">
                                                <span
                                                    v-for="(msg, msgIndex) in group.displayMessages"
                                                    :key="'msg-' + msgIndex">
                                                    {{ msg }}{{msgIndex < group.displayMessages.length - 1 ? "," : "" }}
                                                </span>
                                            <span
                                                v-if="group.totalMessages > 3"
                                                class="ml-2 text-primary-light text-sm mobile:text-xxs tablet:text-sm monitor:text-base">
                                                    ({{ formatMessageCount(group.totalMessages) }})
                                                </span>
                                        </td>
                                        <td class="px-5 py-3 mobile:px-3 flex gap-1 items-center">
                                            <img
                                                :src="`https://ps.w.org/${group.plugin_name}/assets/icon-256x256.png`"
                                                @error="(e) => defaultLogo(e, group.plugin_name)"
                                                class="w-[20px] h-[20px] mobile:w-[16px] mobile:h-[16px] tablet:w-[24px] tablet:h-[24px] monitor:w-[28px] monitor:h-[28px] shrink-0 mr-1"
                                                :alt="group.plugin_name"
                                            />
                                            <span
                                                class="mr-3 text-secondary-full dark:text-mode-secondary-dark text-[14px] wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px">
                                                    {{ group.plugin_display_name || group.plugin_name }}
                                                </span>
                                        </td>
                                        <td
                                            class="ml-2 text-primary-light text-sm mobile:text-xxs tablet:text-sm monitor:text-base cursor-pointer hover:underline"
                                            @click="openModal(group)"
                                            :aria-label="$t('View details for') +' ' +(group.plugin_display_name || group.plugin_name)">
                                            {{ $t('Details') }}
                                        </td>
                                    </tr>

                                    <!-- Warning Messages -->
                                    <tr
                                        v-for="(warning, index) in warningMessages"
                                        :key="'warning-' + index">
                                        <td
                                            class="px-5 py-3 mobile:px-3 text-dark dark:text-white text-[14px] wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px">
                                                <span :class="getStatusBadgeClass('Skip')">
                                                    Skipped
                                                </span>
                                        </td>
                                        <td
                                            class="px-5 py-3 mobile:px-3 text-dark dark:text-white text-[14px] wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px truncate">
                                           <!-- Skipped message -->
                                            This plugin version is not available for checksum verification.
                                        </td>
                                        <td class="px-5 py-3 mobile:px-3 flex gap-1 items-center">
                                            <img
                                                :src="`https://ps.w.org/${warning.plugin_name}/assets/icon-256x256.png`"
                                                @error="(e) => defaultLogo(e, warning.plugin_name)"
                                                class="w-[20px] h-[20px] mobile:w-[16px] mobile:h-[16px] tablet:w-[24px] tablet:h-[24px] monitor:w-[28px] monitor:h-[28px] shrink-0 mr-1"
                                                :alt="warning.plugin_name" />
                                            <span
                                                class="text-secondary-full dark:text-mode-secondary-dark text-[14px] wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px">
                                                    {{ warning.plugin_display_name || warning.plugin_name }}
                                                </span>
                                        </td>
                                        <td
                                            class="ml-2 text-primary-light text-sm mobile:text-xxs tablet:text-sm monitor:text-base cursor-not-allowed"
                                            :aria-label="
                                                    $t('View details for') +
                                                    ' ' +
                                                    (warning.plugin_display_name || warning.plugin_name)">
                                                {{ $t('Details') }}
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </transition>
                    </div>

                    <!-- No Issues -->
                    <div
                        v-else
                        class="p-[16px] wide-mobile:min-h-[48px] mobile:min-h-[56px] wide-mobile:px-10px wide-mobile:py-5px flex mobile:flex-col items-center gap-20px mobile:gap-10px tablet:gap-15px justify-between bg-[#F7FBFF] dark:bg-mode-base rounded-[4px]">
                        <div class="flex wide-mobile:flex-col gap-15px mobile:gap-10px items-center">
                            <i
                                class="xcloud xc-confirm text-xl mobile:text-base wide-mobile:text-lg tablet:text-xl monitor:text-28px large-monitor:text-40px shrink-0 text-success-full"
                            ></i>
                            <h4
                                class="text-dark dark:text-mode-secondary-dark text-[16px] mobile:text-sm wide-mobile:text-base tablet:text-[16px] monitor:text-28px large-monitor:text-3xl leading-tight">
                                {{ result?.output }}
                            </h4>
                        </div>
                        <div class="flex items-center">
                            <div
                                class="transform bg-white dark:bg-mode-light rounded-full w-[24px] h-[24px] mobile:w-[20px] mobile:h-[20px] tablet:w-[28px] tablet:h-[28px] monitor:w-[32px] monitor:h-[32px] flex items-center justify-center p-4 cursor-pointer">
                                <i
                                    class="xcloud xc-angle_down text-secondary-light dark:text-mode-secondary-dark text-[16px] mobile:text-[12px] tablet:text-[18px] monitor:text-[20px]"
                                ></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal for viewing all issues -->
        <Modal
            :show="modalVisible"
            @close="closeModal"
            :title="modalTitle"
            :widthClass="`max-w-7xl mobile:max-w-[90%] wide-mobile:max-w-666px tablet:max-w-850px wide-tablet:max-w-950px laptop:max-w-1050px monitor:max-w-1210px large-monitor:max-w-1350px`">
            <div class="overflow-auto max-h-[70vh] mobile:max-h-[60vh]">
                <table class="w-full text-sm table-auto mobile:min-w-[500px]">
                    <colgroup>
                        <col class="mobile:w-[20%] wide-mobile:w-[20%]" />
                        <col class="mobile:w-[40%] wide-mobile:w-[40%]" />
                        <col class="mobile:w-[40%] wide-mobile:w-[40%]" />
                    </colgroup>
                    <thead
                        class="border-b border-mode-secondary-dark text-dark dark:text-white">
                    <tr class="text-[14px] font-[400] mobile:text-xxs wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px">
                        <th class="text-left py-3 px-5 mobile:px-3">
                            {{ $t('Status') }}
                        </th>
                        <th class="text-left py-3 px-5 mobile:px-3">
                            {{ $t('Message') }}
                        </th>
                        <th class="text-left py-3 px-5 mobile:px-3">{{ $t('File') }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr
                        v-for="(item, index) in modalItems"
                        :key="'modal-item-' + index">
                        <td class="px-5 py-3 mobile:px-3 text-dark dark:text-white text-[14px] wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px">
                                <span :class="getStatusBadgeClass(item.status)">
                                    {{ item.status }}
                                </span>
                        </td>
                        <td class="px-5 py-3 mobile:px-3 text-dark dark:text-white text-[14px] wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px">
                            {{ item.message }}
                        </td>
                        <td class="px-5 py-3 mobile:px-3 text-dark dark:text-white text-[14px] wide-mobile:text-xs tablet:text-sm monitor:text-base large-monitor:text-28px">
                            {{ item.file || 'N/A' }}
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </Modal>
    </div>
</template>
<script setup>
import { computed, ref } from 'vue';
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Modal from "@/Shared/Modal.vue";
import Button from "@/Jetstream/Button.vue";

const props = defineProps({
    title: {
        type: String,
        required: true,
    },
    version: {
        type: String,
        default: '',
    },
    isLoading: {
        type: Boolean,
        default: false,
    },
    result: {
        type: Object,
        default: () => ({
            output: '',
            plugins: {},
        }),
    },
});

const isExpanded = ref(false);
const toggleExpand = () => {
    isExpanded.value = !isExpanded.value;
};

// Modal state
const modalVisible = ref(false);
const modalTitle = ref('');
const modalItems = ref([]);

// Plugins Data
const pluginsData = ref(props?.result?.plugins);

// Plugin name mapping for display purposes
const pluginDisplayNames = {
    'contact-form-7': 'Contact Form 7',
    'woocommerce': 'WooCommerce',
    'betterdocs-ai-chatbot': 'Betterdocs AI Chatbot',
    'essential-addons-elementor': 'Essential Addons Elementor',
    'object-cache-pro': 'Object Cache Pro',
};


const issuesCount = computed(() => {
    const pluginIssues = new Set(processedIssues.value.map(issue => issue.plugin_name));
    const warningPlugins = new Set(warningMessages.value.map(warning => warning.plugin_name));
    const uniquePlugins = new Set([...pluginIssues, ...warningPlugins]);
    const total = uniquePlugins.size;
    return total > 50 ? '50+' : total;
});

const hasIssues = computed(() => {
    const plugins = Object.keys(props.result?.plugins || {}).length
        ? props.result.plugins
        : pluginsData.value;
    return (
        (plugins && Object.keys(plugins).length > 0) ||
        (props.result && props.result.output && props.result.output.length > 0)
    );
});

const isChecksumSuccess = computed(() =>
    props.result?.output?.toLowerCase().startsWith("success:")
);

const processedIssues = computed(() => {
    const plugins = Object.keys(props.result?.plugins || {}).length
        ? props.result.plugins
        : pluginsData.value;
    if (plugins){
    return Object.entries(plugins)
        .filter(([key, value]) => key !== 'misc' && Array.isArray(value))
        .flatMap(([pluginName, issues]) =>
            issues
                .filter(issue => issue.file && issue.message && issue.plugin_name)
                .map(issue => ({
                    plugin_name: issue.plugin_name,
                    plugin_display_name: pluginDisplayNames[issue.plugin_name] || formatPluginName(issue.plugin_name),
                    message: issue.message,
                    status: getStatusFromMessage(issue.message),
                    file: issue.file,
                }))
        );
    }
    return  [];
});


const groupedByPlugin = computed(() => {
    const groups = {};

    // Group issues by plugin_name
    processedIssues.value.forEach(issue => {
        const pluginName = issue.plugin_name;
        if (!groups[pluginName]) {
            groups[pluginName] = {
                plugin_name: pluginName,
                plugin_display_name: pluginDisplayNames[pluginName] || formatPluginName(pluginName),
                items: [],
                statuses: new Set(),
                messages: new Set(),
                totalMessages: 0,
            };
        }
        groups[pluginName].items.push(issue);
        groups[pluginName].statuses.add(issue.status);
        groups[pluginName].messages.add(issue.message);
        groups[pluginName].totalMessages = groups[pluginName].items.length;
    });

    // Convert to array and prepare display messages
    return Object.values(groups).map(group => {
        const uniqueMessages = Array.from(group.messages);
        const displayMessages = uniqueMessages.slice(0, 3); // Show at most 3 messages
        return {
            ...group,
            statuses: Array.from(group.statuses),
            displayMessages: displayMessages,
            totalMessages: group.totalMessages,
        };
    });
});

const warningMessages = computed(() => {
    const warnings = [];
    const warningsByPlugin = {};
    const plugins = Object.keys(props.result?.plugins || {}).length ? props.result.plugins : pluginsData.value;

    // Process misc warnings
    const misc = plugins?.misc || [];
    misc.forEach(item => {
        if (typeof item === 'string' && item.startsWith('Warning:')) {
            const versionMatch = item.match(/version (\d+\.\d+\.\d+) of plugin ([\w-]+)/);
            if (versionMatch) {
                const version = versionMatch[1];
                const pluginName = versionMatch[2];

                if (!warningsByPlugin[pluginName]) {
                    warningsByPlugin[pluginName] = {
                        plugin_name: pluginName,
                        plugin_display_name: pluginDisplayNames[pluginName] || formatPluginName(pluginName),
                        version: version,
                        message: `Could not retrieve the checksums for version ${version} of plugin ${pluginName}, skipping.`,
                        status: 'Warning',
                    };
                }
            }
        }
    });

    for (const key in warningsByPlugin) {
        warnings.push(warningsByPlugin[key]);
    }

    return warnings;
});

const openModal = (group) => {
    modalTitle.value = `${group.plugin_display_name || group.plugin_name} Total Issues Found - ${group.items.length}`;
    modalItems.value = group.items;
    modalVisible.value = true;
};

const openWarningModal = (warning) => {
    modalTitle.value = `Checksum result for ${warning.plugin_display_name || warning.plugin_name}`;
    modalItems.value = [warning]; // Show only the selected warning in the modal
    modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

// Track failed logo loads to prevent infinite loops
const defaultLogo = (e, plugin_name) => {
    e.target.src = `https://ui-avatars.com/api/?name=${plugin_name}&color=7F9CF5&background=EBF4FF`;
};

const getStatusBadgeClass = (status) => {
    return {
        'bg-[#FF4F4F33] px-2 py-1 rounded-[4px]': status === 'Modified',
        'bg-warning px-2 py-1 rounded-[4px]': status === 'Extra',
        'bg-secondary-light px-2 py-1 rounded-[4px] text-black': status === 'Missing',
        'bg-danger px-2 py-1 rounded-[4px] text-white': status === 'Warning',
        'bg-[#E3EDF6] text-[#29304D] px-2 py-1 rounded-[4px]': status === 'Skip',
    };
};

const getStatusFromMessage = (message) => {
    if (message?.toLowerCase() === 'checksum does not match') {
        return 'Modified';
    }
    if (message?.toLowerCase() === 'file is missing') {
        return 'Missing';
    }
    if (message?.toLowerCase() === 'file was added') {
        return 'Extra';
    }
    return 'Unknown';
};

const formatPluginName = (name) => {
    return name
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
};

const formatMessageCount = (count) => {
    if(count > 500) return '500+';
    if(count > 150) return '150+';
    if (count > 100) return '100+';
    if (count > 50) return '50+';
    return count;
};

const beforeEnter = (el) => {
    el.style.height = '0px';
    el.style.opacity = '0';
};

const enter = (el) => {
    el.style.height = el.scrollHeight + 'px';
    el.style.opacity = '1';
};

const leave = (el) => {
    el.style.height = '0px';
    el.style.opacity = '0';
};
</script>

<style scoped>
/* Ensure statuses are spaced nicely */
td span + span {
    margin-left: 0.5rem;
}

/* Truncate long messages */
td.truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
