<template>
    <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-15px mobile:pl-15px mobile:gap-15px wide-mobile:pt-20px wide-mobile:pl-20px wide-mobile:gap-20px tablet:gap-25px wide-tablet:gap-25px laptop:gap-30px">
        <div class="bg-white dark:bg-mode-light px-30px flex flex-col rounded-[4px] mobile:px-15px wide-mobile:px-20px">
            <!-- Header -->
            <div class="min-h-[80px] wide-mobile:min-h-60px mobile:min-h-50px flex mobile:flex-col items-center wide-mobile:px-10px gap-20px mobile:gap-10px justify-between">
                <h4 class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:text-base mobile:text-sm">
                    {{ title }}
                </h4>
            </div>

            <!-- Content -->
            <div class="pb-[24px] mobile:pb-[16px] wide-mobile:pb-[20px]">
                <skeleton class="mb-0" v-if="isLoading" :columns="1" :rows="3" />
                <div v-else>
                    <!-- Has Issues -->
                    <div v-if="hasIssues"
                         class="px-[16px] mx-auto wide-mobile:px-10px wide-mobile:py-5px bg-[#F7FBFF] dark:bg-mode-base rounded-[4px]"
                         :class="isExpanded ? 'pb-[24px] mobile:pb-[16px] wide-mobile:pb-[20px]' : ''">
                        <!-- Header -->
                        <div class="py-[16px] mx-auto wide-mobile:py-10px mobile:py-8px flex mobile:flex-col items-center gap-20px mobile:gap-8px justify-between"
                             :class="isExpanded ? 'border-b border-[#E3EDF6] dark:border-[#29304D]' : ''">
                            <div class="flex wide-mobile:flex-col gap-15px mobile:gap-10px items-center">
                                <i class="xcloud xc-warning1 text-xl mobile:text-base text-danger shrink-0"></i>
                                <h4 class="text-dark dark:text-mode-secondary-dark text-[16px] mobile:text-sm leading-tight">
                                    {{ message }}
                                </h4>
                            </div>
                            <div class="flex wide-mobile:flex-col items-center gap-3 mobile:gap-2">
                                <p class="text-sm mobile:text-xs text-secondary-full dark:text-secondary-light leading-tight">
                                    {{ issuesCount }} {{ $t('Items found') }}
                                </p>
                                <div @click="toggleExpand"
                                     class="bg-white dark:bg-mode-light rounded-full w-[24px] h-[24px] flex items-center justify-center p-4 cursor-pointer"
                                     :aria-expanded="isExpanded"
                                     :aria-label="isExpanded ? 'Collapse issues' : 'Expand issues'">
                                    <i class="xcloud text-secondary-full dark:text-white text-[16px]"
                                       :class="isExpanded ? 'xc-angle_up' : 'xc-angle_down'"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Issues Table -->
                        <transition
                            enter-active-class="transition-all duration-200 ease-out"
                            leave-active-class="transition-all duration-200 ease-in"
                            enter-from-class="translate-y-2"
                            leave-to-class="translate-y-2"
                            @before-enter="beforeEnter"
                            @enter="enter"
                            @leave="leave"
                        >
                            <div v-if="isExpanded" class="mt-5 rounded-[4px] border border-[#E3EDF6] dark:border-[#29304D] bg-white dark:bg-mode-base overflow-x-auto overflow-y-hidden">
                                <table class="w-full text-sm wide-mobile:text-xs mobile:table-auto table-fixed min-w-[300px]">
                                    <colgroup>
                                        <col class="w-[20%]">
                                        <col class="w-[40%]">
                                        <col class="w-[40%] laptop:w-[30%]">
                                    </colgroup>
                                    <thead class="dark:bg-mode-light text-dark dark:text-white border-b border-[#E3EDF6] dark:border-[#29304D] sticky top-0">
                                    <tr class="text-[14px] font-[400] mobile:text-xs">
                                        <th scope="col" class="text-left py-3 px-5 mobile:px-3 mobile:py-2 wide-mobile:px-4 wide-mobile:py-2">
                                            {{ $t('Status') }}
                                        </th>
                                        <th scope="col" class="text-left py-3 px-5 mobile:px-3 mobile:py-2 wide-mobile:px-4 wide-mobile:py-2">
                                            {{ $t('Message') }}
                                        </th>
                                        <th scope="col" class="text-left py-3 px-5 mobile:px-3 mobile:py-2 wide-mobile:px-4 wide-mobile:py-2">
                                            {{ $t('File') }}
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="(item, index) in coreChecksums" :key="index">
                                        <td class="px-5 py-3 mobile:px-3 mobile:py-2 wide-mobile:px-4 wide-mobile:py-2 text-dark dark:text-white text-[14px] wide-mobile:text-xs mobile:text-[10px]">
                                                <span :class="getStatusClass(item[0].trim())">
                                                    {{ item[0]?.trim() }}
                                                </span>
                                        </td>
                                        <td class="px-5 py-3 mobile:px-3 mobile:py-2 wide-mobile:px-4 wide-mobile:py-2 text-dark dark:text-white text-[14px] wide-mobile:text-xs mobile:text-[10px]">
                                            {{ item[1]?.trim() }}
                                        </td>
                                        <td class="px-5 py-3 mobile:px-3 mobile:py-2 wide-mobile:px-4 wide-mobile:py-2 text-dark dark:text-white text-[14px] wide-mobile:text-xs mobile:text-[10px] truncate" :title="item[2]?.trim()">
                                            {{ item[2]?.trim() }}
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </transition>
                    </div>

                    <!-- No Issues -->
                    <div v-else class="p-[16px] mx-auto wide-mobile:min-h-[48px] mobile:min-h-[56px] wide-mobile:px-10px wide-mobile:py-5px mobile:p-10px flex mobile:flex-col items-center gap-20px mobile:gap-10px justify-between bg-[#F7FBFF] dark:bg-mode-base rounded-[4px]">
                        <div class="flex wide-mobile:flex-col gap-15px mobile:gap-10px items-center">
                            <i class="xcloud xc-confirm text-xl mobile:text-base shrink-0 text-success-full"></i>
                            <h4 class="text-dark dark:text-mode-secondary-dark text-[16px] mobile:text-sm leading-tight">
                                {{ message }}
                            </h4>
                        </div>
                        <div class="flex wide-mobile:flex-col items-center gap-3 mobile:gap-2">
                            <div class="transform bg-white dark:bg-mode-light rounded-full w-[24px] h-[24px] flex items-center justify-center p-4 cursor-pointer">
                                <i class="xcloud xc-angle_down text-secondary-light dark:text-mode-secondary-dark text-[16px]"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {computed, ref} from 'vue';
import Skeleton from "@/Shared/Table/Skeleton.vue";

const props = defineProps({
    title: {
        type: String,
        required: true
    },
    lastScan: {
        type: String,
        default: ''
    },
    version: {
        type: String,
        default: ''
    },
    isLoading: {
        type: Boolean,
        default: false
    },
    hasIssues: {
        type: Boolean,
        default: false
    },
    issues: {
        type: Array,
        default: () => []
    }
});

const isExpanded = ref(false);

const toggleExpand = () => {
    isExpanded.value = !isExpanded.value;
}

// Filter issues that are not errors
const otherIssues = computed(() => {
    return props.issues ? props.issues.filter(issue => !issue?.toLowerCase().startsWith("error:")): [];
});

// The first matching message
const message = computed(() => {
    return  props.issues ?  props.issues.find(issue => issue.toLowerCase().startsWith("success:") || issue.toLowerCase().startsWith("error:")) : '';
});

// The count of non-error issues, with a limit of '50+'
const issuesCount = computed(() => {
    const count = otherIssues.value.length;
    return count > 50 ? '50+' : count;
});

// Processed core checksums (up to 50 items)
const coreChecksums = computed(() => {
    return otherIssues.value.slice(0, 50).map(item => item.split(':'));
});

const getStatusClass = (status) => {
    return {
        'bg-[#FF4F4F33] px-2 py-1 rounded-[4px]': status === 'Modified',
        'bg-warning px-2 py-1 rounded-[4px]': status === 'Extra',
        'bg-secondary-light px-2 py-1 rounded-[4px] text-black': status === 'Missing',
        'bg-danger px-2 py-1 rounded-[4px] text-white': status === 'Warning',
    };
};

const beforeEnter = (el) => {
    el.style.height = '0px';
    el.style.opacity = '0';
};

const enter = (el) => {
    el.style.height = el.scrollHeight + 'px';
    el.style.opacity = '1';
};

const leave = (el) => {
    el.style.height = '0px';
    el.style.opacity = '0';
};
</script>
