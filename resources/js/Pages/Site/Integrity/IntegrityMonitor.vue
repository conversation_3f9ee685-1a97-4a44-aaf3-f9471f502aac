<template>
    <single-site :server="server" :site="site" active="Integrity Monitor">
      <!-- Header Section -->
      <div class="gap-30px ml-30px mt-30px dark:bg-mode-light mobile:mt-20px mobile:ml-20px mobile:gap-20px bg-white rounded-10px px-20px py-15px flex justify-between items-center wide-mobile:flex-col wide-mobile:gap-15px">
        <div class="flex wide-mobile:flex-col gap-2 items-center">
          <button
            @click.prevent="reFetch()"
            type="button"
            :disabled="site.is_disabled || refreshing"
            :class="{'cursor-not-allowed opacity-70': site.is_disabled || refreshing}"
            class="inline-flex items-center rounded-3xl shadow-none px-10px py-5px bg-success-full/10 text-sm text-success-full focus:outline-0 gap-1"
          >
            <span class="rounded-full bg-success-full h-25px w-25px shrink-0 flex justify-center items-center">
              <i :class="{'animate-spin': refreshing}" class="xcloud xc-verify_dns text-white pt-[0.02rem]"></i>
            </span>
            <span>{{ $t('Scan Now') }}</span>
          </button>
          <span class="text-sm text-secondary-full dark:text-mode-secondary-dark leading-tight">
            {{ $t('Last scan') }} : {{ lastScanDate }}
          </span>
        </div>
      </div>

        <CoreIntegrityCheck
            :hasIssues="!isCoreChecksumSuccess"
            :title="$t('WordPress Core')"
            :is-loading="refreshing"
            :issues="coreChecksums"
        />

      <!-- Integrity Check Sections -->
      <PluginIntegrityCheck
        :title="$t('Plugins')"
        :is-loading="refreshing"
        :result="pluginChecksums"
      />
    </single-site>
  </template>

  <script setup>
  import { useFlash } from "@/Composables/useFlash";
  import SingleSite from "@/Pages/Site/SingleSite.vue";
  import { onMounted, onUnmounted, ref, computed } from "vue";
  import CoreIntegrityCheck from "@/Pages/Site/Integrity/CoreIntegrityCheck.vue";
  import PluginIntegrityCheck from "@/Pages/Site/Integrity/PluginIntegrityCheck.vue";

  const props = defineProps({
    site: Object,
    server: Object,
    integrityChecksum: Object,
    lastChecked:String
  });

  // Reactive states
  const integrity_checksums = ref(props.integrityChecksum || null);
  const refreshing = ref(false);
  const lastScanDate = ref(props.lastChecked ?? 'Never');


  // Computed properties
  const coreChecksums = computed(() => integrity_checksums.value?.core_checksums || null);
  const pluginChecksums = computed(() => integrity_checksums.value?.plugin_checksums || null);
  const isCoreChecksumSuccess = computed(() =>
      Array.isArray(coreChecksums.value) && coreChecksums.value.some(item => item.toLowerCase().startsWith("success:"))
  );

  // Methods
  const reFetch = () => {
    refreshing.value = true;
    axios.post(`/api/server/${props.server.id}/site/${props.site.id}/scan-wp-checksum`)
      .then(({data}) => {
        useFlash().success(data?.message || "WordPress checksum scan initiated successfully");
      })
      .catch(() => {
        useFlash().error("An error occurred while fetching the integrity checksums.");
      });
  };

  const getChecksumResult = async () => {
      const { server, site } = props;
      const flash = useFlash();
      refreshing.value = true;

      try {
          const { data } = await axios.post(`/api/server/${server.id}/site/${site.id}/wp-checksum-result`);
          const { last_scan = null, message = '', integrityChecksum = null } = data;

          if (integrityChecksum) {
              integrity_checksums.value = integrityChecksum;
          }

          if (last_scan) {
              lastScanDate.value = last_scan;
          }

          if (message) {
              flash.success(message);
          }
      } catch (error) {
          flash.error("An error occurred while fetching the integrity checksums.");
      } finally {
          refreshing.value = false;
      }
  };


  // Lifecycle hooks
  onMounted(() => {
      if (!props?.integrityChecksum){
          reFetch();
      }
      if (window.Echo) {
          window.Echo.private("site." + props.site.id).listen("SiteChecksumUpdated",
              (e) => {
                  getChecksumResult();
              }
          );
      }
  });
  onUnmounted(() => {
      if (window.Echo) {
          window.Echo.private("site." + props.site.id).stopListening("SiteMonitoringUpdated");
      }
  });

</script>


<style>
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
    opacity: 0;
}

.slide-fade-enter-active, .slide-fade-leave-active {
    transition: all 0.3s ease;
}

.slide-fade-enter-from, .slide-fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
}

.slide-fade-enter-to, .slide-fade-leave-from {
    opacity: 1;
    transform: translateY(0);
}

.swal2-checkbox {
    @apply dark:bg-dark;
}
</style>
