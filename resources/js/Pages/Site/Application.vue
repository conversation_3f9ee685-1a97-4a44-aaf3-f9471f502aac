<template>
    <single-site :server="server" :site="site" active="Application">
        <div class="grid grid-cols-2 small-laptop:grid-cols-1">
            <div v-if="site?.permissions?.includes('site:settings')" class="xl:flex">
                <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px flex-1">
                    <!-- Laravel Application Settings (Combined) -->
                    <Card
                        :loading="laravelAppForm.processing"
                        :title="$t('Laravel Application')"
                        buttonStyle="success"
                        :button-title="$t('Save Settings')"
                        @onSubmit="updateLaravelAppSettings"
                        :disable-use-button="laravelAppForm.processing || !server.is_connected || site?.is_disabled">
                        <skeleton v-if="loading" :has-header="false" :rows="1" :columns="1" row-div-classes="h-5" rowClasses="h-20"/>
                        <template v-else>
                            <div class="flex flex-col gap-6">
                                <!-- Debug Mode Toggle -->
                                <div class="flex flex-col gap-4">
                                    <Switch :checked="laravelAppForm.debug_mode" @click.prevent="laravelAppForm.debug_mode = !laravelAppForm.debug_mode">
                                        <span class="mr-2 mobile:text-sm">{{ $t('Enable Debug Mode') }}</span>
                                        <Tooltip align="right" title="When debug mode is enabled, detailed error messages will be displayed. This should be disabled in production.">
                                            <i class="xcloud xc-info"></i>
                                        </Tooltip>
                                    </Switch>
                                </div>

                                <!-- Maintenance Mode Toggle -->
                                <div class="flex flex-col gap-4 mb-2">
                                    <Switch :checked="laravelAppForm.maintenance_mode" @click.prevent="laravelAppForm.maintenance_mode = !laravelAppForm.maintenance_mode">
                                        <span class="mr-2 mobile:text-sm">{{ $t('Enable Maintenance Mode') }}</span>
                                        <Tooltip align="right" title="When maintenance mode is enabled, a maintenance screen will be displayed for all requests to your application.">
                                            <i class="xcloud xc-info"></i>
                                        </Tooltip>
                                    </Switch>
                                </div>

                                <!-- Application Environment -->
                                <div class="flex flex-col">
                                    <select-input v-model="laravelAppForm.app_env" :error="laravelAppForm.errors.app_env" id="app_env" :label="$t('Application Environment')">
                                        <option v-for="(label, value) in appEnvironments" :key="value" :value="value">{{ label }}</option>
                                    </select-input>
                                    <div class="flex items-center mt-2">
                                        <span class="text-xs text-secondary-full dark:text-mode-secondary-light">{{ $t('The application environment affects various Laravel behaviors. This setting updates APP_ENV in your .env file') }}</span>
                                    </div>
                                </div>

                                <!-- Clear Application Cache -->
                                <div class="p-4 border-1 flex items-center wide-mobile:flex-col wide-mobile:items-start border-solid border-secondary-light dark:border-dark rounded-md gap-2">
                                    <div class="flex flex-col gap-1.5">
                                        <h5 class="text-base font-medium leading-tight tracking-tight text-dark dark:text-white">
                                            {{ $t('Clear Application Cache') }}
                                        </h5>
                                        <p class="text-secondary-full dark:text-secondary-light text-xs">
                                            {{ $t('Clears all Laravel caches by running the optimize:clear command.') }}
                                        </p>
                                    </div>
                                    <button class="ml-auto px-4 py-3.5 rounded-10px bg-primary-light/10 inline-flex items-center justify-center text-primary-light shrink-0"
                                            @click.prevent="clearApplicationCache"
                                            :disabled="clearCacheForm.processing"
                                            :class="{'cursor-not-allowed opacity-50' : clearCacheForm.processing}">
                                        <span class="inline-flex items-center justify-center ml-1 mr-2.5 text-base">
                                            <i class="xcloud xc-data-cleaning"></i>
                                        </span>
                                        <span>{{ clearCacheForm.processing ? $t('Clearing...') : $t('Clear Cache') }}</span>
                                    </button>
                                </div>
                            </div>
                        </template>
                    </Card>

                    <!-- Laravel Horizon -->
                    <Card
                        :loading="horizonForm.processing"
                        :title="$t('Laravel Horizon')"
                        buttonStyle="success"
                        :button-title="horizonStatus ? $t('Update Process') : $t('Start Horizon')"
                        @onSubmit="startHorizon"
                        :disable-use-button="!horizonIsInstalled || horizonForm.processing || !server.is_connected || site?.is_disabled">
                        <skeleton v-if="loading" :has-header="false" :rows="1" :columns="1" row-div-classes="h-5" rowClasses="h-20"/>
                        <template v-else>
                            <div class="flex flex-col gap-4">
                                <div v-if="!horizonIsInstalled" class="flex flex-col gap-2">
                                    <div class="flex items-center">
                                        <span class="text-dark dark:text-white font-normal leading-tight">
                                            {{ $t('Laravel Horizon is not installed') }}
                                        </span>
                                    </div>
                                    <p class="text-sm text-secondary-full dark:text-mode-secondary-light">
                                        {{ $t('Horizon is not detected in your composer.json. To use Horizon, you need to install it first:') }}
                                    </p>
                                    <div class="bg-gray-100 dark:bg-mode-focus-light p-4 pt-3 rounded-md mt-2 mb-2 dark:text-white">
                                        <code class="text-xs">composer require laravel/horizon</code>
                                    </div>
                                </div>
                                <div v-else class="flex items-center">
                                    <div :class="[
                                                'flex items-center px-3 py-1.5 rounded-full text-xs font-medium mr-3 capitalize',
                                                horizonStatus ?
                                                    'bg-success-bg text-success-dark dark:bg-opacity-20 dark:text-success-light' :
                                                    'bg-gray-100 text-secondary-full dark:bg-mode-focus-light dark:text-mode-secondary-light'
                                            ]">
                                        <svg v-if="horizonStatus" class="w-3.5 h-3.5 mr-1.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                                        </svg>
                                        <svg v-else class="w-3.5 h-3.5 mr-1.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5.5a.75.75 0 001.5 0V5zm0 9.75a.75.75 0 10-1.5 0 .75.75 0 001.5 0z" clip-rule="evenodd" />
                                        </svg>
                                        {{ horizonStatus ? (horizonProcessStatus ? horizonProcessStatus : $t('Active')): $t('Not Configured') }}
                                    </div>
                                    <span class="text-sm text-dark dark:text-white">
                                         {{ horizonStatus ? $t('Horizon is running') : $t('Horizon is not running') }}
                                    </span>

                                    <button
                                        v-if="horizonStatus"
                                        @click="restartHorizon" class="ml-3 px-3 py-1 text-sm text-white bg-info rounded-md disabled:bg-gray-400 disabled:cursor-not-allowed"
                                        :disabled="horizonForm.processing">
                                        {{ $t('Restart') }}
                                    </button>

                                    <button
                                        v-if="horizonStatus"
                                        @click="stopHorizon" class="ml-3 px-3 py-1 text-sm text-white bg-danger rounded-md disabled:bg-gray-400 disabled:cursor-not-allowed"
                                        :disabled="horizonForm.processing">
                                        {{ $t('Stop') }}
                                    </button>
                                </div>
                                <suggestion type="slot" :light-mode="true">
                                    {{ $t('Horizon provides a beautiful dashboard and code-driven configuration for your Laravel powered Redis queues.') }}
                                </suggestion>
                            </div>
                        </template>
                    </Card>


                </div>
            </div>

            <div class="flex flex-col">
                <div v-if="site?.permissions?.includes('site:settings')">
                    <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px flex-1">

                        <!-- Laravel Scheduler -->
                        <Card
                            :loading="schedulerForm.processing"
                            :title="$t('Laravel Scheduler')"
                            buttonStyle="success"
                            :button-title="schedulerActive ? $t('Update Schedule') : $t('Start Scheduler')"
                            @onSubmit="setupScheduler"
                            :disable-use-button="schedulerForm.processing || !server.is_connected || site?.is_disabled">
                            <skeleton v-if="loading" :has-header="false" :rows="1" :columns="1" row-div-classes="h-5" rowClasses="h-20"/>
                            <template v-else>
                                <div class="flex flex-col gap-4">
                                    <div class="flex items-center">
                                        <div class="flex items-center">
                                            <div :class="[
                                                'flex items-center px-3 py-1.5 rounded-full text-xs font-medium mr-3',
                                                schedulerActive ?
                                                    'bg-success-bg text-success-dark dark:bg-opacity-20 dark:text-success-light' :
                                                    'bg-gray-100 text-secondary-full dark:bg-mode-focus-light dark:text-mode-secondary-light'
                                            ]">
                                                <svg v-if="schedulerActive" class="w-3.5 h-3.5 mr-1.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd" />
                                                </svg>
                                                <svg v-else class="w-3.5 h-3.5 mr-1.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5.5a.75.75 0 001.5 0V5zm0 9.75a.75.75 0 10-1.5 0 .75.75 0 001.5 0z" clip-rule="evenodd" />
                                                </svg>
                                                {{ schedulerActive ? $t('Active') : $t('Not Configured') }}
                                            </div>
                                            <span class="text-sm text-dark dark:text-white">
                                                {{ schedulerActive ? $t('Your scheduler is running properly') : $t('Scheduler needs to be configured') }}
                                            </span>
                                            <button v-if="schedulerActive" @click="stopScheduler" class="ml-3 px-3 py-1 text-sm text-white bg-danger rounded-md" :disabled="schedulerForm.processing">
                                                {{ $t('Stop') }}
                                            </button>
                                        </div>
                                    </div>
                                    <select-input v-model="schedulerForm.frequency" :error="schedulerForm.errors.frequency" id="frequency" :label="$t('Scheduler Frequency')">
                                        <option v-for="(label, value) in cronFrequencies" :key="value" :value="value">{{ label }}</option>
                                    </select-input>
                                    <suggestion type="slot" :light-mode="true">
                                        {{ $t('The Laravel scheduler allows you to fluently and expressively define your command schedule within Laravel itself.') }}
                                    </suggestion>
                                </div>
                            </template>
                        </Card>

                        <!-- Laravel Queue -->
                        <laravel-queue-card
                            :site="site"
                            :server="server"
                            :queue-workers="queueWorkers"
                            :loading="loading"
                            @queue-worker-added="onQueueWorkerAdded"
                            @queue-worker-updated="onQueueWorkerUpdated"
                            @queue-worker-removed="onQueueWorkerRemoved"
                        />

                    </div>
                </div>
            </div>
        </div>
    </single-site>
</template>

<script setup>
import SingleSite from "@/Pages/Site/SingleSite.vue";
import {onMounted, ref} from "vue";
import Card from "@/Shared/Card.vue";
import {useForm} from "@inertiajs/inertia-vue3";
import SelectInput from "@/Shared/SelectInput.vue";
import Switch from "@/Shared/Switch.vue";
import {useFlash} from "@/Composables/useFlash";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import LaravelQueueCard from "@/Pages/Site/Components/LaravelQueueCard.vue";
import {useI18n} from "vue-i18n";
import {Inertia} from "@inertiajs/inertia";
import Tooltip from "@/Shared/Tooltip.vue";

const {t} = useI18n();

const props = defineProps({
    site: Object,
    server: Object,
    horizonProcessStatus: String
});

const loading = ref(false);
const horizonIsInstalled = ref(false);
const horizonStatus = ref(false);
const schedulerActive = ref(false);
const queueWorkers = ref([]);

const laravelAppForm = useForm({
    maintenance_mode: false,
    debug_mode: false,
    app_env: 'production',
    processing: false
});

const clearCacheForm = useForm({
    processing: false
});

const appEnvironments = {
    'local': 'Local',
    'staging': 'Staging',
    'production': 'Production',
    'testing': 'Testing'
};

const horizonForm = useForm({
    processing: false
});

const schedulerForm = useForm({
    frequency: 'every_minute',
    processing: false
});

const cronFrequencies = {
    'every_minute': 'Every Minute',
    'every_five_minutes': 'Every Five Minutes',
    'every_ten_minutes': 'Every Ten Minutes',
    'every_fifteen_minutes': 'Every Fifteen Minutes',
    'every_thirty_minutes': 'Every Thirty Minutes',
    'hourly': 'Hourly'
};

onMounted(() => {
    if (props.server.is_connected) {
        loadLaravelApplicationStatus();
    } else {
        useFlash().error("Server is not connected");
    }
});

const loadLaravelApplicationStatus = () => {
    loading.value = true;
    axios.get(route('api.site.laravel-application', {server: props.server.id, site: props.site.id}))
        .then(({data}) => {

            // Set Laravel application settings
            laravelAppForm.maintenance_mode = data.maintenance_mode;
            laravelAppForm.debug_mode = data.debug_mode;
            laravelAppForm.app_env = data.app_env || 'production';

            // Set horizon status
            horizonIsInstalled.value = data.horizon.installed;
            horizonStatus.value = data.horizon.running;

            // Set scheduler status
            schedulerActive.value = data.scheduler.active;
            if (data.scheduler.frequency) {
                schedulerForm.frequency = data.scheduler.frequency;
            }

            // Set queue workers
            queueWorkers.value = data.queue_workers || [];
        })
        .catch(({response}) => {
            if (response?.status === 422) {
                useFlash().error(response?.data?.message ?? 'Failed to load Laravel application status');
            } else {
                useFlash().error("Failed to load Laravel application status");
            }
        })
        .finally(() => {
            loading.value = false;
        });
};

const updateLaravelAppSettings = () => {
    laravelAppForm.processing = true;
    axios.post(route('api.site.laravel-app.update', {server: props.server.id, site: props.site.id}), {
        maintenance_mode: laravelAppForm.maintenance_mode,
        debug_mode: laravelAppForm.debug_mode,
        app_env: laravelAppForm.app_env
    })
        .then(({data}) => {
            useFlash().success(data.message || 'Laravel application settings updated successfully');
        })
        .catch(({response}) => {
            if (response?.status === 422) {
                useFlash().error(response?.data?.message ?? 'Failed to update Laravel application settings');
            } else {
                useFlash().error("Failed to update Laravel application settings");
            }
        })
        .finally(() => {
            laravelAppForm.processing = false;
        });
};

const clearApplicationCache = () => {
    clearCacheForm.processing = true;
    axios.post(route('api.site.laravel-app.clear-cache', {server: props.server.id, site: props.site.id}))
        .then(({data}) => {
            useFlash().success(data.message || 'Application cache cleared successfully');
        })
        .catch(({response}) => {
            if (response?.status === 422) {
                useFlash().error(response?.data?.message ?? 'Failed to clear application cache');
            } else {
                useFlash().error("Failed to clear application cache");
            }
        })
        .finally(() => {
            clearCacheForm.processing = false;
        });
};

const startHorizon = () => {
    horizonForm.processing = true;
    axios.post(route('api.site.horizon.start', {server: props.server.id, site: props.site.id}))
        .then(({data}) => {
            useFlash().success(data.message || 'Horizon started successfully');
            horizonStatus.value = true;
        })
        .catch(({response}) => {
            if (response?.status === 422) {
                useFlash().error(response?.data?.message ?? 'Failed to start Horizon');
            } else {
                useFlash().error("Failed to start Horizon");
            }
        })
        .finally(() => {
            horizonForm.processing = false;
        });
};

const stopHorizon = () => {
    horizonForm.processing = true;
    // props.horizonProcessStatus.value = "Stopping";
    axios.post(route('api.site.horizon.stop', {server: props.server.id, site: props.site.id}))
        .then(({data}) => {
            useFlash().success(data.message || 'Horizon stopped successfully');
            horizonStatus.value = false;
        })
        .catch(({response}) => {
            if (response?.status === 422) {
                useFlash().error(response?.data?.message ?? 'Failed to stop Horizon');
            } else {
                useFlash().error("Failed to stop Horizon");
            }
        })
        .finally(() => {
            horizonForm.processing = false;
        });
};

const restartHorizon = () => {
    horizonForm.processing = true;
    // props.horizonProcessStatus.value = "Stopping";
    axios.post(route('api.site.horizon.restart', {server: props.server.id, site: props.site.id}))
        .then(({data}) => {
            useFlash().success(data.message || 'Horizon restarted successfully');
        })
        .catch(({response}) => {
            if (response?.status === 422) {
                useFlash().error(response?.data?.message ?? 'Failed to restart Horizon');
            } else {
                useFlash().error("Failed to restart Horizon");
            }
        })
        .finally(() => {
            horizonForm.processing = false;
        });
};

const setupScheduler = () => {
    schedulerForm.processing = true;
    axios.post(route('api.site.scheduler.setup', {server: props.server.id, site: props.site.id}), {
        frequency: schedulerForm.frequency
    })
        .then(({data}) => {
            useFlash().success(data.message || 'Scheduler setup successfully');
            schedulerActive.value = true;
        })
        .catch(({response}) => {
            if (response?.status === 422) {
                useFlash().error(response?.data?.message ?? 'Failed to setup scheduler');
            } else {
                useFlash().error("Failed to setup scheduler");
            }
        })
        .finally(() => {
            schedulerForm.processing = false;
        });
};

const stopScheduler = () => {
    schedulerForm.processing = true;
    axios.post(route('api.site.scheduler.stop', {server: props.server.id, site: props.site.id}))
        .then(({data}) => {
            useFlash().success(data.message || 'Scheduler stopped successfully');
            schedulerActive.value = false;
        })
        .catch(({response}) => {
            if (response?.status === 422) {
                useFlash().error(response?.data?.message ?? 'Failed to stop scheduler');
            } else {
                useFlash().error("Failed to stop scheduler");
            }
        })
        .finally(() => {
            schedulerForm.processing = false;
        });
};

// Queue worker event handlers
const onQueueWorkerAdded = (worker) => {
    // Add the new worker to the list
    queueWorkers.value.push(worker);
};

const onQueueWorkerUpdated = (updatedWorker) => {
    // Find and update the worker in the list
    const index = queueWorkers.value.findIndex(w => w.id === updatedWorker.id);
    if (index !== -1) {
        queueWorkers.value[index] = updatedWorker;
    }
};

const onQueueWorkerRemoved = (workerId) => {
    // Remove the worker from the list
    queueWorkers.value = queueWorkers.value.filter(w => w.id !== workerId);
};
</script>
