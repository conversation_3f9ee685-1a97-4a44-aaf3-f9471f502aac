<template>
  <single-site :server="server" :site="site" active="Nginx Customization">
    <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
      <div class="bg-white dark:bg-mode-light p-2 pt-0 rounded-10px">
        <div class="info_head flex justify-between items-center px-6 py-3">
          <h5 class="text-left text-lg font-normal text-dark dark:text-white">{{ $t('Custom Nginx Config') }}</h5>
          <div class="flex items-center">
            <button @click.prevent="previewNginxConf"
                    type="button"
                    class="inline-flex items-center justify-center rounded-10px focus:outline-0 border-transparent
                        bg-transparent hover:bg-primary-dark
                        border-1 border-primary-light hover:border-primary-dark text-primary-light hover:text-white
                        shadow-none px-25px py-1 min-h-50px mobile:min-h-40px text-base font-medium mr-3"
                    aria-expanded="true" aria-haspopup="true"
                    :disabled="isReadingConf"
                    :class="{'opacity-50 cursor-not-allowed': isReadingConf}"
            >
              <span v-text="isReadingConf ? $t('Fetching Nginx..') : $t('Preview Nginx')"></span>
            </button>

            <button @click.prevent="openCustomNginxModal = true; resetCustomNginxForm()"
                    type="button"
                    class="inline-flex items-center justify-center rounded-10px focus:outline-0 border-transparent
                        shadow-none px-25px py-1 min-h-50px mobile:min-h-40px bg-success-full text-sm font-medium text-white"
                    aria-expanded="true" aria-haspopup="true"
            >
              <span>{{ $t('Add a New Config') }}</span>
            </button>
          </div>
        </div>

        <table class="w-full divide-y-1 divide-light dark:divide-mode-light" v-if="customNginxConfigs.length > 0">
          <tbody class="bg-white rounded-md border-1 dark:border-0 border-light dark:bg-mode-base divide-y-1
                        divide-light  dark:divide-mode-light text-dark dark:text-white dark:border-mode-base
                        group-focus-within:border-none w-full focus:outline-none min-w-0 autofill:bg-light
                        dark:autofill:bg-mode-base"
          >
            <tr class="divide-x-1 divide-light dark:divide-mode-light">
              <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-left
                          text-base font-normal h-50px"
              >
                {{ $t('File') }}
              </td>
              <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-left
                          text-base font-normal h-50px"
              >
                {{ $t('Type') }}
              </td>
              <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-left
                          text-base font-normal h-50px"
              >
                {{ $t('Updated At') }}
              </td>
              <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-center
                          text-base font-normal h-50px"
              >
                {{ $t('Actions') }}
              </td>
            </tr>

            <tr class="divide-x-1 divide-light dark:divide-mode-light"
                v-for="customNginxConfig in customNginxConfigs"
            >
              <!-- File -->
              <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                <span class="hover:underline cursor-pointer"
                      @click.prevent="openEditModal(customNginxConfig)"
                >
                  <i class="xcloud xc-settings text-secondary-full dark:text-secondary-light text-sm"></i>
                  {{ customNginxConfig.file }}.conf
                </span>
              </td>

              <!-- Type -->
              <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                {{ customNginxConfig.type }}
              </td>

              <!-- Updated At -->
              <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                {{ customNginxConfig.updated }}
              </td>

              <!-- Actions -->
              <td class="px-30px tablet:px-20px py-2 text-base font-normal h-50px text-center">
                <Tooltip
                    :title="customNginxForm.processing ? 'Editing...' : 'Edit'"
                    class="pr-3"
                >
                  <button
                      @click.prevent="openEditModal(customNginxConfig)"
                      :disabled="customNginxForm.processing"
                  >
                        <span class="text-sm flex text-secondary-light group-hover:text-white">
                          <span class="text-sm flex text-secondary-light group-hover:text-white">
                            <i class="xcloud xc-edit text-info"></i>
                          </span>
                        </span>
                  </button>
                </Tooltip>

                <Tooltip
                    :title="customNginxForm.processing ? 'Deleting...' : 'Delete'"
                    color="danger"
                >
                  <button
                      :disabled="customNginxForm.processing"
                      @click.prevent="deleteCustomNginxConfig(customNginxConfig)"
                  >
                        <span class="text-sm flex text-secondary-light group-hover:text-white">
                          <span class="text-sm flex text-secondary-light group-hover:text-white">
                            <i class="xcloud xc-delete text-danger"
                               :class="{'opacity-50': customNginxForm.processing}"
                            ></i>
                          </span>
                        </span>
                  </button>
                </Tooltip>
              </td>
            </tr>
          </tbody>
        </table>
        <Empty
            v-else
            class="mb-10"
            :documentation="$t('No custom Nginx configurations found!')"
            :canPerformAction="false"
            type="site"
        />
      </div>
    </div>
  </single-site>

  <!-- Add Custom Nginx Modal -->
  <Modal
      :footer-button="true"
      @close="openCustomNginxModal = false; resetCustomNginxForm()"
      :loading="customNginxForm.processing"
      :show="openCustomNginxModal"
      :title="$t('Create Custom Nginx Configuration for')+' '+ site.name"
      :widthClass="'max-w-1120px'"
  >
    <div class="flex flex-col gap-30px">
      <!-- Predefined configs -->
      <select-input
          v-model="customNginxForm.template"
          :error="customNginxForm.errors.template"
          :label="$t('Select Template')"
          @change="handlePredefinedConfigs"
      >
        <option
            v-for="(key, value) in templates"
            :value="value"
            v-text="value"
        >
        </option>
      </select-input>

      <suggestion
          v-if="pluginInstallRequired"
          class="-mt-4 -mb-3"
          type="warning"
          :light-mode="false"
          :message="'You need to install & configure ' + getReadablePluginName() + ' plugin in your WordPress dashboard first to make this work, otherwise Nginx may not work.'"
      >
      </suggestion>

      <!-- Type -->
      <select-input
          v-model="customNginxForm.type"
          :error="customNginxForm.errors.type"
          :label="$t('Select Config Type')"
          :disabled="customNginxForm.template !== 'Use My Own Config'"
          :class="{'opacity-50 cursor-not-allowed' : customNginxForm.template !== 'Use My Own Config'}"
      >
        <option
            v-for="(key, value) in types"
            :value="value"
            v-text="key"
        >
        </option>
      </select-input>

      <!-- File -->
      <text-input
          v-model="customNginxForm.file"
          :error="customNginxForm.errors.file"
          :label="$t('Config File Name')"
          type="text"
          :placeholder="$t('Enter the configuration file name')"
          :note="generateNote"
      />

      <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-5">
        <div>
          <!-- Content -->
          <label class="text-dark dark:text-white text-base font-normal -mb-5">{{ $t('Config Content') }}</label>
          <v-ace-editor
              :options="{
                  fontSize: '10px',
                  showLineNumbers: true,
                  wrap: true,
              }"
              @init="editorInit"
              v-model:value="customNginxForm.content"
              lang="php"
              :theme="`${navigation.nightMode ? 'tomorrow_night_blue' : 'chrome'}`"
              style="min-height: 350px"
          />
          <Error class="mt-4" v-if="customNginxForm.errors.content" :error="customNginxForm.errors.content" />
        </div>
    <div>

      <!-- Content -->
      <label class="text-dark dark:text-white text-base font-normal -mb-5">{{ $t('Preview Content') }}</label>
      <v-ace-editor
          :options="{
              fontSize: '12px',
              showLineNumbers: false,
              scrollBarAlwaysVisible: true,
              wrap: true,
              readOnly: true,
              highlightActiveLine: false,
              highlightGutterLine: false
          }"
          :wrap="true"
          @init="editorInit"
          v-model:value="preview_content"
          readonly
          lang="php"
          :theme="`${navigation.nightMode ? 'tomorrow_night_blue' : 'chrome'}`"
          style="height: 350px"
      />

    </div>
        </div>

        <div v-if="nginxTestRun">
        <div class="rounded-md flex items-center bg-primary-light/10 dark:bg-mode-base px-6 py-4 border-none border-primary-light
                dark:border-mode-base w-full"
        >
          <div class="flex items-center">
            <i class="xcloud text-xl pl-2"
               :class="{ 'xc-close-o text-danger': !nginxTestRunSuccess, 'xc-tick-o text-success-full': nginxTestRunSuccess}"
            ></i>
            <p class="text-sm text-dark dark:text-white leading-7 pl-4">
              <div v-for="(key, value) in nginxTestRunResponse" v-text="key"></div>
            </p>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="px-20px wide-mobile:px-30px wide-mobile:py-20px flex gap-5 float-right">
        <button
            @click.prevent="runAndDebugNginxConfig()"
            :disabled="customNginxForm.processing && customNginxForm.runAndDebug"
            class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px
                            py-2px text-base text-white bg-primary-dark dark:bg-primary-dark focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
            aria-expanded="true" aria-haspopup="true">
          <span v-text="customNginxForm.processing && customNginxForm.runAndDebug ? $t('Running...') : $t('Run & Debug')"></span>
        </button>
        <button
            @click.prevent="addCustomNginxConfig()"
            :disabled="customNginxForm.processing && !customNginxForm.runAndDebug"
            class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px
                            py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
            aria-expanded="true" aria-haspopup="true">
          <span v-text="customNginxForm.processing && !customNginxForm.runAndDebug ? $t('Saving...') : $t('Save Config')"></span>
        </button>
      </div>
    </template>
  </Modal>

  <Modal
      :closeable="true"
      v-if="showNginxModal"
      :show="showNginxModal"
      @close="showNginxModal = false"
      :title="$t('Nginx Config File')"
      :widthClass="'max-w-950px'"
      @footer-click="showNginxModal = false"
      :footer-button-title="$t('Close')"
        :footer-button="true"
  >
    <div class="flex flex-col">
      <skeleton v-if="isReadingConf" :columns="1" :rows="10"/>
      <v-ace-editor
          v-else
          :readonly="true"
          :options="{
                        fontSize: '10px'
                    }"
          @init="editorInit"
          v-model:value="nginxConf"
          lang="text"
          :theme="`${navigation.nightMode ? 'tomorrow_night_blue' : 'chrome'}`"
          style="height: 500px"/>
    </div>
  </Modal>
</template>

<!-- script -->
<script setup>
import SingleSite from "@/Pages/Site/SingleSite.vue";
import Button from "@/Jetstream/Button.vue";
import TextInput from "@/Shared/TextInput.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Card from "@/Shared/Card.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import {computed, ref, watch, onMounted, nextTick} from "vue";
import {useForm} from "@inertiajs/inertia-vue3";
import Tooltip from "@/Shared/Tooltip.vue";
import Empty from "@/Shared/Empty.vue";
import Modal from "@/Shared/Modal.vue";
import {VAceEditor} from "vue3-ace-editor";
import {useNavigationStore} from "@/stores/NavigationStore";
import ace from 'ace-builds';
import 'ace-builds/src-noconflict/mode-nginx';
import 'ace-builds/src-noconflict/theme-chrome';
import 'ace-builds/src-noconflict/theme-tomorrow_night_blue';
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import Error from "@/Shared/Error.vue";
import {useFlash} from "@/Composables/useFlash";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
  site: Object,
  server: Object,
  types: Object,
  templates: Object,
  customNginxConfigs: Array
});

const editorInit = (editor) => {
  const mode = 'nginx';
  editor.getSession().setMode(`ace/mode/${mode}`);
}

let navigation = useNavigationStore();
let openCustomNginxModal = ref(false);
let nginxTestRun = ref(false);
let nginxTestRunResponse = ref([]);
let nginxTestRunSuccess = ref(false);
const showNginxModal = ref(false);
const isReadingConf = ref(false);
const nginxConf = ref('');
let pluginInstallRequired = ref(false);

const customNginxForm = useForm({
  template: 'Use My Own Config',
  type: 'before',
  file: '',
  content: '',
  runAndDebug: false,
  isEditing: false,
});

const openEditModal = (customNginxConfig) => {
  customNginxForm.type = customNginxConfig.type;
  customNginxForm.file = customNginxConfig.file;
  customNginxForm.content = customNginxConfig.content;
  customNginxForm.isEditing = true;
  openCustomNginxModal.value = true;
};

const preview_content_template = ref('# XCLOUD CONFIG BEFORE SERVER BLOCK\n' +
    'BEFORE_SERVER_BLOCK\n' +
    '\n' +
    'server {\n' +
    // '    # Ports to listen on\n' +
    // '    listen 443 ssl http2;\n' +
    // '    listen [::]:443 ssl http2;\n' +
    '\n' + '    # XCLOUD CONFIG INSIDE SERVER BLOCK' +
    '\n' +
    '    INSIDE_SERVER_BLOCK\n' +
    '\n' +
    '    location / {\n' +
    '        try_files $uri $uri/ /index.php?$args;\n' +
    '    }\n' +
    '\n' +
    '}\n'+
    '\n' + '# XCLOUD CONFIG AFTER SERVER BLOCK\n' +
    'AFTER_SERVER_BLOCK\n' +
    '\n');

const preview_content = ref();

const reload_preview_content = () => {
    preview_content.value = preview_content_template.value;
}

onMounted(reload_preview_content);

const after_middle_before = {
    'before' : 'BEFORE_SERVER_BLOCK',
    'server' : 'INSIDE_SERVER_BLOCK',
    'after' : 'AFTER_SERVER_BLOCK'
}

watch(customNginxForm, (value) => {
    let preview = preview_content_template.value;
    preview_content.value = '\n' + preview.replace(after_middle_before[value.type], value.content);
})

const generateNote = computed(() => {
  return 'The config will be generated as <b>/etc/nginx/xcloud-conf/' + props.site.name + '/' + customNginxForm.type + '/' + customNginxForm.file + '.conf</b>';
});

const resetCustomNginxForm = () => {
  customNginxForm.template = 'Use My Own Config';
  customNginxForm.type = 'before';
  customNginxForm.file = '';
  customNginxForm.content='';
  customNginxForm.runAndDebug = false;
  customNginxForm.isEditing = false;
  nginxTestRunResponse.value = null;
  nginxTestRunSuccess.value = false;
  nginxTestRun.value = false;
}

const getReadablePluginName = () => {
  let readableName = '';
  switch (customNginxForm.template) {
    case 'Hide My WP':
      readableName = 'Hide My WP';
      break;
    case 'Rankmath Rewrite Rules':
      readableName = 'Rankmath';
      break;
  }

  return readableName;
}

const handlePredefinedConfigs = async () => {
  await nextTick(); // Wait for DOM updates(for Firefox)
  let { template: fetchedTemplate, type, content } = props.templates[customNginxForm.template];

  if(customNginxForm.template === 'Use My Own Config'){
    resetCustomNginxForm();
  }else{
    customNginxForm.type = type;
    customNginxForm.content = content;
  }

  pluginInstallRequired.value = fetchedTemplate === 'Hide My WP' || fetchedTemplate === 'Rankmath Rewrite Rules';
}

const addCustomNginxConfig = () => {
  validate();

  if (customNginxForm.errors.type || customNginxForm.errors.file || customNginxForm.errors.content) {
    return;
  }
  customNginxForm.post(route('api.site.nginx.run-and-debug', [
    props.server.id,
    props.site.id,
  ]), {
    preserveScroll: true,
    onSuccess: () => {
      openCustomNginxModal.value = false;

      // reset form
      customNginxForm.type = 'before';
      customNginxForm.file = '';
      customNginxForm.content='';
      customNginxForm.runAndDebug = false;
      customNginxForm.isEditing = false;
      nginxTestRunResponse.value = null;
      nginxTestRunSuccess.value = false;
      nginxTestRun.value = false;
    },
    onFinish: () => {
      customNginxForm.isEditing = false;
    },
  });
};

const runAndDebugNginxConfig = () => {
  validate();

  if (customNginxForm.errors.type || customNginxForm.errors.file || customNginxForm.errors.content) {
    return;
  }

  customNginxForm.runAndDebug = true;
  customNginxForm.post(route('api.site.nginx.run-and-debug', [
    props.server.id,
    props.site.id,
  ]), {
    preserveScroll: true,
    onSuccess: (response) => {
      nginxTestRun.value = true;
      nginxTestRunResponse.value = response?.props?.jetstream?.flash?.response;
      nginxTestRunSuccess.value = response?.props?.jetstream?.flash?.success;
    },
    onFinish: () => {
      customNginxForm.runAndDebug = false;
    },
  });
};

const validate = () => {
  // clear existing errors
  customNginxForm.errors =  {
    type: '',
    file: '',
    content: '',
    runAndDebug: false,
  };

  // type validation
  if (!customNginxForm.type) {
    customNginxForm.errors.type = 'Please select a type';
  }
  // type must be one of the following: before_server_block, after_server_block, inside_server_block
  if (!['before', 'after', 'server'].includes(customNginxForm.type)) {
    customNginxForm.errors.type = 'Invalid type';
  }

  // file validation
  if (!customNginxForm.file) {
    customNginxForm.errors.file = 'Please enter a file name';
  }
  // file name must not contain any space and extension
  if (customNginxForm.file.includes(' ') || customNginxForm.file.includes('.') ) {
    customNginxForm.errors.file = 'File name must not contain any space and any extension. Example: my-custom-config';
  }
  // content validation
  if (!customNginxForm.content) {
    customNginxForm.errors.content = 'Please enter the content';
  }
};

const deleteCustomNginxConfig = (customNginxConfig) => {
  useFlash().deleteConfirmation({
        title: t('Are you sure you want to delete this Nginx Configuration?'),
        text: t('It will be permanently deleted.'),
        width: '600px',
        confirmButtonText: t('Yes, Delete')
      },
      () => {
        customNginxForm.delete(route('api.site.custom-nginx.delete', [
          props.server.id,
          props.site.id,
          customNginxConfig.id,
        ]));
      }
  );
};

const previewNginxConf = () => {
  isReadingConf.value = true;
  axios.get(route('api.site.nginxOptions.preview', {server: props.server.id, site: props.site.id}))
      .then(({data}) => {
        showNginxModal.value = true;
        nginxConf.value = data?.content;
      }).catch(({response}) => {
    if (response?.status===422){
      useFlash().error(response?.data?.message?? 'Failed to load nginx options');
    }else{
      useFlash().error("Failed to load nginx options");
    }
    showNginxModal.value = false;
  }).finally(() => {
    isReadingConf.value = false;
  });
}

</script>
