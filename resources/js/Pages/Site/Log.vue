<template>
    <single-site :server="server" :site="site" active="Logs">
        <div
            class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px"
        >
            <div
                class="flex gap-4 justify-between items-center tablet:flex-col tablet:items-start"
            >
                <div class="font-medium text-dark dark:text-white leading-none">
                    <span v-if="access_logs.value?.file">
                        {{ $t('Showing logs from') }}: {{ access_logs.value?.file }}
                    </span>
                    <span v-else> {{ $t('Loading..') }} </span>
                </div>

                <div
                    class="flex flex-wrap justify-end gap-x-4 gap-y-2.5 items-center ml-auto"
                >
                    <select
                        v-model="filter"
                        class="block bg-white dark:bg-dark text-base font-normal text-dark dark:text-white min-h-30px p-2 pl-25px pr-25px border-1 border-secondary-light dark:border-mode-focus-light group-focus-within:border-success-full focus:outline-none w-60 rounded-md autofill:bg-light dark:autofill:bg-mode-base justify-end"
                        :disabled="!access_logs.value?.file || clearingLog"
                    >
                        <option v-if="site?.is_wordpress" value="wp-debug-log">
                            {{ $t('WordPress Debug Log') }}
                        </option>

                        <option v-if="site?.type === 'laravel'" value="laravel-log">
                            {{ $t('Laravel Debug Log') }}
                        </option>

                        <template v-if="server.stack === 'nginx'">
                            <option value="nginx">Nginx Log</option>
                            <option value="access">Nginx Access Log</option>
                            <option value="error">Nginx Error Log</option>
                        </template>
                        <template v-if="server.stack === 'openlitespeed'">
                            <option value="lsws">OpenLiteSpeed Log</option>
                            <option value="access">OpenLiteSpeed Access Log</option>
                            <option value="error">OpenLiteSpeed Error Log</option>
                        </template>
                    </select>
                    <span class="flex items-center gap-x-4">
                        <btn
                            v-if="access_logs.value?.file && isScrollable"
                            icon="xcloud xc-bottom-arrow"
                            :disabled="!access_logs.value?.file"
                            @click.prevent="scrollToBottom"
                            class="!mt-0"
                            title="Jump to End"
                        >
                            {{ $t("Jump to End") }}
                        </btn>
                        <btn
                            icon="xcloud xc-verify_dns"
                            :loading="!access_logs.value?.file"
                            :disabled="!access_logs.value?.file"
                            @click.prevent="getSiteLog"
                            class="!mt-0"
                        >
                            {{
                                access_logs.value?.file ? $t("Reload") : $t("Loading..")
                            }}
                        </btn>
                        <btn
                            v-if="can_clear_logs"
                            icon="xcloud xc-delete"
                            :loading="clearingLog"
                            :disabled="clearingLog"
                            @click.prevent="clearSiteLog"
                            class="!mt-0"
                        >
                            {{ !clearingLog ? $t("Clear") : $t("Clearing..") }}
                        </btn>
                    </span>
                </div>
            </div>
            <div v-if="access_logs.value?.file" class="log-content">
                <wp-debug-log-table
                    v-if="filter === 'wp-debug-log'"
                    :access_logs="access_logs"
                    :server="server"
                    :site="site"
                />
                <log-table-raw
                    v-else-if="filter === 'laravel-log'"
                    :access_logs="access_logs"
                    :server="server"
                    :site="site"
                />
                <nginx-log-table v-else :access_logs="access_logs" />

                <div class="flex justify-end w-full" v-if="isScrollable">
                    <btn
                        icon="xcloud xc-upper-arrow"
                        :disabled="!access_logs.value?.file"
                        @click.prevent="scrollToTop"
                        class="!mt-0"
                        title="Jump to Start"
                    >
                        {{ $t("Jump to Start") }}
                    </btn>
                </div>
            </div>
            <skeleton v-else :columns="6" :rows="10" />
        </div>
    </single-site>
</template>
<script setup>
import SingleSite from "@/Pages/Site/SingleSite.vue";
import NginxLogTable from "@/Pages/Site/Components/NginxLogTable.vue";
import { onMounted, reactive, ref, watch, onUnmounted, nextTick } from "vue";
import axios from "axios";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import WpDebugLogTable from "@/Pages/Site/Components/WpDebugLogTable.vue";
import Btn from "@/Shared/Btn.vue";
import { useFlash } from "@/Composables/useFlash";
import LogTableRaw from "@/Pages/Site/Components/LogTableRaw.vue";

const access_logs = reactive({
    file: null,
    log: [],
});

const clearingLog = ref(false);

const { site, server, default_log_file } = defineProps({
    site: Object,
    server: Object,
    default_log_file: {
        type: String,
        default: "wp-debug-log",
    },
    can_clear_logs: Boolean,
});

const filter = ref(default_log_file);

const isScrollable = ref(false);

const checkScrollable = () => {
    nextTick(() => {
        const logContent = document.querySelector('.log-content');
        isScrollable.value = logContent && logContent.offsetHeight > 500;
    });
};

const scrollToTop = () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
};

const scrollToBottom = () => {
    window.scrollTo({
        top: document.body.scrollHeight,
        behavior: 'smooth'
    });
};

onMounted(async () => {
    await getSiteLog();
    checkScrollable();
});

onUnmounted(() => {
    window.removeEventListener('resize', checkScrollable);
});

watch(clearingLog, async () => {
    checkScrollable();
});


watch(filter, async () => {
    await getSiteLog();
    checkScrollable();
});

const getSiteLog = async () => {
    access_logs.value = {
        file: null,
        log: [],
    };

    access_logs.value = await axios
        .get(
            `${route("api.site.logs", { site: site.id })}?type=${filter.value}`
        )
        .then((res) => res.data);
    
    checkScrollable();
};

const clearSiteLog = async () => {
    clearingLog.value = true;
    await axios
        .get(
            `${route("api.site.logs.clear", { site: site.id })}?type=${
                filter.value
            }`
        )
        .then((res) => {
            // console.log(res);
            if (res.data.status === "finished") {
                useFlash().success("Log cleared successfully.");
                access_logs.value = {
                    file: filter.value,
                    log: [],
                };
            } else {
                useFlash().error("Failed to clear log.");
            }
            clearingLog.value = false;
        })
        .catch((err) => {
            clearingLog.value = false;
            if (
                err.response &&
                err.response.data &&
                err.response.data.message
            ) {
                useFlash().error(err.response.data.message);
            } else {
                useFlash().error(
                    "An error occurred while processing your request."
                );
            }
        });
};
</script>
