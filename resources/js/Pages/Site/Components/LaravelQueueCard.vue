<template>
    <Card
        :loading="queueForm.processing"
        :title="$t('Laravel Queue')"
        buttonStyle="success"
        :button-title="$t('Add Queue Worker')"
        @onSubmit="openAddQueueWorkerModal"
        :disable-use-button="queueForm.processing || !server.is_connected || site?.is_disabled">
        <skeleton v-if="loading" :has-header="false" :rows="1" :columns="1" row-div-classes="h-5" rowClasses="h-20"/>
        <template v-else>
            <div class="flex flex-col gap-4">
                <div v-if="queueWorkers.length === 0" class="flex items-center">
                    <div class="flex items-center px-3 py-1.5 rounded-full text-xs font-medium mr-3 capitalize
                                bg-gray-100 text-secondary-full dark:bg-mode-focus-light dark:text-mode-secondary-light">
                        <svg class="w-3.5 h-3.5 mr-1.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-13a.75.75 0 00-1.5 0v5.5a.75.75 0 001.5 0V5zm0 9.75a.75.75 0 10-1.5 0 .75.75 0 001.5 0z" clip-rule="evenodd" />
                        </svg>
                        {{ $t('Not Configured') }}
                    </div>

                    <span class="text-sm text-dark dark:text-white">
                        {{ $t('No queue workers are running') }}
                    </span>
                </div>
                <div v-else>
                    <div class="overflow-x-auto">
                        <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                            <thead>
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <th class="px-4 py-2 text-secondary-full dark:text-secondary-light text-left text-base font-normal">
                                        {{ $t('Connection') }}
                                    </th>
                                    <th class="px-4 py-2 text-secondary-full dark:text-secondary-light text-left text-base font-normal">
                                        {{ $t('Queue') }}
                                    </th>
                                    <th class="px-4 py-2 text-secondary-full dark:text-secondary-light text-left text-base font-normal">
                                        {{ $t('Timeout') }}
                                    </th>
                                    <th class="px-4 py-2 text-secondary-full dark:text-secondary-light text-left text-base font-normal">
                                        {{ $t('Processes') }}
                                    </th>
                                    <th class="px-4 py-2 text-secondary-full dark:text-secondary-light text-left text-base font-normal">
                                        {{ $t('Status') }}
                                    </th>
                                    <th class="px-4 py-2 text-secondary-full dark:text-secondary-light text-left text-base font-normal">
                                        {{ $t('Actions') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-mode-base divide-y-1 divide-light dark:divide-mode-light">
                                <tr v-for="worker in queueWorkers" :key="worker.id" class="divide-x-1 divide-light dark:divide-mode-light text-dark dark:text-white">
                                    <td class="px-4 py-2 text-left text-base font-normal ">
                                        {{ worker.connection }}
                                    </td>
                                    <td class="px-4 py-2 text-left text-base font-normal">
                                        {{ worker.queue }}
                                    </td>
                                    <td class="px-4 py-2 text-left text-base font-normal">
                                        {{ worker.max_time > 0 ? worker.max_time + 's' : ('&mdash;') }}
                                    </td>

                                    <td class="px-4 py-2 text-left text-base font-normal">
                                        {{ worker.processes }}
                                    </td>
                                    <td class="px-4 py-2 text-left text-base font-normal">
                                        <div class="flex items-center gap-2">
                                            <status-badge :status="worker.status">
                                            <template #refresh-button>
                                                <tooltip title="Refresh Status" align="top">
                                                    <button
                                                        @click="refreshWorkerStatus(worker)"
                                                        class="text-info hover:text-info-dark p-1 rounded-full focus:outline-none"
                                                        :disabled="worker.isRefreshing || queueForm.processing"
                                                        :class="{'opacity-50 cursor-not-allowed': worker.isRefreshing || queueForm.processing}"
                                                        :title="$t('Refresh Status')">
                                                        <i class="xcloud xc-verify_dns text-xs" :class="{'animate-spin': worker.isRefreshing}"></i>
                                                    </button>
                                                </tooltip>
                                            </template>
                                        </status-badge>
                                        </div>
                                    </td>
                                    <td class="px-4 py-2 text-left text-base font-normal">
                                        <div class="flex gap-2">
                                            <Link
                                                :title="$t('Edit Process')"
                                                :href="route('server.supervisor.edit', [server, worker])"
                                                class="text-info hover:text-info-dark"
                                                :disabled="queueForm.processing"
                                            >
                                                <i class="xcloud xc-edit"></i>
                                            </Link>

                                            <button
                                                :title="$t('Restart Process')"
                                                @click="restartQueueWorker(worker)"
                                                class="text-info hover:text-info-dark"
                                                :disabled="queueForm.processing"
                                                >
                                                <i class="xcloud xc-restart"></i>
                                            </button>



                                            <button
                                                :title="$t('View Output')"
                                                @click="viewQueueWorkerOutput(worker)"
                                                class="text-info hover:text-info-dark"
                                                :disabled="queueForm.processing"
                                            >
                                                <i class="xcloud xc-analytics"></i>
                                            </button>
                                            <button
                                                :title="$t('Delete')"
                                                @click="confirmStopQueueWorker(worker)"
                                                class="text-danger hover:text-danger-dark"
                                                :disabled="queueForm.processing"
                                            >
                                                <i class="xcloud xc-delete"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <suggestion type="slot" :light-mode="true">
                    {{ $t('Queue workers process jobs in the background. They are useful for handling time-consuming tasks like sending emails, processing files, or any other task that should not block the main application.') }}
                </suggestion>
            </div>
        </template>
    </Card>

    <!-- Add Queue Worker Modal -->
    <Modal
        @close="closeAddQueueWorkerModal"
        :loading="queueForm.processing"
        :show="showAddQueueWorkerModal"
        :footer-button="true"
        :title="editMode ? $t('Update Queue Worker') : $t('Add Queue Worker')"
        :widthClass="'max-w-850px'"
    >
        <div class="flex flex-col gap-30px">
            <text-input
                v-model="queueForm.connection"
                :error="queueForm.errors.connection"
                :placeholder="$t('redis')"
                type="text"
                :label="$t('Connection')"
            />

            <text-input
                v-model="queueForm.queue"
                :error="queueForm.errors.queue"
                :placeholder="$t('default')"
                type="text"
                :label="$t('Queue')"
            />

            <text-input
                v-model="queueForm.max_time"
                :error="queueForm.errors.max_time"
                :placeholder="$t('0')"
                type="number"
                :label="$t('Maximum Seconds Per Job')"
                :note="$t('0 = No Timeout')"
            />

            <text-input
                v-model="queueForm.memory"
                :error="queueForm.errors.memory"
                :placeholder="$t('256')"
                type="number"
                :label="$t('Maximum Memory (Optional)')"
                :note="$t('Memory limit in MB')"
            />

            <text-input
                v-model="queueForm.processes"
                :error="queueForm.errors.processes"
                :placeholder="$t('1')"
                type="number"
                :label="$t('Number of Processes')"
                :note="$t('Number of worker processes to run')"
            />
        </div>
        <template #footer>
            <button
                @click.prevent="submitQueueWorkerForm"
                :disabled="queueForm.processing"
                class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px
                        py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                aria-expanded="true" aria-haspopup="true">
                <span v-text="queueForm.processing ? $t('Processing...') : (editMode ? $t('Update Queue Worker') : $t('Add Queue Worker'))"></span>
            </button>
        </template>
    </Modal>

    <!-- Output Log Modal -->
    <Modal
        @close="showOutputModal = false"
        :show="showOutputModal"
        :footerButton="false"
        :widthClass="'max-w-850px'"
        :title="$t('Queue Worker Output')"
    >
        <div class="flex flex-col text-secondary-full dark:text-white leading-loose">
            <div v-if="outputLoading">
                <skeleton columns="1"></skeleton>
            </div>

            <div v-else>
                <!-- Scroll buttons -->
                <div class="flex justify-end my-4">
                    <button @click="scrollToBottom">{{ $t('Scroll to end') }}</button>
                </div>
                <pre ref="outputRef" class="text-left whitespace-pre-wrap break-words text-xs">{{ workerOutput }}</pre>
                <div class="flex justify-end my-4">
                    <button @click="scrollToTop">{{ $t('Scroll to top') }}</button>
                </div>
            </div>
        </div>
    </Modal>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';
import Card from "@/Shared/Card.vue";
import { useForm } from "@inertiajs/inertia-vue3";
import { useFlash } from "@/Composables/useFlash";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import TextInput from "@/Shared/TextInput.vue";
import Modal from "@/Shared/Modal.vue";
import { useI18n } from "vue-i18n";
import { Inertia } from "@inertiajs/inertia";
import Tooltip from "@/Shared/Tooltip.vue";
import StatusBadge from "@/Shared/StatusBadge.vue";

const { t } = useI18n();

const props = defineProps({
    site: Object,
    server: Object,
    queueWorkers: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    },
    queueConnection: {
        type: String,
        default: 'redis'
    }
});

const emit = defineEmits(['queue-worker-added', 'queue-worker-updated', 'queue-worker-removed']);

const showAddQueueWorkerModal = ref(false);
const editMode = ref(false);
const editingWorkerId = ref(null);

// Output modal state
const showOutputModal = ref(false);
const outputLoading = ref(false);
const workerOutput = ref('');
const outputRef = ref(null);

const queueForm = useForm({
    connection: props.queueConnection || 'redis',
    queue: 'default',
    max_time: 0,
    memory: 256,
    processes: 1,
    processing: false
});

const openAddQueueWorkerModal = () => {
    editMode.value = false;
    editingWorkerId.value = null;
    resetForm();
    showAddQueueWorkerModal.value = true;
};

const closeAddQueueWorkerModal = () => {
    showAddQueueWorkerModal.value = false;
    resetForm();
};

const resetForm = () => {
    if (!editMode.value) {
        queueForm.connection = props.queueConnection || 'redis',
        queueForm.queue = 'default';
        queueForm.max_time = 0;
        queueForm.memory = 256;
        queueForm.processes = 1;
    }
    queueForm.errors = {};
    queueForm.processing = false;
};

// This function is not currently used but kept for future reference
// const editQueueWorker = (worker) => {
//     editMode.value = true;
//     editingWorkerId.value = worker.id;
//     queueForm.connection = worker.connection;
//     queueForm.queue = worker.queue;
//     queueForm.max_time = worker.max_time;
//     queueForm.memory = worker.memory;
//     queueForm.processes = worker.processes;
//     showAddQueueWorkerModal.value = true;
// };

const submitQueueWorkerForm = () => {
    queueForm.processing = true;

    if (editMode.value) {
        // Update existing queue worker
        queueForm.put(route('api.site.queue.update', [props.server.id, props.site.id, editingWorkerId.value]), {
            preserveScroll: true,
            onSuccess: (response) => {
                useFlash().success(t('Queue worker updated successfully'));
                closeAddQueueWorkerModal();
                emit('queue-worker-updated', response.data.worker);
            },
            onError: () => {
                queueForm.processing = false;
            }
        });
    } else {
        // Create new queue worker
        queueForm.post(route('api.site.queue.start', [props.server.id, props.site.id]), {
            preserveScroll: true,
            onSuccess: (response) => {
                useFlash().success(t('Queue worker added successfully'));
                closeAddQueueWorkerModal();
                emit('queue-worker-added', response.props.jetstream.flash.worker);
            },
            onError: () => {
                queueForm.processing = false;
            }
        });
    }
};

const confirmStopQueueWorker = (worker) => {
    useFlash().deleteConfirmation(
        {
            title: t('Are you sure you want to stop this queue worker?'),
            text: t('This will remove the queue worker process.'),
            width: '600px',
            confirmButtonText: t('Yes, Stop')
        },
        () => {
            stopQueueWorker(worker);
        }
    );
};

const stopQueueWorker = (worker) => {
    queueForm.processing = true;

    axios.post(route('api.site.queue.stop', [props.server.id, props.site.id, worker.id]))
        .then(() => {
            useFlash().success(t('Queue worker stopped successfully'));
            emit('queue-worker-removed', worker.id);
            queueForm.processing = false;
        })
        .catch(error => {
            useFlash().error(error.response?.data?.error || t('Failed to stop queue worker'));
            queueForm.processing = false;
        });
};

// Restart queue worker function
const restartQueueWorker = (worker) => {
    queueForm.processing = true;

    // Update the worker status to show it's restarting
    worker.status = 'restarting';

    // Use the supervisor process restart route since queue workers are supervisor processes
    axios.post(route('api.server.supervisor.restart', [props.server.id, worker.id]))
        .then(() => {
            useFlash().success(t('Queue worker restarted successfully'));
            queueForm.processing = false;
            // Refresh the page to update the status
            Inertia.reload({ only: ['queueWorkers'] });
        })
        .catch(error => {
            useFlash().error(error.response?.data?.error || t('Failed to restart queue worker'));
            queueForm.processing = false;
        });
};

// View queue worker output function
const viewQueueWorkerOutput = (worker) => {
    outputLoading.value = true;
    showOutputModal.value = true;

    // Use the supervisor process output route since queue workers are supervisor processes
    axios.get(route('api.server.supervisor.output', [props.server.id, worker.id]))
        .then(response => {
            outputLoading.value = false;
            workerOutput.value = response.data;
        })
        .catch(error => {
            outputLoading.value = false;
            workerOutput.value = error.response?.data || t('Error loading output. Please try again.');
        });
};

// Scroll functions for the output modal
const scrollToBottom = () => {
    outputRef.value?.scrollIntoView({ behavior: "smooth", block: "end" });
};

const scrollToTop = () => {
    outputRef.value?.scrollIntoView({ behavior: "smooth", block: "start" });
};


// Function to refresh the status of a single worker
const refreshWorkerStatus = async (worker) => {
    // Add isRefreshing property if it doesn't exist
    if (worker.isRefreshing === undefined) {
        worker.isRefreshing = false;
    }

    if (worker.isRefreshing || queueForm.processing) return;
    worker.isRefreshing = true;

    try {
        // Call the API to check the status of this specific worker
        const response = await axios.get(route('api.server.supervisor.check-status', [props.server.id, worker.id]));

        // Update the worker status with the response
        worker.status = response.data.status;
    } catch (error) {
        console.error('Error refreshing worker status:', error);
        useFlash().error(error.response?.data?.error || t('Failed to refresh worker status'));
    } finally {
        worker.isRefreshing = false;
    }
};
</script>
