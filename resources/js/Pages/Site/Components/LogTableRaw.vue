<template>
    <div class="bg-focused dark:bg-mode-light rounded-10px p-5 flex flex-col mb-10px">
        <div class="overflow-x-auto w-full">
            <div class="text-mode-base dark:text-white whitespace-nowrap" v-for="(access_log, index) in access_logs.value?.log">
                {{ access_log }}
            </div>
        </div>
    </div>
</template>

<!-- script -->
<script setup>

import Datatable from "@/Shared/Table/Datatable.vue";
import NginxLogRow from "@/Pages/Site/Components/NginxLogRow.vue";

const {access_logs} = defineProps({
    access_logs: {
        type: Object,
        required: true
    },
});
</script>
