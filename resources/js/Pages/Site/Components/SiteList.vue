<template>
  <div
      class="group mb-4 relative grid grid-cols-12 gap-x-20px gap-y-10px bg-white dark:bg-mode-light border-2 border-white
              dark:border-mode-light hover:drop-shadow-list-box px-30px py-20px rounded-10px duration-75 ease-in-out min-w-[60rem]"
      :class="{ 'z-[1]': showDropDown }"
  >
    <div class="flex items-center grow col-span-5">
      <div class="flex items-center gap-20px wide-mobile:gap-10px">
        <span class="w-100px wide-mobile:w-60px shrink-0">
            <img
                v-if="site.status === 'provisioned'"
                :src="`https://s.wordpress.com/mshots/v1/${site.site_url}/?w=200`"
                alt="h-full"
            />
            <img v-else :src="asset('img/site_placeholder.gif')" alt="">
        </span>
        <div class="flex flex-col">
          <h2 class="inline-flex items-center">
            <StateToolTip
                :align="index === 0 ? 'bottom' : 'top'"
                class="mr-3"
                :state="site.state"
                :title="site.status_readable"
                :blinking="false"
                :blinking_title="site?.vulnerabilities_count + ' Vulnerabilities Found'"
            />
            <tooltip
                :align="index === 0 ? 'bottom' : 'top'"
                class="cursor-pointer"
                :title="site.name"
            >
                <Link
                    :href="'/site/' + site.id"
                    class="inline-flex items-center text-dark dark:text-light text-3xl leading-7 hover:text-primary-dark dark:hover:text-white transition ease-in-out duration-75"
                >
                    {{ $filters.textLimit(site.name, 20) }}
                </Link>
            </tooltip>
            <tooltip
                :align="index === 0 ? 'bottom' : 'top'"
                v-if="site.state === 'Success'"
                class="cursor-pointer ml-3"
                title="View Site"
            >
                <a
                    :href="site.site_url"
                    target="_blank"
                    class="inline-flex items-center"
                >
                    <i class="xcloud xc-maximize text-base text-secondary-full dark:text-secondary-light hover:text-primary-light
                          dark:hover:text-primary-light cursor-pointer transition ease-in-out duration-75">
                    </i>
                </a>
            </tooltip>
          </h2>

          <div class="flex items-center flex-wrap gap-x-1 gap-y-1 mt-1">
            <span class="text-base text-dark dark:text-light leading-none">
                {{ site.created_at_readable }}
            </span>

            <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm"
                v-if="site.environment"
            >
              <span
                  class="px-10px py-1 wide-mobile:p-1
                    rounded-md transition ease-in-out duration-150 inline-flex items-center gap-1.5"
                  :class="{
                      'text-success-light bg-success-light/10' : site.environment === 'production',
                      'text-[#9AA4B2] bg-[#9AA4B2]/10' : site.environment === 'staging' || site.environment === 'staging_with_own_domain',
                      'text-[#007EFD] bg-[#007EFD]/10' : site.environment === 'demo'
                  }"
              >
                  <span>{{ getSiteEnvironmentReadable(site.environment) }}</span>
              </span>
            </h6>

            <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm text-secondary-full
                            dark:text-mode-secondary-light"
                v-if="site.is_wordpress && site.wordpress_version"
            >
              <span class="px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base rounded-md transition ease-in-out
                          duration-150 inline-flex items-center gap-1.5"
              >
                  <i class="xcloud xc-wordpress text-primary-light"></i>
                  <span>{{ site.wordpress_version }}</span>
              </span>
            </h6>
            <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm text-secondary-full dark:text-mode-secondary-light">
              <span class="px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base  rounded-md transition
                    ease-in-out duration-150 inline-flex items-center gap-1.5">
                  <i class="xcloud text-xl xc-synchronization text-primary-light"></i>
                  <span>
                    {{
                      site?.site_size
                          ? diskSize(Math.ceil(site?.site_size.disk))
                          : "0MB"
                    }}
                  </span>
              </span>
            </h6>
            <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm text-secondary-full dark:text-mode-secondary-light">
              <span class="px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base  rounded-md transition
                    ease-in-out duration-150 inline-flex items-center gap-1.5">
                  {{ $t('PHP') }}:
                  <span>{{ site.php_version }}</span>
              </span>
            </h6>
            <h6 v-if="site?.is_git" class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm text-secondary-full
                  dark:text-mode-secondary-light"
            >
              <span class="px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base rounded-md transition
                    ease-in-out duration-150 inline-flex items-center gap-1.5">
                  <i class="xcloud text-xl xc-git text-primary-light"></i>
                  <span>
                   {{ $t('Git') }}
                  </span>
              </span>
            </h6>

            <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm text-secondary-full
                          dark:text-mode-secondary-light"
                v-if="site.is_multisite"
            >
              <tooltip :title="site.multisite_subdomain ? 'Subdomain' : 'Subdirectory'">
                <span class="px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base
                      rounded-md transition ease-in-out duration-150 inline-flex items-center gap-1.5"
                >
                    <i class="xcloud xc-data-network text-primary-light"></i>
                    <span>Multisite - {{ site.multisite_subdomain ? 'Subdomain' : 'Subfolder' }}</span>
                </span>
              </tooltip>
            </h6>

            <div class="flex items-center justify-between flex-wrap"
                v-if="site.tags?.length > 0"
            >
              <div class="flex items-center">
                <tooltip
                    class="cursor-pointer"
                    :title="site.tags[0]?.name"
                >
                  <span class="text-base tablet:text-sm text-secondary-full dark:text-mode-secondary-light rounded-md">
                      #
                      {{
                        $filters.textLimit(
                            site.tags[0]?.name
                        )
                      }}
                  </span>
                </tooltip>

                <tooltip
                    class="text-xs cursor-pointer ml-2"
                    :title="
                        site.tags
                            .slice(1)
                            .map((tag) => tag.name)
                            .join(', ')
                    "
                >
                  <h6 class="text-base tablet:text-sm text-primary-dark dark:text-primary-light"
                      v-if="site.tags.length > 1"
                  >
                    +{{ site.tags.length - 1 }}
                  </h6>
                </tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex flex-wrap items-center justify-end gap-10px col-span-7">
      <template v-if="site.is_wordpress">
      <Link
          :href="route('site.updates',{
              server: server.id,
              site: site.id
          })"
          :class="{
             'bg-transparent' : site.theme_updates.length + site.plugin_updates.length === 0,
          }"
          class="bg-transparent h-[2.25rem] border-1 border-light dark:border-dark text-dark dark:text-white inline-flex shrink-0 items-center justify-center rounded-md gap-2 pl-2.5 pr-2 cursor-pointer">
                <span
                    :class="{
                       'text-success-full' : site.theme_updates.length + site.plugin_updates.length === 0,
                 'text-delete' : site.theme_updates.length + site.plugin_updates.length > 0,
              }"
              class="h-6 w-6 inline-flex shrink-0 items-center justify-center rounded-md text-lg">
              <i class="xcloud xc-update"></i>
          </span>
        <span class="text-sm leading-none dark:text-white">
          {{
            site.theme_updates.length + site.plugin_updates.length > 0
                ? site.theme_updates.length +
                site.plugin_updates.length +
                " "+$t('Updates')
                : $t("No Updates")
          }}
        </span>
      </Link>
      </template>
      <Link
          :href="route('site.backup',{
              server: server.id,
              site: site.id
          })"
          :class="{
              'bg-transparent': site?.is_backup_enabled,
          }"
          class="bg-transparent h-[2.25rem] border-1 border-light dark:border-dark text-dark dark:text-white inline-flex shrink-0 items-center justify-center rounded-md gap-2 pl-2.5 pr-2 cursor-pointer"
            >
                <span
                    :class="{
                        'text-success-full': site?.is_backup_enabled,
                'text-delete ': !site?.is_backup_enabled,
            }"
            class="h-6 w-6 inline-flex shrink-0 items-center justify-center rounded-md text-white text-lg"
        >
            <i class="xcloud xc-data-recovery"></i>
        </span>
        <span class="text-sm leading-none dark:text-white "> {{ $t('Backup') }} {{site?.is_backup_enabled ? $t('Active') : $t('Inactive') }} </span>
      </Link>
      <Link
          :href="route('server.show',{
              server: server.id
          })"
          class="bg-transparent h-[2.25rem] border-1 border-light dark:border-dark text-dark dark:text-white inline-flex shrink-0 items-center justify-center rounded-md gap-2 pl-2.5 pr-2 cursor-pointer"
            >
                <span
                    class="inline-flex shrink-0 items-center justify-end rounded-md text-white text-xs"
                >
                    <cloud-provider-logo
                        :provider-name="site?.server?.provider_name ?? site.cloud_provider"
                        class="h-4"
                    />
                </span>
                <span class="text-sm leading-none">
                    {{ $filters.textLimit(server?.name, 20) }}
                </span>
            </Link>
        <div
            v-if="site?.permissions?.includes('site:vulnerability-scan') && site?.is_wordpress"
            class="bg-transparent h-[2.25rem] aspect-square shrink-0 border-1 border-light dark:border-dark text-dark dark:text-white inline-flex items-center justify-center rounded-md gap-2 cursor-pointer"
        >
            <span
                class="inline-flex shrink-0 items-center justify-end rounded-md text-white text-xs"
            >
                <Link

                    :href="route('site.vulnerability-scan',{
                        server: server.id,
                        site: site.id
                    })"
                    class="inline-flex items-center justify-center" >
                     <tooltip
                         v-if="site?.has_vulnerability_scan && site?.vulnerabilities_count > 0"
                         :title="`${site?.vulnerabilities_count} Vulnerabilities Found`"
                     >
                        <img :src="asset('img/bug.svg')" alt="bug"/>
                         </tooltip>
                    <tooltip
                        v-else
                        :title="site?.has_vulnerability_scan ? $t('No Vulnerabilities Found') : $t('Vulnerability Scan Not Enabled')"
                    >
                        <img
                            :class="{
                            'grayscale' : !site?.has_vulnerability_scan
                         }"
                            :src="asset('img/shield.svg')"
                            alt="shield"/>
                    </tooltip>
                </Link>

            </span>
        </div>
            <template
                v-if="site.status !== 'deleting'"
            >
            <MagicLoginButton
            :site="site"
            :external="true"
            :buttonClass="'min-h-50px inline-flex justify-center items-center gap-x-2 border-1 border-primary-light dark:border-dark bg-transparent bg px-4 py-1 rounded-10px text-primary-light dark:text-white font-medium whitespace-nowrap translate duration-75 ease-in-out'"
            :disabled="site.state !== 'Success' || !site?.permissions?.includes('site:access-magic-login') || !site?.is_wordpress"
          />

                <div
                    class="flex items-center justify-center server-toggle-button pr-10px"
                >
                    <SiteActions
                        :site="site"
                        :server="server"
                        position="right"
                        @onChangeDropDown="onChangeDropDown"
                    >
                        <template #selector>
                            <div>
                                <button
                                    type="button"
                                    class="inline-flex items-center justify-center min-h-40px border-1 border-primary-light dark:border-dark text-sm font-medium rounded-10px shadow-none text-white dark:text-white bg-primary-light dark:bg-dark hover:bg-white hover:border-white hover:text-dark hover:shadow-md hover:shadow-primary-dark/30 dark:hover:shadow-secondary-full/10 transition ease-in-out duration-300 focus:outline-none disabled:opacity-75 disabled:cursor-not-allowed px-15px"
                                    id="menu-button"
                                    aria-expanded="true"
                                    aria-haspopup="true"
                                >
                                    <span class="sr-only">{{ $t('Open options') }}</span>
                                    <!-- Heroicon name: mini/ellipsis-horizontal -->
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke-width="1.5"
                                        stroke="currentColor"
                                        :data-site="props.site.id"
                                        class="w-6 h-6"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M6.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM12.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0zM18.75 12a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"
                                        />
                                    </svg>
                                </button>
                            </div>
                        </template>
                    </SiteActions>
                </div>
            </template>
    </div>
  </div>
</template>

<script setup>
import Tooltip from "@/Shared/Tooltip.vue";
import StateToolTip from "@/Shared/StateToolTip.vue";
import {onMounted, ref} from "vue";
import SiteActions from "@/Pages/Site/Components/SiteActions.vue";
import CloudProviderLogo from "@/Shared/Icons/CloudProviderLogo.vue";
import {Link} from "@inertiajs/inertia-vue3";
import MagicLoginButton from "@/Shared/MagicLoginButton.vue";

const showDropDown = ref(false);

const props = defineProps({
  site: {
    type: Object,
    required: true,
  },
  server: {
    type: Object,
    required: true,
  },
  index: {
    type: Number,
    required: true,
  },
});

const emit = defineEmits(["site-deleted"]);

onMounted(() => {
  if (window.Echo) {
    console.log(
        "joining",
        "site.status." + props.site.id,
        "SiteStatusChanged"
    );
    window.Echo.private("site.status." + props.site.id).listen(
        "SiteStatusChanged",
        (e) => {
          console.log("SiteStatusChanged", e);
          if (e.status === "deleted") {
            //send to parent
            emit("site-deleted", props.site.id);
            return;
          }
          props.site.status = e.status;
          props.site.status_readable = e.status_readable;
          props.site.state = e.state;
        }
    );
  }
});

const onChangeDropDown = (value) => {
  showDropDown.value = value;
};

function diskSize(value) {
  if(!value) return 0 + 'MB';
  if(value > 1024){
    return (value / 1024).toFixed(2) + 'GB';
  }else{
    return value + 'MB';
  }
}

const getSiteEnvironmentReadable = (environment) => {
  if(environment === 'production') {
    return 'Production';
  } else if(environment === 'demo') {
        return 'Demo';
  } else if(environment === 'staging_with_own_domain' || environment === 'staging') {
    return 'Staging';
  }
}

</script>

<style>
.server-toggle-button > * {
  @apply text-base h-30px w-30px rounded-md border-1 border-light dark:border-dark flex items-center justify-center
  hover:border-primary-light dark:hover:!border-white !basis-auto;
}
.server-toggle-button > * i.xcloud {
  @apply !text-base;
}
</style>
