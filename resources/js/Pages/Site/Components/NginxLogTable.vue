<template>
  <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col mb-10px"
  >
    <div class="overflow-x-auto w-full">
      <Datatable
          :fields="['IP', 'method', 'datetime', 'browser', 'message']"
      >
        <nginx-log-row
            v-for="(access_log,index) in access_logs.value?.log"
            :access_log="access_log"
            :sl="Number(index)+1"
            :key="index"/>
      </Datatable>
    </div>
  </div>
</template>

<!-- script -->
<script setup>

import Datatable from "@/Shared/Table/Datatable.vue";
import NginxLogRow from "@/Pages/Site/Components/NginxLogRow.vue";

const {access_logs} = defineProps({
  access_logs:{
    type: Object,
    required: true
  },
});
</script>
