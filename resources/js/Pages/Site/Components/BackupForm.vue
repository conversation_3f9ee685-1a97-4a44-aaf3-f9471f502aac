<template>
  <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
    <div class="px-30px justify-between py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col items-center wide-mobile:px-15px gap-20px mobile:gap-1">
      <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
        {{ title }}
      </h4>
      <button
        type="button"
        @click.prevent="$emit('submit', form)"
        :disabled="form.processing || backupTaskRunning"
        :class="{ 'cursor-not-allowed opacity-50': form.processing || backupTaskRunning }"
        class="inline-flex items-center justify-center rounded-10px border-transparent shadow-none min-h-50px mobile:min-h-40px px-20px tablet:px-25px py-2px bg-success-full text-sm font-medium text-white focus:outline-0"
      >
        <span>{{ $t('Save') }}</span>
      </button>
    </div>
    <div class="p-30px h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px tablet:gap-10px">
      <form @submit.prevent="$emit('submit', form)">
        <div v-if="form.errors.auto_backup" class="p-5 mb-4 rounded text-red-600 danger bg-danger/20 dark:bg-danger/10 dark:text-danger">
          {{ form.errors.auto_backup }}
        </div>

        <!-- Backup Type -->
        <div class="w-full max-w-lg flex flex-col" v-if="!isLocal">
          <label class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none" for="backup_type">
            {{ $t('Backup Type') }}
          </label>
          <multiselect
            id="backup_type"
            :canClear="false"
            v-model="form.type"
            :placeholder="$t('Select Backup Type')"
            :label="$t('Backup Type')"
            :options="backupTypes"
          ></multiselect>
        </div>

        <!-- Storage Provider (only for remote backups) -->
        <div v-if="!isLocal" class="w-full mt-4 max-w-lg flex flex-col">
          <label class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none" for="bucket">
            {{ $t('Bucket') }}
          </label>
          <Multiselect
            :canClear="false"
            id="bucket"
            v-model="form.storage_provider_id"
            :placeholder="$t('Select Bucket')"
            :label="$t('Server Bucket')"
            :options="storageProviders"
          >
            <template v-slot:singlelabel="{ value }">
              <div class="multiselect-single-label">
                <img class="character-label-icon h-6 w-6 mr-2" :src="value.icon" alt="provider"> {{ value.name }}
              </div>
            </template>
            <template v-slot:option="{ option }">
              <img class="character-option-icon h-6 w-6 mr-2" :src="option.icon">
              {{ option.name }}
            </template>
          </Multiselect>

          <div v-if="form.errors.storage_provider_id" class="flex mt-2">
            <div class="text-sm text-red-600">{{ form.errors.storage_provider_id }}</div>
          </div>

          <Link :href="route('user.storage-provider')" class="text-sm text-primary-full dark:text-white underline py-2">
            {{ $t('Manage Your Storage Providers') }}
          </Link>
        </div>

        <!-- Backup Type for Local -->
        <div v-if="isLocal" class="w-full max-w-lg flex flex-col">
          <label class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none" for="backup_type">
            {{ $t('Backup Type') }}
          </label>
          <multiselect
            id="backup_type"
            :canClear="false"
            v-model="form.type"
            :placeholder="$t('Select Bucket')"
            :label="$t('Server Bucket')"
            :options="backupTypes"
          ></multiselect>
        </div>

        <!-- Backup Items -->
        <h4 class="mt-4 first-line:block font-medium mb-2.5 text-secondary-full leading-none">
          {{ $t('Backup Items') }}
        </h4>

        <div v-if="hasSiteDatabase" class="grid grid-cols-1 wide-mobile:grid-cols-1 mt-4 mb-4 gap-4">
          <!-- Database Backup Switch -->
          <Switch
            :checked="Boolean(form.database)"
            @update:modelValue="form.database = !form.database"
          >
            <span class="mr-2">{{ $t('Database Backup') }}</span>
          </Switch>
          <small v-if="form.database" class="text-black mt-2 rounded px-3 py-1 border-1 border-solid border-light dark:border-mode-base dark:text-white">
            {{ databaseName }} {{ $t('will be backed up.') }}
          </small>

          <!-- Files Backup Switch -->
          <Switch
            v-if="can_take_file_backup"
            :checked="Boolean(form.files)"
            @update:modelValue="form.files = !form.files"
          >
            <span class="mr-2">{{ $t('Files Backup') }}</span>
          </Switch>
        </div>

        <!-- Exclude Paths -->
        <textarea-input
          v-if="form.files && can_take_file_backup"
          id="server_size"
          v-model="form.exclude_paths"
          :error="form.errors.exclude_paths"
          :label="$t('Exclude Paths')"
          :placeholder="excludePathPlaceholder"
        ></textarea-input>

        <!-- Backup Options -->
        <div v-if="form.files || form.database" class="grid grid-cols-1 wide-mobile:grid-cols-1 mt-4 gap-10">
          <div class="grid grid-cols-1 wide-mobile:grid-cols-1 mt-4 gap-5">
            <!-- Auto Backup -->
            <div>
              <Switch
                :checked="Boolean(form.auto_backup)"
                @update:modelValue="form.auto_backup = !form.auto_backup"
              >
                <span v-if="form.type === 'full'" class="mr-2">{{ $t('Automatic Backup') }}</span>
                <span v-else class="mr-2">{{ $t('Automatic Full Backup') }}</span>
              </Switch>
              <div v-if="form.auto_backup" class="flex flex-row justify-between w-full gap-2">
                <select-input
                  :disabled="!form.auto_backup"
                  v-model="form.auto_backup_frequency"
                  :error="form.errors.auto_backup_frequency"
                  label="Select Backup Frequency"
                  class="mt-2.5 flex-1"
                >
                  <option
                    v-for="frequency in autoBackupFrequencies"
                    :key="frequency.value"
                    :value="frequency.value"
                    v-text="frequency.label"
                  ></option>
                </select-input>
              </div>
            </div>

            <!-- Incremental Backup -->
            <div v-if="form.type === 'incremental'">
              <Switch
                :checked="Boolean(form.auto_incremental_backup)"
                @update:modelValue="form.auto_incremental_backup = !form.auto_incremental_backup"
              >
                <span class="mr-2">{{ $t('Automatic Incremental Backup') }}</span>
              </Switch>
              <div v-if="form.auto_incremental_backup" class="flex flex-row justify-between w-full gap-2">
                <select-input
                  :disabled="!form.auto_incremental_backup"
                  v-model="form.auto_incremental_frequency"
                  :error="form.errors.auto_incremental_frequency"
                  :label="$t('Select Incremental Backup Frequency')"
                  class="mt-2.5 flex-1"
                >
                  <option
                    v-for="frequency in autoBackupFrequencies"
                    :key="frequency.value"
                    :value="frequency.value"
                    v-text="frequency.label"
                  ></option>
                </select-input>
              </div>
            </div>

            <!-- Auto Delete -->
            <div>
              <Switch
                :checked="Boolean(form.auto_delete)"
                @update:modelValue="form.auto_delete = !form.auto_delete"
              >
                <span class="mr-2">{{ $t('Automatic Delete') }}</span>
              </Switch>
              <text-input
                type="number"
                v-if="form.auto_delete"
                id="auto_delete"
                v-model="form.delete_after_days"
                :error="form.errors.delete_after_days"
                :label="$t('Delete After Days')"
                :placeholder="$t('30')"
                class="mt-2.5"
              ></text-input>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { Link } from "@inertiajs/inertia-vue3";
import Switch from "@/Shared/Switch.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import TextareaInput from "@/Shared/TextareaInput.vue";
import TextInput from "@/Shared/TextInput.vue";
import Multiselect from "@vueform/multiselect";


const props = defineProps({
  form: Object,
  title: String,
  isLocal: {
    type: Boolean,
    default: false
  },
  can_take_file_backup: {
    type: Boolean,
    default: true
  },
  backupTypes: Object,
  storageProviders: Array,
  backupTaskRunning: Boolean,
  hasSiteDatabase: Boolean,
  isWordpress: Boolean,
  databaseName: String,
  excludePathPlaceholder: String,
  autoBackupFrequencies: Array
});

const emit = defineEmits(['submit']);

</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
