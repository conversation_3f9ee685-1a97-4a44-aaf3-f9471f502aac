<template>
    <single-site :server="server" :site="site" active="Backup Settings">
        <template v-if="is_playground">
            <div class="xc-container">
                <Empty
                    :documentation="$t('This feature is not available in Playground.')"
                    :canPerformAction="false"
                    type="site"/>
            </div>
        </template>
        <template v-else>
            <div
                class="grid grid-cols-2 wide-tablet:grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px"
            >
                <!-- Remote Backup Form -->
                <backup-form
                    :form="form"
                    :title="$t('Remote Backup Settings')"
                    :isLocal="false"
                    :backupTypes="backup_types"
                    :storageProviders="formattedStorageProviders"
                    :backupTaskRunning="backup_task_running"
                    :hasSiteDatabase="site?.has_database"
                    :isWordpress="site?.is_wordpress"
                    :databaseName="site?.database_name"
                    :excludePathPlaceholder="computedExcludePath"
                    :autoBackupFrequencies="auto_backup_frequencies"
                    @submit="submit"
                    @switchAutoDelete="switchAutoDelete"
                    :can_take_file_backup="site?.type !== 'laravel'"
                />

                <!-- Local Backup Form -->
                <backup-form
                    :form="localBackupForm"
                    :title="$t('Local Backup Settings')"
                    :isLocal="true"
                    :backupTypes="backup_types"
                    :storageProviders="formattedStorageProviders"
                    :backupTaskRunning="backup_task_running"
                    :hasSiteDatabase="site?.has_database"
                    :isWordpress="site?.is_wordpress"
                    :databaseName="site?.database_name"
                    :excludePathPlaceholder="computedExcludePath"
                    :autoBackupFrequencies="auto_backup_frequencies"
                    @submit="submit"
                    @switchAutoDelete="switchAutoDelete"
                    :can_take_file_backup="site?.type !== 'laravel'"
                />
            </div>
        </template>
    </single-site>
</template>

<script setup>
import { useForm } from "@inertiajs/inertia-vue3";
import { computed, onMounted, onUnmounted, ref } from "vue";
import SingleSite from "@/Pages/Site/SingleSite.vue";
import { useFlash } from "@/Composables/useFlash";
import Empty from "@/Shared/Empty.vue";
import { useCloudProviderIcon } from "@/Composables/useCloudProviderIcon";
import BackupForm from "@/Pages/Site/Components/BackupForm.vue";

const props = defineProps({
    site: Object,
    server: Object,
    storage_providers: Array,
    backup_settings: Array,
    is_playground: Boolean,
    backup_types: Object
});

// Reactive state
const backup_task_running = ref(props.site?.backup_task_running || false);

// Lifecycle hooks
onMounted(() => {
    if (window.Echo) {
        console.log("Listening for site backup status changed:", props?.site?.id);
        window.Echo.private("site.backup." + props?.site?.id).listen(
            "SiteBackupStateUpdate",
            (e) => {
                backup_task_running.value = e.backup_task_running;
            }
        );
    }
});

onUnmounted(() => {
    if (window.Echo) {
        window.Echo.private("site.backup." + props?.site?.id).stopListening(
            "SiteBackupStateUpdate"
        );
    }
});

// Constants and computed properties
const localBackup = props.backup_settings?.find((backup) => backup.is_local);
const remoteBackup = props.backup_settings?.find((backup) => !backup.is_local);
let defaultExcludePaths = '';

if(props.site?.type === 'laravel') {
    defaultExcludePaths = ['.git','.env','vendor','node_modules','storage/logs','storage/framework/cache','storage/framework/sessions','storage/framework/views','bootstrap/cache'].join('\n');
}else{
    defaultExcludePaths = props.site?.is_wordpress ? '' : ['.git','.env','vendor','node_modules'].join('\n');
}

const computedExcludePath = props.site?.is_wordpress
    ? ['wp-admin','wp-includes', 'wp-content/plugins','wp-content/uploads'].join('\n')
    : ['.git','.env','vendor','node_modules'].join('\n');

const auto_backup_frequencies = [
    {value: "twelve_hours", label: "12 Hours"},
    {value: "daily", label: "Daily"},
    {value: "weekly", label: "Weekly"},
    {value: "monthly", label: "Monthly"},
];

// Format storage providers for the dropdown
const formattedStorageProviders = computed(() => {
    const providers = [];
    for (let key in props.storage_providers) {
        const provider = props.storage_providers[key];
        const { cloudProviderIcon } = useCloudProviderIcon(provider.provider);
        providers.push({
            value: provider.id,
            name: provider.bucket,
            icon: cloudProviderIcon.value,
        });
    }
    return providers;
});

// Form initialization
const form = useForm({
    type: remoteBackup?.type ?? Object.keys(props.backup_types)[0],
    database: remoteBackup ? remoteBackup?.database : props.site?.has_database,
    files: remoteBackup ? remoteBackup?.files : true,
    exclude_paths: remoteBackup?.exclude_paths || defaultExcludePaths,
    storage_provider_id: remoteBackup?.storage_provider_id || "",
    auto_backup: remoteBackup?.auto_backup || false,
    auto_incremental_backup: remoteBackup?.auto_incremental_backup || false,
    auto_delete: remoteBackup?.auto_delete || false,
    auto_backup_frequency: remoteBackup?.auto_backup_frequency || "daily",
    auto_incremental_frequency: remoteBackup?.auto_incremental_frequency || "daily",
    delete_after_days: remoteBackup?.delete_after_days || 1,
    is_local: false,
    time: remoteBackup?.time ?? '01:00 AM',
    incremental_time: remoteBackup?.incremental_time ?? '02:00 AM',
});

const localBackupForm = useForm({
    type: localBackup?.type ?? Object.keys(props.backup_types)[0],
    database: localBackup ? localBackup?.database : props.site?.has_database,
    files: localBackup ? localBackup?.files : true,
    exclude_paths: localBackup?.exclude_paths || defaultExcludePaths,
    auto_backup: localBackup?.auto_backup,
    auto_incremental_backup: localBackup?.auto_incremental_backup || false,
    auto_backup_frequency: localBackup?.auto_backup_frequency || "daily",
    auto_incremental_frequency: localBackup?.auto_incremental_frequency || "daily",
    delete_after_days: localBackup?.delete_after_days || 1,
    auto_delete: localBackup?.auto_delete || false,
    is_local: true,
    time: localBackup?.time ?? '01:00 AM',
    incremental_time: localBackup?.incremental_time ?? '02:00 AM',
});

// Methods
const switchAutoDelete = (backupForm) => {
    if (backupForm.auto_delete) {
        backupForm.delete_after_days = null;
    }
    backupForm.auto_delete = !backupForm.auto_delete;
};

const submit = (backupForm) => {
    if (backupForm.processing || backup_task_running.value) {
        useFlash().error("A backup task is already running!");
        return;
    }

    backup_task_running.value = true;

    backupForm
        .transform(data => ({
            ...data,
            //disable files if site type is laravel
            files: props.site?.type === 'laravel' ? false : data.files,
            auto_backup_frequency: backupForm.auto_backup ? backupForm.auto_backup_frequency : null,
            auto_incremental_frequency: backupForm.auto_incremental_backup ? backupForm.auto_incremental_frequency : null,
            delete_after_days: backupForm.auto_delete ? backupForm.delete_after_days : null,
        }))
        .post(
            route("api.site.backup.update", {
                server: props.server.id,
                site: props.site.id,
            }),
            {
                preserveScroll: true,
                onSuccess: () => {
                    useFlash().success("Backup settings updated successfully!");
                },
                onError: () => {
                    backup_task_running.value = false;
                },
                onFinish: () => {
                    backupForm.processing = false;
                },
            }
        );
};
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
