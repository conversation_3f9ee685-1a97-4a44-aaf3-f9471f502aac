<template>
    <single-site :server="server" :site="site" active="Domain">
        <Card :disable-use-button="site?.is_disabled" @onSubmit="submit" :useButton="true" title="Domain" buttonStyle="success" buttonTitle="Save" :button-title="form.processing ? 'Verifying DNS..' : 'Save'" class="ml-30px mt-30px wide-mobile:ml-20px wide-mobile:mt-20px mobile:mt-15px mobile:ml-15px" :loading="form.processing">
            <div class="grid grid-cols-2 mobile:grid-cols-1 gap-12 mobile:gap-2 ">
                <div>
                    <text-input
                        v-model="form.name"
                        :error="form.errors.name"
                        label="Primary Domain"
                        :autofocus="autofocus"
                        :disabled="!changeDomain"
                    />
                    <Btn v-if="!changeDomain" class="!min-h-50px w-24 mt-2 bg-primary-light dark:bg-dark flex"
                         @click="changeDomain = true; autofocus = true;"
                    >
                        {{ $t('Change') }}
                    </Btn>
                </div>

                <div class="mobile:mt-10px" v-if="changeDomain">
                  <AdditionalDomain :is_disable="site.is_disabled || (form.enable_multisite && form.multisite_subdomain)" :form="form"/>
                </div>

                <div class="mobile:mt-10px" v-else>
                    <div class="text-base font-normal flex items-start mobile:flex-wrap">
                      <span class="text-secondary-full dark:text-mode-secondary-dark mobile:block mobile:w-full mr-2 whitespace-nowrap">
                          {{ $t('Additional Domains') }} :
                      </span>
                    </div>
                    <div class="mt-2" v-if="additional_domains.length > 0">
                        <div v-for="additional_domain in additional_domains" class="flex mb-2">
                            <a :href="`http://${additional_domain.value}`" target="_blank"
                                class="text-dark dark:text-white hover:underline"
                                :key="additional_domain">
                                {{ additional_domain.value }}
                            </a>
                            &nbsp;
                            <span v-if="additional_domain.value && additional_domain.status === 'invalid' && cloudflare_integration"
                                class="text-sm flex text-secondary-light group-hover:text-success-full items-center ml-1 cursor-pointer">
                              <tooltip title="Subdomain must be under primary domain" :color="'danger'">
                                <i class="xcloud xc-hexa-warning text-danger"></i>
                              </tooltip>
                            </span>

                            <a @click="changeDomain = true;"
                               class="text-sm flex text-secondary-light group-hover:text-success-full items-center ml-1 cursor-pointer">
                              <tooltip title="Edit">
                                  <i class="xcloud xc-edit"></i>
                              </tooltip>
                            </a>
                        </div>
                    </div>
                    <div v-else>
                      <div class="mt-2">
                        <span class="text-dark dark:text-white cursor-pointer underline" @click="changeDomain = true">
                          {{ $t('Add new') }}
                        </span>
                      </div>
                    </div>
                </div>
            </div>

            <div class="mt-3" v-if="changeDomain">
              <label class="inline-flex mr-5">
                <input type="checkbox" class="hidden peer" :disabled="!hasCloudflareIntegration" v-model="useCloudflareDns" />
                <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4
                              before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded
                              before:mt-0.5 before:mr-2.5 before:text-xxxs before:inline-flex before:items-center before:justify-center
                              before:text-transparent before:outline-none before:transition before:duration-200
                              peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white"
                >
                   <span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm">
                        <span class="flex" :class="{'opacity-50 cursor-not-allowed' : !hasCloudflareIntegration}">
                          <span>{{ $t('Add DNS and SSL Certificate on Cloudflare') }}</span>
                          <img :src="asset('img/CF_logomark.png')" alt="cloudflare logo" class="ml-1 w-8 h-4">
                          <span>&nbsp;({{ $t('Optional') }})</span>
                        </span>
                        <span class="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                            {{ $t('Integrate Cloudflare for Automatic DNS and SSL management.') }}
                            (<Link
                            class="underline"
                            :href="route('user.integration.cloudflare')"
                            v-text="'Manage your Cloudflare Integration'"
                        />)
                        </span>
                  </span>
                </span>
              </label>

              <suggestion
                  class="mt-5"
                  v-if="hasCloudflareIntegration && form.domain_active_on_cloudflare && hasAdditionalDomains"
                  :message="$t('If you’re using different additional domains that are not part of primary domain, then you need to switch to xCloud SSL.')"
                  :light-mode="true"
              >
              </suggestion>
            </div>

            <suggestion
                v-if="form.enable_multisite && form.multisite_subdomain"
                class="mt-5 -mb-5"
                :message="$t('After changing the domain, you need to install SSL for Multisite Subdomain from the site SSL/HTTPS page.')"
                :light-mode="true"
            >
            </suggestion>

            <div v-if="changeDomain" class="mt-30px rounded-md p-20px border-1 border-secondary-light dark:bg-mode-base dark:border-mode-focus-light">
                <Skeleton v-if="loader" class="mt-30px" columns="2" />
                <div v-else>
                  <div v-if="showDomainSetup">
                    <DNS :form="form" :server="server" class="p-20px"/>
                  </div>
                  <div v-else>
                    <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                      {{ $t('DNS Setup') }}
                    </h3>
                    <div class="grid grid-cols-1 w-full">
                      <suggestion
                          class="mt-3"
                          type="success"
                          :message="$page?.props?.current_white_label ? 'Your DNS setup will be done by Cloudflare and managed by '+$page?.props?.current_white_label?.branding?.brand_name+'.' : 'Your DNS setup will be done by Cloudflare and managed by xCloud.'"
                          :light-mode="true"
                      />
                    </div>
                  </div>
                </div>
            </div>
        </Card>
    </single-site>
</template>

<!-- script -->
<script setup>

import AdditionalDomain from '@/Pages/Site/New/Components/AdditionalDomain.vue';
import SingleSite from "@/Pages/Site/SingleSite.vue";
import TextInput from '@/Shared/TextInput.vue'
import Box from '@/Shared/Box.vue'
import Btn from '@/Shared/Btn.vue'
import {useForm} from "@inertiajs/inertia-vue3";
import {computed, ref, watch} from "vue";
import {useFlash} from "@/Composables/useFlash";
import DNS from "@/Pages/Site/New/Components/DNS.vue";
import Card from "@/Shared/Card.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import {useNameGenerator} from "@/Composables/useNameGenerator";

const props = defineProps({
    site: Object,
    server: Object,
    additional_domains: Object,
    sslProvider: String,
    cloudflare_integration: Boolean,
    hasCloudflareIntegration: Boolean,
    multisite_subdomain_enabled: {
        type: Boolean,
        default: false
    },
})

let form = useForm({
    name: props.site.name,
    additional_domains: props.additional_domains,
    domain_active_on_cloudflare: false,
    cloudflare_account_id: '',
    cloudflare_zone_id: '',
    subdomain: '',
    site_name: '',
    enable_multisite: props.multisite_subdomain_enabled,
    multisite_subdomain: props.multisite_subdomain_enabled,
    previousDomain: props.site.name
});

let useCloudflareDns = ref(props.cloudflare_integration && props.hasCloudflareIntegration);
let changeDomain = ref(false);
const autofocus = ref(false);
let showDomainSetup = ref(!props.cloudflare_integration || !props.hasCloudflareIntegration);
let loader = ref(false)

let submit = () => {
    form.post(route('api.site.update.domain', [props.server.id, props.site.id]), {
        preserveScroll: true,
        onError: () => {
            useFlash().error('Failed to update domain');
        }
    })
}

watch(
    () => form.name,
    () => {
      form.name = form.name.toLowerCase();
    }
);

watch(
    () => form.additional_domains,
    (newVal, oldVal) => {
      for(let i = 0; i < form.additional_domains.length; i++){
        if(form.additional_domains[i].value !== ''){
          form.additional_domains[i].value = form.additional_domains[i].value.toLowerCase();
          // console.log(form.additional_domains[i].value)
          // if user adds additional domains, reset cloudflare integration(user will do manual dns setup)
          resetCloudflareIntegration();
          showDomainSetup.value = true;
        }
      }
    },
    { deep: true } // Use the deep option to watch for changes in array contents
);

watch(
    () => useCloudflareDns,
    (newVal, oldVal) => {
      if(useCloudflareDns.value){
        if(form.name === ''){
          useCloudflareDns.value = false;
          useFlash().swal().fire({
            icon: 'error',
            title: 'Please add a domain name first',
          })
        }else{
          checkDomain();
        }
      } else{
        showDomainSetup.value = true;
        resetCloudflareIntegration()
      }
    },
    { deep: true } // Use the deep option to watch for changes in array contents
);

let hasAdditionalDomains = computed(() => {
  return form.additional_domains.length > 0 && form.additional_domains[0].value !== '';
});

const resetCloudflareIntegration = function (){
  form.domain_active_on_cloudflare = false;
  form.cloudflare_account_id = '';
  form.cloudflare_zone_id = '';
  form.subdomain = '';
  form.site_name = '';
  useCloudflareDns.value = false;
}

const checkDomain = function () {
  loader.value = true;
  form.processing = true;

  axios.get(route("api.user.integration.cloudflare.check-domain-exists", form.name))
      .then((res) => {
        // console.log(res.data.response)
        showDomainSetup.value = !res.data.response.domain_active_on_cloudflare;

        form.domain_active_on_cloudflare = res.data.response.domain_active_on_cloudflare;
        form.cloudflare_account_id = res.data.response.account_id;
        form.cloudflare_zone_id = res.data.response.zone_id;
        form.subdomain = res.data.response.subdomain;
        form.site_name = res.data.response.site_name;

        loader.value = false;
        form.processing = false;

        // useCloudflareDns.value = false;

        if(!res.data.response.domain_active_on_cloudflare){
          useFlash().error('No cloudflare account found for this domain. Please add your domain to cloudflare first.');
        }
      })
      .catch((err) => {
        showDomainSetup.value = true;
        loader.value = false;
        form.processing = false;
        if (err.response && err.response.data && err.response.data.message) {
          useFlash().error(err.response.data.message);
        } else {
          useFlash().error('An error occurred while processing your request.');
        }
      })
}

</script>
