<template>
    <single-site :server="server" :site="site" active="Vulnerability Scan">
        <template v-if="!site?.has_vulnerability_scan">
            <div class="bg-white dark:bg-mode-light rounded-r-lg flex flex-col gap-6 p-6">
                <div class="flex flex-col gap-30px wide-mobile:p-30px mobile:p-20px w-full items-center">
                    <div class="flex flex-col gap-30px items-center">
                        <Empty
                            :can-perform-action="false"
                            type="site"
                        />
                        <p class="text-secondary-full text-lg dark:text-mode-secondary-light">
                            {{ $t('Vulnerability detection is currently disabled for this site. To enable it, please visit Server>Security Settings or') }}
                            <Link :href="route('server.vulnerability-settings', server.id)" class="text-primary-light underline">{{ $t('click here') }}</Link>
                        </p>
                    </div>
                </div>
            </div>
        </template>
        <template v-else>
            <div>
                <div class="flex flex-row items-center px-30px py-5px justify-start text-dark dark:text-white rounded-[4px] mt-5">
                    <div class="flex tablet:flex-col sm:flex-row items-center justify-center gap-2 rounded-lg p-2">
                        <span class="flex items-center gap-2">
                            <i class="xcloud xc-shield text-2xl text-success-full"></i>
                        </span>
                        <span class="text-[16px] font-[16px]">
                            {{ $t('Vulnerability Shield Pro is active!') }}
                        </span>
                        <span class="text-secondary-full dark:text-[#A8ACBC] text-[14px] font-normal">
                            {{ $t("Vulnerability Shield Pro will now deliver faster alerts to keep you ahead of threats.") }}
                        </span>
                    </div>
                </div>
            </div>

            <div class="gap-30px ml-30px mt-30px dark:bg-mode-light  mobile:mt-20px mobile:ml-20px mobile:gap-20px bg-white rounded-10px px-20px py-15px flex justify-between items-center">
                <button @click.prevent="reScan()" type="button"
                        :disabled="site.is_disabled"
                        :class="{
                            'cursor-not-allowed opacity-70':site.is_disabled || refreshing
                        }"
                        class="inline-flex items-center rounded-3xl shadow-none px-1 py-1 pr-10px bg-primary-light/10 text-sm text-primary-light focus:outline-0 gap-1">
                    <span class="rounded-full bg-primary-light h-25px w-25px shrink-0 flex justify-center items-center"><i
                        :class="{'animate-spin': refreshing}" class="xcloud xc-verify_dns text-white pt-[0.02rem]"></i></span><span>{{ $t('Refresh') }}</span>
                </button>

                <!-- Filter Options -->
                <div class="flex items-center gap-2">
                    <div class="flex-row flex gap-2 items-center">
                        <SelectorDropdown
                            :title="filter"
                            position="center"
                        >
                            <template #items>
                                <SelectItem
                                    title="All"
                                    :isActive="filter === 'all'"
                                    @onItemSelected="filter = 'all'"
                                />
                                <SelectItem
                                    title="Insecure"
                                    :isActive="filter === 'insecure'"
                                    @onItemSelected="filter = 'insecure'"
                                />
                                <SelectItem
                                    title="Secure"
                                    :isActive="filter === 'secure'"
                                    @onItemSelected="filter = 'secure'"
                                />
                            </template>
                        </SelectorDropdown>
                    </div>
                    <span class="text-sm text-secondary-full dark:text-secondary-light leading-tight">{{ $t('Last Checked') }} {{ last_update_check }}</span>
                </div>
            </div>
            <box>
                <template #header>
                    {{ $t('WordPress Core') }}
                </template>
                <template #datetime>
                    <div class="inline-flex items-center gap-1">
                        <span>{{ $t('Last Scanned') }}: {{last_update_check}}</span>
                    </div>
                </template>

                <template #body>
                    <div class="flex flex-col grow gap-30px wide-mobile:p-30px mobile:p-20px w-full">
                        <div
                            class="bg-transparent dark:bg-mode-light border-2 border-light dark:border-mode-base rounded-10px flex flex-col">
                            <div class="overflow-x-auto w-full overflow-y-hidden">
                                <skeleton class="mb-0" v-if="refreshing" :columns="5" :rows="Math.max(patchstackVulnerability?.vulnerabilities?.wp_cores?.length,5)"/>
                                <div
                                    v-else-if="filter_wp_cores?.length === 0"
                                    class="my-2"
                                >
                                    <empty
                                        :documentation="`<div class='text-center flex flex-col gap-2'>
                                        <p class='text-[24px] font-medium text-[#1F2328] dark:text-white'>${$t('Excellent! Your WordPress Core is fully secure!')}</p>
                                        <p class='text-[#40464F] dark:text-[#919DB9] text-[14px] font-normal'>${$t('No issues were detected by Vulnerability Shield Pro. You’ll be notified immediately if any issues are found.')}</p>
                                        </div>`"
                                        :can-perform-action="false"
                                        :type="emptyComponentType"
                                        :no-vulnerabilities="true"
                                    />
                                </div>
                                <table v-else class="w-full divide-y-1 divide-light dark:divide-mode-light">
                                    <thead class="bg-primary-dark dark:bg-mode-focus-dark text-white">
                                    <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Name') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Status') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('CVSS Score') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Action') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-mode-base divide-y-1 divide-light  dark:divide-mode-light text-dark dark:text-white">
                                    <tr v-if="patchstackVulnerability?.vulnerabilities?.wp_cores" v-for="wp_core in filter_wp_cores" class="divide-x-1 divide-light dark:divide-mode-light">
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                            <div
                                                class="inline-flex items-center text-base font-normal text-dark dark:text-white group">
                                                <i class="xcloud xc-wordpress text-dark dark:text-white text-4xl mr-10px"></i>
                                                <span v-if="wp_core?.security_type === 'insecure'" class="flex flex-col">
                                                    <span class="text-lg min-w-[15rem]">{{ wp_core.name }} {{ wp_core.affected_in }} - {{ wp_core.vuln_type }}</span>
                                                     <span class="text-xs">{{ $t('Current Version') }}: {{ site.wordpress_version }}</span>
                                                </span>
                                                <span v-else class="flex flex-col">
                                                    <span class="text-lg min-w-[15rem]">WordPress</span>
                                                     <span class="text-xs">{{ $t('Current Version') }}: {{ site.wordpress_version }}</span>
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                            <div class="flex flex-row gap-2 items-center" v-if="wp_core?.security_type === 'insecure'">
                                                <span
                                                    class="rounded-full bg-warning h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                        <i class="xcloud inline-flex xc-close-o text-white text-xxs"></i>
                                                    </span>
                                                <span class="text-success-lite">Insecure</span>
                                                <!--view details-->
                                                <button @click.prevent="showDetails(wp_core)" type="button" class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white underline rounded capitalize">{{ $t('Details') }}</button>
                                            </div>
                                            <div v-else class="flex flex-row gap-2 items-center">
                                             <span
                                                 class="rounded-full bg-success-full h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                    <i class="xcloud inline-flex xc-done text-white text-xxs"></i>
                                                </span>
                                                <span class="text-success-lite">{{ $t('Secure') }}</span>
                                            </div>
                                        </td>
                                        <td class="px-40px py-2 text-left text-base font-normal h-60px">
                                            <template v-if="wp_core">
                                                <tooltip
                                                    :html="true"
                                                    :title="'<b>Remediation</b> - Update to version '+wp_core.fixed_in+', or a newer patched version'">
                                                <span
                                                    :class="{
                                                        'bg-danger': wp_core.score >= 9.0,
                                                        'bg-danger/10 text-danger': wp_core.score >= 7.0 && wp_core.score < 9.0,
                                                        'bg-warning/10 text-warning': wp_core.score >= 4.0 && wp_core.score < 7.0,
                                                        'text-primary-light bg-primary-light/10': wp_core.score < 4.0
                                                    }"
                                                    style="text-wrap: nowrap"
                                                    class="text-white text-sm px-2 py-1 rounded-3xl">
                                                    {{ wp_core.score }}
                                                    <span v-if="getSecurityLabel(wp_core.score)">
                                                        <b>[{{ getSecurityLabel(wp_core.score) }}]</b>
                                                    </span>
                                                </span>
                                                </tooltip>
                                            </template>
                                        </td>
                                        <td
                                            class="px-30px flex flex-row py-2 text-left text-base font-normal h-60px gap-2">
                                            <div
                                                v-if="wp_core?.security_type === 'insecure'"
                                                class="flex gap-2">
                                                <button type="button"
                                                        v-if="wp_core?.fixed_in"
                                                        @click="updateWordPress(wp_core)"
                                                        class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1.5 ml-2 pr-3.5 bg-primary-light/10 text-sm text-primary-light focus:outline-0 gap-1.5"
                                                        aria-expanded="true" aria-haspopup="true">
                                                    <span
                                                        class="rounded-full bg-primary-light h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                        <!--<i class="xcloud xc-spinner text-white animate-spin text-xxs"></i>-->
                                                        <i class="xcloud xc-angle_right text-white text-xxs"></i>
                                                    </span>
                                                    <span>{{ wp_core?.fixed_in }} </span>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </template>
            </box>
            <box>
                <template #header>
                    {{ $t('Plugins') }}
                </template>
                <template #datetime>
                    <div class="inline-flex items-center gap-1">
                        <span>{{ $t('Last Scanned') }}: {{last_update_check}}</span>
                    </div>
                </template>
                <template #body>
                    <div class="flex flex-col grow gap-30px wide-mobile:p-30px mobile:p-20px w-full">
                        <div
                            class="bg-transparent dark:bg-mode-light border-2 border-light dark:border-mode-base rounded-10px flex flex-col">
                            <div class="overflow-x-auto w-full overflow-y-hidden">
                                <skeleton class="mb-0" v-if="refreshing" :columns="5" :rows="Math.max(patchstackVulnerability?.vulnerabilities?.plugins?.length,5)"/>
                                <div
                                    v-else-if="filter_plugins?.length === 0"
                                    class="my-2"
                                >
                                    <empty
                                    :documentation="`<div class='text-center flex flex-col gap-2'>
                                        <p class='text-[24px] font-medium text-[#1F2328] dark:text-white'>${$t('Excellent! Your plugins are fully secure!')}</p>
                                        <p class='text-[#40464F] dark:text-[#919DB9] text-[14px] font-normal'>${$t('No issues were detected by Vulnerability Shield Pro. You’ll be notified immediately if any issues are found.')}</p>
                                        </div>`"
                                        :can-perform-action="false"
                                        :type="emptyComponentType"
                                        :no-vulnerabilities="true"
                                    />
                                </div>
                                <table v-else class="w-full divide-y-1 divide-light dark:divide-mode-light">
                                    <thead class="bg-primary-dark dark:bg-mode-focus-dark text-white">
                                    <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Name') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Status') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('CVSS Score') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Action') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-mode-base divide-y-1 divide-light  dark:divide-mode-light text-dark dark:text-white">
                                    <tr v-if="patchstackVulnerability?.vulnerabilities?.plugins" v-for="plugin in filter_plugins" class="divide-x-1 divide-light dark:divide-mode-light">
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                            <div
                                                class="inline-flex items-center text-base font-normal text-dark dark:text-white group">
                                                <img
                                                    :src="`https://ps.w.org/${getItemSlug(plugin.name)}/assets/icon-256x256.png`"
                                                    @error="(e) => {
                                                        if(e.errorCount){
                                                            return;
                                                        }
                                                        e.target.src = defaultLogo(getItemSlug(plugin.name))
                                                         e.errorCount = true;
                                                    }"
                                                    class="w-40px h-40px min-w-40px shrink-0 bg-light dark:bg-mode-base rounded-full mr-10px flex items-center justify-center object-cover" :alt="plugin?.slug"/>
                                                <span v-if="plugin?.security_type === 'insecure'" class="flex flex-col">
                                                    <span class="text-lg min-w-[15rem]">{{ plugin.name }} {{ plugin.affected_in }} - {{ plugin.vuln_type }}</span>
                                                     <span class="text-xs" v-if="getItemVersion(plugin.name, 'plugins')">{{ $t('Current Version') }}: {{ getItemVersion(plugin.name, 'plugins') }}</span>
                                                </span>
                                                <span v-else class="flex flex-col">
                                                    <span class="text-lg min-w-[15rem]">{{ plugin.title }}</span>
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                            <div class="flex flex-row gap-2 items-center" v-if="plugin?.security_type === 'insecure'">
                                                <span
                                                    class="rounded-full bg-warning h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                        <i class="xcloud inline-flex xc-close-o text-white text-xxs"></i>
                                                    </span>
                                                <span class="text-success-lite">Insecure</span>
                                                <!--view details-->
                                                <button @click.prevent="showDetails(plugin)" type="button" class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white underline rounded capitalize">{{ $t('Details') }}</button>
                                            </div>
                                            <div v-else class="flex flex-row gap-2 items-center">
                                             <span
                                                 class="rounded-full bg-success-full h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                    <i class="xcloud inline-flex xc-done text-white text-xxs"></i>
                                                </span>
                                                <span class="text-success-lite">{{ $t('Secure') }}</span>
                                            </div>
                                        </td>
                                        <td class="px-40px py-2 text-left text-base font-normal h-60px">
                                            <template v-if="plugin">
                                                <tooltip
                                                    :html="true"
                                                    :title="'<b>Remediation</b> - Update to version '+plugin.fixed_in+', or a newer patched version'">
                                                <span
                                                    :class="{
                                                        'bg-danger': plugin.score >= 9.0,
                                                        'bg-danger/10 text-danger': plugin.score >= 7.0 && plugin.score < 9.0,
                                                        'bg-warning/10 text-warning': plugin.score >= 4.0 && plugin.score < 7.0,
                                                        'text-primary-light bg-primary-light/10': plugin.score < 4.0
                                                    }"
                                                    style="text-wrap: nowrap"
                                                    class="text-white text-sm px-2 py-1 rounded-3xl">
                                                    {{ plugin.score }}
                                                    <span v-if="getSecurityLabel(plugin.score)">
                                                        <b>[{{ getSecurityLabel(plugin.score) }}]</b>
                                                    </span>
                                                </span>
                                                </tooltip>
                                            </template>
                                        </td>
                                        <td
                                            class="px-30px flex flex-row py-2 text-left text-base font-normal h-60px gap-2">
                                            <div
                                                v-if="plugin?.security_type === 'insecure'"
                                                class="flex gap-2">
                                                <button v-if="plugin?.fixed_in && !plugin.hasOwnProperty('update_action')"
                                                        @click.prevent="updatePlugin(plugin)"
                                                        class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white underline rounded capitalize"
                                                >
                                                    {{ $t('Update') }}
                                                </button>
                                            </div>
                                            <div
                                                v-if="plugin?.security_type === 'insecure'"
                                                class="flex gap-2">
                                                <button
                                                    @click.prevent="ignoreVulnerability(plugin,ignoreList.includes(getItemSlug(plugin?.name)),'plugin')"
                                                    class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white rounded capitalize"
                                                >
                                                    {{ ignoreList.includes(getItemSlug(plugin?.name)) ? $t('Ignored') : $t('Ignore') }}
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </template>
            </box>
            <box>
                <template #header>
                    {{ $t('Themes') }}
                </template>
                <template #datetime>
                    <div class="inline-flex items-center gap-1">
                        <span>{{ $t('Last Scanned') }}: {{last_update_check}}</span>
                    </div>
                </template>
                <template #body>
                    <div class="flex flex-col grow gap-30px wide-mobile:p-30px mobile:p-20px w-full">
                        <div
                            class="bg-transparent dark:bg-mode-light border-2 border-light dark:border-mode-base rounded-10px flex flex-col">
                            <div class="overflow-x-auto w-full overflow-y-hidden">
                                <skeleton class="mb-0" v-if="refreshing" :columns="5" :rows="Math.max(patchstackVulnerability?.vulnerabilities?.themes?.length,4)"/>
                                <div
                                    v-else-if="filter_themes?.length === 0"
                                    class="my-2"
                                >
                                    <empty
                                        :documentation="`<div class='text-center flex flex-col gap-2'>
                                        <p class='text-[24px] font-medium text-[#1F2328] dark:text-white'>${$t('Excellent! Your theme is fully secure!')}</p>
                                        <p class='text-[#40464F] dark:text-[#919DB9] text-[14px] font-normal'>${$t('No issues were detected by Vulnerability Shield Pro. You’ll be notified immediately if any issues are found.')}</p>
                                        </div>`"
                                        :can-perform-action="false"
                                        :type="emptyComponentType"
                                        :no-vulnerabilities="true"
                                    />
                                </div>
                                <table v-else class="w-full divide-y-1 divide-light dark:divide-mode-light">
                                    <thead class="bg-primary-dark dark:bg-mode-focus-dark text-white">
                                    <tr class="divide-x-1 divide-light dark:divide-mode-light">

                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Name') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Status') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('CVSS Score') }}</th>
                                        <th class="px-30px py-2 text-left text-base font-normal h-60px">{{ $t('Action') }}</th>
                                    </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-mode-base divide-y-1 divide-light  dark:divide-mode-light text-dark dark:text-white">
                                    <tr v-if="patchstackVulnerability?.vulnerabilities?.themes" v-for="theme in filter_themes" :key="theme.name"
                                        class="divide-x-1 divide-light dark:divide-mode-light">
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                            <div
                                                class="inline-flex justify-center items-center text-base font-normal text-dark dark:text-white group">
                                                <img
                                                    :alt="theme.name"
                                                    :src="`https://ts.w.org/wp-content/themes/${getItemSlug(theme.name)}/screenshot.png?ver=${theme.version}`"
                                                    @error="(e) => {
                                                        if(e.errorCount){
                                                            return;
                                                        }
                                                       e.target.src = defaultLogo(getItemSlug(theme.name))
                                                        e.errorCount = true;
                                                    }"
                                                    class="w-40px h-40px min-w-40px shrink-0 bg-light dark:bg-mode-base rounded-full mr-10px flex items-center justify-center object-cover"/>

                                                <span v-if="theme?.security_type === 'insecure'" class="flex flex-col">
                                                    <span class="text-lg">{{ theme.name }} {{ theme.affected_in }} - {{ theme.vuln_type }}</span>
                                                    <span class="text-xs" v-if="getItemVersion(theme.name, 'themes')">Current Version: {{ getItemVersion(theme.name, 'themes') }}</span>
                                                </span>

                                                <span v-else class="flex flex-col">
                                                    <span class="text-lg">{{ theme.title }}</span>
                                                </span>
                                            </div>
                                        </td>
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                            <div class="flex flex-row gap-2 items-center" v-if="theme?.security_type === 'insecure'">
                                                <span
                                                    class="rounded-full bg-warning h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                        <i class="xcloud inline-flex xc-close-o text-white text-xxs"></i>
                                                    </span>
                                                <span class="text-success-lite">Insecure</span>
                                                <!--view details-->
                                                <button @click.prevent="showDetails(theme)" type="button" class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white underline rounded capitalize">Details</button>
                                            </div>
                                            <div v-else class="flex flex-row gap-2 items-center">
                                             <span
                                                 class="rounded-full bg-success-full h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                                                    <i class="xcloud inline-flex xc-done text-white text-xxs"></i>
                                                </span>
                                                <span class="text-success-lite">{{ $t('Secure') }}</span>
                                            </div>
                                        </td>
                                        <td class="px-30px py-2 text-left text-base font-normal h-60px">

                                            <template v-if="theme">
                                                <tooltip
                                                    :html="true"
                                                    :title="'<b>Remediation</b> - Update to version '+theme.fixed_in+', or a newer patched version'">
                                                <span
                                                    :class="{
                                                        'bg-danger': theme.score >= 9.0,
                                                        'bg-danger/10 text-danger': theme.score >= 7.0 && theme.score < 9.0,
                                                        'bg-warning/10 text-warning': theme.score >= 4.0 && theme.score < 7.0,
                                                        'text-primary-light bg-primary-light/10': theme.score < 4.0
                                                    }"
                                                    style="text-wrap: nowrap"
                                                    class="text-white text-sm px-2 py-1 rounded-3xl">
                                                    {{ theme.score }}
                                                    <span v-if="getSecurityLabel(theme.score)">
                                                        <b>[{{ getSecurityLabel(theme.score) }}]</b>
                                                    </span>
                                                </span>
                                                </tooltip>
                                            </template>
                                        </td>
                                        <td
                                            class="px-30px py-2 text-left text-base font-normal flex gap-2 h-60px">
                                            <div
                                                v-if="theme?.security_type === 'insecure'"
                                                class="flex gap-2">
                                                <button
                                                    v-if="theme?.fixed_in && !theme.hasOwnProperty('update_action')"
                                                    @click.prevent="updateTheme(theme)"
                                                    class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white underline rounded capitalize"
                                                >
                                                    {{ $t('Update') }}
                                                </button>
                                            </div>
                                            <div
                                                v-if="theme?.security_type === 'insecure'"
                                                class="flex gap-2">
                                                <button
                                                    @click.prevent="ignoreVulnerability(theme, ignoreList.includes(getItemSlug(theme?.name)),'theme')"
                                                    class="transition ease-in-out duration-150 text-secondary-full hover:text-dark dark:text-secondary-light dark:hover:text-white rounded capitalize"
                                                >
                                                    {{ ignoreList.includes(getItemSlug(theme?.name)) ? $t('Ignored') : $t('Ignore') }}
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </template>
            </box>

            <template
                v-if="vulnerability!=null"
            >
                <Modal
                    @close="vulnerability = null"
                    :footer-button-title="$t('Close')"
                    :footer-button="true"
                    :show="vulnerability!=null"
                    :title="vulnerability?.name"
                    :widthClass="'max-w-850px'">
                    <div class="flex flex-col gap-30px">
                        <div class="warning flex items-center bg-danger/20 px-6 py-4 rounded-md">
                            <img :src="asset('img/warning.svg')" alt="warning_img"/>
                            <p class="text-sm text-dark dark:text-white leading-7 pl-4">
                                <b>{{ $t('Warning') }}:</b> {{ vulnerability?.name }} {{ vulnerability?.affected_in }} - {{ vulnerability?.vuln_type }}
                            </p>
                        </div>

                        <div class="flex flex-col gap-30px">
                            <!-- Vulnerability Details -->
                            <!--<p>
                                <span class="text-dark dark:text-white">{{ vulnerability?.description }}</span>
                            </p>-->
                            <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                                <tbody class="bg-white border dark:bg-mode-base divide-y-1 divide-light dark:divide-mode-light text-dark dark:text-white">
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Patched') }}</th>
                                    <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.vpatch > 0 ? 'Yes' : 'No' }}</td>
                                </tr>
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Fixed in') }}</th>
                                    <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.fixed_in }}</td>
                                </tr>
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('CVSS Score') }}</th>
                                    <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.score }}</td>
                                </tr>
                                <tr  class="divide-x-1 divide-light dark:divide-mode-light">
                                    <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Remediation') }}</th>
                                    <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ 'Update to version '+vulnerability?.fixed_in+', or a newer patched version' }}</td>
                                </tr>
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Direct URL') }}</th>
                                    <td class="px-30px py 2 text-left text-base font-normal h-60px">
                                        <a
                                            class="text-primary-light underline text-sm"
                                            target="_blank" :href="`${vulnerability?.vuln_url}`">{{vulnerability?.vuln_url}}</a>
                                    </td>
                                </tr>
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Disclosure Date') }}</th>
                                    <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.disclosure_date }}</td>
                                </tr>
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <th class="px-30px py 2 text-left text-base font-normal h-60px">{{ $t('Resolve Date') }}</th>
                                    <td class="px-30px py 2 text-left text-base font-normal h-60px">{{ vulnerability?.resolve_date }}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                    </div>
                    <template #footer>
                        <button
                            @click.prevent="vulnerability = null"
                            class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                            aria-expanded="true" aria-haspopup="true">
                            <span>{{ $t('Close') }}</span>
                        </button>
                    </template>
                </Modal>
            </template>
        </template>
    </single-site>
</template>

<!-- script -->
<script setup>
import { useFlash } from "@/Composables/useFlash";
import SingleSite from "@/Pages/Site/SingleSite.vue";
import Box from '@/Shared/Box.vue';
import { computed, ref } from "vue";
import Modal from "@/Shared/Modal.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Empty from "@/Shared/Empty.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import SelectorDropdown from "@/Shared/SelectorDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import { useI18n } from "vue-i18n";
import axios from "axios";
import {Inertia} from "@inertiajs/inertia";
import { useNavigationStore } from "@/stores/NavigationStore";
const { t } = useI18n();
const navigation = useNavigationStore();

const nightMode = computed(() => navigation.nightMode);

const props = defineProps({
    site: Object,
    server: Object,
    scan_result: Object,
    patchstack_vulnerability: Object,
    ignored_vulnerabilities: Object,
    last_patchstack_scan: String
})

const vulnerability = ref(null);
const patchstackVulnerability = ref(props.patchstack_vulnerability);
const refreshing = ref(false);
const filter = ref('insecure');
const ignoreList = ref(Object.values(props.ignored_vulnerabilities));
const last_update_check = ref(props.last_patchstack_scan);

const emptyComponentType = computed(() => {
    return nightMode.value ? 'vulnerability_pro_dark' : 'vulnerability_pro_light';
})

const showDetails = (vulnerabilities) => {
    vulnerability.value = vulnerabilities;
}

const highestScoredVulnerability = (vulnerabilities) => {
    return vulnerabilities.score;
}

const getSecurityLabel = (score) => {
    if (score >= 9.0) return 'Critical'
    if (score >= 7.0 && score < 9.0) return 'High'
    if (score >= 4.0 && score < 7.0) return 'Medium'
    if (score < 4.0) return 'Low'
    return ''
}

const getItemVersion = (name, type) => {
    const site = props?.site?.wp_updates && props?.site?.wp_updates[type]?.find((item) => item.title === name)
    return site?.version;
}

const getItemSlug = (item) => {
    return item
        .replace(/([a-z])([A-Z])/g, '$1-$2')
        .replace(/[\s_]+/g, '-')
        .toLowerCase()
}

const filter_wp_cores = computed(() => {
    const wp_cores = patchstackVulnerability.value?.vulnerabilities?.wp_cores || [];

    if (!wp_cores.length) return [];

    return wp_cores.filter((core) => {
        switch (filter.value) {
            case 'all':
                return true;
            case 'secure':
                return core.security_type === 'secure';
            case 'insecure':
                return core.security_type === 'insecure';
            default:
                return true;
        }
    });
});


const filter_plugins = computed(() => {
    const plugins = patchstackVulnerability.value?.vulnerabilities?.plugins || [];

    if (!plugins.length) return [];

    return plugins.filter((plugin) => {
        switch (filter.value) {
            case 'all':
                return true;
            case 'secure':
                return plugin.security_type === 'secure';
            case 'insecure':
                return plugin.security_type === 'insecure';
            default:
                return plugin.name !== 'xcloud-auto-login';
        }
    });
});

const filter_themes = computed(() => {
    const themes = patchstackVulnerability.value?.vulnerabilities?.themes || [];

    if (!themes.length) return [];

    return themes.filter((theme) => {
        switch (filter.value) {
            case 'all':
                return true;
            case 'secure':
                return theme.security_type === 'secure';
            case 'insecure':
                return theme.security_type === 'insecure';
            default:
                return true;
        }
    });
});

const updateWordPress = (wp_core) => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        const updateData = {
            version: wp_core.fixed_in,
            package_url: `https://downloads.wordpress.org/release/wordpress-${wp_core.fixed_in}.zip`,
            update_type: wp_core.patch_priority >= 2 ? 'major' : 'minor',
        }

        useFlash().deleteConfirmation({
            'title': t('Are you sure you want to update WordPress?'),
            'text': t('Update WordPress Core'),
            'btn_text': t('Update')
        }, () => {
            axios.post(`/api/server/${props.server.id}/site/${props.site.id}/updateWordPress`, updateData)
                .then(({data}) => {
                    useFlash().success('WordPress update started');
                })
                .catch(() => {
                    useFlash().error('WordPress update failed');
                })
        })
    }
}
const updatePlugin = (plugin) => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        const pluginInfo =  props.site?.wp_updates?.plugins.find((item => item.title === plugin.name));
        const plugins =  props.site?.wp_updates?.plugins.filter((item => item.title === plugin.name));
        useFlash().deleteConfirmation({
            'title': t('Are you sure you want to update the plugin?'),
            'text': t('Plugin will be updated to')+` ${pluginInfo?.update_version ?? ''}`,
            'btn_text': t('Update')
        }, () => {
            axios.post(`/api/server/${props.server.id}/site/${props.site.id}/updatePlugin`, {plugins: plugins})
                .then(({data}) => {
                    useFlash().success( "Plugin update started");
                })
                .catch(() => {
                    useFlash().error('Plugin update failed');
                })
        })
    }
}

const updateTheme = (theme) => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        const themeInfo =  props.site?.wp_updates?.themes.find((item => item.title === theme.name));
        const themes = props.site?.wp_updates?.themes.filter((item) => item.title === theme.name && item?.update_version && !item.hasOwnProperty('update_action'));
        useFlash().deleteConfirmation({
            'title': t('Are you sure you want to update the theme?'),
            'text': t('You have select')+` ${themeInfo.title} `+t('theme'),
            'btn_text': t('Update')
        }, () => {
            axios.post(`/api/server/${props.server.id}/site/${props.site.id}/updateTheme`, {themes: themes})
                .then(({data}) => {
                    useFlash().success( "Theme update started");
                })
                .catch(() => {
                    useFlash().error('Theme update failed');
                })
        })
    }
}

const ignoreVulnerability = (vulnerability,ignored,type) => {
    useFlash().deleteConfirmation({
        'title': t('Are you sure you want to')+` ${ignored ? t('not ignore') : t('ignore') } `+t('this vulnerability?'),
        'text': ``,
        'btn_text': t('Ignore')
    }, () => {
        axios.post(`/api/server/${props.server.id}/site/${props.site.id}/ignore/patchstack-vulnerability`, {slug: getItemSlug(vulnerability?.name), is_ignored: !ignored})
            .then(({data}) => {
                useFlash().success(`Vulnerability ${ignored ? 'not ignored' : 'ignored'}`);
                ignoreList.value = ignored ? ignoreList.value.filter((item) => item !== getItemSlug(vulnerability?.name)) : [...ignoreList.value, getItemSlug(vulnerability?.name)];
            })
            .catch(() => {
                useFlash().error('Vulnerability ignore failed');
            })
    });
}

const defaultLogo = (name) => {
    return `https://ui-avatars.com/api/?name=${name}&color=7F9CF5&background=EBF4FF`;
}

const reScan = () => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else{
        if (!refreshing.value) {
            refreshing.value = true
            axios.post(route('api.site.scan.patchstack-vulnerability', [props.server.id, props.site.id]))
                .then(({data}) => {
                    //patchstackVulnerability.value = data?.patchstack_vulnerability;
                    last_update_check.value = data?.last_update_check;
                    useFlash().success('Site vulnerability scan successful');
                    Inertia.reload();
                })
                .catch(() => {
                    useFlash().error('Site vulnerability scan failed');
                }).finally(() => {
                    refreshing.value = false
                    Inertia.reload();
                });
        } else {
            useFlash().info('Vulnerability scan already running');
        }
    }
}
</script>
<style>
.swal2-checkbox{
    @apply dark:bg-dark;
}
</style>
