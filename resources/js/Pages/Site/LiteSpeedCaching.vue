<template>
    <single-site :server="server" :site="site" active="Caching">
        <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <div
                :class="{
                    'pb-0': has_fullpage_caching || hasFullPageCache,
                    'pb-10px': !(has_fullpage_caching || hasFullPageCache)
                }"
                class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col ">
                <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col items-center
              wide-mobile:px-15px gap-20px mobile:gap-1">
                    <tooltip :title="form.processing
                          ? 'Action will be available once the Cache operation is finished'
                          : ''
                  ">
                        <Switch :disabled="form.processing || form.processing" @click.prevent="toggleFullPageCache" :checked="form.has_fullpage_caching || hasFullPageCache">
                            <span class="mr-2"> {{ $t('LiteSpeed Cache') }} </span>
                            <span class="inline-flex items-center">
                  <i class="xcloud xc-verify_dns animate-spin items-center origin-center inline-flex pt-[0.011em]" v-show="form.processing"/>
              </span>
                        </Switch>
                    </tooltip>
                </div>
                <div class="w-full px-30px pt-30px h-full bg-white dark:bg-mode-base grid grid-cols-3 tablet:grid-cols-1 gap-30px wide-mobile:grid-gap-20px">
                    <text-input
                        type="text"
                        :label="$t('Redis User')"
                        :modelValue="site?.site_user"
                        disabled
                    >
                        <CopyButton align="middle" :content="site?.site_user"></CopyButton>
                    </text-input>
                    <text-input
                        class="w-full"
                        type="text"
                        :label="$t('Redis Password')"
                        :modelValue="'*'.repeat(redis_password.length)"
                        disabled
                    >
                        <CopyButton align="middle" :content="redis_password"></CopyButton>
                    </text-input>
                    <text-input
                        type="text"
                        :label="$t('Redis Object Cache Key')"
                        :modelValue="redis_object_cache_key"
                        disabled
                    >
                        <CopyButton align="middle" :content="redis_object_cache_key"></CopyButton>
                    </text-input>
                </div>
                <div v-show="has_fullpage_caching || hasFullPageCache" class="p-30px h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px tablet:gap-10px mb-2">
                    <div class="flex flex-col items-start gap-4">
                        <div class="flex items-center">
                            <img :src="asset('img/litespeed-cache-wp-logo.svg')" alt="" class="h-12 w-12">
                            <h4 class="text-dark dark:text-white text-lg font-medium leading-tight tracking-tighter  ml-3">
                                {{ $t('LiteSpeed Cache') }} </h4>
                        </div>
                        <form class="w-full">
                            <p class="text-secondary-full dark:text-secondary-light">
                                {{ $t('LiteSpeed Cache for WordPress (LSCWP) is an all-in-one site acceleration plugin, featuring an exclusive server-level cache and a collection of optimization features.') }} </p>
                            <div class="p-4 border-1 flex items-center wide-mobile:flex-col wide-mobile:items-start border-solid border-secondary-light
                    dark:border-dark rounded-md mt-6 gap-2">
                                <div class="flex flex-col gap-1.5">
                                    <h5 class="text-lg font-medium leading-tight tracking-tight text-dark dark:text-white">
                                        {{ $t('Clear LiteSpeed Cache') }} </h5>
                                    <p class="text-secondary-full dark:text-secondary-light">
                                        {{ $t('This will slow down your site until the caches are rebuilt.') }} </p>
                                </div>
                                <button class="ml-auto px-4 py-3.5 rounded-10px bg-primary-light/10 inline-flex items-center justify-center text-primary-light shrink-0" @click.prevent="purgeCache" :class="{'cursor-not-allowed opacity-50' : site?.is_disabled || form.processing}">
                  <span class="inline-flex items-center justify-center ml-1 mr-2.5 text-xl">
                      <i class="xcloud xc-data-cleaning"></i>
                  </span>
                                    <span>{{ flushCacheForm.processing ? $t('Purging Cache...') : $t('Purge Cache') }}</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <object-cache-pro
            :site="site"
            :server="server"
            :object_cache_pro="object_cache_pro"
        />
        <cloudflare-edge-cache
            :site="site"
            :server="server"
            :cloudflare_edge_cache="cloudflare_edge_cache"
        />
    </single-site>
</template>

<!-- script -->
<script setup>
import SingleSite from "@/Pages/Site/SingleSite.vue";
import TextInput from "@/Shared/TextInput.vue";
import Switch from "@/Shared/Switch.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import TextareaInput from "@/Shared/TextareaInput.vue";
import {useForm} from "@inertiajs/inertia-vue3";
import {ref} from "vue";
import {useFlash} from "@/Composables/useFlash.js";
import Btn from "@/Shared/Btn.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import Advanced from "@/Shared/Advanced.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import CopyButton from "@/Shared/CopyButton.vue";
import { useI18n } from "vue-i18n";
import ObjectCachePro from "@/Pages/Site/Components/ObjectCachePro.vue";
import CloudflareEdgeCache from "@/Pages/Site/Components/CloudflareEdgeCache.vue";
const { t } = useI18n();

const props = defineProps({
    site: Object,
    server: Object,
    has_fullpage_caching: Boolean,
    has_redis_object_caching: Boolean,
    wp_cache_plugin: Boolean,
    cache_plugin_name: String,
    cache_plugin_slug: String,
    cache_plugins: Object,
    cache_duration: String,
    cache_duration_unit: String,
    cache_exclusion_http_rules: String,
    cache_exclusion_cookie_rules: String,
    has_page_caching: Boolean,
    redis_password: String,
    redis_object_cache_key: String,
    object_cache_pro:Object,
    cloudflare_edge_cache: Object
});

let form = useForm({
    has_fullpage_caching: props.has_fullpage_caching,
    cache_duration: props.cache_duration,
    cache_duration_unit: props.cache_duration_unit,
    cache_exclusion_http_rules: props.cache_exclusion_http_rules,
    cache_exclusion_cookie_rules: props.cache_exclusion_cookie_rules,
    has_redis_object_caching: props.has_redis_object_caching,
});

let hasFullPageCache = ref(props.has_fullpage_caching)

let flushCacheForm = useForm();

let toggleFullPageCache = () => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        if (form.has_fullpage_caching) {
            useFlash().deleteConfirmation({
                btn_text: t('Yes, disable it!'),
                text: t('Are you sure you want to disable LiteSpeed Cache?'),
            }, () => {
                form.has_fullpage_caching = false;
                hasFullPageCache.value = false;
                form.post(route('api.fullPageCache.disable', props.site.id), {preserveScroll: true});
            })
        } else {
            hasFullPageCache.value = true;
            form.post(route("api.fullPageCache.enable", props.site.id), {
                preserveScroll: true,
            });
        }
    }
}
const purgeCache = () => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        flushCacheForm.post(
            route('api.site.purge-cache', {
                server: props.server.id,
                site: props.site.id
            }), {preserveScroll: true})
    }
}
</script>
