<template>
    <single-site :server="server" :site="site" active="Monitoring">
        <div class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <div
                class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col wide-tablet:col-span-1">
                <div class="flex items-center gap-20px px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px  wide-mobile:px-15px">
                    <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                        {{ $t('Monitoring Stats') }}
                    </h4>
                    <button type="button"
                            @click.prevent="updateSiteMonitoring"
                            :disabled="siteStore.isSiteMonitoringLoading"
                            :class="{
                                 'cursor-not-allowed opacity-70':site.is_disabled
                            }"
                            class="inline-flex items-center rounded-3xl
                                  shadow-none px-1 py-1 pr-10px bg-success-light/10 text-sm text-success-light focus:outline-0 gap-1"
                            aria-expanded="true" aria-haspopup="true">
                        <span class="rounded-full bg-success-light h-25px w-25px shrink-0 flex items-center justify-center">
                            <i class="xcloud xc-verify_dns text-white"
                               :class="{'animate-spin':siteStore.isSiteMonitoringLoading}">
                            </i>
                        </span>
                        <span>{{ $t('Refresh') }}</span>
                    </button>
                    <div class="ml-auto mobile:ml-0">
                        <span class="text-base text-dark dark:text-mode-secondary-light">
                            {{ $t('Last checked') }}: {{ monitoringData.lastChecked }}
                        </span>

                    </div>
                </div>
                <div class="overflow-x-auto w-full">
                    <div class="grid grid-cols-3 wide-tablet:grid-cols-1 gap-30px wide-mobile:gap-20px p-30px wide-mobile:p-20px bg-light
                            dark:bg-mode-base rounded-md text-dark dark:text-white">
                        <div class="flex items-center justify-between gap-15px px-30px wide-mobile:px-20px py-20px wide-mobile:py-15px
                            bg-white dark:bg-mode-focus-dark rounded-md">
                            <div>
                                <h5 class="text-dark dark:text-white text-lg font-medium">{{ $t('CPU Usages') }}</h5>
                                <h6 class="text-secondary-full dark:text-mode-secondary-light text-lg flex items-center pt-15px
                                        before:flex before:h-10px before:w-10px before:shrink-0 before:bg-primary-light before:rounded-full
                                        before:mr-10px">
                                    {{ $t('In Use') }}:
                                    {{ cpuUsagePercentage }}%
                                </h6>
                            </div>
                            <div>
                                <HalfProgressBar :percentage="cpuUsagePercentage"/>
                            </div>
                        </div>
                        <div class="flex items-center justify-between gap-15px px-30px wide-mobile:px-20px py-20px wide-mobile:py-15px
                                bg-white dark:bg-mode-focus-dark rounded-md">
                            <div>
                                <h5 class="text-dark dark:text-white text-lg font-medium">{{ $t('RAM Usages') }}</h5>
                                <h6 class="text-secondary-full dark:text-mode-secondary-light text-lg flex items-center pt-15px before:flex
                                        before:h-10px before:w-10px before:shrink-0 before:bg-primary-light before:rounded-full before:mr-10px">
                                    {{ $t('In Use') }}:
                                    {{ memoryInUse }} GB
                                </h6>
                            </div>
                            <div>
                                <HalfProgressBar :percentage="memoryUsagePercentage"/>
                            </div>
                        </div>
                        <div class="flex items-center justify-between gap-15px px-30px wide-mobile:px-20px py-20px wide-mobile:py-15px
                                    bg-white dark:bg-mode-focus-dark rounded-md">
                            <div>
                                <h5 class="text-dark dark:text-white text-lg font-medium">{{ $t('Disk Usages') }}</h5>
                                <h6 class="text-secondary-full dark:text-mode-secondary-light text-lg flex items-center pt-15px before:flex
                                        before:h-10px before:w-10px before:shrink-0 before:bg-primary-light before:rounded-full before:mr-10px">
                                    {{ $t('In Use') }}:
                                    {{ diskUsage }} GB
                                </h6>
                            </div>
                            <div>
                                <HalfProgressBar :percentage="diskUsagePercentage"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-2 wide-tablet:grid-cols-2 tablet:grid-cols-1 gap-30px mobile:gap-20px">
                <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col wide-tablet:col-span-1">
                    <div
                        class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex items-center wide-mobile:px-15px">
                        <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                            {{ $t('SSL Overview') }}
                        </h4>
                    </div>
                    <div class="overflow-x-auto w-full overflow-y-hidden">
                        <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                            <tbody class="bg-white dark:bg-mode-base divide-y-1 divide-light dark:divide-mode-light text-dark dark:text-white">
                            <tr class="divide-light dark:divide-mode-light">
                                <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                    {{ $t('DNS Status') }}:
                                </td>
                                <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                    <button class="text-success-full dark:text-white px-4 py-1 bg-success-full/10
                                           capitalize dark:bg-mode-focus-dark rounded-25px">
                                        {{ monitoringData.sslOverview.dns_status }}
                                    </button>
                                </td>
                            </tr>
                            <tr class="divide-light dark:divide-mode-light">
                                <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                    {{ $t('SSL Status') }}:
                                </td>
                                <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                    <button class="text-success-full dark:text-white px-4 py-1 bg-success-full/10
                                           capitalize dark:bg-mode-focus-dark rounded-25px">
                                        {{ monitoringData.sslOverview.ssl_status }}
                                    </button>
                                </td>
                            </tr>
                            <tr class="divide-light dark:divide-mode-light">
                                <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                    {{ $t('Expiry') }}:
                                </td>
                                <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                    <h5 href="#" class="text-dark dark:text-white">
                                        {{ monitoringData.sslOverview.expiry }}
                                    </h5>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div
                    v-if="site?.is_wordpress"
                    class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col wide-tablet:col-span-1">
                    <div
                        class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex items-center wide-mobile:px-15px">
                        <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                            {{ $t('WordPress Logs') }}
                        </h4>
                    </div>
                    <div class="overflow-x-auto w-full overflow-y-hidden">
                        <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                            <tbody
                                class="bg-white dark:bg-mode-base divide-y-1 divide-light dark:divide-mode-light text-dark dark:text-white">
                            <tr class="divide-light dark:divide-mode-light">
                                <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                    {{ $t('Updates') }}:
                                </td>
                                <td class="flex gap-15px items-center px-30px py-2 text-left text-base font-normal h-60px">
                                    <div>
                                        <a v-if="monitoringData.wordpressLogs.wp_update_available"
                                             class="flex"
                                             :href="route('site.updates', [server.id, site.id])"
                                        >
                                            <img :src="asset('img/updates.svg')"
                                                 class="max-w-full w-15px mr-10px" alt="IconImg" />
                                            <h5 class="text-base text-success-full">{{ $t('Update Available') }}</h5>
                                        </a>
                                        <div v-else class="flex items-center text-dark dark:text-white">
                                            <i class="xcloud xc-tick-o text-lg tablet:text-base mobile:text-sm text-success-full"></i>
                                            <h4 class="text-base ml-10px">{{ $t('Up To Date') }}</h4>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="divide-light dark:divide-mode-light">
                                <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                    {{ $t('Plugins') }}:
                                </td>
                                <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                    <div v-if="monitoringData.wordpressLogs.plugin_update">
                                        <a :href="route('site.updates', [server.id, site.id])"
                                            class="flex items-center text-dark dark:text-white">
                                            <img :src="asset('img/updates.svg')"
                                                 class="max-w-full w-15px mr-10px" alt="IconImg" />
                                            <h5 class="text-base text-success-full"
                                                v-text="monitoringData.wordpressLogs.plugin_update > 1
                                                        ? monitoringData.wordpressLogs.plugin_update + ' '+$t('Updates Available')
                                                        : monitoringData.wordpressLogs.plugin_update + ' '+$t('Update Available')
                                                    ">
                                            </h5>
                                        </a>
                                    </div>
                                    <div v-else class="flex items-center text-dark dark:text-white">
                                        <i class="xcloud xc-tick-o text-lg tablet:text-base mobile:text-sm text-success-full"></i>
                                        <h4 class="text-base ml-10px">{{ $t('Up To Date') }}</h4>
                                    </div>
                                </td>
                            </tr>
                            <tr class="divide-light dark:divide-mode-light">
                                <td class="px-30px py-2 text-left text-base font-normal h-60px">
                                    {{ $t('Themes') }}:
                                </td>
                                <td class="flex gap-15px items-center px-30px py-2 text-left text-base font-normal h-60px">
                                    <div v-if="monitoringData.wordpressLogs.theme_update">
                                        <a :href="route('site.updates', [server.id, site.id])"
                                           class="flex items-center text-dark dark:text-white">
                                            <img :src="asset('img/updates.svg')"
                                                 class="max-w-full w-15px mr-10px" alt="IconImg" />
                                            <h5 class="text-base text-success-full"
                                                v-text="monitoringData.wordpressLogs.theme_update > 1
                                                        ? monitoringData.wordpressLogs.theme_update + ' Updates Available'
                                                        : monitoringData.wordpressLogs.theme_update + ' Update Available'
                                                    ">
                                            </h5>
                                        </a>
                                    </div>
                                    <div v-else class="flex items-center text-dark dark:text-white">
                                        <i class="xcloud xc-tick-o text-lg tablet:text-base mobile:text-sm text-success-full"></i>
                                        <h4 class="text-base ml-10px">{{ $t('Up To Date') }}</h4>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </single-site>
</template>
<script setup>
import HalfProgressBar from "@/Shared/Icons/HalfProgressBar.vue";
import {computed} from "vue";
import SingleSite from "@/Pages/Site/SingleSite.vue";
import {useSiteStore} from "@/stores/SiteStore";
import {useSite} from "@/Composables/useSite";

const props = defineProps({
    server: Object,
    site: Object,
    monitoringData: [Array, Object]
})

// cpu usage
const cpuUsagePercentage = computed(() => {
    if (props.monitoringData.monitoringStats) {
        return parseFloat(props.monitoringData.monitoringStats.cpu_usage?.in_use);
    }
    return 0;
})

const memoryInUse = computed(() => {
    if (props.monitoringData.monitoringStats) {
        return mbToGb(props.monitoringData.monitoringStats.memory_usage.in_use);
    }
    return 0;
})

// memory usage
const memoryUsagePercentage = computed(() => {
    if (props.monitoringData.monitoringStats) {
        return Math.round(props.monitoringData.monitoringStats.memory_usage.used_percentage);
    }
    return 0;
})

// disk usage
const diskUsage = computed(() => {
    if (props.monitoringData.monitoringStats) {
        return mbToGb(props.monitoringData.monitoringStats.disk_usage.in_use);
    }
    return 0;
})

// disk usage percentage
const diskUsagePercentage = computed(() => {
    if (props.monitoringData.monitoringStats) {
        return Math.round(props.monitoringData.monitoringStats.disk_usage.used_percentage);
    }
    return 0;
})

function mbToGb(value) {
    return (value / 1024).toFixed(2);
}

const siteStore = useSiteStore();

const {updateSiteMonitoring} = useSite(props.server, props.site, props.monitoringData);

</script>
