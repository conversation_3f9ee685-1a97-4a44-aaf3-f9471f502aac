<template>
    <single-site :server="server" :site="site" active="SSL/HTTPS">
        <div class="ml-30px mt-30px dark:bg-mode-light  mobile:mt-20px mobile:ml-20px mobile:gap-20px"
            v-if="isSslAutoManaged">
          <div class="rounded-md flex items-center bg-white dark:bg-mode-light px-6 py-4 border-1 border-primary-light
                dark:border-mode-base w-ful mobile:mb-5px">
            <img :src="asset('img/note.svg')" alt="Suggestion message" />
            <p class="text-sm text-dark dark:text-white leading-7 pl-4" v-text="sslAutoManagedMessage"></p>
          </div>
        </div>
        <div v-else
             class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <div class="bg-focused dark:bg-mode-light rounded-10px flex flex-col p-2 pt-0 pb-0">
                <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col items-center
                      wide-mobile:px-15px gap-20px mobile:gap-1">
                    <Switch @click.prevent="toggleHttps" :checked="form.httpsEnabled">
                        {{ form.httpsEnabled ? $t('HTTPS Is Enabled') : $t('Do You Want To Enable HTTPS?') }}
                    </Switch>
                </div>

                <div v-if="form.httpsEnabled"
                     class="p-30px h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px
                            tablet:gap-10px mb-2"
                >
                    <skeleton :rows="3" :columns="4" v-if="form.processing || sslCertificate?.status === 'new'" />
                    <fieldset v-else>
                        <div>
                          <!-- xcloud managed -->
                          <div>
                            <RadioInput v-model="form.ssl_provider" value="xcloud">
                              {{ $t('Use free SSL certificate issued & managed by') }} {{ $page?.props?.current_white_label ? $page?.props?.current_white_label?.branding?.brand_name : 'xCloud' }}.
                            </RadioInput>

                            <suggestion
                                class="mt-5"
                                v-if="props.multisite_subdomain_enabled && form.ssl_provider === 'xcloud' && domainChallenge?.status !== 'completed' && !sslCertificate"
                                :light-mode="true"
                                :message="$page?.props?.current_white_label ? $page?.props?.current_white_label?.branding?.brand_name : 'xCloud' +' will issue a free wildcard SSL certificate for your site. This process requires you to add a DNS TXT record to your domain. This may require couple of tries to propagate the DNS record.'"
                            />

                            <!-- DNS setup for dns txt record for Multisite -->
                            <div class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base
                                  dark:bg-mode-base rounded-md"
                                 v-show="showRecord"
                            >
                              <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                                {{ $t('DNS Setup For Multisite SSL') }}
                              </h3>

                              <p class="text-secondary-full block text-base font-normal">
                                {{ $t('Add the following record to the DNS for your domain. This required when when you want to get a free SSL certificate issued & managed by') }}
                                {{ $page?.props?.current_white_label ? $page?.props?.current_white_label?.branding?.brand_name : 'xCloud' }}.
                              </p>

                              <skeleton v-if="showLoaderWhileProcessingDomainChallenge" class="mt-30px" columns="1" />
                              <!-- SSL DNS Txt Record -->
                              <div class="flex mt-20px mobile:mt-15px gap-0.5 flex-wrap" v-else>
                                <div class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light">
                                  <h5 class="text-sm text-secondary-full">{{ $t('Record') }}</h5>
                                  <p class="text-sm mt-1 font-medium text-dark dark:text-white">
                                    {{ $t('TXT') }}
                                  </p>
                                </div>
                                <div
                                    class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light"
                                >
                                  <h5 class="text-sm text-secondary-full">{{ $t('Name') }}</h5>
                                  <p class="text-sm mt-1 font-medium text-dark dark:text-white">
                                    {{ domainChallenge?.domain }}
                                  </p>

                                  <CopyButton
                                      :content="domainChallenge?.domain"
                                      class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light"
                                  />
                                </div>
                                <div
                                    class="grow relative rounded-md px-7 py-5 mobile:px-5 mobile:p-3 bg-light dark:bg-mode-light"
                                >
                                  <h5 class="text-sm text-secondary-full">{{ $t('Value') }}</h5>
                                  <p class="text-sm mt-1 font-medium text-dark dark:text-white">
                                    {{ domainChallenge?.record }}
                                  </p>

                                  <CopyButton
                                      :content="domainChallenge?.record"
                                      class="right-20px top-15px mobile:top-2.5 mobile:right-15px !text-primary-light"
                                  />
                                </div>
                              </div>
                            </div>

                            <div v-if="form.errors.ssl_provider" class="text-red-500 text-sm mt-2">
                              {{ form.errors.ssl_provider }}
                            </div>

                            <SslCertificateStatus
                                :ssl_certificate="sslCertificate"
                                v-if="showSSLCertificateStatus || domainChallenge?.status === 'completed'"
                            />
                          </div>

                          <!-- custom managed -->
                          <div class="mt-20px">
                            <RadioInput v-model="form.ssl_provider" value="custom">
                              {{ $t('Provide your own certificate & manage it yourself') }}
                            </RadioInput>

                            <div class="flex flex-col w-full mt-20px" v-if="form.ssl_provider === 'custom'">
                              <div class="grid grid-cols-1 tablet:grid-cols-1 gap-30px">
                                <TextareaInputForPaste
                                    v-model="form.ssl_certificate"
                                    :error="form.errors.ssl_certificate"
                                    id="ssl_certificate" label="Certificate"
                                    :placeholder="$t('Paste Your Certificate Here')"/>

                                <TextareaInputForPaste
                                    v-model="form.ssl_private_key"
                                    :error="form.errors.ssl_private_key"
                                    id="ssl_private_key" label="Private Key"
                                    :placeholder="$t('Paste Your Private Key Here')"/>
                              </div>
                            </div>
                          </div>

                          <!-- Cloudflare managed -->
                          <div class="mt-20px">
                            <RadioInput v-model="form.ssl_provider" value="cloudflare">
                              {{ $t('Use Cloudflare managed SSL Certificate.') }}
                            </RadioInput>

                            <div v-if="form.errors.ssl_provider" class="text-red-500 text-sm mt-2">
                              {{ form.errors.ssl_provider }}
                            </div>

                            <SslCertificateStatus
                                :ssl_certificate="sslCertificate"
                                v-if="form.ssl_provider === 'cloudflare' && sslCertificate && sslCertificate?.provider === 'cloudflare'"
                            />

                              <suggestion
                                  v-if="form.ssl_provider === 'cloudflare'"
                                  class="mt-5"
                                  :light-mode="true"
                                  :message="cloudflareHelpText"
                              />
                          </div>
                        </div>

                        <div>
                          <button
                              type="submit"
                              @click.prevent="updateSsl"
                              class="inline-flex items-center justify-center min-h-40px py-2 px-20px mt-30px mobile:mt-15px
                                    rounded-md shadow-none text-base text-center text-white font-normal bg-success-full
                                    focus:outline-none hover:bg-success-full hover:text-white ease-in-out transition
                                    duration-200 hover:shadow-lg hover:shadow-primary-dark/30"
                              :disabled="disableButton || site.is_disabled"
                              :class="{'cursor-not-allowed opacity-50': disableButton}"
                          >
                            <span class="drop-shadow-button" v-text="buttonTitle"></span>
                          </button>
                        </div>
                    </fieldset>
                </div>
            </div>
        </div>

    </single-site>
</template>

<!-- script -->
<script setup>

import SingleSite from "@/Pages/Site/SingleSite.vue";
import {computed, onMounted, onUnmounted, ref, watch} from "vue";
import SslCertificateStatus from "@/Pages/Site/New/Components/SslCertificateStatus.vue";
import TextareaInputForPaste from '@/Shared/TextareaInputForPaste.vue'
import {useForm, usePage} from "@inertiajs/inertia-vue3";
import RadioInput from '@/Shared/RadioInput.vue'
import Switch from "@/Shared/Switch.vue";
import {useFlash} from "@/Composables/useFlash";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import CopyButton from "@/Shared/CopyButton.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
    site: Object,
    server: Object,
    ssl_provider: String,
    ssl_certificate: Object,
    multisite_subdomain_enabled: Boolean
})

const sslCertificate = ref(props.ssl_certificate);
let loader = ref(false);
let challengeFailed = ref(false);
let fetchingTxtRecord = ref(false);
let domainChallenge = ref(null);
let challengeResponse = ref('');
const cloudflareHelpText = 'This option will only work if your Cloudflare account is integrated to ' + (usePage().props?.current_white_label ? usePage().props?.current_white_label?.branding?.brand_name : 'xCloud');

onMounted(() => {
    if (window.Echo) {
        console.log("Listening for site ssl status changed:",sslCertificate?.value?.site_id);
        window.Echo.private("site.ssl." + sslCertificate?.value?.site_id).listen(
            "SiteSSLStatusChanged",
            (e) => {
                sslCertificate.value = {
                    ...sslCertificate.value,
                    ...e,
                };

                console.log('Site SSL Status Changed:', e, sslCertificate.value);
            }
        );
    }
});
onUnmounted(() => {
    if (window.Echo) {
        window.Echo.private("site.ssl." +sslCertificate?.value?.site_id).stopListening(
            "SiteSSLStatusUpdated"
        );
        console.log('Stopped listening for site ssl status changed: ' + domainChallenge?.value?.id);
        window.Echo.private("domain_challenge.updating." + domainChallenge?.value?.id).stopListening(
            "DomainChallengeStatusChanged"
        );
    }
});

const cloudflareHelpText = 'This option will only work if your Cloudflare account is integrated to ' + (usePage().props?.current_white_label ? usePage().props?.current_white_label?.branding?.brand_name : 'xCloud');

let listenForDomainChallengeStatus = () => {
    if (window.Echo) {
        console.log("Listening for domain change status changed:",domainChallenge?.value?.id);
        window.Echo.private("domain_challenge.updating." + domainChallenge?.value?.id).listen(
            "DomainChallengeStatusChanged",
            (e) => {
                domainChallenge.value = e.domainChallenge;
                if(domainChallenge?.value?.status === 'dns_propagated') {
                  useFlash().success('DNS Propagated Successfully. Please wait for the SSL certificate to be issued.');
                }
                if(domainChallenge?.value?.status === 'completed'){
                    showSSLCertificateStatus.value = true;
                    showRecord.value = false;
                    useFlash().success('Domain Challenge Completed Successfully. Your SSL certificate has been issued.');
                }else if(domainChallenge?.value?.status === 'failed'){
                    showRecord.value = false;
                    form.errors.ssl_provider = 'Domain Challenge Failed. Please try again.';
                    useFlash().error('Domain Challenge Failed. Please try again.');
                }
            }
        );
    }
}

let form = useForm({
    ssl_certificate: sslCertificate.value?.ssl_certificate ?? '',
    ssl_private_key: sslCertificate.value?.ssl_private_key ?? '',
    ssl_provider: props.ssl_provider,
    httpsEnabled: props.ssl_provider !== '' && props.ssl_provider !== null,
    domain_active_on_cloudflare: false,
    cloudflare_account_id: '',
    cloudflare_zone_id: '',
    subdomain: '',
    site_name: ''
});

const showLoaderWhileProcessingDomainChallenge = computed(() => {
    return fetchingTxtRecord.value || domainChallenge?.value?.status === 'challenged';
});

const showRecord = computed(() => {
    if(props.multisite_subdomain_enabled && domainChallenge.value){
      return form.ssl_provider === 'xcloud' && (fetchingTxtRecord.value || domainChallenge?.value?.status !== 'new' || domainChallenge?.value?.status !== 'completed' || domainChallenge?.value?.status === 'failed');
    }
    return false;
});

const showSSLCertificateStatus = computed(() => {
    return form.ssl_provider === 'xcloud' && sslCertificate.value && sslCertificate?.value?.provider === 'xcloud';
});

const buttonTitle = computed(() => {
    if(props.multisite_subdomain_enabled && form.ssl_provider === 'xcloud'){
      if(domainChallenge?.value?.status === 'processing' || fetchingTxtRecord.value){
        return 'Initiating Challenge...';
      }else if(domainChallenge?.value?.status === 'record_adding') {
        return 'I Have Added the Record';
      }else if(domainChallenge?.value?.status === 'checking_dns') {
        return 'Checking DNS Record...';
      }else if(domainChallenge?.value?.status === 'verifying' || domainChallenge?.value?.status === 'challenged') {
        return 'Verifying Record & Obtaining SSL...';
      }else{
        return 'Begin Challenge';
      }
    }else{
        return loader.value ? 'Checking Cloudflare...' : 'Save';
    }
});

watch(
    () => form.ssl_provider,
    (newVal, oldVal) => {
      // if(form.ssl_provider === 'cloudflare'){
      //   checkDomain();
      // }
    },
    { deep: true } // Use the deep option to watch for changes in array contents
);

const disableButton = computed(() => {
    if(props.multisite_subdomain_enabled && form.ssl_provider === 'xcloud'){
      if(domainChallenge.value){
        return fetchingTxtRecord.value || (domainChallenge?.value?.status !== 'failed' && domainChallenge?.value?.status !== 'record_adding' && domainChallenge?.value?.status !== 'completed');
      }else if(fetchingTxtRecord.value){
        return true;
      }
    }else{
      return form.processing || loader.value;
    }
    return false;
});

let handleACMEDomainChallenge = async () => {
  if(props.multisite_subdomain_enabled){
    form.errors.ssl_provider = '';
    if(domainChallenge?.value?.status === 'record_adding'){
      await axios.get(route('api.site.domain-challenged', {server: props.server.id, domainChallenge: domainChallenge?.value?.id}))
          .then(response => {
          }).catch(error => {
            console.error(error);
          }).then(() => {
          });
    }else{
      fetchingTxtRecord.value = true;

      await axios.get(route('api.site.domain-challenge.initiate-challenge', {server: props.server.id, siteId: props.site.id})).then(response => {
        domainChallenge.value = response.data.domainChallenge;
        listenForDomainChallengeStatus();
        challengeFailed.value = response.data.challengeFailed;
        fetchingTxtRecord.value = false;
        challengeResponse.value = response.data.message;
        if(challengeFailed.value){
          useFlash().error(challengeResponse.value);
        }
      }).catch(error => {
        console.error(error);
        challengeFailed.value = true;
        fetchingTxtRecord.value = false;
      }).then(() => {
        fetchingTxtRecord.value = false;
      });
    }
  }
}


const isSslAutoManaged = computed(() => {
    return props.ssl_provider === 'staging';
});

let sslAutoManagedMessage = computed(() => {
    if (props.ssl_provider === 'staging') {
        let brandName = usePage().props.value?.current_white_label ? usePage().props.value?.current_white_label?.branding?.brand_name : 'xCloud';
        return t('Your staging SSL is being managed by')+' '+brandName+'.';
    } else if (props.ssl_provider === 'cloudflare') {
        return t('Your SSL is being managed by Cloudflare.');
    }
});

let checkDomain = async() => {
  try {
    loader.value = true;

    // Await the axios call and store the response
    const res = await axios.get(route("api.user.integration.cloudflare.check-domain-exists", props.site.name));

    // Update form values based on response
    form.domain_active_on_cloudflare = res.data.response.domain_active_on_cloudflare;
    form.cloudflare_account_id = res.data.response.account_id;
    form.cloudflare_zone_id = res.data.response.zone_id;
    form.subdomain = res.data.response.subdomain;
    form.site_name = res.data.response.site_name;
    form.ssl_provider = 'cloudflare';

    loader.value = false;

    // Show success or error based on the domain active status
    if (!res.data.response.domain_active_on_cloudflare) {
      useFlash().error('No cloudflare account found for this domain. Please add your domain to cloudflare first.');
    }
  } catch (err) {
    loader.value = false;

    // Handle errors
    if (err.response && err.response.data && err.response.data.message) {
      useFlash().error(err.response.data.message);
    } else {
      useFlash().error('An error occurred while processing your request.');
    }
  }
}


const stagingHelper = 'xCloud provides a temporary staging domain to make your site live. This helps quickly share progress as well as gather feedback from your teammates or clients.\n' +
    '\n' +
    'Later, you can add your own domain to make it live to your users/visitors.';

const updateSsl = async() => {
  if(props.site?.is_disabled){
    useFlash().info('This site is disabled. To enable, visit the site settings page.');
    return;
  }
  if(props.multisite_subdomain_enabled && form.ssl_provider === 'xcloud'){
    await handleACMEDomainChallenge();
  }else{
    if(form.ssl_provider === 'cloudflare'){
      await checkDomain();

      if(!form.domain_active_on_cloudflare){
        useFlash().error('No cloudflare account found for this domain. Please add your domain to cloudflare first.');
        return;
      }
    }

    await form.post(route('api.site.ssl.update', {server: props.server.id, site: props.site.id}));
  }
}

const toggleHttps = () => {
    if(props.site?.is_disabled){
        useFlash().info('This site is disabled. To enable, visit the site settings page.');
    }else {
        if (form.httpsEnabled === true && props.ssl_provider) {
            useFlash().deleteConfirmation({
                btn_text: t('Yes, disable it!'),
                text: t('Are you sure you want to disable HTTPS?'),
            }, () => {
                form.httpsEnabled = false;
                form.delete(route('api.site.ssl.destroy', [props.server.id, props.site.id]));
            })
        } else {
            form.httpsEnabled = !form.httpsEnabled;
        }
    }
}

</script>
