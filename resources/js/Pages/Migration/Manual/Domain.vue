<template>
    <migration-layout
        :steps="steps"
        :form="form"
        :site_migration="site_migration"
        :previous_step="previous_step"
        :current_step="current_step"
        :next_step="next_step"
        :suggestion_message="suggestion_message"
        :previous_route="previous_route"
        :post_route="route(post_route,{server: server.id})"
    >
        <template v-slot:header>
            {{ $t('DNS & SSL For') }} ({{ site_migration?.domain_name ? site_migration?.domain_name : server.name }})
        </template>

        <form>
            <div class="mt-5 grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px wide-mobile:gap-4 mb-30px">
            <div
                :class="{
                      'border-secondary-light dark:border-mode-focus-light': form.domain_parking_method !== 'staging_env',
                      'border-primary-light ring-1 ring-primary-light': form.domain_parking_method === 'staging_env',
                  }"
                class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                @click="form.domain_parking_method = 'staging_env'"
            >
              <div class="shrink-0 inline-flex items-center justify-center">
                <img
                    :src="asset('img/png/staging2.png')"
                    alt="xcloud_logo"
                    class="w-12 h-auto"
                />
              </div>
              <div class="flex flex-col gap-2">
                <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                  {{ $t('Demo Site') }}
                </h5>
                <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                  {{ $t('Create a demo site with our test domain and customize before going live.') }}
                </p>
              </div>
            </div>

            <div
                :class="{
                      'border-primary-light ring-1 ring-primary-light':
                          form.domain_parking_method === 'migrate_into_new_domain',
                      'border-secondary-light dark:border-mode-focus-light':
                          form.domain_parking_method !== 'migrate_into_new_domain',
                  }"
                class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                @click="form.domain_parking_method = 'migrate_into_new_domain'"
            >
              <div class="shrink-0 inline-flex items-center justify-center">
                <img
                    :src="asset('img/png/live.png')"
                    alt="xcloud_logo"
                    class="w-12 h-auto"
                />
              </div>
              <div class="flex flex-col gap-2">
                <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                  {{ $t('Migrate into a New Domain') }}
                </h5>
                <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                  {{ $t('Get your site up and running for the world to see by simply pointing your domain to the server.') }}
                </p>
              </div>
            </div>
          </div>

            <suggestion
                v-if="form.domain_parking_method === 'staging_env'"
                :message="stagingHelper"
            />

            <div v-if="form.domain_parking_method === 'migrate_into_new_domain'">
              <div class="grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px mt-5">
                <text-input
                    v-model="form.site_title"
                    :error="form.errors.site_title"
                    id="site_title"
                    :placeholder="$t('Site Title')"
                    :label="$t('New Site Title')"/>

                <text-input
                    v-model="form.domain_name"
                    :error="form.errors.domain_name"
                    id="domain_name"
                    placeholder="Domain"
                    label="Domain Name"
                    :note="!useCloudflareDns && !$page?.props?.current_white_label ? domainNameHelpText : ''"/>
              </div>

              <div class="mt-3" v-if="showCloudflareDnsOption">
                <label class="inline-flex mr-5">
                  <input type="checkbox" class="hidden peer" :disabled="!hasCloudflareIntegration" v-model="useCloudflareDns"
                         :class="{'opacity-50 cursor-not-allowed' : !hasCloudflareIntegration}"
                  />
                  <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4
                                before:bg-transparent before:border-1 before:border-secondary-light dark:border-mode-secondary-dark before:rounded
                                before:mt-0.5 before:mr-2.5 before:text-xxxs before:inline-flex before:items-center before:justify-center
                                before:text-transparent before:outline-none before:transition before:duration-200
                                peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white"
                                >
                      <span class="font-normal text-secondary-full dark:text-mode-secondary-dark text-sm">
                          <span class="flex" :class="{'opacity-50 cursor-not-allowed' : !hasCloudflareIntegration}">
                            <span>{{ $t('Add DNS and SSL Certificate on Cloudflare') }}</span>
                            <img :src="asset('img/CF_logomark.png')" alt="cloudflare logo" class="ml-1 w-8 h-4">
                            <span>&nbsp;({{ $t('Optional') }})</span>
                          </span>
                          <span class="text-xs text-gray-500 dark:text-gray-400 block mt-1">
                              {{ $t('Integrate Cloudflare for Automatic DNS and SSL management.') }}
                              (<Link
                              class="underline"
                              :href="route('user.integration.cloudflare')"
                              v-text="'Manage your Cloudflare Integration'"
                          />)
                          </span>
                      </span>
                  </span>
                </label>
              </div>

              <Skeleton v-if="loader" class="mt-30px" columns="2" />
                <div v-else>
                  <div v-if="showDomainSetup">
                    <div class="mt-30px rounded-md p-20px border-1 border-secondary-light dark:bg-mode-base dark:border-mode-focus-light">
                      <DNS :form="form" :server="server"/>
                      <Https :form="form" :disable-verification="true" :server="server"/>
                    </div>
                  </div>
                  <div v-else
                       class="mt-30px mobile:mt-20px p-30px mobile:p-20px border-1 border-secondary-light dark:border-mode-base
                                  dark:bg-mode-base rounded-md"
                  >
                    <h3 class="text-dark dark:text-white font-normal text-lg leading-tight mb-20px">
                      {{ $t('DNS Setup') }}
                    </h3>
                    <div class="grid grid-cols-1 w-full">
                      <suggestion
                          class="mt-3"
                          type="success"
                          :message="dnsHelpText"
                          :light-mode="true"
                      />
                    </div>
                  </div>
              </div>
            </div>
        </form>
    </migration-layout>
</template>

<script setup>

import MigrationLayout from "@/Pages/Migration/Components/MigrationLayout.vue";
import TextInput from '@/Shared/TextInput.vue'
import Https from '@/Pages/Site/New/Components/Https.vue';
import {useForm, usePage} from "@inertiajs/inertia-vue3";
import CheckBox from "@/Shared/CheckBox.vue";
import DNS from "@/Pages/Site/New/Components/DNS.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";
import {computed, ref, watch} from "vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

let props = defineProps({
    form: Object,
    server: Object,
    steps: Object,
    previous_step: String,
    current_step: String,
    next_step: String,
    site_migration: Object,
    suggestion_message: String,
    post_route: String,
    previous_route: String,
    site_type: String,
    hasCloudflareIntegration : Boolean
})

let showCloudflareDnsOption = ref(false)
let useCloudflareDns = ref(false)
let domainParkingMethod = ref('staging_env');
let loader = ref(false)
let showDomainSetup = ref(true);

let brandName = usePage().props.value?.current_white_label ? usePage().props.value?.current_white_label?.branding?.brand_name : 'xCloud';
const stagingHelper = brandName+ t(' offers a temporary test domain that allows you to quickly deploy your site. ') +
    t('This temporary domain enables you to share your work in progress with teammates or clients for review ') +
    t('and input before you finalize and launch it with your own custom domain for public access.');

const dnsHelpText = computed(() => {
  const brandName = Inertia.page.props?.current_white_label
      ? Inertia.page.props.current_white_label.branding.brand_name
      : 'xCloud';
  return `Your DNS setup and SSL Certificate will be done by Cloudflare and managed by ${brandName}.`;

});


const domainNameHelpText = 'Click <a class="underline" target="_blank" href="https://xcloud.host/docs/how-to-setup-dns-record-in-xcloud-server/">here</a>\n' +
    'to know how to setup your DNS provider.';

let form = useForm({
    site_title: props.form?.site_title,
    domain_name: props.form?.domain_name,
    ssl_provider:  domainParkingMethod.value !== 'staging_env' ? props.form?.ssl_provider : null,
    ssl_certificate: domainParkingMethod.value !== 'staging_env' ? props.form?.ssl_certificate : null,
    ssl_private_key: domainParkingMethod.value !== 'staging_env' ? props.form?.ssl_private_key : null,
    domain_parking_method: domainParkingMethod,
    domain_active_on_cloudflare: '',
    cloudflare_account_id: '',
    cloudflare_zone_id: '',
    subdomain: '',
    site_name: '',
    site_type: props.site_type,
});

watch(
    () => useCloudflareDns,
    (newVal, oldVal) => {
      if(useCloudflareDns.value){
        if(form.domain_name === '' || typeof form.domain_name === 'undefined'){
          useCloudflareDns.value = false;
          useFlash().swal().fire({
            icon: 'error',
            title: 'Please add a domain name first',
          })
        }else{
          checkDomain();
        }
      }else{
        showDomainSetup.value = true;
        resetCloudflareIntegration()
      }
    },
    { deep: true } // Use the deep option to watch for changes in array contents
);

watch(
    () => form.domain_name,
    () => {
      showCloudflareDnsOption.value = true;
      form.domain_name = form.domain_name.toLowerCase()
    }
);

const resetCloudflareIntegration = function (){
  form.domain_active_on_cloudflare = false;
  form.cloudflare_account_id = '';
  form.cloudflare_zone_id = '';
  form.subdomain = '';
  form.site_name = '';
  useCloudflareDns.value = false;
}

const checkDomain = function () {
  loader.value = true;
  form.processing = true;

  axios.get(route("api.user.integration.cloudflare.check-domain-exists", form.domain_name))
      .then((res) => {
        // console.log(res.data.response)
        showDomainSetup.value = !res.data.response.domain_active_on_cloudflare;

        form.domain_active_on_cloudflare = res.data.response.domain_active_on_cloudflare;
        form.cloudflare_account_id = res.data.response.account_id;
        form.cloudflare_zone_id = res.data.response.zone_id;
        form.subdomain = res.data.response.subdomain;
        form.site_name = res.data.response.site_name;
        form.ssl_provider = 'cloudflare';

        loader.value = false;
        form.processing = false;

        // useCloudflareDns.value = false;

        if(!res.data.response.domain_active_on_cloudflare){
          useFlash().error('No cloudflare account found for this domain. Please add your domain to cloudflare first.');
        }
      })
      .catch((err) => {
        showDomainSetup.value = true;
        loader.value = false;
        form.processing = false;
        if (err.response && err.response.data && err.response.data.message) {
          useFlash().error(err.response.data.message);
        } else {
          useFlash().error('An error occurred while processing your request.');
        }
      })
}
</script>
