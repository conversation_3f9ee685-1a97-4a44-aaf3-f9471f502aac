<template>
    <single-server
        active="Site List"
        :server="server"
        :can-delete-server="can_delete_server"
        :can_add_site="can_add_site"
        :can_restart_server="can_restart_server"
        :can_restart_nginx_server="can_restart_nginx_server"
        :can_restart_mysql_server="can_restart_mysql_server"
        :can_clone_server="can_clone_server"
    >
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="flex flex-col pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px small-laptop:!overflow-x-auto w-full table-responsive overflow-y-auto no-scrollbar"
            v-if="sites.data.length > 0">
<!--            <SiteTable :sites="sites"/>-->
            <template v-for="site in sites.data">
                <SiteList @siteDeleted="Inertia.reload();" :site="site" :server="site.server" :index="site.id"/>
            </template>
            <pagination :links="sites.links"/>
        </div>
        <div v-else-if="sites.current_page===1">
            <div v-if="can_add_site" class="mt-50px">
                <div v-if="false" class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                         aria-hidden="true">
                        <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round"
                              stroke-width="2"
                              d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z"/>
                    </svg>
                    <h3 class="mt-3 text-sm font-semibold text-gray-900 dark:text-mode-secondary-dark">{{ $t('No sites on this server') }}</h3>
                    <p class="mt-3 text-sm text-gray-500 dark:text-mode-secondary-dark" >{{ $t('Get started by creating a new site!') }}</p>
                    <div class="mt-6">
                        <btn
                            @click="() =>$inertia.visit(route('site.create', [server.id]))"
                            type="button"
                            class="px-3 py-2"
                        >
                            <i class="xcloud xc-add text-xs"></i> &nbsp;
                            {{ $t('New Site') }}
                        </btn>
                    </div>
                </div>
                <Choose
                    :force-open-help-bar="false"
                    v-else
                    :server="server"
                    :monitoring="server?.monitoring"
                    :back="null"
                    :siteTypes="siteTypes"
                    :defaultType="defaultType"
                />
            </div>

        </div>
    </single-server>
</template>
<script setup>
import SingleServer from "@/Pages/Server/SingleServer.vue";
import Btn from "@/Shared/Btn.vue";
import Choose from "@/Pages/Site/New/Choose.vue";
import SiteList from "@/Pages/Site/Components/SiteList.vue";
import Pagination from "@/Shared/Pagination.vue";
import {Inertia} from "@inertiajs/inertia";

const props = defineProps({
    server: Object,
    siteTypes: Object,
    sites: Object,
    can_add_server: Boolean,
    can_delete_server: Boolean,
    can_add_site: Boolean,
    can_restart_server: Boolean,
    can_restart_nginx_server: Boolean,
    can_restart_mysql_server: Boolean,
    can_clone_server: Boolean,
    defaultType: String
})

</script>
