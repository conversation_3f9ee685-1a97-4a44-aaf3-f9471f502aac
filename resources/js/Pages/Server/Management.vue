<template>
    <single-server
        active="Settings"
        :server="server"
        :can-delete-server="can_delete_server"
        :can_add_site="can_add_site"
        :can_restart_server="can_restart_server"
        :can_restart_nginx_server="can_restart_nginx_server"
        :can_restart_mysql_server="can_restart_mysql_server"
    >
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">

            <!-- Server configuration and upgrade -->

            <div v-if="isResizable"
                class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                <div class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex justify-between
                        items-center gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px">
                    <h4 class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
                        {{ $t('Upgrade Server') }}
                    </h4>
                    <div>
                        <button :disabled="disableForUpgradation || !canUpgradeServer"
                            @click.prevent="openResizeModal = true"
                            class="inline-flex items-center justify-center rounded-10px border-transparent
                                shadow-none min-h-50px mobile:min-h-40px px-20px tablet:px-25px py-2px bg-success-full
                                text-sm font-medium text-white focus:outline-0"
                            :class="{'cursor-not-allowed opacity-50' : disableForUpgradation || !canUpgradeServer}"
                           aria-expanded="true" aria-haspopup="true">
                        <span>{{ $t('Upgrade Server') }}</span>
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto bg-white dark:bg-mode-focus-dark rounded-md w-full px-30px pt-30px pb-16
                        tablet:pb-50px wide-mobile:px-20px">
                    <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-20px pb-30px tablet:pb-20px">
                        <div>
                            <label class="block text-sm font-normal mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none">
                                {{ $t('Server Name') }}
                            </label>
                            <div class="group relative">
                                <input type="text" name="text" :placeholder="server.name" disabled
                                       class="flex-1 block bg-light dark:bg-mode-base text-base font-normal text-dark cursor-not-allowed
                                            dark:text-white min-h-60px p-2 pl-14 pr-25px border-0 border-none dark:border-mode-focus-light
                                            group-focus-within:border-none w-full focus:outline-none min-w-0 rounded-md autofill:bg-light
                                            dark:autofill:bg-mode-base placeholder:text-dark dark:placeholder:text-white shadow-none
                                            outline-none appearance-none">
                                <button type="button" class="absolute left-20px top-15px flex w-full items-center justify-start shadow-none
                                            focus:outline-none text-sm font-normal text-dark dark:text-white cursor-default">
                                    <div class="w-30px h-30px min-w-30px shrink-0 rounded-full">
                                        <img :src="cloudProviderIcon" alt="h-full" />
                                    </div>
                                </button>
                            </div>
                        </div>
                        <div class="group">
                            <label class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none">
                                {{ $t('Choose Your Server Size') }}
                            </label>

                            <select-input
                                class="my-2"
                                id="server_size"
                                v-model="serverSize"
                                :error="form.errors.size"
                                @change="chosenPlan"
                                :placeholder="$t('Server Size')">
<!--                                <option selected disabled :text="currentServerTypeInfo.title" :value="Object">Choose Server</option>-->
                                <!-- Show loading indicator option when data is being loaded -->
                                <option disabled :value="null" v-if="!currentServerTypeInfo">Choose Server</option>
                                <option disabled :value="currentServerTypeInfo?.slug" v-else>{{ currentServerTypeInfo?.title }}</option>

                                <option disabled :value="null" v-if="isLoading">Loading...</option>
                                <optgroup v-bind:label="capitalizeFirstLetter(description)" v-for="(serverList, description) in upgradableServerTypeList" :key="description">
                                    <option v-for="server in serverList" :value="server.slug" :disabled="server.disabled" v-text="server.title" :key="server.slug">
                                    </option>
                                </optgroup>


<!--                                <option v-else v-for="server in upgradableServerTypeList" :value="server.slug"-->
<!--                                        :disabled="server.disabled"-->
<!--                                        v-text="server.title"></option>-->


                            </select-input>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-20px">
                        <div v-if="server.is_connected && server.is_provisioned"
                            class="info_body relative bg-success-full/10 rounded-md p-8 pb-20  gap-50px">
                            <p class="text-dark dark:text-white text-base leading-8 pl-15px">
                                {{ $t('Be cautious when upgrading to a larger hosting plan since you can’t switch back. Also, it’s crucial to back up important data before trying to resize partitions, which can be risky.') }}
                            </p>
                            <i class="absolute top-40px left-20px text-lg text-primary-light flex xcloud xc-note"/>
                        </div>
                        <div v-else
                             class="info_body relative bg-success-full/10 rounded-md p-8 pb-20  gap-50px">
                            <p class="text-dark dark:text-white text-base leading-8 pl-15px">
                                {{ $t('Can not perform server resize action on disconnected server.') }}
                            </p>
                            <i class="absolute top-40px left-20px text-lg text-danger flex xcloud xc-warning"/>
                        </div>
                        <div>
                            <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                                <tbody class="bg-white rounded-md border-1 dark:border-0 border-light dark:bg-mode-base divide-y-1
                                            divide-light  dark:divide-mode-light text-dark dark:text-white dark:border-mode-base
                                            group-focus-within:border-none w-full focus:outline-none min-w-0 autofill:bg-light
                                            dark:autofill:bg-mode-base">
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-left
                                            text-base font-normal h-50px">
                                    </td>
                                    <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-left
                                            text-base font-normal h-50px">
                                        {{ $t('Current Status') }}
                                    </td>
                                    <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-left
                                            text-base font-normal ">
                                        {{ $t('After Upgrading') }}
                                    </td>
                                </tr>
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ $t('RAM Size') }}
                                    </td>
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ currentServerTypeInfo?.memory / 1024 }} GB
                                    </td>
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ chosenPlan?.memory ? (chosenPlan?.memory / 1024) + 'GB' : '-'  }}
                                    </td>
                                </tr>
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ $t('CPU Core') }}
                                    </td>
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ currentServerTypeInfo?.cpu > 1 ? currentServerTypeInfo?.cpu + ' CPUs' : currentServerTypeInfo?.cpu + ' CPU' }}
                                    </td>
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ chosenPlan?.cpu? (chosenPlan?.cpu > 1 ? chosenPlan?.cpu + ' CPUs' : chosenPlan?.cpu + ' CPU') : '-' }}
                                    </td>
                                </tr>
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ $t('SSD Space') }}
                                    </td>
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ currentServerTypeInfo?.disk ? currentServerTypeInfo?.disk + ' GB' : '-' }}
                                    </td>
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ chosenPlan?.disk ? chosenPlan?.disk + ' GB' : '-' }}
                                    </td>
                                </tr>
                                <tr class="divide-x-1 divide-light dark:divide-mode-light">
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ $t('Price') }}
                                    </td>
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ currentServerTypeInfo?.price ? '$' + currentServerTypeInfo?.price : '-' }}
                                    </td>
                                    <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                        {{ chosenPlan?.price ? '$' + chosenPlan?.price : '-' }}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>




            <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                <div
                    class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-50px flex justify-between items-center
                            gap-20px wide-mobile:flex-wrap wide-mobile:gap-0 wide-mobile:px-15px">
                    <h4 class="text-lg font-medium text-dark dark:text-white leading-none wide-mobile:py-2">
                        {{ $t('Server Utilities') }}
                    </h4>
                </div>
                <div class="overflow-x-auto w-full overflow-y-hidden">
                    <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                        <thead class="bg-primary-dark dark:bg-mode-focus-dark text-white">
                        <tr class="divide-x-1 divide-light dark:divide-mode-light">
                            <th class="px-30px py-2 text-left text-base font-normal h-60px">
                                {{ $t('Server') }}
                            </th>
                            <th class="px-30px py-2 text-left text-base font-normal h-60px">
                                {{ $t('Status') }}
                            </th>
                            <th class="px-30px py-2 text-left text-base font-normal h-60px">
                                {{ $t('Action') }}
                            </th>
                        </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-mode-base divide-y-1 divide-light  dark:divide-mode-light text-dark dark:text-white">
                            <Item
                                v-if="server?.permissions?.includes('server:manage-services') && server?.stack === 'openlitespeed'"
                                :server="server"
                                service="lsws"
                                name="OpenLiteSpeed"
                                :status="server.services?.lsws"
                            />
                            <Item
                                v-if="server?.permissions?.includes('server:manage-services')"
                                :server="server"
                                :name="database_type === 'MySQL' ? 'MySQL' : `${database_type}`"
                                :service="'mysql'"
                                :status="server.services?.mysql"
                            />
                            <Item
                                v-if="server?.permissions?.includes('server:manage-services') && server?.stack === 'nginx'"
                                :server="server"
                                service="nginx"
                                name="NGINX"
                                :status="server.services?.nginx"
                            />
                            <Item
                                :server="server"
                                service="redis"
                                name="Redis"
                                :status="server.services?.redis"
                            />
                            <Item
                                v-if="server?.permissions?.includes('server:manage-php') && server?.stack === 'nginx'"
                                :server="server"
                                service="php"
                                name="PHP"
                                :status="server.services?.php"
                            />
                            <Item
                                v-if="server?.permissions?.includes('server:manage-services')"
                                :server="server"
                                service="ssh"
                                name="OpenSSH"
                                :status="server.services?.ssh"
                            />

                        </tbody>
                    </table>
                </div>
            </div>

            <div class="grid grid-cols-2 wide-tablet:grid-cols-1 gap-8">

                <!-- Delete Server -->
                <div v-if="server?.permissions?.includes('server:delete')" class="bg-white dark:bg-mode-light p-2 pt-0 rounded-10px flex flex-col">
                    <div class="info_head flex justify-between items-center px-6 py-3">
                        <h5 class="text-left text-lg font-normal text-dark dark:text-white">
                            {{ $t('Delete your Server') }}
                        </h5>
                        <button type="button"
                            :disabled="!can_delete_server"
                            @click.prevent="openDeleteModal = true"
                            class="inline-flex items-center justify-center rounded-10px border-transparent shadow-none px-5
                                  py-3 bg-delete text-sm font-medium text-white focus:outline-0"
                            :class="{'cursor-not-allowed opacity-50' : !can_delete_server}"
                            aria-expanded="true"
                            aria-haspopup="true"
                        >
                            <span>{{ $t('Delete') }}</span>
                        </button>
                    </div>
                    <div
                        class="info_body bg-delete/10 rounded-md p-8 pb-20 flex-1 flex gap-50px">
                        <p class="text-dark dark:text-white text-base leading-8">
                            {{ $t('This action is irreversible, and will also remove the server from your provider, and all data will be erased. We recommend that you keep a backup before you proceed.') }}
                        </p>
                    </div>
                </div>
            </div>

        </div>
    </single-server>

    <!-- Resize modal-->
    <Modal
        @close="openResizeModal = false"
        :footer-button-title="$t('Resize')"
        :footer-button="true"
        :show="openResizeModal"
        :title="$t('Are You Sure You Want To Resize Server')+' '+ server.name +'?'"
        :widthClass="'max-w-850px'">
        <div class="flex flex-col gap-30px">
            <div class="warning flex items-center bg-warning/20 px-6 py-4 rounded-md">
                <img :src="asset('img/warning.svg')" alt="warning_img" />
                <div class="text-sm text-dark dark:text-white leading-7 pl-4">
                    <b class="text-xl">{{ $t('Warning: Before you upgrade') }}</b>
                    <ul class="list-disc px-10">
                        <li>
                            {{ $t('This will require a HARD restart and the process can take up to 15 min. It is recommended to take a full server backup of your server before proceeding.') }}
                        </li>
                        <li>
                            {{ $t('New Plan') }}:
                            {{ chosenPlan?.cpu > 1 ? chosenPlan?.cpu + ' CPUs' : chosenPlan?.cpu + ' CPU' }} /
                            {{ chosenPlan?.memory / 1024 }} GB RAM /
                            {{ chosenPlan?.disk ? chosenPlan?.disk + ' GB' : '-' }} SSD - ${{ chosenPlan?.price }} {{ hasBackupOnCurrentServer ? '+ $' + Math.ceil(chosenPlan?.price * .20) + ' (backup)' : null}} per month.
                        </li>
                        <li v-if="is_xcloud_or_white_label">

                            {{ $t('If you upgrade now, you’ll be charged') }} : <strong>${{ chargeNow(chosenPlan?.price) }} </strong> <br>
<!--                            Current server cost: <strong>${{ currentServerTypeInfo?.price }}  {{ hasBackupOnCurrentServer ? '+ $' + currentServerTypeInfo?.price * .20 + ' (backup)' : null }} per month.</strong> <br>-->

<!--                            Total hour usage: <strong>{{ usageHoursOnThisMonth }}</strong> hours <br>-->
<!--                            Returnable amount: <small>AMOUNT PAID - (TOTAL HOUR USAGE * COST PER HOUR) </small>: <strong>${{ returnableAmount }}</strong>. <br>-->
<!--                            Cost per hour: <strong>${{ costPerHourMonthly }}</strong> . <br>-->
<!--                            If you upgrade now, you'll be charged <strong>${{ chargeNow(chosenPlan?.price) }}</strong>-->
<!--                            for the remaining days of this month, then <strong>${{ chosenPlan?.price }}</strong> per month-->
                        </li>
                    </ul>
                </div>

            </div>
            <div class="flex flex-col gap-30px">
                <text-input v-model="resizeForm.resize_confirmation"
                            :error="resizeForm.errors.resize_confirmation"
                            type="text"
                            :label="$t('Type')+` ${server.name} `+$t('to confirm')"/>
            </div>
        </div>
        <template #footer>
                <button
                    :disabled="!checkResizeInputMatch || resizeForm.processing"
                    @click.prevent="resizeServer()"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px
                            py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                    :class="{'cursor-not-allowed opacity-50': !checkResizeInputMatch || resizeForm.processing}"
                    aria-expanded="true" aria-haspopup="true"
                >
                    <span v-text="resizeForm.processing ? $t('Initiating Resizing...') : $t('Resize')"></span>
                </button>
        </template>
    </Modal>
    <!-- Delete modal -->
   <DeleteServer
       :server-name="server.name"
       :server-id="server.id"
       :show="openDeleteModal"
       :provider="server.provider_readable"
       :is_xcloud="is_xcloud"
       :is_xcloud_or_white_label="is_xcloud_or_white_label"
       :has-cloudflare-integration="hasCloudflareIntegration"
       :is_any_provider="is_any_provider"
       @close="openDeleteModal = false"
   >
   </DeleteServer>

</template>
<script setup>
import {computed, onMounted, ref, watch} from "vue";
import SingleServer from "@/Pages/Server/SingleServer.vue";
import TextInput from '@/Shared/TextInput.vue';
import {useForm} from '@inertiajs/inertia-vue3';
import Modal from "@/Shared/Modal.vue"
import Item from "@/Pages/Server/Components/Management/Item.vue";
import Button from "@/Jetstream/Button.vue";
import DeleteServer from "@/Pages/Server/Components/DeleteServer.vue";
import {useFlash} from "@/Composables/useFlash";
import SelectInput from "@/Shared/SelectInput.vue";
import {useCloudProviderIcon} from "@/Composables/useCloudProviderIcon";

const props = defineProps({
    server: Object,
    currentServerTypeInfo: Object,
    hasBackupOnCurrentServer: Boolean,
    isResizable: Boolean,
    openDeleteModal: {
        type: Boolean,
        default: false
    },
    // upgradableServerTypeList: Object,
    returnableAmount: Number,
    getMonthlyPaid: Number,
    usageHoursOnThisMonth: Number,
    costPerHourMonthly: Number,
    unusedAmount: Number,
    canUpgradeServer: Boolean,
    can_add_server: Boolean,
    can_delete_server: Boolean,
    can_add_site: Boolean,
    can_restart_server: Boolean,
    can_restart_nginx_server: Boolean,
    can_restart_mysql_server: Boolean,
    is_xcloud: Boolean,
    is_xcloud_or_white_label: Boolean,
    is_any_provider: Boolean,
    database_type: String,
    hasCloudflareIntegration: {
        type: Boolean,
        default: false
    }
})

const openResizeModal = ref(false)
const serverSize = ref(props.server.size)
const { cloudProviderIcon } = useCloudProviderIcon(props.server.provider_name);

const form = useForm({
    archive_confirmation: '',
});

const resizeForm = useForm({
    resize_confirmation: '',
    size: serverSize,
});

const checkArchiveInputMatch = computed(() => {
    return form.archive_confirmation === props.server.name
})

const checkResizeInputMatch = computed(() => {
    return resizeForm.resize_confirmation === props.server.name
})

const disableForUpgradation = computed(() => {
    return (resizeForm.size === props.server.size) || !(props.server.is_connected && props.server.is_provisioned)
})

// function to capitalize the first letter of a string
const capitalizeFirstLetter = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
}


// const currentServerPlan = computed(() => {
//     if(props.isResizable){
//         for (const [key, item] of Object.entries(props.upgradableServerTypeList)) {
//             if (Array.isArray(item)) {
//                 const matchingPlan = item.find(plan => plan.slug === props.server.size);
//                 if (matchingPlan) {
//                     return matchingPlan;
//                 }
//             } else if (item.slug === props.server.size) {
//                 return item;
//             }
//         }
//         return '';
//     }else if(props.server.provider_readable === 'Google Cloud'){
//         const server = props.upgradableServerTypeList.find(s => s.name === props.server.size)
//         return server ? server.size : ''
//     }
// })

const chosenPlan = computed(() => {
    if (props.isResizable && upgradableServerTypeList.value && typeof upgradableServerTypeList.value === 'object') {
        for (const [key, item] of Object.entries(upgradableServerTypeList.value)) {
            if (Array.isArray(item)) {
                const matchingPlan = item.find(plan => plan.slug === serverSize.value);
                if (matchingPlan) {
                    return matchingPlan;
                }
            } else if (item.slug === serverSize.value) {
                return item;
            }
        }
    }
    return null; // Return null to indicate no selection if not resizable or if no plan matches.
});



let chargeNow = (price) => {

    if (props.hasBackupOnCurrentServer) {
        return Math.ceil(price + (price * .20) - props.returnableAmount);
    }

    return Math.ceil(price - props.returnableAmount);
}

function resizeServer() {
    resizeForm.post(route('api.server.update.resize', props.server.id), {
        preserveScroll: true,
        onSuccess: () => {
            openResizeModal.value = false
        },
        onError: () => {
            openResizeModal.value = false
        }
    })
}


// Reactive data for storing the server list and loading state
const upgradableServerTypeList = ref({});
const isLoading = ref(false);

// Function to fetch the server list
async function fetchUpgradableServerTypeList() {
    if (!props.server || !props.server.id) {
        console.error("Server ID is undefined.");
        return;
    }

    isLoading.value = true;
    try {
        const url = route('api.server.upgradable_types', { server: props.server.id });
        const response = await axios.get(url);
        upgradableServerTypeList.value = response.data;
    } catch (error) {
        console.error("Failed to fetch server types:", error);
        // Handle errors, e.g., show a notification or set an error message
        // Optionally reset upgradableServerTypeList to an empty object here
        upgradableServerTypeList.value = {};
    } finally {
        isLoading.value = false;
    }
}

// Fetch the list when the component is mounted
onMounted(() => {
    if (props.isResizable) {
        fetchUpgradableServerTypeList();
    }
});
</script>
