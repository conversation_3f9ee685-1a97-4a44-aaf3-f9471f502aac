<template>
    <single-server
        active="Vulnerability Settings"
        :server="server"
    >
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <Card
                :title="$t('Vulnerability Scan')"
                buttonStyle="success"
                :button-title="$t('Save Settings')"
                @onSubmit="updateVulnerabilityScanSettings"
                :disable-use-button="sites?.total === 0"
                :use-button="can_perform_support_level_action"
            >
                <div class="flex gap-4 items-center w-full bg-focused dark:bg-mode-light rounded px-6 py-4">
                    <div class="flex gap-3 items-center grow">
                        <div class="flex flex-col gap-2">
                            <h5 class="text-lg font-normal leading-none tracking-tighter text-dark dark:text-white">
                                {{ $t('Enable Vulnerability Scan') }}</h5>
                        </div>
                    </div>
                    <Switch
                        :disabled="sites?.total === 0"
                        :checked="!!vulnerabilityScanForm.vulnerability_scan"
                        @change="vulnerabilityScanForm.vulnerability_scan = !vulnerabilityScanForm.vulnerability_scan"
                    >
                    </Switch>
                </div>
                <div v-if="vulnerabilityScanForm.vulnerability_scan" class="grid grid-cols-2 tablet:grid-cols-1 gap-6">
                    <div class="flex gap-4 items-center w-full bg-focused dark:bg-mode-light rounded px-6 py-4">
                        <div class="flex gap-3 items-center grow">
                            <div class="flex flex-col gap-2">
                                <h5 class="text-lg font-normal leading-none tracking-tighter text-dark dark:text-white">{{ $t('Enable Auto Update') }}</h5>
                                <p class="text-sm text-secondary-full dark:text-mode-secondary-light">{{ $t('If vulnerability found we will update in 24 hours') }}</p>
                            </div>
                        </div>
                        <Switch
                            :checked="!!vulnerabilityScanForm.auto_update"
                            @change="vulnerabilityScanForm.auto_update = !vulnerabilityScanForm.auto_update"
                        >
                        </Switch>
                    </div>
                    <div v-if="!!vulnerabilityScanForm.auto_update" class="flex gap-4 items-center w-full bg-focused dark:bg-mode-light rounded px-6 py-4">
                        <div class="flex gap-3 items-center grow">
                            <div class="flex flex-col gap-2">
                                <h5 class="text-lg font-normal leading-none tracking-tighter text-dark dark:text-white">
                                    {{ $t('Enable Auto Backup') }}
                                </h5>
                            </div>
                        </div>
                        <Switch
                            :checked="!!vulnerabilityScanForm.auto_backup"
                            @change="vulnerabilityScanForm.auto_backup = !vulnerabilityScanForm.auto_backup"
                        >
                        </Switch>
                    </div>
                </div>
                <div v-if="vulnerabilityScanForm.vulnerability_scan && sites?.total > 0"
                     class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                    <div
                        v-if="sites?.data?.length"
                        class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex flex-wrap items-center wide-mobile:px-15px gap-20px mobile:gap-1 justify-between">
                        <label for="all_site" class="inline-flex ml-4 small-laptop:ml-3 tablet:ml-2">
                            <input
                                id="all_site"
                                :checked="vulnerabilityScanForm.all_sites"
                                @click="selectAllSites()"
                                type="checkbox" class="hidden peer"/>
                            <span
                                class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-2 before:border-dark dark:before:border-dark  before:rounded before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white">
                                        </span>
                        </label>
                        <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                            {{ $t('Select All Sites') }}
                        </h4>
                        <div class="gap-2 ml-auto flex align-center">

                        </div>
                    </div>
                    <div v-if="sites?.data"
                         class="w-full h-full flex flex-col gap-2 px-4 small-laptop:px-3 tablet:px-2 overflow-x-auto">
                        <div
                            v-for="(site,index) in sites?.data"
                            :key="site?.id"
                            class="group mb-4 relative flex items-center gap-x-20px gap-y-10px bg-white dark:bg-mode-base  hover:drop-shadow-list-box px-30px py-20px rounded-10px duration-75 ease-in-out min-w-[60rem]"
                        >
                            <div class="flex items-center grow">
                                <div class="flex items-center gap-20px wide-mobile:gap-10px">
                                <span class="inline-flex items-center">
                                    <label class="inline-flex">
                                        <input
                                            :checked="vulnerabilityScanForm.site_ids.includes(site?.id) || vulnerabilityScanForm.all_sites"
                                            @change="toggleSiteSelection(site?.id)"
                                            :value="site?.id" type="checkbox" class="hidden peer"/>
                                        <span
                                            class="flex before:content-['\e927'] before:font-xc before:w-5 before:shrink-0 before:h-5 before:bg-transparent before:border-2 before:border-dark dark:before:border-dark  before:rounded before:mt-0.5 before:mr-2.5 before:text-xxs before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none before:transition before:duration-200 peer-checked:before:border-success-full peer-checked:before:bg-success-full peer-checked:before:text-white">
                                        </span>
                                    </label>
                                </span>
                                    <span class="w-100px wide-mobile:w-60px shrink-0">
                                     <img
                                         v-if="site.status === 'provisioned'"
                                         :src="`https://s.wordpress.com/mshots/v1/${site?.site_url}/?w=200`"
                                         alt="h-full"
                                     />
                                    <img v-else src="/img/site_placeholder.gif" alt="">
                                </span>
                                    <div class="flex flex-col">
                                        <h2 class="inline-flex items-center">
                                            <StateToolTip
                                                :align="index === 0 ? 'bottom' : 'top'"
                                                class="mr-3"
                                                :state="site.state"
                                                :title="site.status_readable"
                                            />
                                            <tooltip
                                                :align="index === 0 ? 'bottom' : 'top'"
                                                class="cursor-pointer"
                                                :title="site.name"
                                            >
                                                <Link
                                                    :href="'/site/' + site.id"
                                                    class="inline-flex items-center text-dark dark:text-light text-3xl leading-7 hover:text-primary-dark dark:hover:text-white transition ease-in-out duration-75"
                                                >
                                                    {{ $filters.textLimit(site.name, 20) }}
                                                </Link>
                                            </tooltip>
                                            <tooltip
                                                :align="index === 0 ? 'bottom' : 'top'"
                                                v-if="site.state === 'Success'"
                                                class="cursor-pointer ml-3"
                                                title="View Site"
                                            >
                                                <a
                                                    :href="site.site_url"
                                                    target="_blank"
                                                    class="inline-flex items-center"
                                                >
                                                    <i class="xcloud xc-maximize text-base text-secondary-full dark:text-secondary-light hover:text-primary-light dark:hover:text-primary-light cursor-pointer transition ease-in-out duration-75">
                                                    </i>
                                                </a>
                                            </tooltip>
                                        </h2>
                                        <div class="flex items-center flex-wrap gap-x-1 gap-y-1 mt-1">
                                            <span class="text-base text-dark dark:text-light leading-none">{{
                                                    site.created_at_readable
                                                }}</span>
                                            <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm"
                                                v-if="site.environment">
                                        <span
                                            :class="{'text-success-light bg-success-light/10' : site.environment === 'production', 'text-[#9AA4B2] bg-[#9AA4B2]/10' : site.environment === 'staging' || site.environment === 'staging_with_own_domain', 'text-[#007EFD] bg-[#007EFD]/10' : site.environment === 'demo'}"
                                            class="px-10px py-1 wide-mobile:p-1
                                              rounded-md transition ease-in-out duration-150 inline-flex items-center gap-1.5"
                                        >
                                            <span>{{ getSiteEnvironmentReadable(site.environment) }}</span>
                                        </span>
                                            </h6>
                                            <h6 class="flex items-center flex-wrap text-base !leading-tight tablet:text-sm text-secondary-full dark:text-mode-secondary-light"
                                                v-if="site.wordpress_version"
                                            >
                                            <span
                                                class="px-10px py-1 wide-mobile:p-1 border-1 border-light dark:border-mode-base rounded-md transition ease-in-out duration-150 inline-flex items-center gap-1.5">
                                                <i class="xcloud xc-wordpress text-primary-light"></i>
                                                <span>{{ site.wordpress_version }}</span>
                                            </span>
                                            </h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <pagination-button
                            @chosenPage="changeSitesPage"
                            :links="sites.links"></pagination-button>
                    </div>
                </div>

            </Card>
        </div>
    </single-server>
</template>
<script setup>
import SingleServer from "@/Pages/Server/SingleServer.vue";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import {useFlash} from "@/Composables/useFlash";
import {onMounted, ref} from "vue";
import Tooltip from "@/Shared/Tooltip.vue";
import Card from "@/Shared/Card.vue";
import Switch from "@/Shared/Switch.vue";
import StateToolTip from "@/Shared/StateToolTip.vue";
import PaginationButton from "@/Shared/PaginationButton.vue";

const props = defineProps({
    server: Object,
    vulnerability_setting: Object,
    vulnerability_scan: Boolean,
    can_perform_support_level_action: Boolean,
})
let sites = ref(null);

onMounted(() => {
    getSiteList();
});

const vulnerabilityScanForm = useForm({
    vulnerability_scan: props.vulnerability_setting!=null,
    auto_update: props?.vulnerability_setting?.auto_update,
    auto_backup: props?.vulnerability_setting?.auto_backup,
    site_ids: props?.vulnerability_setting?.sites ?? [],
    all_sites: props?.vulnerability_setting && props?.vulnerability_setting?.sites == null
});

const toggleSiteSelection = (site_id) => {
    if(vulnerabilityScanForm.all_sites){
        vulnerabilityScanForm.all_sites = false;
    }
    if (vulnerabilityScanForm.site_ids.includes(site_id)) {
        vulnerabilityScanForm.site_ids = vulnerabilityScanForm.site_ids.filter(id => id !== site_id);
    } else {
        vulnerabilityScanForm.site_ids.push(site_id);
    }
}

const updateVulnerabilityScanSettings = () => {
    vulnerabilityScanForm
        .transform(data => ({
            ...data,
            auto_update: data?.auto_update ?? false,
            auto_backup: data?.auto_backup ?? false,
            all_sites: data?.all_sites ?? false,
            site_ids: data?.vulnerability_scan ? data?.site_ids : null
        }))
        .post(route('api.server.vulnerability-scan.update', props.server.id), {
        preserveScroll: true,
        onSuccess: () => {
            useFlash().success('Vulnerability scan settings updated successfully.');
        },
        onError: (e) => {
            console.error(e);
            useFlash().error('Failed to update vulnerability scan settings.');
        }
    });
}

const changeSitesPage = (page) => {
    axios.get(page).then(({data}) => {
        sites.value = data;
    }).catch(error => {
        sites.value = [];
    });
}
const getSiteList = () => {
    axios.get(route('api.server.site-list', props.server.id))
        .then(({data}) => {
            sites.value = data;
        }).catch(error => {
        sites.value = [];
    });
}

const selectAllSites = () => {
    vulnerabilityScanForm.all_sites = !vulnerabilityScanForm.all_sites;
    if (vulnerabilityScanForm.all_sites) {
        vulnerabilityScanForm.site_ids = sites.value.data.map(site => site.id);
    }else{
        vulnerabilityScanForm.site_ids = [];
    }
}

const getSiteEnvironmentReadable = (environment) => {
    if(environment === 'production') {
        return 'Production';
    } else if(environment === 'staging_with_own_domain' || environment === 'staging') {
        return 'Staging';
    }else{
        return 'Demo';
    }
}
</script>
