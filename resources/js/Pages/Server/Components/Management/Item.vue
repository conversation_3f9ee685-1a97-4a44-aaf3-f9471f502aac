<template>
    <tr class="divide-x-1 divide-light dark:divide-mode-light">
        <td class="px-30px py-2 text-left text-base font-normal h-60px">
            {{ name }}
        </td>
        <td class="px-30px py-2 text-left text-sm font-normal h-60px">
            <span
                class="inline-flex items-center rounded-[40px] border-transparent
                shadow-none px-4 py-1.5 text-sm focus:outline-0"
                :class="{ ' text-success-light bg-success-light/10': status === 'active', 'text-danger bg-danger/10':
                 status === 'inactive' }"
                aria-expanded="true" aria-haspopup="true">
                <span class="px-2">{{ statusTitle }}</span>
            </span>
        </td>
        <td class="px-30px py-2 text-left text-sm font-normal h-60px ">
            <div class="flex gap-2" v-if="status === 'active'">
                <button type="button"
                        @click="serviceAction = service + ':' + 'restart';  openServiceModal = true;action= 'restart';"
                        class="inline-flex items-center rounded-3xl shadow-none px-1.5 py-1.5 pr-3.5 bg-primary-light/10 text-sm text-primary-light focus:outline-0 gap-1.5"
                        aria-expanded="true" aria-haspopup="true">
                <span
                    class="rounded-full bg-primary-light h-[1.125rem] w-[1.125rem] shrink-0 flex items-center justify-center">
                    <i class="xcloud xc-verify_dns text-white text-xxs"></i>
                </span>
                    <span>{{ $t('Reboot') }}</span>
                </button>
                <button type="button"
                        @click="serviceAction = service + ':' + 'disable';  openServiceModal = true;action= 'disable';"
                        class="inline-flex items-center rounded-[40px] border-transparent
                          shadow-none px-4 py-1.5 bg-warning/10 text-sm text-warning focus:outline-0 hover:bg-warning hover:text-white"
                        aria-expanded="true" aria-haspopup="true">
                    <span class="px-2">{{ $t('Disable') }}</span>
                </button>
            </div>
            <div v-else class="flex gap-2">
                <button type="button"
                        @click="serviceAction = service + ':' + 'enable';  openServiceModal = true;action= 'enable';"
                        class="inline-flex items-center rounded-[40px] border-transparent
                                shadow-none px-4 py-1.5 bg-success-light/10 text-sm text-success-light focus:outline-0 hover:bg-success-light hover:text-white"
                        aria-expanded="true"
                        aria-haspopup="true">
                    <span class="px-2">{{ $t('Enable') }}</span>
                </button>
            </div>

        </td>
    </tr>

    <!-- Disable Service modal -->
    <Modal
        @close="openServiceModal = false"
        :footer-button-title="$t('Disable')"
        :footer-button="true"
        :show="openServiceModal"
        :loading="serviceItem.processing"
        :title="$t('Are You Sure You Want To')+' '+action+' ' + service +' '+$t('Service?')"
        :widthClass="'max-w-850px'">
        <div class="flex flex-col gap-30px">
            <div class="warning flex items-center bg-danger/20 px-6 py-4 rounded-md">
                <img :src="asset('img/warning.svg')" alt="warning_img"/>
                <p class="text-sm text-dark dark:text-white leading-7 pl-4">
                    <b>{{ $t('Note') }}:</b> {{ $t('You can again enable this service.') }}
                </p>
            </div>

            <div class="flex flex-col gap-30px">
                <text-input v-model="serviceItem.confirmationMessage"
                            type="text"
                            :label="$t('Type')+` ${service} `+$t('to confirm')"/>
            </div>
        </div>
        <template #footer>
            <button :disabled="!checkDisableServiceInputMatch"
                    @click.prevent="executeService()"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px
                py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                    aria-expanded="true" aria-haspopup="true">
                <span>{{ $t('Execute') }}</span>
            </button>
        </template>
    </Modal>
</template>

<script setup>
import {computed, ref} from "vue";
import {useServer} from "@/Composables/useServer.js";
import Modal from "@/Shared/Modal.vue";
import TextInput from "@/Shared/TextInput.vue";
import {useForm} from "@inertiajs/inertia-vue3";

const props = defineProps(['server', 'service', 'name', 'status'])

let openServiceModal = ref(false);

const serviceAction = ref('')

const action = ref('');

//convert status to title
const statusTitle = computed(() => {
    return props.status === 'active' ? 'Active' : 'Stopped'
})

const {restartService, enableService, disableService} = useServer(props.server);

const serviceItem = useForm({
    service: props.service,
    confirmationMessage: ''
});

const checkDisableServiceInputMatch = computed(() => {
    return serviceItem.confirmationMessage === props.service;
})

function executeService() {
    switch (serviceAction.value) {
        case props.service + ':disable':
            disableService(props.service);
            openServiceModal.value = false;
            serviceItem.confirmationMessage = '';
            return;
        case props.service + ':enable':
            enableService(props.service);
            openServiceModal.value = false;
            serviceItem.confirmationMessage = '';
            return;
        case props.service + ':restart':
            restartService(props.service);
            openServiceModal.value = false;
            serviceItem.confirmationMessage = '';
            return;
    }
}

</script>
