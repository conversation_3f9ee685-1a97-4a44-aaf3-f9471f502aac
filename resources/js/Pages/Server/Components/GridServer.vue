<template>
    <div
        class="relative p-40px tablet:p-30px wide-mobile:p-20px rounded-10px group flex flex-wrap items-center
               justify-between gap-x-20px gap-y-30px wide-mobile:gap-y-20px bg-white dark:bg-mode-light border-2 border-white
               dark:border-mode-light hover:box-shadow-grid-box dark:hover:bg-dark group
               transition ease-in-out duration-150">
        <div class="flex items-center gap-20px wide-mobile:gap-10px">
            <span class="w-50px wide-mobile:w-40px shrink-0">
                <Tooltip class="ml-3" :title="server.provider_readable">
                <img :src="cloudProviderIcon" class="w-full h-full" alt="h-full" />
                </Tooltip>
            </span>
            <div class="flex flex-col">
                <h2 class="inline-flex items-center pr-25px">
                    <Link class="text-3xl wide-tablet:text-2xl wide-mobile:text-xl mobile:text-lg text-dark
                                 hover:text-primary-dark dark:text-light dark:hover:text-white leading-none cursor-pointer"
                        :href= "server.status !== 'deleting' ? '/server/' + server.id + '/sites' : '#'">
                        {{ $filters.textLimit(server.name) }}
                    </Link>
                    <StateToolTip class="ml-3"
                                  :state="server.state"
                                  :title="server.status_readable"/>
                </h2>
                <span v-if="false" class="text-sm text-secondary-full dark:text-mode-secondary-light mt-1.5">{{ $t('Created') }}: {{ server.created_at_readable }}</span>
                <div class="flex items-center flex-wrap gap-x-15px gap-y-2 mt-1.5">
                    <h6 v-if="server?.public_ip" class="flex items-center flex-wrap text-base text-secondary-full dark:text-mode-secondary-light">
                        <CopyAbleText align="bottom" :text="server?.public_ip"/>
                    </h6>
                    <span v-if="server.region"
                        class="flex items-center text-base px-10px py-1 text-secondary-full dark:text-mode-secondary-light
                                divide-x divide-light
                                group-hover:divide-primary-dark rounded-md">
                        <RegionFlag
                            :region="server.region_flag"/>
                    </span>
                    <span v-if="server.plan_title"
                        class="flex items-center text-base py-1 text-secondary-full dark:text-mode-secondary-light
                                divide-x divide-light
                                group-hover:divide-primary-dark rounded-md">
                       <tooltip align="bottom" :title="server?.plan_title">
                            {{ server?.plan_title?.slice(0, 18) + ".." }}
                            </tooltip>
                    </span>

                    <h6 v-if="server.status !== 'deleting'"
                        class="flex items-center text-base text-secondary-full dark:text-mode-secondary-light">
                        <span class="px-10px py-1 border-1 border-light dark:border-mode-base dark:group-hover:border-transparent
                              dark:group-hover:bg-mode-focus-dark group-hover:bg-white/10 divide-x divide-light dark:divide-mode-base
                              group-hover:divide-primary-dark dark:group-hover:divide-dark rounded-md">

                              <Link v-if="!server.sites_count && server.is_provisioned"
                                      class="text-sm text-dark hover:text-primary-dark
                                      dark:text-light dark:hover:text-white leading-none cursor-pointer"
                                      :href="route('site.create.server-choose')">
                                      {{ $t('Add site') }}
                              </Link>
                              <span v-else-if="server.is_provisioned">
                                {{
                                    server.sites_count > 1
                                    ? server.sites_count + " "+$t('sites')
                                    : server.sites_count + " "+$t('site')
                                }}
                              </span>
                              <span v-else>0 {{ $t('sites') }}</span>
                        </span>
                    </h6>
                    <!-- Server Stack -->
                    <div v-if="server.stack"
                          class="flex px-2 items-center text-base py-1 text-secondary-full dark:text-mode-secondary-light  divide-x divide-light group-hover:divide-primary-dark rounded-md">
                        <Tooltip
                            :title="`${server.stack_readable} Web Server`"
                            align="top"
                        >
                            <div class="flex flex-row items-center gap-5px">
                                <img
                                    :src="{ 'openlitespeed': asset('img/openlitespeed.svg'),'nginx': asset('img/nginx-logo.svg') }[server.stack]"
                                    :alt="server.stack"
                                    class="w-25px h-25px"
                                />
                                <h5 class="leading-none text-sm">
                                    {{ server.stack === 'openlitespeed' ? 'OLS' : 'NGINX' }}
                                </h5>
                            </div>
                        </Tooltip>
                    </div>

                    <!-- Database Server -->
                  <div v-if="server.database_type_readable"
                       class="flex px-2 items-center text-base text-secondary-full dark:text-mode-secondary-light
                                    divide-x divide-light group-hover:divide-primary-dark rounded-md"
                  >
                    <Tooltip
                        :title="server.database_type_readable + ' Database Server'"
                        align="top"
                    >
                      <div class="flex flex-row items-center gap-5px">
                        <i class="xcloud"
                           :class="server.database_type_readable === 'MySQL' ? 'xc-mysql' : 'xc-mariadb'"
                        ></i>
                        <h6 class="text-secondary-full dark:text-mode-secondary-light leading-none text-sm">
                          {{ server.database_type_readable }}
                        </h6>
                      </div>
                    </Tooltip>
                  </div>


                    <div v-if="server.tags.length > 0" class="flex items-center">
                        <span class="text-base tablet:text-sm text-secondary-full dark:text-mode-secondary-light rounded-md">
                            # {{ server.tags[0]?.name }}
                        </span>

                        <tooltip class="text-xs cursor-pointer ml-2"
                                :title="server.tags.slice(1).map((tag) => tag.name).join(', ')">
                            <h6  v-if="server.tags.length > 1"
                                class="text-base text-primary-dark dark:text-primary-light">
                                +{{ server.tags.length - 1 }}
                            </h6>
                        </tooltip>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-2 gap-20px w-full">
            <div class="flex flex-col justify-center gap-10px grow">
                <span class="flex items-center flex-wrap justify-between text-secondary-full dark:text-mode-secondary-light">
                    <span class="text-sm">{{ $t('Storage') }}</span>
                    <span class="text-xs">
                        <Tooltip
                            align="top"
                            :title="
                                (server.monitoring.disk?.used ?? 0) +
                                ' GB' +
                                ' is used of ' +
                                (server.monitoring.disk?.total ?? 0) +
                                ' GB'">
                            {{ (server.monitoring.disk?.used ?? 0) + " GB" }} /
                            {{ (server.monitoring.disk?.total ?? 0) + " GB" }}
                        </Tooltip>
                    </span>
                </span>
                <div class="bg-gray-200 rounded-full h-5px dark:bg-gray-700 w-full">
                    <div :class="server.monitoring.disk?.isLow ? 'bg-[#F4573C]' : 'bg-primary-dark'"
                        class="h-5px rounded-full"
                        :style="'width:' + usedStoragePercentage + '%'">
                    </div>
                </div>
            </div>
            <div class="flex flex-col justify-center gap-10px grow">
                <span class="flex items-center flex-wrap justify-between text-secondary-full dark:text-mode-secondary-light">
                    <span class="text-sm">{{ $t('Ram') }}</span>
                    <span class="text-xs">
                        <Tooltip
                            align="top"
                            :title="useUnitSize((server.monitoring.ram?.used ?? 0),'MB','GB') +
                                ' is used of '+useUnitSize((server.monitoring.ram?.total ?? 0),'MB','GB')">
                            {{ useUnitSize((server.monitoring.ram?.used ?? 0),'MB','GB') }} /
                            {{ useUnitSize((server.monitoring.ram?.total ?? 0),'MB','GB')}}
                        </Tooltip>
                    </span>
                </span>
                <div class="bg-gray-200 rounded-full h-5px dark:bg-gray-700 w-full">
                    <div :class="server.monitoring.ram?.isLow ? 'bg-[#F4573C]' : 'bg-primary-dark'"
                        class="h-5px rounded-full"
                        :style="'width:' + usedRamPercentage + '%'">
                    </div>
                </div>
            </div>
        </div>
        <div v-if="server.status !== 'deleting'" class="absolute top-40px right-40px tablet:top-30px tablet:right-30px wide-mobile:top-20px wide-mobile:right-20px cursor-pointer">
            <div class="flex items-center justify-center server-toggle-button">
                <ServerActions
                    position="right"
                    :server="server"
                    :can_add_site="can_add_site"
                    :can_restart_server="can_restart_server"
                    :can_restart_nginx_server="can_restart_nginx_server"
                    :can_restart_mysql_server="can_restart_mysql_server"
                    :can-delete-server="canDeleteServer"
                    :can_clone_server="can_clone_server"
                />
            </div>
        </div>
    </div>
</template>

<script setup>
import { useCloudProviderIcon } from "@/Composables/useCloudProviderIcon.js";
import ServerActions from "@/Pages/Server/Components/ServerActions.vue";
import CopyAbleText from "@/Shared/CopyAbleText.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import StateToolTip from "@/Shared/StateToolTip.vue";
import RegionFlag from "@/Shared/Icons/RegionFlag.vue";
import "flag-icons/css/flag-icons.min.css";
import ListServer from "@/Pages/Server/Components/ListServer.vue";
import {useUnitSize} from "@/Composables/useUnitSize";
import {asset} from "laravel-vapor";
import {onMounted} from "vue";

const props = defineProps({
    server: {
        type: Object,
        required: true,
    },
    canDeleteServer: Boolean,
    can_add_site: Boolean,
    can_restart_server: Boolean,
    can_clone_server: Boolean,
    can_restart_nginx_server: Boolean,
    can_restart_mysql_server: Boolean,
});

const emit = defineEmits(["server-deleted"]);


onMounted(() => {
    if (window.Echo) {
        window.Echo.private("server.status." + props.server.id).listen(
            "ServerStatusChanged",
            (e) => {
                if (e.status === "deleted") {
                    //send to parent
                    emit("server-deleted", props.server.id);
                    return;
                }
                props.server.status = e.status;
                props.server.status_readable = e.status_readable;
                props.server.state = e.state;
            }
        );
    }
});

const usedRamPercentage =
    isNaN(parseInt(props.server.monitoring.ram?.used)) ||
    isNaN(parseInt(props.server.monitoring.ram?.total))
        ? 0
        : (parseInt(props.server.monitoring.ram?.used) * 100) /
        parseInt(props.server.monitoring.ram?.total || 1);

const usedStoragePercentage =
    isNaN(parseInt(props.server.monitoring.disk?.used)) ||
    isNaN(parseInt(props.server.monitoring.disk?.total))
        ? 0
        : (parseInt(props.server.monitoring.disk?.used) * 100) /
        parseInt(props.server.monitoring.disk?.total || 1);

const { cloudProviderIcon } = useCloudProviderIcon(props.server.provider_name);
</script>
