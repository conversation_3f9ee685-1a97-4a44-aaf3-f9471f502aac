<template>
  <div class="xc-container">
    <div class="flex-1 flex items-center"
         :class="navigation.isOpenHelpbar(helper.isOpen) ? 'mr-400px small-laptop:mr-0' : 'mr-0'">
      <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
        <div
            class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
          <h2
              class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark
                               dark:text-white mb-20px wide-mobile:text-3xl mobile:text-28px tablet:mb-15px wide-mobile:mb-2.5">
            {{ $t('Set Up Your Server With') }}
            <img :src="asset('img/google-cloud-platform.svg')"
                 alt="GCP Logo"
                 class="h-50px tablet:h-40px wide-mobile:h-30px inline-flex -mt-1 ml-2"/>
          </h2>
          <p class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark text-center mb-50px mobile:mb-25px">
            {{ $t('Connect xCloud with your Google Cloud Platform account') }}
          </p>
          <form>
            <div class="grid grid-cols-3 tablet:grid-cols-2 mobile:grid-cols-2 gap-30px tablet:gap-y-25px">
              <text-input
                  id="label"
                  type="text"
                  autocomplete="label"
                  :label="$t('Provider Label')"
                  :placeholder="$t('Label')"
                  v-model="provider.name"
                  readonly
              >
              </text-input>

              <div class="mt-auto">
                <button class="flex-1 flex justify-center items-center text-xl font-normal text-white
                                        min-h-60px p-2 px-25px w-full rounded-md bg-success-full" disabled>
                  <i class="xcloud mr-15px flex xc-confirm"></i>
                  {{ $t('Connected') }}
                </button>
              </div>
            </div>

            <div class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px my-10">
              <div class="mt-auto">
                <select-input
                    id="region"
                    :label="$t('Region')"
                    class="min-w-[250px]"
                    @change="getZones"
                    v-model="form.region"
                >
                  <option value="">
                      {{ $t('Choose Regions') }}
                  </option>
                  <option v-for="region in regions"
                          :value="region.region">
                    {{ region.name }}
                  </option>
                </select-input>
              </div>

              <div class="mt-auto">
                <select-input
                    id="zone"
                    :disabled="!getZones()"
                    class="min-w-[250px]"
                    :spin="form.processing && !regions && !getZones()"
                    v-on:change="getSizes()"
                    :label="$t('Zone')"
                    v-model="form.zone">
                  <option value="">{{ $t('Choose Zones') }}</option>
                  <option :value="zone.region" v-for="zone in getZones()">
                    {{ zone.region+' ( '+zone.name+' ) ' }}
                  </option>
                </select-input>
              </div>

              <div class="mt-auto">
                <select-input
                    id="server_size"
                    v-bind:disabled="!sizes"
                    :spin="form.processing && !sizes && regions"
                    class="min-w-[250px]"
                    label="Size"
                    v-model="form.size">
                  <option value="">{{ $t('Choose Sizes') }}</option>
                  <optgroup v-bind:label="description" v-for="(serverList, description) in sizes">
                    <option v-for="server in serverList" :value="server.slug" v-text="server.title"></option>
                  </optgroup>
                </select-input>
              </div>

              <div class="mt-auto">
                <text-input
                    v-bind:disabled="!form.size"
                    id="server_name"
                    v-model="form.name"
                    :error="form.errors.name"
                    :type="'text'"
                    autocomplete="name"
                    :label="$t('Server Name')"
                    :placeholder="$t('Server Name')">
                </text-input>
              </div>

              <div>
                <label
                    class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none">
                  {{ $t('Tags') }}
                </label>
                <multiselect
                    v-bind:disabled="!form.size"
                    id="tags"
                    class="w-full xcloud-multiselect"
                    :class="{ 'ring-1 ring-danger-light': tagsErrors()}"
                    v-model="form.tags"
                    mode="tags"
                    :close-on-select="false"
                    :searchable="true"
                    :create-option="true"
                    @select="onTagSelect"
                    :placeholder="$t('Select or create tags')"
                    regex="^[a-zA-Z0-9_ -]+$"
                    :options="tags"/>
                  <Error :error="tagsErrors()"/>
              </div>
            </div>

            <div class="flex flex-col gap-y-1">
              <label>
                <input type="checkbox" class="hidden peer" v-model="form.consent"/>
                <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4 before:bg-transparent
                           before:border-1 before:border-secondary-light  before:rounded before:mt-0.5 before:mr-2.5 before:text-xxxs
                           before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                           before:transition before:duration-200 peer-checked:before:border-success-full
                           peer-checked:before:bg-success-full peer-checked:before:text-white"
                      :class="{'before:border-danger' : form.errors.consent}"
                >
                    <span class="text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none flex items-center">
                        {{ $t('I have understood that the billing of this server will be handled on my server provider account.') }}
                    </span>
                </span>
              </label>
              <div class="mt-2 text-sm dark:text-light text-red-600 flex gap-2 items-center" v-if="form.errors.consent">
                <img :src="asset('img/eeror.svg')" alt="warning_img" class="h-4"/>
                <span class="mt-1 text-red-600">
                  {{ form.errors.consent }}
                </span>
              </div>
            </div>
          </form>
        </div>

        <WizardProgress :processing="form.processing" :back="route('server.create.gcp.credential.choose')" @next="submit"
                        :progress-width="progressWidth(4,4)" progress-text="Step 4 of 4">
          <template v-if="form.processing">
            <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
            <span>{{ $t('Verifying') }}</span>
          </template>
          <template v-else>
            <span>{{ $t('Finish') }}</span>
          </template>
        </WizardProgress>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useFlash } from "@/Composables/useFlash";
import SelectInput from "@/Shared/SelectInput.vue";
import TextInput from "@/Shared/TextInput.vue";
import WizardProgress from '@/Shared/WizardProgress.vue';
import { useHelpStore } from "@/stores/HelpStore";
import { useNavigationStore } from "@/stores/NavigationStore";
import { useForm } from "@inertiajs/inertia-vue3";
import Multiselect from "@vueform/multiselect/src/Multiselect.vue";
import {onMounted, reactive, ref, watch} from "vue";
import Label from "@/Jetstream/Label.vue";
import Error from "@/Shared/Error.vue";


let sizes = ref();

const props = defineProps({
  provider: '',
  regions: Object,
  zones: Object,
  tags: Object
});
const progressWidth = (start, end) => (start * 100) / (end ?? 1);

let navigation = useNavigationStore();
let helper = useHelpStore();

let form = useForm({
  provider: props.provider,
  project_id: '',
  credential: '',
  region: '',
  zone: '',
  size: '',
  name: '',
  tags: [],
  consent: false
});

let getZones = () => {
    if (!props.regions.length) return;
    return props.zones[form?.region];
}

let getSizes = () => {
  form.processing = true;
  axios.get(route('api.gcp.getSizes', {
    'provider': form.provider,
    'zone': form.zone,
  }), {
    params: {
      preserveScroll: true
    }
  })
      .then(response => {
        console.log(response.data);
        sizes.value = response.data.sizes;
      })
      .catch(error => {
        console.log(error);
        if (error.response && error.response.data) {
          useFlash().swal().fire({
            icon: 'error',
            title: 'Failed to fetch zones',
          });
        }
      }).finally(() => {
          form.processing = false;
    });
}

let submit = () => {
  form.post(route('api.server.store.gcp', {
    cloudProvider: form.provider
  }), {
    preserveScroll: true,
    onSuccess: () => {
      useFlash().success('Server created successfully');
    }
  });
}
const onTagSelect = (tag) => {
  //check does tag has contained any special character like #, @, $, %, ^, /, \, |, {, }, [, ], ~
  if (tag.match(/[!#@$%^&*()-+=<>\/\\|~`{}\[\]]/g)) {
    //remove from tag list
    form.tags = form.tags.filter((item) => item !== tag);
  }
}
const tagsErrors = function() {
    const errors = [];

    form.tags.forEach((tag, index) => {
        const error = form.errors[`tags.${index}`] ?? null;
        if (error) {
            errors.push(error);
        }
    });

    return errors.length ? errors[0] : null;
};
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
