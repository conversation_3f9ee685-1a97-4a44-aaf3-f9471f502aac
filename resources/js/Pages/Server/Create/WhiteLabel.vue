<template>
    <div class="xc-container">
        <div
            :class="
                navigation.isOpenHelpbar(helper.isOpen)
                    ? 'mr-400px small-laptop:mr-0'
                    : 'mr-0'
            "
            class="flex-1 flex items-center"
        >
            <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <div
                    class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                    <h2 class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark
                        dark:text-white mb-20px wide-mobile:text-3xl mobile:text-28px tablet:mb-15px wide-mobile:mb-2.5">
                        {{ $t('Set Up Your Server With') }}
                        <img
                            :src="$page?.props?.current_white_label?.brand_photo_url"
                            :alt="$page?.props?.current_white_label?.branding?.brand_name"
                            class="h-50px wide-mobile:h-40px inline-flex -mt-1"
                        />
                    </h2>
                    <p class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark text-center mb-50px mobile:mb-25px">
                        {{ $t('Fill in the details below to get your server set up with') }} {{$page?.props?.current_white_label?.branding?.brand_nam}}
                    </p>

                    <!-- Suggestion -->
                    <div class="grid grid-cols-1 w-full mb-5" v-if="purchasedProducts?.length > 0">
                      <div class="mb-10px small-laptop:mb-40px tablet:mb-30px wide-mobile:mb-20px">
                        <div v-for="(product,index) in purchasedProducts" :key="index"
                             class="flex flex-col gap-20px tablet:gap-15px wide-mobile:gap-10px mb-2"
                        >
                          <transition
                              leave-to-class="opacity-0"
                              enter-active-class="transition ease-out duration-300"
                              enter-from-class="opacity-0"
                              enter-to-class="opacity-100"
                              leave-active-class="transition ease-in duration-300"
                              leave-from-class="opacity-100"
                          >
                            <div class="warning flex flex-wrap gap-1 items-center justify-between bg-warning/20 px-6 py-4 rounded-md">
                              <div class="flex items-center">
                                <div class="flex items-center">
                                  <img :src="asset('img/note.svg')" alt="warning_img" class="w-6"/>
                                  <p class="text-dark dark:text-white leading-7 pl-3.5">
                                    <span class="font-bold">{{ product?.title }}</span>
                                    {{ $t('is pending for setup.') }}
                                  </p>
                                </div>
                              </div>
                              <div class="flex items-center gap-4">
                                <Link
                                    :href="product?.checkout_url"
                                    class="h-30px inline-flex items-center justify-center rounded border-1 border-solid border-success-full shrink-0 px-3 py-1 cursor-pointer bg-success-full dark:bg-success-dark text-white text-sm font-medium leading-snug transition duration-150 ease-in-out">
                                  {{ $t('Setup Now') }}
                                </Link>
                              </div>
                            </div>
                          </transition>
                        </div>
                      </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-medium text-dark dark:text-white leading-tight mb-15px">
                            {{ $t('Server Details') }}
                        </h3>
                        <div class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
                            <text-input
                                v-model="form.name"
                                :error="form.errors.name"
                                :label="$t('Server Name')"
                                :placeholder="$t('Server Name')"
                                type="text"
                            >
                            </text-input>

                            <div>
                                <label
                                    class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none">
                                    {{ $t('Server Tag(Optional)') }}
                                </label>
                                <multiselect
                                    :class="{
                                        'ring-1 ring-danger-light': tagsErrors()
                                    }"
                                    autocomplete="off"
                                    v-model="form.tags"
                                    :close-on-select="false"
                                    :create-option="true"
                                    :options="tagList"
                                    :searchable="true"
                                    regex="^[a-zA-Z0-9_ -]+$"
                                    :label="$t('Tags')"
                                    mode="tags"
                                    :placeholder="$t('Select or create tags')"
                                />
                                <Error :error="tagsErrors()"/>
                            </div>
                        </div>
                    </div>

                    <div class="mt-30px">
                        <h3 class="text-lg font-medium text-dark dark:text-white leading-tight mb-15px">
                            {{ $t('Server Type') }}
                        </h3>
                        <div class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
                            <!--v-if="serverType === 'general' && serverSizes?.length > 0"-->
                            <div
                                :class="{
                                    'border-primary-light ring-1 ring-primary-light':
                                        serverType === 'general',
                                    'border-secondary-light dark:border-mode-focus-light':
                                        serverType !== 'general',
                                }"
                                class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                                @click="serverType = 'general'"
                            >
                                <div class="shrink-0 inline-flex items-center justify-center">
                                    <img
                                        :src="
                                            asset('img/png/cloud-storage 1.png')
                                        "
                                        alt="cloud_logo"
                                        class="w-12 h-auto"
                                    />
                                </div>
                                <div class="flex flex-col gap-2">
                                    <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                        {{ $t('General') }}
                                    </h5>
                                    <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                        {{ $t('Cost-effective servers powered by Intel CPUs and regular SSDs.') }}
                                    </p>
                                </div>
                            </div>
                            <!--v-if="serverType === 'premium' && serverSizes?.length > 0"-->
                            <div
                                :class="{
                                    'border-secondary-light dark:border-mode-focus-light':
                                        serverType !== 'premium',
                                    'border-primary-light ring-1 ring-primary-light':
                                        serverType === 'premium',
                                }"
                                class="flex gap-5 items-center w-full rounded-xl border-1 border-solid px-6 py-4 cursor-pointer"
                                @click="serverType = 'premium'"
                            >
                                <div class="shrink-0 inline-flex items-center justify-center">
                                    <img
                                        :src="
                                            asset('img/png/cloud-server 2.png')
                                        "
                                        alt="cloud-2"
                                        class="w-12 h-auto"
                                    />
                                </div>
                                <div class="flex flex-col gap-2">
                                    <h5 class="text-xl font-medium leading-none tracking-tighter text-dark dark:text-white">
                                        {{ $t('Premium') }}
                                    </h5>
                                    <p class="text-sm font-normal leading-relaxed tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                        {{ $t('Blazing-fast servers with quicker 3GHz+ Intel Xeon CPUs, speedy memory, and NVMe storage.') }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-30px">
                        <div class="flex items-cent mb-15px">
                            <h3 class="text-lg font-medium text-dark dark:text-white leading-tight">
                                {{ $t('Server Size') }}
                            </h3>
                            <ul class="ml-auto inline-flex items-center list-disc marker:text-primary-light">
                                <li class="text-dark dark:text-white">
                                    {{ $t('Billed Monthly') }}
                                </li>
                            </ul>
                        </div>
                        <div
                            class="grid grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-5 mobile:gap-4"
                        >
                            <div
                                v-for="serverSize in serverSizes"
                                :key="serverSize.slug"
                                :class="{
                                    'border-primary-light ring-1 ring-primary-light':
                                        serverSize.id === form.product_id,
                                    'border-secondary-light dark:border-mode-focus-light':
                                        serverSize.id !== form.product_id,
                                }"
                                class="cursor-pointer   flex flex-col justify-center items-stretch w-full rounded-xl border-1 border-solid divide-y-1 divide-secondary-light dark:divide-mode-focus-light"
                                @click.prevent="updateServerSize(serverSize)"
                            >
                                <div class="flex items-center gap-3 p-4 justify-start">
                                    <h4 class="text-lg leading-none font-medium tracking-tighter text-dark dark:text-white">
                                        {{ serverSize?.title }}
                                    </h4>
                                    <tooltip v-if="serverSize?.tooltip" :title="serverSize?.tooltip">
                                        <span
                                            class="text-xs inline-flex text-secondary-full dark:text-mode-secondary-light">
                                            <i class="xcloud xc-info-2"></i>
                                        </span>
                                    </tooltip>
                                </div>
                                <div class="px-4 py-5">
                                    <ul class="list-disc marker:text-light flex flex-col gap-2">
                                        <li class="text-secondary-full dark:text-mode-secondary-light ml-3">
                                            {{ $t('RAM') }} -
                                            <span class="text-dark dark:text-white">
                                              {{
                                                    useUnitSize(serverSize?.memory)
                                                }}
                                            </span>
                                        </li>
                                        <li class="text-secondary-full dark:text-mode-secondary-light ml-3">
                                            {{ $t('SSD') }} -
                                            <span class="text-dark dark:text-white">
                                              {{ serverSize?.disk }} GB
                                            </span>
                                        </li>
                                        <li class="text-secondary-full dark:text-mode-secondary-light ml-3">
                                            {{ $t('vCPU') }} -
                                            <span class="text-dark dark:text-white">
                                              {{ serverSize?.cpu }}
                                            </span>
                                        </li>
                                        <li class="text-secondary-full dark:text-mode-secondary-light ml-3">
                                            {{ $t('Bandwidth') }} -
                                            <span class="text-dark dark:text-white">{{
                                                    useUnitSize(serverSize?.bandwidth, 'GB')
                                                }}</span>
                                        </li>
                                    </ul>
                                </div>
                                <div class="flex items-center gap-3 p-4 justify-start">
                                    <h4 class="text-lg leading-none font-medium tracking-tighter text-dark dark:text-white">
                                        ${{ serverSize?.price }}/
                                        <small class="text-secondary-full dark:text-mode-secondary-light">month</small>
                                    </h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="form.size" class="mt-30px">
                        <h3 class="text-lg font-medium text-dark dark:text-white leading-tight mb-15px">
                            {{ $t('Region') }}
                        </h3>
                        <div
                            class="grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-30px tablet:gap-y-25px"
                        >
                            <select-input
                                id="continent"
                                v-model="continent"
                                :label="$t('Choose Your Continent')"
                            >
                                <option
                                    v-for="regionContinent in continents"
                                    :key="regionContinent.replace(' ', '_').toLowerCase()"
                                    :value="regionContinent" v-text="regionContinent"
                                />
                            </select-input>
                            <Error :error="form.errors.region"/>
                        </div>

                        <div
                            class="grid grid-cols-7 wide-tablet:grid-cols-5 wide-mobile:grid-cols-3 mobile:grid-cols-2 gap-6 tablet:gap-5 mobile:gap-4 mt-6">
                            <div
                                v-for="region in regions.filter(re =>re.continent === continent)"
                                :key="region"
                                :class="{
                                    'border-primary-light ring-1 ring-primary-light':
                                        form.region === region.id,
                                    'border-secondary-light dark:border-mode-focus-light':
                                        form.region !== region.id,
                                }"
                                class="cursor-pointer flex flex-col justify-center items-center text-center rounded-md border-1 border-solid p-4 w-full"
                                @click.prevent="form.region = region.id"
                            >
                                <RegionFlag
                                    :region="{'country': region.country,'country_code':region.country.toLowerCase()}"
                                    :show-region="false"
                                    class-list="''"/>
                                <h5 class="text-sm font-normal leading-none tracking-tighter mt-4 text-dark dark:text-white">
                                    {{ region.city }}
                                </h5>
                                <p class="text-xxs font-medium leading-none tracking-tighter mt-2 text-secondary-full dark:text-mode-secondary-light">
                                    {{ region.country }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-30px">
                        <h3 class="text-lg font-medium text-dark dark:text-white leading-tight mb-15px">
                            {{ $t('Database') }}
                        </h3>

                        <div
                            class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-4 mobile:gap-4"
                        >
                            <!-- <select-input
                                id="database_type"
                                v-model="form.database_type"
                                :error="form.errors.database_type"
                                label="Select Database Server"
                                placeholder="Select Database Server">
                                <option
                                    :value="_database_type.value"
                                    v-for="_database_type in database_type_list"
                                    v-text="_database_type.label"
                                    :disabled="_database_type?.disabled">
                                </option>
                            </select-input> -->
                            <div>
                                <h3 class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none">
                                    {{ $t('Select Database Server') }}
                                </h3>
                                <Multiselect
                                    id="database_type"
                                    v-model="form.database_type"
                                    :class="{ 'ring-1 ring-danger-light': form.errors.database_type}"
                                    :placeholder="$t('Select Database Server')"
                                    :label="$t('Select Database Server')"
                                    :options="database_type_list.map( database=> ({value:database?.value,name:database.label, disabled: database?.disabled}))"
                                >
                                    <template v-slot:singlelabel="{ value }">
                                        <div class="multiselect-single-label rounded-md">
                                          <i class="xcloud text-2xl mr-2.5"
                                             :class="value.value === 'mysql8' ? 'xc-mysql' : 'xc-mariadb'"
                                          ></i> {{ value.name }}
                                        </div>
                                    </template>

                                    <template v-slot:option="{ option }">
                                        <i class="xcloud text-2xl mr-2"
                                           :class="{'xc-mysql' : option.value === 'mysql8', 'xc-mariadb' : option.value === 'mariadb10'}"
                                        ></i> {{ option.name }}
                                    </template>
                                </Multiselect>
                                <Error :error="form.errors.database_type" />
                            </div>
                        </div>
                    </div>
                    <div class="mt-30px">
                        <h3 class="text-lg font-medium text-dark dark:text-white leading-tight mb-15px">
                            {{ $t('Recommended Option') }}
                        </h3>

                        <div
                            class="grid grid-cols-2 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-4 tablet:gap-3"
                        >
                            <div
                                class="flex gap-4 items-center w-full rounded-md border-1 border-solid border-secondary-light dark:border-mode-focus-light px-5 py-4"
                            >
                                <div class="flex gap-3 items-center grow">
                                    <div class="h-40px w-40px shrink-0 rounded-full border-1 border-secondary-light
                                        dark:border-mode-focus-light inline-flex items-center justify-center text-2xl text-primary-light">
                                        <i class="xcloud xc-synchronization"></i>
                                    </div>
                                    <div class="flex flex-col gap-2">
                                        <h5 class="text-sm font-normal leading-none tracking-tighter text-dark dark:text-white">
                                            {{ $t('Backup') }}
                                        </h5>
                                        <p
                                            class="text-xxs font-medium leading-none tracking-tighter text-secondary-full dark:text-mode-secondary-light"
                                        >
                                            Enable Auto Backups
                                            (+${{ backupCost }}/month)
                                        </p>
                                    </div>
                                </div>
                                <label class="inline-flex outline-none">
                                    <input
                                        v-model="form.backups"
                                        :checked="form.backups"
                                        class="hidden peer"
                                        type="checkbox"
                                    />
                                    <span
                                        class="flex w-11 h-6 shrink-0 bg-secondary-light rounded-25px relative before:w-4 before:shrink-0
                                                before:h-4 before:bg-white before:rounded-full ease-in-out transition duration-75 outline-none
                                                before:transition-switch before:ease-in-out before:duration-75 before:absolute before:top-1
                                                before:left-1 peer-checked:bg-success-full peer-checked:before:left-6 peer-checked:before:bg-white
                                                cursor-pointer"
                                        @click.prevent="form.backups = !form.backups;"
                                    >
                                    </span>
                                </label>
                            </div>
                            <div
                                v-if="form?.email"
                                class="flex gap-4 items-center w-full rounded-md border-1 border-solid border-secondary-light
                                      dark:border-mode-focus-light px-5 py-4"
                            >
                                <div class="flex gap-3 items-center grow">
                                    <div class="h-40px w-40px shrink-0 rounded-full border-1 border-secondary-light dark:border-mode-focus-light
                                          inline-flex items-center justify-center text-xl text-primary-light">
                                        <i class="xcloud xc-mail"></i>
                                    </div>
                                    <div class="flex flex-col gap-2">
                                        <h5 class="text-sm font-normal leading-none tracking-tighter text-dark dark:text-white">
                                            {{ $t('Email') }}
                                        </h5>
                                        <p class="text-xxs font-medium leading-none tracking-tighter text-secondary-full dark:text-mode-secondary-light">
                                            {{ $t('Enable Auto Backups') }} (+${{ backupCost }}/month)
                                        </p>
                                    </div>
                                </div>
                                <label class="inline-flex outline-none">
                                    <input
                                        class="hidden peer"
                                        type="checkbox"
                                    />
                                    <span
                                        class="flex w-11 h-6 shrink-0 bg-secondary-light rounded-25px relative before:w-4 before:shrink-0 before:h-4 before:bg-white before:rounded-full ease-in-out transition duration-75 outline-none before:transition-switch before:ease-in-out before:duration-75 before:absolute before:top-1 before:left-1 peer-checked:bg-success-full peer-checked:before:left-6 peer-checked:before:bg-white cursor-pointer">
                                    </span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <label v-if="can_create_demo">
                        <input type="checkbox" class="hidden peer" v-model="form.demo"/>
                        <span class="flex mt-30px before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4 before:bg-transparent
                                             before:border-1 before:border-secondary-light  before:rounded before:mt-0.5 before:mr-2.5 before:text-xs
                                             before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                                             before:transition before:duration-200 peer-checked:before:border-success-full
                                             peer-checked:before:bg-success-full peer-checked:before:text-white">
                          <span
                              class="text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none flex items-center">
                             {{ $t('Demo Server for Billing Plan') }}
                          </span>
                      </span>
                    </label>

                    <StackInput v-model="form.stack" :error="form.errors.stack"/>

                    <div class="mt-30px">
                        <p class="text-lg font-medium leading-none tracking-tighter text-dark dark:text-white">
                            {{ $t('Total Cost') }} : <b class="text-[2rem]">${{ totalCost }}</b>/month
                        </p>
                    </div>
                </div>
                <WizardProgress
                    :back="route('server.create')"
                    :processing="form.processing"
                    :progress-width="progressWidth(2, 3)"
                    progress-text="Step 2 of 3"
                    @next="submit"
                >
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                        <span>{{ $t('Verifying') }}</span>
                    </template>
                    <template v-else>
                        <span>{{ $t('Next') }}</span>
                    </template>
                </WizardProgress>
            </div>
        </div>
    </div>

    <Modal
        :show="showDisableBackups"
        :widthClass="'max-w-850px'"
        :footer-button="true"
        :footer-button-title="$t('Disable Auto Backup')"
        title="Do you want to turn off Auto Backups?"
        @close="showDisableBackups = false">
        <div class="flex flex-col gap-30px">
            <div class="warning flex items-center bg-danger/20 px-6 py-4 rounded-md">
                <img :src="asset('img/warning.svg')" alt="warning_img"/>
                <p class="text-sm text-dark dark:text-white leading-7 pl-4">
                    <b>{{ $t('Warning') }}:</b> {{ $t('Disabling Auto Backups means losing easy data recovery, backup mobility, and disaster recovery; click to proceed if you understand the risks.') }}
                </p>
            </div>
        </div>
        <template #footer>
                <button
                    aria-expanded="true"
                    aria-haspopup="true"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                    @click.prevent="form.backups = !form.backups; showDisableBackups = false">
                    <span>{{ $t('Disable Auto Backup') }}</span>
                </button>
        </template>
    </Modal>
</template>

<script setup>
import TextInput from "@/Shared/TextInput.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import {useNavigationStore} from "@/stores/NavigationStore";
import {useHelpStore} from "@/stores/HelpStore";
import {computed, onMounted, ref, watch} from "vue";
import Multiselect from "@vueform/multiselect";
import Label from "@/Jetstream/Label.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import {asset} from "laravel-vapor";
import RegionFlag from "@/Shared/Icons/RegionFlag.vue";
import Modal from "@/Shared/Modal.vue";
import WizardProgress from "@/Shared/WizardProgress.vue";
import Error from "@/Shared/Error.vue";
import {useUnitSize} from "@/Composables/useUnitSize";
import {Inertia} from "@inertiajs/inertia";
import {useFlash} from "@/Composables/useFlash";
import StackInput from "@/Shared/StackInput.vue";
import Suggestion from "@/Pages/Migration/Components/Suggestion.vue";

let navigation = useNavigationStore();
let helper = useHelpStore();

const props = defineProps({
    sizes: Object,
    regions: Object,
    availableProducts: [Object, Array],
    billingActive: Boolean,
    provider: [Object,String],
    tagList: [Array, Object],
    selectedSize: [Array,Object],
    database_type: [Object,String],
    database_type_list: Object,
    supportManualBilling: Boolean,
    helpers: {
        type: Object,
        default: {},
    },
    can_create_demo: {
        type: Boolean,
        default: false,
    },
    purchasedProducts: {
        type: Object
    }
});


let showDisableBackups = ref(false);
const premiumSizes =  (
    props.sizes['premium'] && props.sizes['premium'].length > 0
) ?  props.sizes['premium'].map(size => size.slug) : [];
let serverType = ref(props.selectedSize && premiumSizes.includes(props.selectedSize['slug']) ? "premium" :"general");
const serverSizes = computed(() => {
    return props.sizes[serverType.value];
});
const getFirstServer = () => {
    if(props.selectedSize){
      return props.selectedSize;
    }
    return  undefined !== serverSizes.value ?  serverSizes.value[0] : [];
}
let chooseServer = ref(getFirstServer());
let regions = ref(props.regions[chooseServer.value?.slug] ?? []);
let continent = ref(regions.value[0]?.continent);

const continents = ref([...new Set(regions.value.map(region => region.continent))]);

let backupCost = ref(0);
//to check if the selected server is already buy
const isAvailableToUse = ref(false);
onMounted(() => {
    if(props.selectedSize){
        form.size = props.selectedSize['slug'];
        form.product_id = props.selectedSize['id'];
        // Initialize backup cost from selected size
        if (props.selectedSize?.backupCost) {
            backupCost.value = props.selectedSize.backupCost;
        }
    }
    updateServerSize(chooseServer.value);
});

watch(regions, (value) => {
    continents.value = [...new Set(regions.value.map(region => region.continent))];
    continent.value = regions.value[0]?.continent;
});

watch(chooseServer, (value) => {
    form.size = value?.slug;
    form.product_id = value?.id;
    // Get backup cost from backend via product dependents
    if (value?.backupCost) {
        backupCost.value = value.backupCost;
    }
    regions.value = props.regions[value?.slug] ?? [];
});

watch(continent, (value) => {
    form.region = regions.value.filter(region => region.continent === value)[0]?.id;
});


watch(serverType, (value) => {
    if (serverSizes.value && serverSizes.value?.length>0){
        chooseServer.value = serverSizes.value[0];
    }else {
        chooseServer.value = [];
    }
});

const totalCost = computed(() => {
    if (chooseServer.value) {
        let cost = isAvailableToUse.value ? 0 : chooseServer.value.price;
        if (form.backups) {
            cost += backupCost.value;
        }
        return cost;
    } else {
        return 0;
    }
});

let form = useForm({
    name: "",
    size: chooseServer.value?.slug,
    product_id: chooseServer.value?.id,
    region: regions.value[0]?.id,
    database_type: props.database_type,
    stack: "nginx",
    tags: [],
    backups: false,
    demo: false,
    continent: continents.value[0],
    email: ''
});
const progressWidth = (start, end) => (start * 100) / (end ?? 1);

const updateServerSize = (serverSize) => {
    if (Object.values(props.availableProducts).includes(serverSize?.slug) || props.billingActive || props.supportManualBilling){
        chooseServer.value = serverSize;
        isAvailableToUse.value = Object.values(props.availableProducts).includes(serverSize?.slug);

        // Set backup cost when server size is updated
        if (serverSize?.backupCost) {
            backupCost.value = serverSize.backupCost;
        }
    } else{
        useFlash().warning('You need to add a card and active your billing plan to use this server');
        //redirect to billing page
        Inertia.visit(route('user.bills-payment'));
    }
};

const submit = () => {
    form.post(
        route("api.server.store.white-label.vultr", {
            cloudProvider: props.provider.id,
        }),
        {
            preserveScroll: true,
        }
    );
};
const tagsErrors = function() {
    const errors = [];

    form.tags.forEach((tag, index) => {
        const error = form.errors[`tags.${index}`] ?? null;
        if (error) {
            errors.push(error);
        }
    });

    return errors.length ? errors[0] : null;
};

</script>

<style>
@import "@vueform/multiselect/themes/tailwind.css";
@import "flag-icons/css/flag-icons.min.css";
</style>
