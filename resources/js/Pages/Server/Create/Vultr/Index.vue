<template>
    <div class="xc-container">
        <div class="flex-1 flex items-center"
             :class="navigation.isOpenHelpbar(helper.isOpen) ? 'mr-400px small-laptop:mr-0' : 'mr-0'">
            <div class="max-w-1120px w-full mx-auto my-50px small-laptop:mb-50px small-laptop:mt-0">
                <div class="p-60px tablet:p-50px wide-mobile:p-40px mobile:p-25px bg-white dark:bg-mode-light rounded-10px">
                    <h2
                        class="section-title text-4xl text-center font-normal leading-snug tracking-tighter text-dark h-20
                               dark:text-white mb-20px wide-mobile:text-3xl mobile:text-28px tablet:mb-15px wide-mobile:mb-2.5">
                        {{ $t('Set Up Your Server With') }}
                        <img :src="asset('img/vultr-text.svg')" alt="vultr logo"
                             class="h-50px tablet:h-40px wide-mobile:h-30px inline-flex -mt-1 ml-2"/>
                    </h2>
                    <p class="text-base font-normal text-secondary-full dark:text-mode-secondary-dark text-center mb-50px mobile:mb-25px">
                        {{ $t('Connect xCloud with your Vultr Account') }}
                    </p>

                    <div class="grid grid-cols-3 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px mb-5">
                        <text-input
                            id="label"
                            type="text"
                            autocomplete="label"
                            :label="$t('Provider Label')"
                            :placeholder="provider.name"
                            readonly>
                        </text-input>

                        <div class="mt-auto">
                            <button class="flex-1 flex justify-center items-center text-xl font-normal text-white
                                        min-h-60px p-2 px-25px w-full rounded-md bg-success-full" disabled>
                                <i class="xcloud mr-15px flex xc-confirm"></i>
                                {{ $t('Connected') }}
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 tablet:grid-cols-2 mobile:grid-cols-1 gap-30px tablet:gap-y-25px">
                        <text-input
                            type="text"
                            :label="$t('Server Name')"
                            :placeholder="$t('Server Name')"
                            :error="form.errors.name"
                            v-model="form.name">
                        </text-input>

                        <select-input
                            id="server_size"
                            v-model="chooseServer"
                            :error="form.errors.size"
                            :label="$t('Server Size')">
                            <option value="" disabled>{{ $t('Choose Server Size') }}</option>
                            <optgroup
                                v-bind:label="key"
                                v-for="(sizeList, key) in sizes">
                                <option v-for="(size, key) in sizeList" :value="size">
                                    {{ size.title }}
                                </option>
                            </optgroup>
                        </select-input>

                        <select-input
                            id="region"
                            v-model="form.region"
                            :error="form.errors.region"
                            :label="$t('Choose region')">
                            <option value="" disabled>{{ $t('Choose Region') }}</option>
                            <option v-for="region in regions" :value="region.id">
                                {{ region.city + ' - '+ region.continent + ' ('+ region.country+ ') ' }}
                            </option>
                        </select-input>

                        <div>
                          <h3 class="block text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none">
                            {{ $t('Select Database Server') }}
                          </h3>
                          <Multiselect
                              id="database_type"
                              v-model="form.database_type"
                              :class="{ 'ring-1 ring-danger-light': form.errors.database_type}"
                              :placeholder="$t('Select Database Server')"
                              :label="$t('Select Database Server')"
                              :options="database_type_list.map( database=> ({value:database?.value,name:database.label, disabled: database?.disabled}))"
                          >
                            <template v-slot:singlelabel="{ value }">
                              <div class="multiselect-single-label rounded-md">
                                <i class="xcloud text-2xl mr-2.5"
                                   :class="value.value === 'mysql8' ? 'xc-mysql' : 'xc-mariadb'"
                                ></i> {{ value.name }}
                              </div>
                            </template>

                            <template v-slot:option="{ option }">
                              <i class="xcloud text-2xl mr-2"
                                 :class="{'xc-mysql' : option.value === 'mysql8', 'xc-mariadb' : option.value === 'mariadb10'}"
                              ></i> {{ option.name }}
                            </template>

                          </Multiselect>
                          <Error :error="form.errors.database_type" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2.5 text-secondary-full dark:text-mode-secondary-light leading-none">
                                {{ $t('Select Tags') }}
                            </label>
                            <multiselect
                                :label="$t('Tags')"
                                :class="{ 'ring-1 ring-danger-light': tagsErrors()}"
                                v-model="form.tags"
                                mode="tags"
                                :close-on-select="false"
                                :searchable="true"
                                :create-option="true"
                                :placeholder="$t('Select or create tags')"
                                regex="^[a-zA-Z0-9_ -]+$"
                                :options="tagList"/>
                            <Error :error="tagsErrors()"/>

                        </div>
                    </div>

                    <StackInput v-model="form.stack" :error="form.errors.stack"/>

                  <div class="my-6 flex flex-col gap-y-1">
                    <label  v-if="chooseServer">
                    <input type="checkbox" class="hidden peer" v-model="form.backups" :checked="form.backups"/>
                    <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4 before:bg-transparent
                           before:border-1 before:border-secondary-light before:rounded before:mt-0.5 before:mr-2.5 before:text-xxxs
                           before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                           before:transition before:duration-200 peer-checked:before:border-success-full
                           peer-checked:before:bg-success-full peer-checked:before:text-white">
                    <span class="text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none flex items-center">
                    {{ $t('Enable Vultr') }}
                    <a href="#" class="underline dark:decoration-mode-secondary-dark ml-1">{{ $t('Auto Backups') }}</a> &nbsp;
                    (+${{Math.ceil(chooseServer.price * .20)}}/month)
                    </span>
                    </span>
                    </label>
                    <label class="my-2">
                      <input type="checkbox" class="hidden peer" v-model="form.consent"/>
                      <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4 before:bg-transparent
                                             before:border-1 before:border-secondary-light  before:rounded before:mt-0.5 before:mr-2.5 before:text-xxxs
                                             before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                                             before:transition before:duration-200 peer-checked:before:border-success-full
                                             peer-checked:before:bg-success-full peer-checked:before:text-white"
                            :class="{'before:border-danger' : form.errors.consent}"
                      >
                          <span class="text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none flex items-center">
                              {{ $t('I have understood that the billing of this server will be handled on my server provider account.') }}
                          </span>
                      </span>
                    </label>
                    <label class="my-2">
                      <input type="checkbox" class="hidden peer" v-model="form.consent_documentation"/>
                      <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4 before:bg-transparent
                                             before:border-1 before:border-secondary-light  before:rounded before:mt-0.5 before:mr-2.5 before:text-xxxs
                                             before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                                             before:transition before:duration-200 peer-checked:before:border-success-full
                                             peer-checked:before:bg-success-full peer-checked:before:text-white"
                            :class="{'before:border-danger' : form.errors.consent_documentation}"
                      >
                          <span class="text-sm text-secondary-full font-medium dark:text-mode-secondary-light leading-none flex items-center">
                              {{ $t("I am sure that I've read the") }}
                              <a target="_blank"
                                 class="mx-1 underline font-bold"
                                 href="https://xcloud.host/docs/how-to-get-free-hosting-with-vultr/">
                                  {{ $t('documentation') }}
                              </a>
                              {{ $t('and added Any') }} <span class="font-bold mx-1">IPv4</span> {{ $t('and Any') }} <span class="font-bold mx-1"> IPv6 </span> {{ $t('both under Access Control.') }}
                          </span>
                      </span>
                    </label>
                    <div class="mt-2 text-sm dark:text-light text-red-600 flex gap-2 items-center" v-if="form.errors.consent_documentation">
                      <img :src="asset('img/eeror.svg')" alt="warning_img" class="h-4"/>
                      <span class="mt-1 text-red-600">
                        {{ form.errors.consent_documentation }}
                      </span>
                    </div>

                    <label v-if="can_create_demo">
                        <input type="checkbox" class="hidden peer" v-model="form.demo"/>
                        <span class="flex before:content-['\e927'] before:font-xc before:w-4 before:shrink-0 before:h-4 before:bg-transparent
                                           before:border-1 before:border-secondary-light  before:rounded before:mt-0.5 before:mr-2.5 before:text-xxxs
                                           before:inline-flex before:items-center before:justify-center before:text-transparent before:outline-none
                                           before:transition before:duration-200 peer-checked:before:border-success-full
                                           peer-checked:before:bg-success-full peer-checked:before:text-white">
                            <span class="text-sm font-medium text-secondary-full dark:text-mode-secondary-light leading-none flex items-center">
                               {{ $t('Demo Server for Billing Plan') }}
                            </span>
                        </span>
                    </label>
                  </div>
                </div>

                <WizardProgress :processing="form.processing" :back="route('server.choose.credential', 'vultr')" @next="submit"
                                :progress-width="progressWidth(2,3)" progress-text="Step 2 of 3">
                    <template v-if="form.processing">
                        <i class="xcloud xc-spinner mr-2.5 animate-spin"></i>
                        <span>{{ $t('Verifying') }}</span>
                    </template>
                    <template v-else>
                        <span>{{ $t('Next') }}</span>
                    </template>
                </WizardProgress>
            </div>
        </div>
    </div>
</template>

<script setup>

import TextInput from '@/Shared/TextInput.vue'
import SelectInput from '@/Shared/SelectInput.vue'
import WizardProgress from '@/Shared/WizardProgress.vue'
import {useForm} from "@inertiajs/inertia-vue3";
import {useNavigationStore} from "@/stores/NavigationStore";
import {useHelpStore} from "@/stores/HelpStore";
import {ref, watch} from "vue";
import Multiselect from "@vueform/multiselect";
import PasswordInput from "@/Shared/PasswordInput.vue";
import Label from "@/Jetstream/Label.vue";
import Error from "@/Shared/Error.vue";
import StackInput from "@/Shared/StackInput.vue";

let navigation = useNavigationStore();
let helper = useHelpStore();

const props = defineProps({
    sizes: Object,
    regions: Object,
    provider: Object,
    tagList: Array,
    database_type_list: Object,
    database_type: String,
    can_create_demo: {
        type: Boolean,
        default: false,
    },
    helpers: {
        type: Object,
        default: {}
    }
});

let form = useForm({
    name: '',
    size: '',
    region: '',
    database_type: props.database_type,
    stack: 'nginx',
    tags: [],
    backups: false,
    consent: false,
    consent_documentation: false,
    demo: false,
});

let chooseServer = ref('');
let regions = ref('');

watch(chooseServer, (value) => {
    form.size = value.slug;
    regions.value = props.regions[value.slug] !== undefined ? props.regions[value.slug] : ''
});

const progressWidth = (start, end) => (start * 100) / (end ?? 1);

const submit = () => {
    form.post(route('api.server.store.provider', {cloudProvider: props.provider.id}), {
        preserveScroll: true
    });
}
const tagsErrors = function() {
    const errors = [];

    form.tags.forEach((tag, index) => {
        const error = form.errors[`tags.${index}`] ?? null;
        if (error) {
            errors.push(error);
        }
    });

    return errors.length ? errors[0] : null;
};
</script>

<style>
@import '@vueform/multiselect/themes/tailwind.css';
</style>
