<template>
    <single-server
        active="Firewall Management"
        :server="server"
    >
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <!-- Firewall Management -->
            <div class="bg-focused dark:bg-mode-light p-2 pt-0 rounded-10px">
                <div class="info_head flex justify-between items-center px-6 py-3">
                    <h5 class="text-left text-lg font-normal text-dark dark:text-white">{{ $t('Firewall Management') }}</h5>
                    <button v-if="can_add_firewall_rule" @click.prevent="openAddNewRuleModal = true"
                            type="button"
                            class="inline-flex items-center justify-center rounded-10px focus:outline-0 border-transparent
                        shadow-none px-25px py-1 min-h-50px mobile:min-h-40px bg-success-full text-sm font-medium text-white"
                            aria-expanded="true" aria-haspopup="true"
                    >
                        <span>{{ $t('Add New Rule') }}</span>
                    </button>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full divide-y-1 divide-light dark:divide-mode-light">
                        <tbody class="bg-white rounded-md border-1 dark:border-0 border-light dark:bg-mode-base divide-y-1
                                  divide-light  dark:divide-mode-light text-dark dark:text-white dark:border-mode-base
                                  group-focus-within:border-none w-full focus:outline-none min-w-0 autofill:bg-light
                                  dark:autofill:bg-mode-base"
                        >
                        <tr class="divide-x-1 divide-light dark:divide-mode-light">
                            <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-left
                                    text-base font-normal h-50px"
                            >
                                {{ $t('Name') }}
                            </td>
                            <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-center
                                  text-base font-normal"
                            >
                                {{ $t('Protocol') }}
                            </td>
                            <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-center
                                  text-base font-normal"
                            >
                                {{ $t('Port') }}
                            </td>
                            <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-center
                                  text-base font-normal"
                            >
                                {{ $t('IP Address') }}
                            </td>
                            <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-center
                                  text-base font-normal"
                            >
                                {{ $t('Traffic') }}
                            </td>
                            <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-center
                                  text-base font-normal"
                            >
                                {{ $t('Active') }}
                            </td>
                            <td v-if="can_add_firewall_rule" class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-left
                                  text-base font-normal"
                            >
                                {{ $t('Action') }}
                            </td>
                        </tr>
                        <tr v-for="rule in firewallRules" :key="rule.id"
                            class="divide-x-1 divide-light dark:divide-mode-light">
                            <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                {{ rule?.name }}
                            </td>
                            <td class="px-30px tablet:px-20px py-2 text-base font-normal h-50px capitalize text-center">
                                {{ rule?.protocol }}
                            </td>
                            <td class="px-30px tablet:px-20px py-2 text-base font-normal h-50px text-center">
                                {{ rule?.port ?? '-' }}
                            </td>
                            <td class="px-30px tablet:px-20px py-2 text-base font-normal h-50px text-center">
                                {{ rule?.ip_address ?? '-' }}
                            </td>
                            <td class="px-30px tablet:px-20px py-2 text-base font-normal h-50px capitalize text-center flex justify-center">
                                <span
                                    class="text-dark dark:text-mode-secondary-light text-sm flex items-center justify-center leading-none">
                                  <span class="px-4 py-1.5 rounded-3xl"
                                        :class="{'text-success-full bg-success-full/10' : rule?.traffic === 'allow', 'text-danger bg-danger/10' : rule?.traffic === 'deny'}">
                                    <span v-text="rule?.traffic"></span>
                                  </span>
                                </span>
                            </td>

                            <td class="px-30px tablet:px-20px py-2 text-base font-normal h-50px capitalize text-center">
                                <tooltip :title="rule?.is_active ? 'Active' : 'Inactive'"
                                         :color="rule?.is_active ? 'success' : 'danger'"
                                >
                                   <span class="tooltipWrapper text-xs cursor-pointer ml-2"
                                         :class="{'text-success-full' : rule?.is_active, 'dark:text-white/40 text-black/40' : !rule?.is_active}"
                                   >
                                    ●
                                  </span>
                                </tooltip>
                            </td>
                            <td v-if="can_add_firewall_rule" class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                                <div class="flex gap-4">
                                    <tooltip title="Enable Rule" placement="top" color="primary" v-if="!rule.is_active">
                                        <button
                                            @click.prevent="enableFirewallRule(rule.id)"
                                            :disabled="disablingRule"
                                            :class="{'cursor-not-allowed opacity-50': disablingRule}"
                                        >
                                        <span class="text-sm flex text-secondary-light group-hover:text-white">
                                          <i class="xcloud xc-verify_dns text-info"
                                             :class="{'animate-spin opacity-50': enablingRule}"
                                          ></i>
                                        </span>
                                        </button>
                                    </tooltip>

                                    <tooltip title="Disable Rule" placement="top" color="warning"
                                             v-else-if="rule.is_active">
                                        <button
                                            :disabled="disablingRule"
                                            :class="{'cursor-not-allowed opacity-50': disablingRule}"
                                            @click.prevent="disableFirewallRule(rule.id)"
                                        >
                                        <span class="text-sm flex text-secondary-light group-hover:text-white">
                                          <i class="xcloud xc-close1 text-warning"
                                             :class="{'opacity-50': disablingRule}"
                                          ></i>
                                        </span>
                                        </button>
                                    </tooltip>

                                    <tooltip title="Delete Rule" placement="top" color="danger">
                                        <button
                                            :disabled="deletingFirewall"
                                            :class="{'cursor-not-allowed opacity-50': deletingFirewall}"
                                            @click.prevent="deleteFirewallRule(rule.id)"
                                        >
                                        <span class="text-sm flex text-secondary-light group-hover:text-white">
                                          <span class="text-sm flex text-secondary-light group-hover:text-white">
                                            <i class="xcloud xc-delete text-danger"
                                               :class="{'opacity-50': deletingFirewall}"
                                            ></i>
                                          </span>
                                        </span>
                                        </button>
                                    </tooltip>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="grid grid-cols-2 wide-tablet:grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
            <!-- Fail2Ban Banned IP List -->
            <div class="bg-white dark:bg-mode-light p-2 pt-0 rounded-10px">
                <div class="info_head flex justify-between items-center px-6 py-3">
                    <h5 class="text-left text-lg font-normal text-dark dark:text-white">{{ $t('Fail2Ban Management') }}</h5>
                    <button v-if="can_add_firewall_rule" @click.prevent="openBanIpModal = true"
                            type="button"
                            class="inline-flex items-center justify-center rounded-10px focus:outline-0 border-transparent
                        shadow-none px-25px py-1 min-h-50px mobile:min-h-40px bg-success-full text-sm font-medium text-white"
                            aria-expanded="true" aria-haspopup="true"
                    >
                        <span>{{ $t('Ban New IP Address') }}</span>
                    </button>
                </div>
                <template v-if="fetchingBannedIpAddress">
                    <skeleton
                        rows="1"
                        columns="1"
                    />
                </template>
                <table class="w-full divide-y-1 divide-light dark:divide-mode-light" v-else>
                    <tbody class="bg-white rounded-md border-1 dark:border-0 border-light dark:bg-mode-base divide-y-1
                          divide-light  dark:divide-mode-light text-dark dark:text-white dark:border-mode-base
                          group-focus-within:border-none w-full focus:outline-none min-w-0 autofill:bg-light
                          dark:autofill:bg-mode-base"
                    >
                    <tr class="divide-x-1 divide-light dark:divide-mode-light">
                        <td class="px-30px tablet:px-20px py-2 text-secondary-full dark:text-secondary-light text-left text-base font-normal h-50px">
                            {{ $t('Banned IP Addresses') }}
                        </td>
                    </tr>

                    <tr class="divide-x-1 divide-light dark:divide-mode-light"
                        v-if="bannedIpAddresses.length > 0"
                        v-for="bannedIpAddress in bannedIpAddresses"
                    >
                        <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                            {{ bannedIpAddress }}
                            <Tooltip title="Unban" class="float-right">
                                <button
                                    v-if="can_add_firewall_rule"
                                    @click.prevent="unbanIpAddress(bannedIpAddress)"
                                >
                      <span class="text-sm flex text-secondary-light group-hover:text-white">
                        <span class="text-sm flex text-secondary-light group-hover:text-white">
                          <i class="xcloud xc-delete text-danger"
                             :class="{'opacity-50': unbanningIp}"
                          ></i>
                        </span>
                      </span>
                                </button>
                            </Tooltip>
                        </td>
                    </tr>
                    <tr v-else-if="bannedIpAddressesErrorMessage" class="divide-x-1 divide-light dark:divide-mode-light">
                        <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px text-danger">
                            {{  bannedIpAddressesErrorMessage }}
                        </td>
                    </tr>
                    <tr v-else class="divide-x-1 divide-light dark:divide-mode-light">
                        <td class="px-30px tablet:px-20px py-2 text-left text-base font-normal h-50px">
                            {{ $t('No banned IP addresses.') }}
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </single-server>

    <!-- Add Firewall Rule Modal -->
    <Modal
        @close="openAddNewRuleModal = false"
        :loading="firewallRuleForm.processing"
        :show="openAddNewRuleModal"
        :footer-button="true"
        :title="$t('Add new firewall rule for')+' ' + server.name"
        :widthClass="'max-w-850px'"
    >
        <div class="flex flex-col gap-30px">
            <text-input
                v-model="firewallRuleForm.name"
                :error="firewallRuleForm.errors.name"
                :placeholder="$t('SSH')"
                type="text"
                :label="$t('Name')"
            />

            <text-input
                v-model="firewallRuleForm.port"
                :error="firewallRuleForm.errors.port"
                :placeholder="$t('port')"
                type="text"
                :label="$t('Port')"
                :note="$t('You can use multiple ports separated by comma. Leave empty to allow all ports. Also you can use a range of ports by using colon(i.e 6000:7000).')"
            />

            <text-input
                v-model="firewallRuleForm.ip_address"
                :error="firewallRuleForm.errors.ip_address"
                :type="'text'"
                :label="$t('IP Address (Optional)')"
                :placeholder="$t('Valid IP address')"
                :note="$t('You can use multiple IP addresses separated by comma. Leave empty to allow all IP addresses. Also you can use a subnet. (i.e *************, *************, *************/24)')"
            />

            <div class="flex flex-col gap-4 wide-mobile:flex-wrap">
                <div>
                    <p class="text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none">
                        {{ $t('Protocol') }}
                    </p>
                </div>

                <div class="flex gap-2 w-[30%]">
                    <radio-input v-model="firewallRuleForm.protocol"
                                 :error="firewallRuleForm.errors.protocol"
                                 value="any">
                        {{ $t('All') }}
                    </radio-input>
                    <radio-input v-model="firewallRuleForm.protocol"
                                 :error="firewallRuleForm.errors.protocol"
                                 value="tcp">
                        {{ $t('TCP') }}
                    </radio-input>
                    <radio-input v-model="firewallRuleForm.protocol"
                                 :error="firewallRuleForm.errors.protocol"
                                 value="udp">
                        {{ $t('UDP') }}
                    </radio-input>
                </div>
                <div class="flex w-full">
                    <Error :error="firewallRuleForm.errors.protocol"/>
                </div>
            </div>

            <div class="w-[30%] flex flex-col gap-4 wide-mobile:flex-wrap">
                <div>
                    <p class="text-base font-medium mb-2.5 text-secondary-full dark:text-light leading-none">
                        {{ $t('Traffic') }}
                    </p>
                </div>

                <div class="flex gap-2">
                    <radio-input
                        v-model="firewallRuleForm.traffic"
                        :error="firewallRuleForm.errors.traffic"
                        value="allow"
                    >
                        {{ $t('Allow') }}
                    </radio-input>
                    <radio-input
                        v-model="firewallRuleForm.traffic"
                        :error="firewallRuleForm.errors.traffic"
                        value="deny"
                    >
                        {{ $t('Deny') }}
                    </radio-input>
                </div>
            </div>
        </div>
        <template #footer>
                <button
                    @click.prevent="addNewFirewallRule()"
                    :disabled="firewallRuleForm.processing"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px
                            py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                    aria-expanded="true" aria-haspopup="true">
                    <span v-text="firewallRuleForm.processing ? $t('Adding Rule...') : $t('Add Rule')"></span>
                </button>
        </template>
    </Modal>

    <!-- Ban IP Address Modal -->
    <Modal
        @close="openBanIpModal = false"
        :loading="banIpAddressForm.processing"
        :show="openBanIpModal"
        :title="$t('Ban an IP Address on')+' ' + server.name"
        :widthClass="'max-w-850px'"
        :footer-button="true"
    >
        <div class="flex flex-col gap-30px">
            <text-input
                v-model="banIpAddressForm.ip_address"
                :error="banIpAddressForm.errors.ip_address"
                :placeholder="$t('IP Address')"
                type="text"
                :label="$t('IP Address')"
                :note="$t('Enter the IP address you want to ban. You can use comma to separate multiple IP addresses (e.g xx.xx.xx.xx, xx.xx.xx.xx)')"
            />
        </div>
        <template #footer>
          <button
              @click.prevent="banIpAddress()"
              :disabled="banIpAddressForm.processing"
              class="inline-flex items-center justify-center rounded-md border-transparent shadow-none min-h-50px px-25px
                            py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
              aria-expanded="true" aria-haspopup="true">
            <span v-text="banIpAddressForm.processing ? $t('Banning IP...') : $t('Ban IP')"></span>
          </button>
        </template>
    </Modal>
</template>
<script setup>
import TextInput from "@/Shared/TextInput.vue";
import SingleServer from "@/Pages/Server/SingleServer.vue";
import {Link, useForm} from "@inertiajs/inertia-vue3";
import {useFlash} from "@/Composables/useFlash";
import {onMounted, ref} from "vue";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Button from "@/Jetstream/Button.vue";
import Modal from "@/Shared/Modal.vue";
import RadioInput from "@/Shared/RadioInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import Error from "@/Shared/Error.vue";
import Card from "@/Shared/Card.vue";
import Switch from "@/Shared/Switch.vue";
import ServerActions from "@/Pages/Server/Components/ServerActions.vue";
import StateToolTip from "@/Shared/StateToolTip.vue";
import PaginationButton from "@/Shared/PaginationButton.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const props = defineProps({
    server: Object,
    firewallRules: Object,
    can_add_firewall_rule: Boolean,
})
const openAddNewRuleModal = ref(false);
let enablingRule = ref(false);
let disablingRule = ref(false);
let deletingFirewall = ref(false);
let bannedIpAddresses = ref([]);
let bannedIpAddressesErrorMessage = ref(null);
let fetchingBannedIpAddress = ref(false);
let unbanningIp = ref(false);
let openBanIpModal = ref(false);
let sites = ref(null);

onMounted(() => {
    fetchBannedIpAddress();
});

const firewallRuleForm = useForm({
    name: '',
    protocol: 'any',
    port: '',
    ip_address: '',
    traffic: 'allow'
});

const banIpAddressForm = useForm({
    ip_address: '',
});

const enableFirewallForm = useForm({
    id: null
});

const disableFirewallForm = useForm({
    id: null
});

const deleteFirewallForm = useForm({
    id: null
});

const unbanIpAddressForm = useForm({
    ipAddress: null
});

const addNewFirewallRule = () => {
    // clear existing errors
    firewallRuleForm.errors = {
        name: '',
        protocol: '',
        port: '',
        ip_address: '',
        traffic: ''
    };

    validateFirewallRule();

    if (Object.values(firewallRuleForm.errors).some(error => error !== '')) {
        return;
    }

    firewallRuleForm.post(route('api.firewall-rule.create', props.server.id), {
        preserveScroll: true,
        onSuccess: () => {
            openAddNewRuleModal.value = false;
            firewallRuleForm.reset();
        },
        onFinish: () => {
            openAddNewRuleModal.value = false;
        }
    });
}

const validateFirewallRule = () => {
    // ## Name validation ## //
    if (firewallRuleForm.name === '') {
        firewallRuleForm.errors.name = 'Name is required';
    }

    // ## Port Validation ## //
    // check if port contains a range(using colon), if contains a range, then protocol can not be all
    if (firewallRuleForm.port.includes(':') && firewallRuleForm.protocol === 'any') {
        firewallRuleForm.errors.protocol = 'Protocol must be TCP/UDP when port contains a range';
    }

    // valid port format: number, number:number, empty
    let portIsValid = /^(\d*)(:\d+)?$/.test(firewallRuleForm.port) || firewallRuleForm.port === '';
    if (!portIsValid) {
        firewallRuleForm.errors.port = 'Invalid port format. Valid format: number, number:number, empty';
    }

    // ## IP Address Validation ## //
    // valid ip format: ip, ip, ip/subnet, empty
    let ipIsValid = /^(\d{1,3}(\.\d{1,3}){3}(\/\d{1,2})?\s*,?\s*)+$/.test(firewallRuleForm.ip_address) || firewallRuleForm.ip_address === '';
    if (!ipIsValid) {
        firewallRuleForm.errors.ip_address = 'Invalid IP format. Valid format: ip, ip, ip/subnet, empty';
    }

    // ## If IP address is given, but port is empty, protocol can not be all ## //
    if (firewallRuleForm.ip_address !== '' && firewallRuleForm.port === '' && firewallRuleForm.protocol === 'any') {
        firewallRuleForm.errors.protocol = 'Protocol must be TCP/UDP when IP address is given';
    }
}

const disableFirewallRule = (firewallRule) => {
    useFlash().deleteConfirmation(
        {
            title: t('Are you sure you want to disable this firewall rule?'),
            text: t('You can enable it later.'),
            width: '600px',
            confirmButtonText: t('Yes, Disable')
        },
        () => {
            disablingRule.value = true;
            disableFirewallForm.post(route('api.firewall-rule.disable', [
                props.server.id,
                firewallRule
            ]), {
                preserveScroll: true,
                onSuccess: () => {
                    disablingRule.value = false;
                },
                onFinish: () => {
                    disablingRule.value = false;
                }
            });
        }
    );
}

const enableFirewallRule = (firewallRule) => {
    useFlash().deleteConfirmation(
        {
            title: t('Are you sure you want to enable this firewall rule?'),
            text: t('You can disable it later.'),
            width: '600px',
            confirmButtonText: t('Yes, Enable')
        },
        () => {
            enablingRule.value = true;
            enableFirewallForm.post(route('api.firewall-rule.enable', [
                props.server.id,
                firewallRule
            ]), {
                preserveScroll: true,
                onSuccess: () => {
                    enablingRule.value = false;
                },
                onFinish: () => {
                    enablingRule.value = false;
                }
            });
        }
    );
}

const deleteFirewallRule = (firewallRule) => {
    useFlash().deleteConfirmation(
        {
            title: t('Are you sure you want to delete this firewall rule?'),
            text: t('Deleting firewall rule will remove it permanently.'),
            width: '600px',
            confirmButtonText: t('Yes, Delete')
        },
        () => {
            deletingFirewall.value = true;
            deleteFirewallForm.post(route('api.firewall-rule.delete', [
                props.server.id,
                firewallRule
            ]), {
                preserveScroll: true,
                onSuccess: () => {
                    deletingFirewall.value = false;
                },
                onFinish: () => {
                    deletingFirewall.value = false;
                }
            });
        }
    );
}

const fetchBannedIpAddress = () => {
    fetchingBannedIpAddress.value = true;
    axios.get(route('api.server.fail2ban.banned-ip-addresses', props.server.id)).then(response => {
        bannedIpAddresses.value = response.data?.banned_ip_addresses ?? [];
        bannedIpAddressesErrorMessage.value = response.data?.error;
        fetchingBannedIpAddress.value = false;
    }).catch(error => {
        bannedIpAddresses.value = [];
        fetchingBannedIpAddress.value = false;
    });
}
const unbanIpAddress = (ipAddress) => {
    useFlash().deleteConfirmation(
        {
            title: t('Are you sure you want to unban')+' ' + ipAddress + '?',
            text: t('This will remove the IP address from the banned list.'),
            width: '600px',
            confirmButtonText: t('Yes, Unban')
        },
        () => {
            unbanIpAddressForm.ipAddress = ipAddress;
            unbanningIp.value = true;
            unbanIpAddressForm.post(route('api.server.fail2ban.unban-ip-address', [
                props.server.id,
            ]), {
                preserveScroll: true,
                onSuccess: () => {
                    unbanningIp.value = false;
                    fetchBannedIpAddress();
                },
                onFinish: () => {
                    unbanningIp.value = false;
                }
            });
        }
    );
}

const banIpAddress = () => {
    // clear existing errors
    banIpAddressForm.errors = {
        ip_address: ''
    };

    // validation
    if (banIpAddressForm.ip_address === '') {
        banIpAddressForm.errors.ip_address = 'IP Address is required';
        return;
    }

    banIpAddressForm.post(route('api.server.fail2ban.ban-ip-address', props.server.id), {
        preserveScroll: true,
        onSuccess: () => {
            openBanIpModal.value = false;
            banIpAddressForm.reset();
            fetchBannedIpAddress();
        },
        onFinish: () => {
            openBanIpModal.value = false;
        }
    });
}

</script>
