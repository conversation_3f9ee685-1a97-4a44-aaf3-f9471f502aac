<template>
    <Head :title='$inertia.page.component.split("/")[1] + " - " + server.name'/>

    <SingleLayout>
        <template v-slot:header>
            <div class="basis-250px small-laptop:basis-50px shrink-0 bg-white dark:bg-mode-light mr-2px p-30px
                        small-laptop:px-10px rounded-tl-lg">
                <Link href="/server" class="inline-flex">
                    <span class="bg-light dark:bg-mode-base rounded-md inline-flex items-center justify-center text-xs
                        text-secondary-light ease-in-out duration-300 w-30px h-30px shrink-0">
                        <i class="xcloud xc-angle_left"></i>
                    </span>

                    <span class="text-xl ml-10px text-secondary-full hover:text-dark dark:hover:text-white small-laptop:hidden">
                        {{ $t('Back To Servers') }}
                    </span>
                </Link>
            </div>
            <div class="grow flex items-center p-30px bg-white dark:bg-mode-light rounded-tr-lg gap-20px wide-mobile:flex-wrap
                    mobile:p-20px mobile:gap-10px w-[calc(theme(width.full)-theme(spacing.50px))]">
                <div class="flex items-center">
                    <tooltip
                        class="mr-15px shrink-0"
                        :title="server.provider_readable"
                    >
                        <span class="w-50px tablet:w-40px mobile:w-30px">
                            <img :src="cloudProviderIcon" alt="h-full" />
                        </span>
                    </tooltip>
                    <div class="flex flex-col">
                        <div class="flex">
                            <h2 class="text-3xl tablet:text-2xl mobile:text-lg text-dark dark:text-white mb-10px tablet:mb-1
                                mobile:mb-0 leading-none">
                                <CopyAbleText
                                    :text="server.name"
                                    align="top"
                                    color="primary"
                                />
                            </h2>
                            <tooltip
                                :class="
                                    server.is_connected
                                        ? 'text-success-full'
                                        : 'dark:text-white/40 text-black/40'
                                "
                                class="text-xs cursor-pointer ml-2"
                                :title="
                                    server.is_connected
                                        ? 'Connected'
                                        : 'Disconnected'
                                "
                            >
                                ●
                            </tooltip>
                        </div>
                        <div class="flex gap-x-15px gap-y-1.5 flex-wrap">
                            <span class="text-dark dark:text-mode-secondary-light text-sm flex items-center leading-none">
                                <span :class="
                                        server.is_connected
                                            ? 'text-success-full bg-success-full/10'
                                            : 'bg-danger dark:bg-danger/10 text-white dark:text-danger'
                                    "
                                    class="px-6 py-1.5 rounded-3xl"
                                >
                                    <span v-if="server.is_connected">Connected</span>
                                    <span v-else>Disconnected</span>
                                </span>
                            </span>
                            <span v-if="server.sites_count"
                                class="text-dark dark:text-mode-secondary-light text-sm flex items-center leading-none"
                            >
                                <span class="w-3.5 mobile:w-2.5 mr-2">
                                    <TotalSitesLogo></TotalSitesLogo>
                                </span>
                                <span>
                                    {{ $t('Total Sites') }}: {{ server.sites_count }}
                                </span>
                            </span>

                            <span
                                v-if="server.team"
                                class="text-dark dark:text-mode-secondary-light text-sm flex items-center leading-none"
                            >
                                <span class="w-3.5 mobile:w-2.5 mr-2">
                                    <i class="text-primary-light xcloud xc-team"></i>
                                </span>
                                <a v-if="$inertia.page.props.auth_user?.role === 'super_admin'"
                                    :href="'/admin/resources/teams/' + server.team.id"
                                    target="_blank"
                                        class="text-dark dark:text-mode-secondary-light hover:text-primary-light
                                         dark:hover:text-primary-light text-sm flex items-center leading-none"
                                >
                                  <span>
                                    {{ $t('Team') }}: {{ server.team.name }}
                                  </span>
                                </a>
                                <span v-else>
                                    {{ $t('Team') }}: {{ server.team.name }}
                                </span>
                            </span>

                            <button @click.prevent="refreshConnectionStatus"
                                class="text-dark dark:text-mode-secondary-light hover:text-primary-light
                                       dark:hover:text-primary-light text-sm flex items-center leading-none"
                            >
                                <span class="w-4 mobile:w-3 mr-2 flex">
                                    <i class="xcloud xc-verify_dns animate-spin text-primary-light pt-[0.02rem]"
                                        v-if="isProcessingConnection === true"
                                    >
                                    </i>
                                    <i v-else
                                        class="xcloud xc-verify_dns text-primary-light pt-[0.02rem]"
                                    >
                                    </i>
                                </span>
                                <span> {{ $t('Refresh') }} </span>
                            </button>

                            <div
                                class="flex items-center"
                                v-if="server.tags.length > 0"
                            >
                                <span class="text-sm text-dark dark:text-mode-secondary-light rounded-md">
                                    #{{ server.tags[0]?.name }}
                                </span>
                                <tooltip
                                    class="text-xs cursor-pointer ml-2"
                                    :title="
                                        server.tags
                                            .slice(1)
                                            .map((tag) => tag.name)
                                            .join(', ')
                                    "
                                >
                                    <h6
                                        class="text-sm text-primary-dark dark:text-primary-light"
                                        v-if="server.tags.length > 1"
                                    >
                                        +{{ server.tags.length - 1 }}
                                    </h6>
                                </tooltip>
                            </div>
                        </div>
                        <!-- Load Server Tags -->
                        <!-- <div class="flex gap-2 mt-10px flex-wrap">
                            <span
                                v-for="tag in server.tags"
                                :key="tag.id"
                                class="text-xs p-2 text-secondary-full dark:text-mode-secondary-light bg-light dark:bg-mode-base px-5px py-2px rounded-md"
                            >
                                {{ tag.name }}
                            </span>
                        </div> -->
                    </div>
                </div>
                <div class="relative ml-auto flex justify-end gap-2.5 items-center flex-wrap">
                    <button
                        @click.prevent="lowSpaceModal = true"
                        v-if="server.monitoring.disk.isLow && props.server?.permissions?.includes('site:create')"
                        class="whitespace-nowrap inline-flex items-center justify-center rounded-10px border-success-full
                              shadow-none min-h-50px wide-mobile:min-h-40px px-25px wide-mobile:px-20px bg-success-full text-base wide-mobile:text-sm text-white focus:outline-0 wide-mobile:hidden"
                    >
                        <span class="inline-flex mr-2 text-sm">
                            <i class="xcloud xc-add"></i>
                        </span>
                        {{ $t('New Site') }}
                    </button>
                    <Link
                        v-if="!server.monitoring.disk.isLow && props.server?.permissions?.includes('site:create') && can_add_site"
                        :href="route('site.create', {
                        server: server.id
                    })"
                        class="whitespace-nowrap inline-flex items-center justify-center rounded-10px border-success-full
                                  shadow-none min-h-50px wide-mobile:min-h-40px px-25px wide-mobile:px-20px bg-success-full
                                  text-base wide-mobile:text-sm text-white focus:outline-0 wide-mobile:hidden"
                    >
                        <span class="inline-flex mr-2 text-sm">
                            <i class="xcloud xc-add"></i>
                        </span>
                        {{ $t('New Site') }}
                    </Link>
                    <ServerActions
                        :server="server"
                        position="right"
                        :title="$t('Actions')"
                        :can_add_site="props.server?.permissions?.includes('site:create')"
                        :can_restart_server="can_restart_server"
                        :can_restart_nginx_server="can_restart_nginx_server"
                        :can_restart_mysql_server="can_restart_mysql_server"
                        :can-delete-server="props.server?.permissions?.includes('server:delete')"
                        :can_clone_server="can_clone_server"
                        :can_archive_server="props.server?.permissions?.includes('server:archive')"
                    />
                </div>
            </div>
        </template>

        <template v-slot:sidebar>
            <Sidebar @updateSearch="handleSearch" :filteredItems="filteredMenuItems" :items="sidebarItems"></Sidebar>
        </template>

        <template v-slot:content>
            <div class="flex py-20px divide-x-2 divide-white/10 dark:divide-transparent bg-primary-light
                    dark:bg-dark w-full tablet:grid tablet:grid-cols-1 tablet:gap-30px tablet:divide-x-0 mobile:gap-15px"
            >
                <div class="flex divide-x-2 divide-white/10 dark:divide-transparent grow mobile:flex-col
                      mobile:divide-x-0 mobile:gap-15px">
                    <div class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px">
                        <h4 class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase
                              leading-none mobile:basis-1/2">
                            {{ $t('IP Address') }}
                        </h4>
                        <h5 class="text-white text-base leading-none inline-flex">
                            <CopyAbleText
                                v-if="server.public_ip"
                                :text="server.public_ip"
                                color="success"
                            />
                            <span v-else class="text-white text-base leading-none">
                                -
                            </span>
                        </h5>
                    </div>
                    <div v-if="server?.plan_title" class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px">
                        <h4 class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase leading-none mobile:basis-1/2">
                            {{ $t('Plan') }}
                        </h4>
                        <h5 class="text-white text-base leading-none">
                            <tooltip align="bottom" :title="server?.plan_title">
                            {{ server?.plan_title?.slice(0, 18) + ".." }}
                            </tooltip>
                        </h5>
                    </div>
                    <div class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px">
                        <h4 class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase leading-none mobile:basis-1/2">
                            {{ server?.sku ? $t('Size') : $t('RAM') }}
                        </h4>
                        <h5 class="text-white text-base leading-none">
                            {{ server?.sku ?? `${useUnitSize(server?.monitoring?.ram?.total,'MB','GB')}` }}
                        </h5>
                    </div>
                </div>
                <div class="flex divide-x-2 divide-white/10 dark:divide-transparent grow mobile:flex-col mobile:divide-x-0 mobile:gap-15px">
                    <div class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px">
                        <h4 class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase leading-none mobile:basis-1/2">
                            {{ $t('WEB SERVER') }}
                        </h4>
                        <div class="flex flex-row items-center gap-5px">
                            <img
                                :src="{
                                        'openlitespeed': asset('img/openlitespeed.svg'),
                                        'nginx': asset('img/nginx-logo.svg'),
                                    }[server.stack]"
                                :alt="server.stack"
                                class="w-25px"
                            />
                            <h5 class="text-white text-base leading-none">
                                {{  server.stack_readable }}
                            </h5>
                        </div>
                    </div>
                    <div class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px">
                        <h4 class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase leading-none mobile:basis-1/2">
                            {{ $t('Database') }}
                        </h4>
                        <div class="flex flex-row items-center gap-5px">
                            <span class="inline-flex items-center justify-center text-white text-2xl">
                                <i class="xcloud"
                                   :class="server.database_type_readable === 'MySQL' ? 'xc-mysql' : 'xc-mariadb'"
                                ></i>
                            </span>
                            <h5 class="text-white text-base leading-none"
                                v-text="server.database_type_readable"
                            >
                            </h5>
                        </div>
                    </div>
                    <div class="flex-1 justify-start pl-50px small-laptop:pl-30px mobile:flex mobile:pl-20px mobile:pr-20px">
                        <h4 class="text-white dark:text-mode-secondary-light text-base mb-15px mobile:mb-0 uppercase leading-none mobile:basis-1/2">
                            {{ server?.provider ? $t('Region') : $t('Ubuntu') }}
                        </h4>
                        <h5 v-if="server?.provider" class="text-white text-base leading-none">
                            {{ server.location }} <span v-if="server.region">({{server.region}})</span>
                        </h5>
                        <h5 v-else-if="server?.monitoring" class="text-white text-base leading-none">
                            {{ server.ubuntu_version }}
                        </h5>
                    </div>
                </div>
            </div>
            <div class="flex items-center gap-20px px-50px small-laptop:px-30px py-15px rounded-br-lg mobile:px-20px mobile:flex-col mobile:items-start mobile:gap-10px"
                :class="{
                    'bg-warning/20': server.monitoring.disk.isLow,
                    'bg-danger/90': server.monitoring.disk.isCritical,
                    'bg-primary-dark dark:bg-mode-focus-dark': !server.monitoring.disk.isLow && !server.monitoring.disk.isCritical,
                }"
            >
                <div>
                    <span
                        class="text-base inline-flex items-center"
                        :class="{
                            'dark:text-mode-secondary-light text-white': !server.monitoring.disk.isLow,
                            'text-dark dark:text-light': server.monitoring.disk.isLow || server.monitoring.disk.isCritical
                        }"
                    >
                        <img
                            :src="asset('img/danger_white.svg')"
                            alt="critical"
                            class="w-6 mr-2.5"
                            v-if="server.monitoring.disk.isCritical"
                        />
                        <img
                            :src="asset('img/warning.svg')"
                            alt="warning_img"
                            class="w-6 mr-2.5"
                            v-else-if="server.monitoring.disk.isLow"
                        />
                        {{ $t('Disk Usage') }}:<span
                            class="font-bold px-1"
                            :class="{
                                'text-warning': server.monitoring.disk.isLow,
                                'dark:text-white': !server.monitoring.disk.isLow,
                            }" >{{ server.monitoring.disk.used }} GB</span
                        >of<span class="font-bold px-1 dark:text-white">{{
                            server.monitoring.disk.total
                        }} GB</span>
                        used</span
                    >
                </div>
                <div class="ml-auto mobile:ml-0">
                    <span class="text-base inline-flex items-center"
                        :class="{
                            'dark:text-mode-secondary-light text-white':
                                !server.monitoring.disk.isLow,
                            'text-dark dark:text-light': server.monitoring.disk.isLow,
                        }">
                      {{ $t('Checked') }}: {{ server.monitoring.last_checked }}
                        <span v-if="props.server?.permissions?.includes('server:manage-services')"
                            class="ml-1 inline-flex rounded-full h-25px w-25px shrink-0 items-center justify-center"
                        >
                            <i class="xcloud xc-verify_dns"
                                :class="{
                                    'animate-spin text-white':
                                        serverStore.isMonitoringLoading,
                                }"
                            >
                            </i>
                        </span>
                        <button v-if="props.server?.permissions?.includes('server:manage-services')"
                            @click="updateMonitoring()"
                            href="#"
                            :disabled="serverStore.isMonitoringLoading"
                            class="font-bold pl-1 underline dark:text-white"
                        >
                            {{ $t('Check Again') }}
                        </button>
                    </span>
                </div>
            </div>
            <div v-if="server?.reboot_require" class="grid grid-cols-1 gap-30px pl-30px pt-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">
                <upgradeable-servers :show_title="false" :servers="[server]" />
            </div>
            <slot></slot>
        </template>
    </SingleLayout>

    <Modal
        @close="lowSpaceModal = false"
        :show="lowSpaceModal"
        :title="server?.name + ' Disk Space Low'"
        :widthClass="'max-w-850px'"
    >
        <div class="flex flex-col gap-30px">
            <div class="warning flex items-center bg-warning/20 px-6 py-4 rounded-md">
                <img :src="asset('img/warning.svg')" alt="warning_img" class="w-6" />
                <p class="text-sm text-dark dark:text-white leading-7 pl-3.5">
                    <b>{{ $t('Warning') }}:</b> {{ $t('Your disk space is low. Please upgrade your plan to avoid any downtime.') }}
                </p>
            </div>
        </div>
        <template #footer>
            <div class="px-50px py-30px wide-mobile:px-30px wide-mobile:py-20px">
                <button
                    @click.prevent="lowSpaceModal = false"
                    class="inline-flex items-center justify-center rounded-md border-transparent shadow-none
                          min-h-50px px-25px py-2px text-base text-white bg-success-full focus:outline-0 disabled:cursor-not-allowed disabled:opacity-50"
                    aria-expanded="true" aria-haspopup="true"
                >
                    <span>{{ $t('Close') }}</span>
                </button>
            </div>
        </template>
    </Modal>
</template>

<script setup>
import { useFlash } from "@/Composables/useFlash";
import { useServer } from "@/Composables/useServer.js";
import SingleLayout from "@/Layouts/SingleLayout.vue";
import ServerActions from "@/Pages/Server/Components/ServerActions.vue";
import CopyAbleText from "@/Shared/CopyAbleText.vue";
import TotalSitesLogo from "@/Shared/Icons/TotalSitesLogo.vue";
import Modal from "@/Shared/Modal.vue";
import Sidebar from "@/Shared/Sidebar.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import { useServerStore } from "@/stores/ServerStore.js";
import { Link } from "@inertiajs/inertia-vue3";
import { reactive, ref, computed, watch } from "vue";
import { useCloudProviderIcon } from "@/Composables/useCloudProviderIcon.js";
import {useUnitSize} from "@/Composables/useUnitSize";
import {asset} from "laravel-vapor";
import Fuse from 'fuse.js';
import UpgradeableServers from "@/Shared/Server/UpgradeableServers.vue";
import { useI18n } from "vue-i18n";
const { t } = useI18n();

const searchKeyword = ref('');

const props = defineProps({
    server: Object,
    active: String,
    canDeleteServer: Boolean,
    can_add_site: Boolean,
    can_restart_server: Boolean,
    can_restart_nginx_server: Boolean,
    can_restart_mysql_server: Boolean,
    can_clone_server: Boolean,
});

const emit = defineEmits(['update:filteredItems']);
const { cloudProviderIcon } = useCloudProviderIcon( props.server.provider_name);

let isProcessingConnection = ref(false);
let lowSpaceModal = ref(false);

let refreshConnectionStatus = () => {
    isProcessingConnection.value = true;
    axios
        .post(route("api.server.test", props.server.id))
        .then(
            (response) =>
                (props.server.is_connected = response.data.is_connected)
        )
        .catch((error) =>
            useFlash().error(
                error?.response?.data?.message
                    ? error.response.data.message
                    : error
            )
        )
        .finally(() => (isProcessingConnection.value = false));
};

const sidebarItems = reactive({
    activeItem: props.active,
    data: [
        {
            title: t("Site List"),
            name: "Site List",
            icon: "xc-browser",
            url: "/server/" + props.server.id + "/sites",
            isShow: true,
            keyword: ["Site List"]
        },
        {
            title: t("Management"),
            name: "Management",
            icon: "xc-data-network",
            isShow: props.server?.permissions?.includes("server:manage-services") || props.server?.permissions?.includes("server:manage-databases") || props.server?.permissions?.includes("server:cron-job") || props.server?.permissions?.includes("server:manage-php") || props.server?.permissions?.includes("server:manage-access"),
            subItems: [
                {
                    title: t("Settings"),
                    name: "Settings",
                    icon: "xc-data-network",
                    url: "/server/" + props.server.id + "/server-settings",
                    isShow: props.server?.permissions?.includes("server:manage-services"),
                    keyword: ["Upgrade Server", "Server Utilities", "Archive your Server", "Delete your Server", "MySQL Restart", "Nginx Restart", "Redis Restart", "PHP Restart"]
                },
                {
                    title: t("Database"),
                    name: "Database",
                    icon: "xc-database",
                    url: "/server/" + props.server.id + "/database",
                    isShow: props.server?.permissions?.includes("server:manage-databases"),
                    keyword: ["Database Info", "Database Name", "Database Users"]
                },
                {
                    title: t("Cron Jobs"),
                    name: "Cron Jobs",
                    icon: "xc-cron-job",
                    url: "/server/" + props.server.id + "/cron-job",
                    isShow: props.server?.permissions?.includes("server:cron-job"),
                    keyword: ["Cron Job", "Add Cron Job"]
                },
                {
                    title: t("Supervisor"),
                    name: "Supervisor",
                    icon: "xc-process",
                    url: "/server/" + props.server.id + "/supervisor",
                    isShow: props.server?.permissions?.includes("server:manage-services"),
                    keyword: ["Supervisor", "Process Management", "Background Processes", "Daemon", "Supervisor Processes", "Add Supervisor Process"]
                },
                {
                    title: t("PHP Configuration"),
                    name: "PHP Configuration",
                    icon: "xc-php",
                    url: "/server/" + props.server.id + "/php-configuration",
                    isShow: props.server?.permissions?.includes("server:manage-php"),
                    keyword: [
                        "Update PHP Configuration", "PHP Version", "Max Execution Time", "Max Input Time", "Max Input Vars", "Memory Limit", "Post Max Size", "Max File Upload Size", "Session GC Maxlifetime", "Node", "Node Version"]
                },
                {
                    title: t("Sudo Users"),
                    name: "Sudo Users",
                    icon: "xc-team",
                    url: "/server/" + props.server.id + "/sudo",
                    isShow: props.server?.permissions?.includes("server:manage-access"),
                    keyword: ["Add Sudo User", "Sudo Users"]
                },
                {
                    title: t("Commands"),
                    name: "Commands",
                    icon: "xc-analytics",
                    url: "/server/" + props.server.id + "/command-runner",
                    isShow: props.server?.permissions?.includes("server:custom-command-runner"),
                    keyword: ["Commands", "Command Runner", "Custom Command"]
                },
            ]
        },
        {
            title: t("Monitoring"),
            name: "Monitoring",
            icon: "xc-analytics-1",
            isShow: props.server?.permissions?.includes("server:manage-logs-and-events"),
            subItems: [
                {
                    title: t("Monitoring"),
                    name: "Monitoring",
                    icon: "xc-analytics-1",
                    url: "/server/" + props.server.id + "/monitoring",
                    isShow: props.server?.permissions?.includes("server:manage-logs-and-events"),
                    keyword: ["Monitoring", "Server Health", "Ram", "CPU Usage", "Hard Disk Usage", "Uptime Overview", "Server Statistics", "Individual Site Details"]
                },
                {
                    title: t("Logs"),
                    name: "Logs",
                    icon: "xc-data-transfer",
                    url: "/server/" + props.server.id + "/logs",
                    isShow: props.server?.permissions?.includes("server:manage-logs-and-events"),
                    keyword: ["Nginx Error Log", "Nginx Access Log"]
                },
                {
                    title: t("Events"),
                    name: "Events",
                    icon: "xc-analytics",
                    url: "/server/" + props.server.id + "/events",
                    isShow: props.server?.permissions?.includes("server:manage-logs-and-events"),
                    keyword: ["Actions", "Events"]
                },
            ]
        },
        {
            title: t("Security"),
            name: "Security",
            icon: "xc-keys",
            isShow: props.server?.permissions?.includes("server:manage-firewall"),
            subItems: [
                {
                    title: t("Firewall Management"),
                    name: "Firewall Management",
                    url: "/server/" + props.server.id + "/firewall-management",
                    isShow: props.server?.permissions?.includes("server:manage-firewall"),
                    keyword:["Server security", "Firewall Management", "Fail2ban management", "Ban New IP Address", "Banned IP Addresses"]
                },
                {
                    title: t("Vulnerability Settings"),
                    name: "Vulnerability Settings",
                    url: "/server/" + props.server.id + "/vulnerability-settings",
                    isShow: props.server?.permissions?.includes("server:vulnerability-settings"),
                    keyword: ["Vulnerability Settings", "Security Settings", "Security Vulnerability"]
                },
                {
                    title: t("Security Update"),
                    name: "Security Update",
                    url: "/server/" + props.server.id + "/security-update",
                    isShow: props.server?.permissions?.includes("server:security-update"),
                    keyword: ["Security Update", "Security Patch", "Automatic Reboot"]
                }
            ]
        },
        {
            title: t("Backup"),
            name: "Backup",
            icon: "xc-data-recovery",
            url: "/server/" + props.server.id + "/backup",
            isShow: props.server?.permissions?.includes("server:manage-provider-backup") && props.server?.is_provider_backup_manageable,
            keyword: ["Provider Backup Setting", "Enable Backup", "Disable Backup", "Backup Schedule", "Backup List"]
        },
        {
            title: t("Full Server migration"),
            name: "Full Server migration",
            icon: "xc-data-network",
            url: "/server/" + props.server.id + "/migration",
            keyword: ["Full Server Migration"]
        },
        {
            title: t("Metadata"),
            name: "Metadata",
            icon: "xc-indent",
            url: "/server/" + props.server.id + "/meta",
            isShow: props.server?.permissions?.includes("server:manage-services"),
            keyword: ["Metadata", "Information", "Notes", "Change Server Name"]
        },
        {
            title: t("Others"),
            name: "Others",
            icon: "xc-database",
            url: "/server/" + props.server.id + "/others",
            isShow: props.server?.permissions?.includes("server:manage-services"),
            keyword: ["Connection Settings", "Server Settings", "Magic Login Settings", "Server Time Zone"]
        },
    ],
});
const serverStore = useServerStore();

const { updateMonitoring } = useServer(props.server);

const handleSearch = (newSearch) => {
    searchKeyword.value = newSearch;
};

const itemsToSearch = sidebarItems.data.flatMap(item => {
    const combinedItem = {
        ...item,
        subItems: item.subItems || [],
    };

    const allItems = [combinedItem, ...combinedItem.subItems];

    return allItems.flatMap(currentItem => {
        const keywords = currentItem.keyword || [];
        return keywords.map(keyword => ({
            icon: currentItem.icon,
            isShow: currentItem.isShow,
            keyword: keyword,
            name: currentItem.title,
            title: keyword,
            url: `${currentItem.url}#:~:text=${encodeURIComponent(keyword)}`,
        }));
    });
});

const fuse = new Fuse(itemsToSearch, {
    keys: ["keyword"],
    includeScore: true,
    minMatchCharLength: 3,
});

const filteredMenuItems = computed(() => {
    if (!searchKeyword.value) return [];

    const results = fuse.search(searchKeyword.value);
    const items = results.map(result => result.item).filter(item => item.isShow);

    // Reconstruct the hierarchical structure
    const filteredMenu = [];
    items.forEach(matchedItem => {
        if (matchedItem?.subItems && matchedItem?.subItems?.length > 0) {
            matchedItem.subItems.forEach(subItem => {
                if (!filteredMenu.some(item => item.name === subItem.name)) {
                    filteredMenu.push({ ...subItem });
                }
            });
        } else {
            if (!filteredMenu.some(item => item.name === matchedItem.name)) {
                filteredMenu.push({ ...matchedItem });
            }
        }
    });

    return filteredMenu;
});

watch(filteredMenuItems, (newFilteredItems) => {
    emit('update:filteredItems', newFilteredItems);
});
</script>
