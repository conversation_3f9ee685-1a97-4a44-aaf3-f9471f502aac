<template>
    <single-server :server="server" active="Supervisor">
        <Head :title="`Update Supervisor Process ${supervisor_process.command}`"/>
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px">

            <div class="bg-focused dark:bg-mode-light rounded-10px p-2 pt-0 flex flex-col">
                <div
                    class="px-30px py-10px min-h-70px wide-mobile:min-h-60px mobile:min-h-max flex mobile:flex-col items-center wide-mobile:px-15px gap-20px mobile:gap-1 justify-between">

                    <h4 class="text-lg font-medium text-dark dark:text-white leading-none">
                        {{ $t('Update Supervisor Process') }}
                    </h4>
                </div>

                <div
                    class="p-30px wide-mobile:p-25px mobile:p-4 h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px tablet:gap-10px">
                    <form @submit.prevent="submit" class="h-full bg-white dark:bg-mode-base rounded-md flex flex-col tablet:grid tablet:grid-cols-1 gap-30px tablet:gap-10px">
                        <input type="submit" class="hidden">
                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.command"
                                        :error="form.errors.command"
                                        type="text"
                                        placeholder="Enter the command to run (e.g., python app.py)"
                                        :label="$t('Command') + '*'"
                                        autocomplete="off"
                                        :help="$t('You can run any executable command here. For Python, Node.js, or PHP scripts, specify the full command (e.g., python3 script.py).')"
                            />
                        </div>

                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.directory"
                                        :error="form.errors.directory"
                                        type="text"
                                        placeholder="e.g., /var/www/project"
                                        :label="$t('Directory (Optional)')"
                                        autocomplete="off"
                                        :help="$t('Optional. Specify the working directory for the process.')"
                            />
                        </div>

                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.user"
                                        :error="form.errors.user"
                                        type="text"
                                        placeholder="e.g., root"
                                        :label="$t('User')+ '*'"
                                        autocomplete="off"
                                        :help="$t('By default, you can use root. To run as a different user, enter the username here.')"
                            />
                        </div>

                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.numprocs"
                                        :error="form.errors.numprocs"
                                        type="number"
                                        placeholder="e.g., 1"
                                        :label="$t('Number of Processes')+ '*'"
                                        autocomplete="off"
                                        min="1"
                                        max="100"
                                        :help="$t('Number of process instances to keep running.')"
                            />
                        </div>

                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.startsecs"
                                        :error="form.errors.startsecs"
                                        type="number"
                                        placeholder="e.g., 0"
                                        :label="$t('Start Seconds (Optional)')"
                                        autocomplete="off"
                                        min="0"
                                        :help="$t('Supervisor will consider the process started after this many seconds.')"
                            />
                        </div>

                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.stopsecs"
                                        :error="form.errors.stopsecs"
                                        type="number"
                                        placeholder="e.g., 10"
                                        :label="$t('Stop Seconds (Optional)')"
                                        autocomplete="off"
                                        min="0"
                                        :help="$t('Supervisor will wait this many seconds before force stopping the process.')"
                            />
                        </div>

                        <div class="grid grid-cols-2 wide-tablet:grid-cols-1">
                            <text-input v-model="form.stopsignal"
                                        :error="form.errors.stopsignal"
                                        type="text"
                                        placeholder="e.g., TERM, KILL, INT"
                                        :label="$t('Stop Signal (Optional)')"
                                        autocomplete="off"
                                        :help="$t('Supervisor sends this signal to stop the process. Leave empty for default (TERM).')"
                            />
                        </div>

                        <div class="flex">
                            <Btn @click.prevent="submit" type="submit" :loading="form.processing">
                                {{ $t('Update') }}
                            </Btn>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </single-server>
</template>

<script setup>
import {ref} from "vue";
import SingleServer from "@/Pages/Server/SingleServer.vue";
import TextInput from '@/Shared/TextInput.vue';
import {useForm} from '@inertiajs/inertia-vue3';
import Btn from "@/Shared/Btn.vue";
import { Head } from '@inertiajs/inertia-vue3';

let props = defineProps({
    server: Object,
    supervisor_process: Object,
})

const form = useForm({
    command: props.supervisor_process.command,
    directory: props.supervisor_process.directory,
    user: props.supervisor_process.user,
    numprocs: props.supervisor_process.numprocs,
    startsecs: props.supervisor_process.startsecs,
    stopsecs: props.supervisor_process.stopsecs,
    stopsignal: props.supervisor_process.stopsignal
});

const submit = () => {
    form.put(route('api.server.supervisor.update', [props.server.id, props.supervisor_process.id]));
}
</script>
