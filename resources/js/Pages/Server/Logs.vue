<template>
    <single-server
        active="Logs"
        :server="server"
        :can-delete-server="can_delete_server"
        :can_add_site="can_add_site"
        :can_restart_server="can_restart_server"
        :can_restart_nginx_server="can_restart_nginx_server"
        :can_restart_mysql_server="can_restart_mysql_server"
    >
        <div
            :class="{
                'pt-30px': !server?.reboot_require
            }"
            class="grid grid-cols-1 gap-30px pl-30px mobile:pt-20px mobile:pl-20px mobile:gap-20px"
        >
            <div class="flex flex-wrap justify-end gap-x-4 gap-y-2.5 items-center">
                <span class="flex items-center gap-x-4">
                    <label for="filter" class="text-dark dark:text-white"
                        >{{ $t('Filter') }}</label
                    >
                    <select
                        v-model="filter"
                        class="block bg-white dark:bg-dark text-base font-normal text-dark dark:text-white min-h-30px p-2 pl-25px pr-25px border-1 border-secondary-light dark:border-mode-focus-light group-focus-within:border-success-full focus:outline-none w-52 rounded-md autofill:bg-light dark:autofill:bg-mode-base"
                        :disabled="!access_logs.value?.file || clearingLog"
                    >
                        <template v-if="server.stack === 'openlitespeed'">
                            <option value="access">OpenLiteSpeed Access Log</option>
                            <option value="error">OpenLiteSpeed Error Log</option>
                        </template>
                        <template v-else>
                             <option value="access">Nginx Access Log</option>
                            <option value="error">Nginx Error Log</option>
                        </template>

                        <option value="fail2ban">Fail2ban Log</option>
                        <option value="auth_log">auth.log</option>
                    </select>
                </span>
                <span class="flex items-center gap-x-4">
                    <btn
                        icon="xcloud xc-verify_dns"
                        :loading="!access_logs.value?.file"
                        :disabled="!access_logs.value?.file"
                        @click.prevent="getSiteLog"
                        class="!mt-0"
                    >
                        {{ access_logs.value?.file ? $t("Reload") : $t("Loading..") }}
                    </btn>
                    <btn
                        v-if="can_clear_logs"
                        icon="xcloud xc-delete"
                        :loading="clearingLog"
                        :disabled="clearingLog"
                        @click.prevent="clearServerLog"
                        class="!mt-0"
                    >
                        {{ !clearingLog ? $t("Clear") : $t("Clearing..") }}
                    </btn>
                </span>
            </div>

            <nginx-log-table
                v-if="access_logs.value?.file && filter === 'access' || filter === 'error'"
                :access_logs="access_logs"
            />
            <log-table-plain
                v-else-if="access_logs.value?.file"
                :access_logs="access_logs"
            />

            <skeleton v-else :columns="6" :rows="10" />
        </div>
    </single-server>
</template>
<script setup>
import SingleServer from "@/Pages/Server/SingleServer.vue";
import NginxLogTable from "@/Pages/Site/Components/NginxLogTable.vue";
import { onMounted, reactive, ref, watch } from "vue";
import axios from "axios";
import Skeleton from "@/Shared/Table/Skeleton.vue";
import Btn from "@/Shared/Btn.vue";
import { useFlash } from "@/Composables/useFlash";
import LogTablePlain from "@/Pages/Site/Components/LogTablePlain.vue";

const filter = ref("access");
const access_logs = reactive({
    file: null,
    log: [],
});
const { server, tags } = defineProps({
    server: Object,
    can_add_server: Boolean,
    can_delete_server: Boolean,
    can_add_site: Boolean,
    can_restart_server: Boolean,
    can_restart_nginx_server: Boolean,
    can_restart_mysql_server: Boolean,
    can_clear_logs: Boolean,
});

let clearingLog = ref(false);

onMounted(async () => {
    await getSiteLog();
});

watch(filter, async () => {
    await getSiteLog();
});
const getSiteLog = async () => {
    access_logs.value = {
        file: null,
        log: [],
    };

    access_logs.value = await axios
        .get(
            `${route("api.server.logs", { server: server.id })}?type=${
                filter.value
            }`
        )
        .then((res) => res.data);
};

const clearServerLog = async () => {
    clearingLog.value = true;
    await axios
        .get(
            `${route("api.server.logs.clear", { server: server.id })}?type=${
                filter.value
            }`
        )
        .then((res) => {
            // console.log(res);
            if (res.data.status === "finished") {
                useFlash().success("Log cleared successfully.");
                access_logs.value = {
                    file: filter.value,
                    log: [],
                };
            } else {
                useFlash().error("Failed to clear log.");
            }
            clearingLog.value = false;
        })
        .catch((err) => {
            clearingLog.value = false;
            if (
                err.response &&
                err.response.data &&
                err.response.data.message
            ) {
                useFlash().error(err.response.data.message);
            } else {
                useFlash().error(
                    "An error occurred while processing your request."
                );
            }
        });
};
</script>
