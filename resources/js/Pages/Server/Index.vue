<template>
    <Head title="All Servers"></Head>
    <div class="xc-container-2">
        <div
            class="flex flex-col"
            :class="{
                'mr-400px small-laptop:mr-0': navigation.isOpenHelpbar(
                    helper.isOpen
                ),
            }"
        >
            <div
                class="flex wide-tablet:flex-col gap-20px items-center wide-tablet:items-end justify-between mb-30px"
            >
                <div
                    class="flex items-center gap-40px wide-tablet:justify-between wide-tablet:w-full"
                >
                    <h4
                        class="text-dark leading-none dark:text-white wide-tablet:items-end text-40px wide-mobile:text-4xl mobile:text-3xl"
                    >
                        {{ $t('All Servers') }}
                    </h4>
                    <div class="flex bg-white dark:bg-mode-light rounded-md">
                        <div
                            @click="setView(GRID)"
                            class="h-50px wide-mobile:h-40px w-50px wide-mobile:w-40px shrink-0 rounded-md flex justify-center items-center text-lg cursor-pointer"
                            :class="{
                                'dark:bg-mode-focus-light text-white bg-primary-light':
                                    viewType === GRID,
                                'dark:bg-mode-light bg-white dark:text-white text-primary-light':
                                    viewType === LIST,
                            }"
                        >
                            <i class="xcloud xc-grid"></i>
                        </div>
                        <div
                            @click="setView(LIST)"
                            class="h-50px wide-mobile:h-40px w-50px wide-mobile:w-40px shrink-0 rounded-md flex justify-center items-center text-lg cursor-pointer"
                            :class="{
                                'dark:bg-mode-focus-light text-white bg-primary-light':
                                    viewType === LIST,
                                'dark:bg-mode-light bg-white dark:text-white text-primary-light':
                                    viewType === GRID,
                            }"
                        >
                            <i class="xcloud xc-list"></i>
                        </div>
                    </div>
                </div>
                <div
                    class="flex flex-wrap justify-end gap-x-20px wide-mobile:gap-x-10px gap-y-10px"
                >
                    <SelectorTitleDropdown
                        :selected-item="sortByWebServer"
                        :custom-class="'!w-52'"
                    >
                        <div
                            class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
                        >
                            <div class="p-2px">
                                <SelectItem
                                    title="All Web Servers"
                                    :is-active="sortByWebServer === 'All Web Servers'"
                                    @onItemSelected="onFilterChanged($event)"
                                />
                                <SelectItem
                                    title="Nginx"
                                    :is-active="sortByWebServer === 'Nginx'"
                                    @onItemSelected="onSortByWebServerChanged($event)"
                                />
                                <SelectItem
                                    title="OpenLiteSpeed"
                                    :is-active="sortByWebServer === 'OpenLiteSpeed'"
                                    @onItemSelected="onSortByWebServerChanged($event)"
                                />
                            </div>
                        </div>
                    </SelectorTitleDropdown>

                    <SelectorTitleDropdown
                        :selected-item="sortBy"
                        :custom-class="'!w-52'"
                    >
                        <div
                            class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
                        >
                            <div class="p-2px">
                                <SelectItem
                                    title="Name"
                                    :is-active="sortBy === 'Name'"
                                    @onItemSelected="onSortByChanged($event)"
                                />
                                <!--<SelectItem
                                    title="PHP Version"
                                    :is-active="sortBy === 'PHP Version'"
                                    @onItemSelected="onSortByChanged($event)"
                                />-->
                                <SelectItem
                                    title="Created At"
                                    :is-active="sortBy === 'Created At'"
                                    @onItemSelected="onSortByChanged($event)"
                                />
                            </div>
                        </div>
                    </SelectorTitleDropdown>

                    <SelectorTitleDropdown
                        :selected-item="serverFilter"
                        :custom-class="'!w-52'"
                    >
                        <div
                            class="rounded-10px bg-white dark:bg-mode-focus-dark max-h-400px overflow-y-auto scrollbar-thin dark:scrollbar-thin"
                        >
                            <div class="p-2px">
                                <SelectItem
                                    title="All Servers"
                                    :is-active="serverFilter === 'All Servers'"
                                    @onItemSelected="onFilterChanged($event)"
                                />
                                <SelectItem
                                    title="Provisioned"
                                    :is-active="serverFilter === 'Provisioned'"
                                    @onItemSelected="onFilterChanged($event)"
                                />
                                <SelectItem
                                    title="Provisioning"
                                    :is-active="serverFilter === 'Provisioning'"
                                    @onItemSelected="onFilterChanged($event)"
                                />
                            </div>
                            <div class="p-2px">
                                <SelectItem
                                    v-for="tag in tags"
                                    :title="tag"
                                    icon="xc-price-tag"
                                    :is-active="serverFilter === tag"
                                    @onItemSelected="
                                        onFilterChanged($event, 'tag')
                                    "
                                />
                            </div>
                        </div>
                    </SelectorTitleDropdown>
                    <Link
                        v-if="can_add_server"
                        :href="route('server.create')"
                        class="inline-flex items-center justify-center rounded-10px border-success-full shadow-none gap-2 min-h-50px wide-mobile:min-h-40px px-25px wide-mobile:px-20px bg-success-full text-base wide-mobile:text-sm text-white focus:outline-0"
                    >
                        <i class="xcloud xc-add"></i>
                        {{ $t('New Server') }}
                    </Link>
                </div>
            </div>
            <div v-if="servers.data?.length > 0">
                <div
                    v-if="viewType === GRID"
                    class="grid gap-20px laptop:gap-15px wide-mobile:gap-10px grid-cols-3 laptop:grid-cols-2 tablet:grid-cols-1 mb-30px"
                >
                    <Server
                        v-for="server in servers.data"
                        :server="server"
                        :key="server.id"
                        :can-delete-server="can_delete_server"
                        :can_add_site="can_add_site"
                        :can_restart_server="can_restart_server"
                        :can_restart_nginx_server="can_restart_nginx_server"
                        :can_restart_mysql_server="can_restart_mysql_server"
                        :can_clone_server="can_clone_server"
                        @serverDeleted="Inertia.reload({ only: ['servers'] })"
                        :view_type="viewType"
                    />
                </div>
                <div
                    v-if="viewType === LIST"
                    class="w-full flex flex-col gap-20px laptop:gap-15px wide-mobile:gap-10px mb-30px"
                >
                    <Server
                        v-for="server in servers.data"
                        :server="server"
                        :key="server.id"
                        :can-delete-server="can_delete_server"
                        :can_add_site="can_add_site"
                        :can_restart_server="can_restart_server"
                        :can_restart_nginx_server="can_restart_nginx_server"
                        :can_restart_mysql_server="can_restart_mysql_server"
                        :view_type="viewType"
                        :can_clone_server="can_clone_server"
                    />
                </div>

                <Pagination :links="servers.links" :query-string="generateFilterQueryString()" />
            </div>

            <Empty
                v-else
                :can-perform-action="can_add_server"
                type="server"
                item-to-create="server"
                visit-url="/server/create"
                :documentation="
                    '<p>'+$t('Check out our ')+' <a class=' +
                    'underline' +
                    ' target=' +
                    '_blank' +
                    ' href=' +
                    'https://xcloud.host/docs/how-to-get-free-hosting-with-vultr/' +
                    '>'+$t('Quick Start Documentation')+'</a>.</p>'
                "
            />
        </div>
    </div>
</template>

<script setup>
import { ref } from "vue";
import { Inertia } from "@inertiajs/inertia";
import Server from "@/Pages/Server/Components/Server.vue";
import Pagination from "@/Shared/Pagination.vue";
import { useNavigationStore } from "@/stores/NavigationStore.js";
import { computed } from "vue";
import { useHelpStore } from "@/stores/HelpStore";
import Empty from "@/Shared/Empty.vue";
import SelectorTitleDropdown from "@/Shared/SelectorTitleDropdown.vue";
import SelectItem from "@/Shared/SelectItem.vue";
import UpgradeableServers from "@/Shared/Server/UpgradeableServers.vue";
import {usePage} from "@inertiajs/inertia-vue3";

const props = defineProps({
    servers: Object,
    incompleteServers: Object,
    filter: String,
    sortBy: String,
    sortByWebServer: String,
    sortOrder: {
        type: String,
        default: "desc",
    },
    tags: Object,
    can_add_server: Boolean,
    can_delete_server: Boolean,
    can_add_site: Boolean,
    can_clone_server: Boolean,
    can_archive_server: Boolean,
    can_restart_server: Boolean,
    can_restart_nginx_server: Boolean,
    can_restart_mysql_server: Boolean
});

let navigation = useNavigationStore();
let helper = useHelpStore();

const serverFilter = ref(props.filter);
const sortBy = ref(props.sortBy);
const sortByWebServer = ref(props.sortByWebServer);
const sortOrder = ref(props.sortOrder);

const navigationStore = useNavigationStore();

//is grid and is list computed
// const isGrid = computed(() => navigationStore.isGrid);
// const isList = computed(() => !navigationStore.isGrid);

const viewType = ref(usePage().props.value?.view_type?.server);
const GRID = "grid";
const LIST = "list";
const setView = (type) => {
    viewType.value = type;

    Inertia.post(route("store.view.type"), {
        server_view_type: type,
        preserveScroll: true,
        onSuccess: () => {
            Inertia.reload();
        }
    });
}

function onFilterChanged(filter, type = "status") {
    serverFilter.value = filter;
    //load servers based on filter
    Inertia.get(route("server.index"), {
        filter: serverFilter.value,
        filterType: type,
    });
}

function onSortByChanged(sort) {
    if (sortBy.value === sort) {
        sortOrder.value = sortOrder.value === "asc" ? "desc" : "asc";
    } else {
        sortBy.value = sort;
        sortOrder.value = "desc"; // Reset to default descending order
    }

    // load servers based on sortBy
    Inertia.get(route("server.index"), {
        sortBy: sortBy.value,
        sortOrder: sortOrder.value,
        server_sort_type: sortBy.value
    });
}

function onSortByWebServerChanged(webServer) {
    sortByWebServer.value = webServer;
    //load servers based on filter
    Inertia.get(route("server.index"), {
        sortByWebServer: sortByWebServer.value,
    });
}

const generateFilterQueryString = () => {
    let queryString = '';

    if (sortByWebServer.value) {
        queryString += `&sortByWebServer=${sortByWebServer.value}`;
    }

    if (sortBy.value && sortOrder.value) {
        queryString += `&sortBy=${sortBy.value}&sortOrder=${sortOrder.value}`;
    }

    if (serverFilter.value) {
        queryString += `&filter=${serverFilter.value}&filterType=status`;
    }

    return queryString;
}
</script>
