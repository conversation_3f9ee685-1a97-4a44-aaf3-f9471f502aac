<template>
  <h3 class="text-lg text-dark dark:text-white leading-none px-20 py-10">
    {{ $t('Processing payment... Please do not cancel or refresh the page.') }}
  </h3>
</template>

<script setup>

import {useForm} from "@inertiajs/inertia-vue3";
import {loadStripe} from '@stripe/stripe-js';
import {onMounted, ref} from "vue";
import {useFlash} from "@/Composables/useFlash";
import {Inertia} from "@inertiajs/inertia";

const props = defineProps({
  clientSecret: String,
  paymentIntentStatus: String,
  paymentIntentId: String,
  affiliateId: String,
  invoiceId: String,
  nextRoute: String,
  routeParam: String,
  subscriptionProductId: String,
  stripeSubscriptionId: String
});

let form = useForm({
  paymentIntentId: props.paymentIntentId,
  affiliateId: props.affiliateId,
  invoiceId: props.invoiceId,
  nextRoute: props.nextRoute,
  routeParam: props.routeParam,
  subscriptionProductId: props.subscriptionProductId,
  stripeSubscriptionId: props.stripeSubscriptionId
});

const clientSecret = ref(props.clientSecret);
let stripePromise = loadStripe(VITE_STRIPE_PUBLISH_KEY);

onMounted(async () => {
  const stripe = await stripePromise;

  // Check if stripe is properly initialized and clientSecret is available
  if (!stripe || !clientSecret.value) {
    console.error('Stripe is not initialized or clientSecret is missing');
    return;
  }

  await handle3DSecurePayment(stripe);
});

const handle3DSecurePayment = async (stripe) => {
  const result = await stripe.confirmCardPayment(clientSecret.value);

  if (result.error) {
    console.error('Payment confirmation failed:', result.error.message);
    Inertia.visit(route('user.detailedInvoices'), {
      preserveScroll: true,
      onSuccess: () => {
        useFlash().error('Payment failed.');
      }
    });
  } else {
    if (result.paymentIntent && result.paymentIntent.status === 'succeeded') {
      // Payment succeeded, perform further actions such as informing your server
      confirmPayment(result.paymentIntent.id);
    }
  }
};

const confirmPayment = (paymentIntentId) => {
  form.post(route('api.subscription-product.payment.confirm-3d-secured-payment'), {
    preserveScroll: true,
    onSuccess: () => {
      useFlash().success('Payment successfully processed.');
    }
  });
};

</script>
