<template>
    <single-report active="Sales Report">
        <Head title="Reports" />

        <div class="xc-container-2">
            <div
                class="flex flex-wrap items-center justify-between mb-15px gap-x-2 gap-y-2"
            >
                <h4
                    class="text-dark dark:text-white text-xl md:text-xl leading-none"
                >
                    Total Sales Statistics
                </h4>
            </div>

            <div
                class="grid grid-cols-4 small-laptop:grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px small-laptop:gap-20px tablet:gap-15px wide-mobile:gap-10px mb-30px tablet:mb-25px wide-mobile:mb-20px"
            >
                <stat-box
                    title="Total Sales"
                    :is-price="true"
                    :price="total_sales"
                />
                <stat-box
                    title="Total Sales xCloud Managed"
                    :is-price="true"
                    :price="total_sale_xcloud_managed"
                />
                <stat-box
                    title="Total Sales xCloud Provider"
                    :is-price="true"
                    :price="total_sale_xcloud_provider"
                />
                <stat-box
                    title="Total Sales LTD Packages"
                    :is-price="true"
                    :price="total_sale_ltd_packages"
                />
                <stat-box
                    title="Total Sales Emails"
                    :is-price="true"
                    :price="total_sale_email_provider"
                />
                <stat-box
                    title="Total Sales Self Managed"
                    :is-price="true"
                    :price="total_sale_self_managed"
                />
<!--                <stat-box-->
<!--                    title="Total Refunds"-->
<!--                    :is-count="true"-->
<!--                    :count="total_sale_refunds" />-->
<!--                <stat-box title="Total Users" :is-count="true" count="0" />-->
<!--                <stat-box title="Total Pro Users" :is-count="true" count="0" />-->
<!--                <stat-box title="Total Free Users" :is-count="true" count="0" />-->
<!--                <stat-box-->
<!--                    title="Total Discount/Giveaway xCloud Managed"-->
<!--                    :is-count="true"-->
<!--                    count="0"-->
<!--                />-->
<!--                <stat-box-->
<!--                    title="Total Discount/Giveaway Self Managed"-->
<!--                    :is-count="true"-->
<!--                    count="0"-->
<!--                />-->
            </div>
        </div>

        <div class="xc-container-2">
            <div
                class="flex flex-wrap items-center justify-between mb-15px gap-x-2 gap-y-2"
            >
                <h4
                    class="text-dark dark:text-white text-xl md:text-xl leading-none"
                >
                    Monthly Recurring Revenue (MRR)
                </h4>
                <div class="flex items-center gap-2">
                    <multiselect
                        @select="mrrMonthlyFilter"
                        class="border-none justify-end !w-44"
                        v-model="form.filter_by_month"
                        :multiple="false"
                        :close-on-select="true"
                        :searchable="true"
                        :create-option="false"
                        placeholder="Choose month to filter"
                        :options="months"/>

                    <multiselect
                        @select="mrrYearlyFilter"
                        class="border-none justify-end !w-44"
                        v-model="form.filter_by_year"
                        :multiple="false"
                        :close-on-select="true"
                        :searchable="true"
                        :create-option="false"
                        placeholder="Choose year to filter"
                        :options="years"/>
                </div>
            </div>

            <div
                class="grid grid-cols-4 small-laptop:grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px small-laptop:gap-20px tablet:gap-15px wide-mobile:gap-10px mb-30px tablet:mb-25px wide-mobile:mb-20px"
            >
                <stat-box
                    title="Total Monthly Recurring Revenue (MRR)"
                    :is-price="true"
                    :price="total_monthly_recurring_revenue"
                />
                <stat-box
                    title="xCloud MRR"
                    :is-price="true"
                    :price="xcloud_mrr"
                />
                <stat-box
                    title="Self Managed (MRR)"
                    :is-price="true"
                    :price="self_managed_mrr"
                />
                <stat-box
                    title="Email MRR"
                    :is-price="true"
                    :price="email_provider_mrr"
                />
            </div>
        </div>

        <div class="xc-container-2">
            <div
                class="flex flex-wrap items-center justify-between mb-15px gap-x-2 gap-y-2"
            >
                <h4
                    class="text-dark dark:text-white text-xl md:text-xl leading-none"
                >
                    Package Wise Sold
                </h4>
            </div>

            <div
                class="grid grid-cols-4 small-laptop:grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px small-laptop:gap-20px tablet:gap-15px wide-mobile:gap-10px mb-30px tablet:mb-25px wide-mobile:mb-20px"
            >
                <stat-box
                    v-for="packageWiseSoldData in packageWiseSold"
                    :title="packageWiseSoldData?.package_name"
                    :is-price="true"
                    :price="packageWiseSoldData?.total_paid_amount"
                    :is-label-count="true"
                    :label-count="packageWiseSoldData?.paid_invoice_count"
                />
            </div>
        </div>

        <div class="xc-container-2">
            <div
                class="flex flex-wrap items-center justify-between mb-15px gap-x-2 gap-y-2"
            >
                <h4
                    class="text-dark dark:text-white text-xl md:text-xl leading-none"
                >
                    Package Unpaid/Failed
                </h4>
            </div>

            <div
                class="grid grid-cols-4 small-laptop:grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px small-laptop:gap-20px tablet:gap-15px wide-mobile:gap-10px mb-30px tablet:mb-25px wide-mobile:mb-20px"
            >
                <stat-box
                    v-for="packageWiseUnsoldData in packageWiseSold"
                    :title="packageWiseUnsoldData?.package_name"
                    :is-price="true"
                    :price="packageWiseUnsoldData?.total_unpaid_amount"
                    :is-label-count="true"
                    :label-count="packageWiseUnsoldData?.unpaid_invoice_count"
                />
            </div>
        </div>

        <!--<div class="xc-container-2">
            <div
                class="flex flex-wrap items-center justify-between mb-15px gap-x-2 gap-y-2"
            >
                <h4
                    class="text-dark dark:text-white text-xl md:text-xl leading-none"
                >
                    Profit
                </h4>
            </div>

            <div
                class="grid grid-cols-4 small-laptop:grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px small-laptop:gap-20px tablet:gap-15px wide-mobile:gap-10px mb-30px tablet:mb-25px wide-mobile:mb-20px"
            >
                <stat-box
                    title="Total Profit = xCloud + Email + Self Managed - Refunds"
                    :is-price="true"
                    :price="0"
                />
                <stat-box
                    title="xCloud Profit = xCloud sales- vultr cost"
                    :is-price="true"
                    :price="0"
                />
                <stat-box
                    title="Email Profit = Email Sales - Elastic cost"
                    :is-price="true"
                    :price="0"
                />
                <stat-box
                    title="Self Managed Sales/Profit"
                    :is-price="true"
                    :price="0"
                />
                <stat-box
                    title="Total Cost On Vultr"
                    :is-price="true"
                    :price="0"
                />
                <stat-box
                    title="Total Cost On Elastic Email"
                    :is-price="true"
                    :price="0"
                />
                <stat-box
                    title="Total Refunds"
                    :is-price="true"
                    :price="0"
                />
            </div>
        </div>-->

        <div class="xc-container-2">
            <div
                class="flex flex-wrap items-center justify-between mb-15px gap-x-2 gap-y-2"
            >
                <h4
                    class="text-dark dark:text-white text-xl md:text-xl leading-none"
                >
                    Email Report
                </h4>
            </div>

            <div
                class="grid grid-cols-4 small-laptop:grid-cols-3 tablet:grid-cols-2 wide-mobile:grid-cols-1 gap-20px small-laptop:gap-20px tablet:gap-15px wide-mobile:gap-10px mb-30px tablet:mb-25px wide-mobile:mb-20px"
            >
                <stat-box
                    title="Total Emails Sold"
                    :is-price="true"
                    :price="total_email_sold"
                />
                <stat-box
                    title="Total Free Emails"
                    :is-count="true"
                    :count="total_free_email"
                />
            </div>
        </div>
    </single-report>
</template>

<script setup>
import {onMounted, ref} from "vue";
import SingleReport from "@/Pages/Reports/SingleReport.vue";
import StatBox from "@/Pages/Reports/StatBox.vue";
import {DatePicker} from "v-calendar";
import Multiselect from "@vueform/multiselect";
import {useForm} from "@inertiajs/inertia-vue3";
import {Inertia} from "@inertiajs/inertia";

const props = defineProps({
    total_sales: {
        type: Number,
        default: 0,
    },
    total_sale_xcloud_managed: {
        type: Number,
        default: 0,
    },
    total_sale_xcloud_provider: {
        type: Number,
        default: 0,
    },
    total_sale_self_managed: {
        type: Number,
        default: 0,
    },
    total_sale_email_provider: {
        type: Number,
        default: 0,
    },
    total_sale_refunds: {
        type: Number,
        default: 0,
    },
    total_sale_ltd_packages: {
        type: Number,
        default: 0,
    },
    total_monthly_recurring_revenue: Number,
    xcloud_mrr: Number,
    self_managed_mrr: Number,
    email_provider_mrr: Number,
    total_email_sold: Number,
    total_free_email: Number,
    filter_by_month: String,
    filter_by_year: String
});

const form = useForm({
    filter_by_month: props.filter_by_month ?? '',
    filter_by_year: props.filter_by_year ?? '',
});

const packageWiseSold = ref([]);

const urlParams = new URLSearchParams(window.location.search);

const mrrMonthlyFilter = (month) => {
    Inertia.get(route("reports"), {
        filter_by_month: form.filter_by_month,
        filter_by_year: form.filter_by_year
    });
}

const mrrYearlyFilter = (year) => {
    Inertia.get(route("reports"), {
        filter_by_month: form.filter_by_month,
        filter_by_year: form.filter_by_year
    });
}

const months = ref([
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
]);

const years = ref([
    "2023",
    "2024",
    "2025",
]);

let date = ref({
    start: urlParams.has("startDate") ? urlParams.get("startDate") : null,
    end: urlParams.has("endDate") ? urlParams.get("endDate") : null,
});

const getPackageWiseSold = () => {
    axios.get(route('reports.package-wise-sold'))
        .then(({data}) => {
            packageWiseSold.value = data
        }).catch(error => {

    })
}

onMounted(() => {
    getPackageWiseSold()
})
</script>

<style>
@import "@vueform/multiselect/themes/tailwind.css";
</style>
