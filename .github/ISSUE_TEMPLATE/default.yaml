name: Development Task
description: >
  Use this template for a development task, including features, bug fixes,
  refactoring, or other improvements. Complete all applicable fields below.
body:
  - type: markdown
    attributes:
      value: |
        Please provide a summary and details of the development task using the fields below.
  - type: textarea
    id: description
    attributes:
      label: Description
      description: Provide a detailed description of the task or issue.
      placeholder: |
        Explain what needs to be done and any relevant context.
    validations:
      required: true
  - type: textarea
    id: technical-steps
    attributes:
      label: Technical Steps
      description: Outline the technical steps required to complete this task.
      placeholder: |
        e.g., database changes, refactoring notes, logic implementation steps...
    validations:
      required: false
  - type: checkboxes
    id: dependencies
    attributes:
      label: Dependencies
      description: Select any dependencies relevant to this task.
      options:
        - label: Free plan
        - label: Metro VPS
        - label: Premium Plan
        - label: White Label
        - label: Other (please specify in details)
        - label: Skip
        - label: I acknowledge these dependencies and will ensure they are met before completing this task.
          required: true
  - type: textarea
    id: dependency-details
    attributes:
      label: Dependency Details
      description: |
        If any dependencies are selected, provide relevant details. You may cross-reference UI/UX notes if the design depends on them.
      placeholder: |
        e.g., feature only available in Premium Plan, UI theme depends on White Label setup...
  - type: textarea
    id: ui-ux-notes
    attributes:
      label: UI/UX Requirements
      description: |
        Describe any UI/UX considerations for this task. If applicable, note any plan-based or client-specific design variations.
      placeholder: |
        e.g., mockup links, layout behavior, adaptive UI based on subscription tier or white-label config...
    validations:
      required: false
