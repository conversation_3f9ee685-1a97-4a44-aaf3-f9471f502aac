**Type** : Feature, Hotfix, Improvement, Small Issue, Bug Fix, Refactor, Feature & Bug Fix

**Description**
- Add Description Here
- Multiple Description here

**Test Cases**
- [ ] Create the model
- [ ] Check validation

**Existing Pull Requests related to this**
- #ENTER_PR_NUMBER_HERE // please remove this section if not needed
- #ANOTHER_PR_NUMBER // please remove this section if not needed

**Review Required by Developer**
- @mention the developer and write the issue code review or conflict in detail // please remove this section if not needed

**Development/Testing notes**
- Please run `php artisan fix:migrate` on production after merging this PR // please remove this section if not needed
- There's a risk of data loss // please remove this section if not needed
- Please write any other notes here // please remove this section if not needed

**Developer Acknowledgment ( Only for Backend Devs )**
- [ ] Code is clean well documented and follows the coding standards.
- [ ] Code is tested and passes all unit and integration tests.
- [ ] Code is reviewed by at least one other developer. (will be done by the reviewer)
- [ ] `dev` branch is merged into the current branch and conflicts are resolved (if applicable).
- [ ] Added authorized user roles and permissions on controller methods (if applicable).
- [ ] Database structure discussed with the team lead / senior developer and implemented accordingly (if applicable).
- [ ] Database migrations are clean no issue with rollback (if applicable).

**Frontend Dev Acknowledgment ( Only for Frontend Devs )**
- [ ] Code is clean well documented and follows the coding standards.
- [ ] All the `console.log` removed after testing. (if applicable)
- [ ] Code is reviewed by at least one other developer. (will be done by the reviewer)
- [ ] `dev` branch is merged into the current branch and conflicts are resolved (if applicable).
- [ ] Tested on all major browsers (Chrome, Firefox, Safari, Edge).
- [ ] Responsive design is tested on all major devices (mobile, tablet, desktop). (if applicable)

**QA Validation Acknowledgment ( Only for QA )**
- [ ] Verified new changes.
- [ ] Validated input fields as per expected rules.
- [ ] Ensured related features work correctly.
- [ ] Compared old vs. new functionality for regressions.
- [ ] Checked database migrations (if applicable).
- [ ] Checked if any existing bugs related to this change have been resolved.
- [ ] Verified UI/UX changes for both light mode and dark mode (if applicable).
- [ ] Test with different user roles and permissions (if applicable).
- [ ] Confirm that logs and error handling work as expected.
- [ ] Check security vulnerabilities (if applicable).
- [ ] Check string translations (if applicable).
