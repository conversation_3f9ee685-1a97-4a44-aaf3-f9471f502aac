{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "ext-curl": "*", "ext-json": "*", "ext-zip": "*", "alexwenzel/nova-dependency-container": "^1.10", "aws/aws-sdk-php-laravel": "^3.7", "barryvdh/laravel-dompdf": "^2.0", "beyondcode/laravel-websockets": "^1.13", "blade-ui-kit/blade-icons": "^1.1", "digital-creative/nova-slider-filter": "^1.0", "fakerphp/faker": "^1.9.1", "fruitcake/laravel-cors": "^2.0", "google/cloud": "^0.195.0", "graham-campbell/digitalocean": "^10.0", "guzzlehttp/guzzle": "^7.4", "hashids/hashids": "^4.1", "inertiajs/inertia-laravel": "^0.6.4", "jeremykendall/php-domain-parser": "^6.3", "laravel/framework": "^9.32.0", "laravel/horizon": "^5.11", "laravel/jetstream": "^2.6", "laravel/nova": "^4.27.14", "laravel/sanctum": "^2.14", "laravel/slack-notification-channel": "^3.2", "laravel/telescope": "^4.11", "laravel/tinker": "^2.5", "laravel/vapor-cli": "^1.55", "laravel/vapor-core": "^2.29", "laravel/vapor-ui": "^1.7", "league/flysystem-aws-s3-v3": "^3.12", "league/flysystem-sftp-v3": "^3.0", "league/iso3166": "^4.3", "lkdevelopment/hetzner-cloud-php-sdk": "dev-patch-1", "maatwebsite/excel": "^3.1", "mailchimp/marketing": "^3.0", "mailgun/mailgun-php": "^3.6", "naoray/nova-json": "^3.0", "outl1ne/nova-multiselect-filter": "^4.0", "paragonie/ciphersweet": "4.1.0", "pusher/pusher-php-server": "^7.2", "romanzipp/laravel-mailcheck": "^2.0", "spatie/laravel-ciphersweet": "^1.0", "spatie/laravel-ignition": "^1.0", "stripe/stripe-php": "^10.18", "tightenco/ziggy": "^1.0", "vultr/vultr-php": "dev-projectx", "webinarium/linode-api": "^2.2"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "pestphp/pest-plugin-laravel": "^1.4", "pestphp/pest-plugin-parallel": "^1.2", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["bootstrap/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi", "@php artisan vapor-ui:publish --ansi", "@php artisan horizon:publish --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "repositories": [{"type": "composer", "url": "https://nova.laravel.com"}, {"type": "vcs", "url": "https://github.com/bashar94/hetzner-cloud-php-sdk.git"}, {"type": "vcs", "url": "https://github.com/bashar94/vultr-php.git"}], "minimum-stability": "dev", "prefer-stable": true}