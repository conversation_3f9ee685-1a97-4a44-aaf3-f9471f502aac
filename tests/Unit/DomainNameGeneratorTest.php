<?php

namespace Tests\Unit;

use App\Services\Database\DatabaseNameGenerator as Generator;
use PHPUnit\Framework\TestCase;

class DomainNameGeneratorTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    public function test_example()
    {
        $cases = [
            'x-cloud.app' => 'x_cloud',
            'example.com' => 'example',

            'wpdeveloper.com' => 'wpdeveloper',
            'staging.wpdeveloper.com' => 'staging',

            'wp.x-cloud.app' => 'wp_x_cloud',
            'nobin.x-cloud.app' => 'nobin',

            'domainnamegeneratortest.com' => 'domainnamegenera',
            'lorem.domainnamegeneratortest.com' => 'lorem',

            'very-long-domain.x-cloud.app' => 'very_long_domain',
            'very-long-long-domain.x-cloud.app' => 'very_long_long_d',

            's1_x-cloud.app' => 's1_x_cloud',
            's1999_example.com' => 's1999_example',
            's19999999_example.com' => 's19999999_exampl',
            's199_wp.19999999_example.com' => 's199_wp',
            's1_nobin.x-cloud.app' => 's1_nobin',
            's9999_nobin.x-cloud.app' => 's9999_nobin',
            's999_very-long-domain.x-cloud.app' => 's999_very_long_d',
            'xcloud-php-test-80.nobin.me' => 'xcloud_php_test',
        ];

        // $this->don
//
//        foreach ($cases as $input => $expected) {
//            $this->assertEquals($expected, Generator::generate($input));
//        }
    }
}
