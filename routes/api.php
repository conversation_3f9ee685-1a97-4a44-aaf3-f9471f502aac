<?php

use App\Http\Controllers\API\ApiEndpointController as APIApiEndpointController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// API Endpoint CRUD routes
Route::apiResource('api-endpoints', APIApiEndpointController::class);

// Additional API Endpoint routes
Route::patch('api-endpoints/{apiEndpoint}/toggle-status', [APIApiEndpointController::class, 'toggleStatus']);
Route::patch('api-endpoints/{apiEndpoint}/increment-count', [APIApiEndpointController::class, 'incrementCount']);
Route::patch('api-endpoints/{apiEndpoint}/reset-count', [APIApiEndpointController::class, 'resetCount']);
