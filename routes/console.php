<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Register the GSM OLED scraper commands
Artisan::command('scrape:gsmoled', function () {
    $this->info('Starting GSM OLED scraper...');

    // First, scrape the product listings
    $this->call('scrape:gsmoled-products');

    // Then, fetch detailed information for the products
    $this->call('scrape:gsmoled-details', ['--limit' => 50]);

    $this->info('GSM OLED scraping completed!');
})->purpose('Scrape products from GSM OLED website');

// Register the PCComponentes category scraper command
Artisan::command('scrape:pccomponentes-category', function () {
    $this->info('Starting PCComponentes category scraper...');

    // Scrape products from the category page
    $this->call('scrape:category-products');

    $this->info('PCComponentes category scraping completed!');
})->purpose('Scrape products from PCComponentes category page');
