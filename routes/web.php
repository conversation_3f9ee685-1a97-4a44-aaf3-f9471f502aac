<?php

use App\Http\Controllers\ApiEndpointController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// API Endpoint CRUD routes
Route::resource('api-endpoints', ApiEndpointController::class);

// Additional API Endpoint routes
Route::patch('api-endpoints/{apiEndpoint}/toggle-status', [ApiEndpointController::class, 'toggleStatus'])
    ->name('api-endpoints.toggle-status');
Route::patch('api-endpoints/{apiEndpoint}/increment-count', [ApiEndpointController::class, 'incrementCount'])
    ->name('api-endpoints.increment-count');
Route::patch('api-endpoints/{apiEndpoint}/reset-count', [ApiEndpointController::class, 'resetCount'])
    ->name('api-endpoints.reset-count');
