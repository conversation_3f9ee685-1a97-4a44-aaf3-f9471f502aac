<?php

namespace App\Rules;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SiteUserRule extends FormRequest
{
    static function rules(int $serverId, int $ignoreSiteId = null): array
    {
        return [
            'required',
            'string',
            'max:16',
            'not_in:xcloud,root', // don't allow xcloud for now
            'regex:/^[a-z][a-z0-9_]*$/',
            Rule::unique('sites', 'site_user')->where('server_id', $serverId)->ignore($ignoreSiteId),
            Rule::unique('sudo_users', 'sudo_user')->where('server_id', $serverId),
        ];
    }
}
