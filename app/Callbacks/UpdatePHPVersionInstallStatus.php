<?php

namespace App\Callbacks;

use App\Models\Server;
use App\Models\Task;

class UpdatePHPVersionInstallStatus
{
    public function __construct(private int $id, private string $phpVersion)
    {
    }

    function handle(Task $task)
    {
        if ($server = Server::find($this->id)) {
            $server->phpManager()->update(
                $this->phpVersion,
                $task->successful() ? 'installed' : 'failed'
            );
        }
    }
}
