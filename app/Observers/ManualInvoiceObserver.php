<?php

namespace App\Observers;

use App\Enums\XcloudBilling\BillingStatus;
use App\Models\ManualInvoice;
use App\Notifications\SendManualInvoicePaidNotification;
use App\Notifications\SendMonthlyManualInvoiceNotification;
use Illuminate\Support\Str;

class ManualInvoiceObserver
{
    public function creating( ManualInvoice $manualInvoice): void
    {
        if (!$manualInvoice->team_id && $manualInvoice->bills()->exists()) {
            $manualInvoiceCount = $manualInvoice->bills()->groupby('team_id')->distinct()->pluck('team_id')->count();
            $manualInvoice->team_id = $manualInvoiceCount === 1 ? $manualInvoice->bills()->first()->team_id : null;
        }

        if (!$manualInvoice->invoice_number) {
            $manualInvoice->invoice_number = 'XC-INV-' . date('Ymd') . '-' . strtoupper(Str::random(12));
        }
    }

    /**
     * Handle the ManualInvoice "created" event.
     *
     * @param  \App\Models\ManualInvoice  $manualInvoice
     * @return void
     */
    public function created(ManualInvoice $manualInvoice)
    {
        if (auth()->check()) {
            $manualInvoice->manually_created_by = auth()->user()->id;
        }

        // Set default status to pending if not set
        if (!$manualInvoice->status) {
            $manualInvoice->status = BillingStatus::Pending;
        }

        // If invoice is created with a paid status, dispatch job to ensure service is provided
        if ($manualInvoice->status === BillingStatus::Paid) {
            \App\Jobs\EnsurePaymentServiceProvidedBasedOnInvoiceStatus::dispatch($manualInvoice);
        }
        // If invoice is created with a failed or cancelled status, dispatch job to revoke service
        elseif (in_array($manualInvoice->status, [
            BillingStatus::Failed,
            BillingStatus::PaymentFailed,
            BillingStatus::Cancelled
        ])) {
            \App\Jobs\EnsurePaymentServiceProvidedBasedOnInvoiceStatus::dispatch($manualInvoice);
        }
    }

    /**
     * Handle the ManualInvoice "updated" event.
     *
     * @param  \App\Models\ManualInvoice  $manualInvoice
     * @return void
     */
    public function updated(ManualInvoice $manualInvoice)
    {
        if ($manualInvoice->isDirty('status') && $manualInvoice->status === BillingStatus::Paid) {

            if (!$manualInvoice->amount_received) {
                $manualInvoice->amount_received = $manualInvoice->amount;
            }

            $manualInvoice->bills()->unpaid()->update([
                'status' => BillingStatus::Paid
            ]);

            // Dispatch job to ensure service is provided
            \App\Jobs\EnsurePaymentServiceProvidedBasedOnInvoiceStatus::dispatch($manualInvoice);
        }

        // If invoice status changed to failed or cancelled, dispatch job to revoke service
        if ($manualInvoice->isDirty('status') && in_array($manualInvoice->status, [
            BillingStatus::Failed,
            BillingStatus::PaymentFailed,
            BillingStatus::Cancelled
        ])) {
            \App\Jobs\EnsurePaymentServiceProvidedBasedOnInvoiceStatus::dispatch($manualInvoice);
        }

        if (auth()->check()) {
            $manualInvoice->manually_updated_by = auth()->user()->id;
        }
    }

    public function deleting(ManualInvoice $manualInvoice)
    {
        if ($manualInvoice->bills()->exists()) {
            throw new \Exception('Manual Invoice cannot be deleted because it has bills');
        }
    }

    /**
     * Handle the ManualInvoice "deleted" event.
     *
     * @param  \App\Models\ManualInvoice  $manualInvoice
     * @return void
     */
    public function deleted(ManualInvoice $manualInvoice)
    {
        if ($manualInvoice->bills()->exists()) {
            throw new \Exception('Manual Invoice deleted but it has bills');
        }
    }

    /**
     * Handle the ManualInvoice "restored" event.
     *
     * @param  \App\Models\ManualInvoice  $manualInvoice
     * @return void
     */
    public function restored(ManualInvoice $manualInvoice)
    {
        //
    }

    /**
     * Handle the ManualInvoice "force deleted" event.
     *
     * @param  \App\Models\ManualInvoice  $manualInvoice
     * @return void
     */
    public function forceDeleted(ManualInvoice $manualInvoice)
    {
        //
    }
}
