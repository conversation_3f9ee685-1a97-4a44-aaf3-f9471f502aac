<?php

namespace App\Observers;

use App\Models\Product;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Stripe\Checkout\Session;
use Stripe\Stripe;
use Stripe\StripeClient;

class ProductObserver
{
    public function creating($model): void
    {
        Cache::forget('xc_product');
//        if (!$model->checkout_url) {
//            if($model->white_label_id) {
////                dd($model);
////                $model->checkout_url = route('cart.checkoutWithProductForWhiteLabel', ['product' => $model->id]);
//            } else {
//                $model->checkout_url = route('cart.checkoutWithProduct', ['product' => $model->id]);
//            }
//        }

        if ($model->source_product_id) {
            $sourceProduct = Product::find($model->source_product_id);

            if (!$sourceProduct) {
                throw new \Exception('Source product not found');
            }

            $model->slug = $sourceProduct->slug;
            $model->source_model = $sourceProduct->source_model;
            $model->service_type = $sourceProduct->service_type;
        }

    }

    public function created($model): void
    {
        Cache::forget('xc_product');

        if (!$model->checkout_url) {
            if($model->white_label_id) {
                $model->checkout_url = $model->whiteLabel->url . '/cart/products/' . $model->id .'/checkout';
            } else {
                $model->checkout_url = route('cart.checkoutWithProduct', ['product' => $model->id]);
            }

            $model->save();
        }
    }

    public function updating($model): void
    {
        Cache::forget('xc_product');
    }
}
