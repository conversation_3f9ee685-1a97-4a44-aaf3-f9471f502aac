<?php

namespace App\Observers;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Models\GeneralInvoice;
use App\Repository\StripePaymentRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Stripe\Exception\ApiErrorException;

class GeneralInvoiceObserver
{
    public function creating(GeneralInvoice $invoice): void
    {
        if (!$invoice->reference_no) {
            $invoice->reference_no = uniqid();
        }

        if (!$invoice->team_id && $invoice->bills()->exists()) {
            $invoiceCount = $invoice->bills()->groupby('team_id')->distinct()->pluck('team_id')->count();
            $invoice->team_id = $invoiceCount === 1 ? $invoice->bills()->first()->team_id : null;
        }

        if (!$invoice->source || !InvoiceSourceEnum::isValid($invoice->source)) {
            $invoice->source = InvoiceSourceEnum::SinglePurchase;
        }

        if (!$invoice->invoice_number) {
            $invoice->invoice_number = 'XC-INV-' . date('Ymd') . '-' . strtoupper(Str::random(12));
        }
    }
    /**
     * Handle the GeneralInvoice "created" event.
     *
     * @param \App\Models\GeneralInvoice $invoice
     *
     * @throws ApiErrorException
     */
    public function created(GeneralInvoice $invoice): void
    {
        if ($invoice->status == BillingStatus::Paid) {
            try {
                $invoice->team->onPaymentSuccess($invoice);
            } catch (\Exception $e) {
                $invoice->saveLog('fluent_crm_error', $e->getMessage());
            }
        }
    }

    /**
     * Handle the GeneralInvoice "updated" event.
     *
     * @param  \App\Models\GeneralInvoice  $invoice
     *
     */
    public function updated(GeneralInvoice $invoice): void
    {
        if ($invoice->isDirty('status') && $invoice->status == BillingStatus::RefundPermissionPending) {
            $invoice->updateQuietly([
                'refundable_amount' => $invoice->amount,
            ]);
        }

        if ($invoice->isDirty('status') && $invoice->status == BillingStatus::Paid) {
            try {
                $invoice->team->onPaymentSuccess($invoice);
            } catch (\Exception $e) {
                $invoice->saveLog('fluent_crm_error', $e->getMessage());
            }
        }
    }

    public function deleting(GeneralInvoice $invoice)
    {
        if (!auth()->user()->isSuperAdmin()) {
            throw new \Exception('Invoice can not be deleted');
        }

        if ($invoice->bills) {
            $bills = $invoice->bills;

            foreach ($bills as $bill) {
                $bill->updateQuietly([
                    'invoice_id' => null,
                    'meta->updated_by' => auth()->user()->id. ' - '. auth()->user()->email,
                ]);
            }
        }

        if ($invoice->cartForm) {
            $cartForms = $invoice->cartForm;

            foreach ($cartForms as $cartForm) {
                $cartForm->updateQuietly([
                    'invoice_id' => null,
                    'meta->updated_by' => auth()->user()->id. ' - '. auth()->user()->email,
                ]);
            }
        }
    }

    /**
     * Handle the GeneralInvoice "deleted" event.
     *
     * @param  \App\Models\GeneralInvoice  $invoice
     *
     */
    public function deleted(GeneralInvoice $invoice): void
    {
        if (!auth()->user()->isSuperAdmin()) {
            throw new \Exception('Invoice#' . $invoice->id . ' can not be deleted');
        }

        Log::error('Invoice#' . $invoice->id . ' has been deleted');
    }

    /**
     * Handle the GeneralInvoice "restored" event.
     *
     * @param  \App\Models\GeneralInvoice  $invoice
     *
     */
    public function restored(GeneralInvoice $invoice): void
    {
        //
    }

    /**
     * Handle the GeneralInvoice "force deleted" event.
     *
     * @param  \App\Models\GeneralInvoice  $invoice
     *
     */
    public function forceDeleted(GeneralInvoice $invoice): void
    {
        throw new \Exception('Invoice#' . $invoice->id . ' can not be deleted');
    }
}
