<?php

namespace App\Enums;

enum CronJobFrequency: string
{
    case EVERY_MINUTE = 'every_minute';
    case EVERY_FIVE_MINUTE = 'every_five_minutes';
    case EVERY_TEN_MINUTE = 'every_ten_minutes';
    case EVERY_FIFTEEN_MINUTE = 'every_fifteen_minutes';
    case EVERY_THIRTY_MINUTE = 'every_thirty_minutes';
    case HOURLY = 'hourly';
    case WEEKLY = 'weekly';
    case MONTHLY = 'monthly';
    case ON_REBOOT = 'on_reboot';
    case CUSTOM = 'custom';

    public static function getFrequencyList(): array
    {
        return [
            self::EVERY_MINUTE->value => 'Every Minute',
            self::EVERY_FIVE_MINUTE->value => 'Every Five Minutes',
            self::EVERY_TEN_MINUTE->value => 'Every Ten Minutes',
            self::EVERY_FIFTEEN_MINUTE->value => 'Every Fifteen Minutes',
            self::EVERY_THIRTY_MINUTE->value => 'Every Thirty Minutes',
            self::HOURLY->value => 'Hourly',
            self::WEEKLY->value => 'Weekly',
            self::MONTHLY->value => 'Monthly',
            self::ON_REBOOT->value => 'On Reboot',
            self::CUSTOM->value => 'Custom',
        ];
    }

    public function getFrequencyLabel(): string
    {
        return match ($this) {
            self::EVERY_MINUTE => 'Every Minute',
            self::EVERY_FIVE_MINUTE => 'Every Five Minutes',
            self::EVERY_TEN_MINUTE => 'Every Ten Minutes',
            self::EVERY_FIFTEEN_MINUTE => 'Every Fifteen Minutes',
            self::EVERY_THIRTY_MINUTE => 'Every Thirty Minutes',
            self::HOURLY => 'Hourly',
            self::WEEKLY => 'Weekly',
            self::MONTHLY => 'Monthly',
            self::ON_REBOOT => 'On Reboot',
            self::CUSTOM => 'Custom',
        };
    }

    public function getPatternByFrequencyInRandomTime($default): string
    {
        return match ($this) {
            self::EVERY_MINUTE => '* * * * *',
            self::EVERY_FIVE_MINUTE => implode(',', range(rand(1, 4), 59, 5)).' * * * *',
            self::EVERY_TEN_MINUTE => implode(',', range(rand(1, 9), 59, 10)).' * * * *',
            self::EVERY_FIFTEEN_MINUTE => implode(',', range(rand(1, 14), 59, 15)).' * * * *',
            self::EVERY_THIRTY_MINUTE => implode(',', range(rand(1, 29), 59, 30)).' * * * *',
            self::HOURLY => rand(1, 59).' * * * *',
            self::WEEKLY => rand(1, 59).' '.rand(1, 23).' * * '.rand(0, 6),
            self::MONTHLY => rand(1, 59).' '.rand(1, 23).' '.rand(1, 28).' * *',
            self::ON_REBOOT => '@reboot',
            default => $default,
        };
    }

    public function getPatternByFrequency($default): string
    {
        return match ($this) {
            self::EVERY_MINUTE => '* * * * *',
            self::EVERY_FIVE_MINUTE => '*/5 * * * *',
            self::EVERY_TEN_MINUTE => '*/10 * * * *',
            self::EVERY_FIFTEEN_MINUTE => '*/15 * * * *',
            self::EVERY_THIRTY_MINUTE => '*/30 * * * *',
            self::HOURLY => '0 * * * *',
            self::WEEKLY => '0 0 * * 0',
            self::MONTHLY => '0 0 1 * *',
            self::ON_REBOOT => '@reboot',
            default => $default,
        };
    }

    public function isCustom(): bool
    {
        return $this === self::CUSTOM;
    }
}
