<?php

namespace App\Enums;

use App\Traits\EnumHelper;

enum AvailableStagingDomainEnum: string
{
    use EnumHelper;

    case WP1_SITE = 'wp1.site';
    case WP1_SH = 'wp1.sh';
    case X_CLOUD_APP = 'x-cloud.app';

    case ONE_WP_SITE = '1wp.site';

    case WP1_HOST = 'wp1.host';

    public static function getDefaultDomain(): string
    {
        return config('services.cloudflare_updated.active') ?? 'x-cloud.app';
    }

    public static function getDomains() :array
    {
        if (config('app.env') === 'production') {
            return [
                // self::WP1_SITE, // disable temporality due to limitation of Cloudflare
                self::WP1_SH,
                self::WP1_HOST,
                self::ONE_WP_SITE,
            ];
        }

        ## USE THIS FOR LOCAL DEVELOPMENT
//        if (config('app.env') === 'local') {
//            return [
//                self::WP1_SH,
//                self::WP1_HOST,
//                self::ONE_WP_SITE,
//            ];
//        }

        if (config('app.env') === 'staging') {
            return [
                self::X_CLOUD_APP
            ];
        }

        return [
            self::getDefaultDomain()
        ];
    }
}
