<?php

namespace App\Enums;

use App\Traits\EnumHelper;

enum Stack: string
{
    use EnumHelper;

    case Nginx = 'nginx';
    case OpenLiteSpeed = 'openlitespeed';

    function isNginx(): bool
    {
        return $this->value == self::Nginx->value;
    }

    function isOpenLiteSpeed(): bool
    {
        return $this->value == self::OpenLiteSpeed->value;
    }

    static function availableStacks(): array
    {
        return self::asValue();
    }

    function cachingMechanism(): string
    {
        return match ($this) {
            self::Nginx => 'Full Page Cache',
            self::OpenLiteSpeed => 'LiteSpeed Cache',
        };
    }

    function title(): string
    {
        return match ($this) {
            self::Nginx => 'Nginx',
            self::OpenLiteSpeed => 'OpenLiteSpeed',
        };
    }

    function getServices(): array
    {
        return match ($this) {
            self::Nginx => ['php', 'nginx', 'mysql', 'redis', 'ssh', 'supervisor'],
            self::OpenLiteSpeed => ['lsws', 'mysql', 'redis', 'ssh', 'supervisor'],
        };
    }
}
