<?php

namespace App\Enums;

use App\Models\Site;
use App\Traits\EnumHelper;
use View;

enum CustomNginxEnum: string
{
    use EnumHelper;
    case BEFORE_SERVER_BLOCK = 'before';
    case AFTER_SERVER_BLOCK = 'after';
    case INSIDE_SERVER_BLOCK = 'server';

    const NGINX_CONFIG_VERSION = 2;

    case USE_MY_OWN_CONFIG = 'Use My Own Config';
    case RANKMATH_REWRITE_RULES = 'Rankmath Rewrite Rules';
    case REDIRECT_301 = 'Redirect 301';
    case SMUSH_LOCAL_WEBP = 'Smush Local WebP';
    case IMAGIFY_WEBP = 'Imagify WebP';
    case CONVERTER_FOR_MEDIA = 'Converter For Media';
    case HIDE_MY_WP = 'Hide My WP';

    case SHOW_REAL_IP_PROXIED = 'Show Real IP (CloudFlare-Proxied)';

    public static function getTypes(): array
    {
        return [
            self::BEFORE_SERVER_BLOCK->value => 'Before Server Block',
            self::INSIDE_SERVER_BLOCK->value => 'Inside Server Block',
            self::AFTER_SERVER_BLOCK->value => 'After Server Block',
        ];
    }

    public static function getTemplates(Site $site): array
    {
        $templates = [
            self::USE_MY_OWN_CONFIG->value => 'Use My Own Config',
        ];

        if ($site->isWordPress()) {
            $templates[self::HIDE_MY_WP->value] = [
                'template' => 'Hide My WP',
                'type' => self::INSIDE_SERVER_BLOCK->value,
                'file' => 'hide-my-wp',
                'content' => View::make('scripts.site.custom-nginx-templates.hide-my-wp', [
                    'site' => $site
                ])->render()
            ];
            $templates[self::RANKMATH_REWRITE_RULES->value] = [
                'template' => 'Rankmath Rewrite Rules',
                'type' => self::INSIDE_SERVER_BLOCK->value,
                'file' => 'rankmath-rewrite-rules',
                'content' => View::make('scripts.site.custom-nginx-templates.rankmath-rewrite-rules')->render()
            ];
        }

        $templates[self::SHOW_REAL_IP_PROXIED->value] = [
            'template' => 'Show Real IP (Proxied)',
            'type' => self::INSIDE_SERVER_BLOCK->value,
            'file' => 'cf-real-visitor',
            'content' => View::make('scripts.site.custom-nginx-templates.show-real-ips-proxied')->render()
        ];


//            self::REDIRECT_301->value => [
//                'template' => 'Redirect 301',
//                'type' => self::INSIDE_SERVER_BLOCK->value,
//                'content' => View::make('scripts.site.custom-nginx-templates.test-redirection')->render()
//            ],
//            self::SMUSH_LOCAL_WEBP->value => [
//                'template' => 'Smush Local WebP',
//                'type' => self::INSIDE_SERVER_BLOCK->value,
//                'content' => View::make('scripts.site.custom-nginx-templates.smush-local-webp')->render()
//            ],
//            self::IMAGIFY_WEBP->value => [
//                'template' => 'Imagify WebP',
//                'type' => self::INSIDE_SERVER_BLOCK->value,
//                'content' => View::make('scripts.site.custom-nginx-templates.imagify-webp')->render()
//            ],
//            self::CONVERTER_FOR_MEDIA->value => [
//                'template' => 'Converter For Media',
//                'type' => self::INSIDE_SERVER_BLOCK->value,
//                'content' => View::make('scripts.site.custom-nginx-templates.converter-for-media')->render()
//            ],
        // ];

        return $templates;
    }
}
