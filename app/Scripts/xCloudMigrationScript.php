<?php

namespace App\Scripts;

use App\Models\Site;
use App\Services\Migration\MigrationConnector;

class xCloudMigrationScript extends Script
{
    public string $name = 'Upload x Cloud Migration CLI file into a server';

    private Site $site;
    private MigrationConnector $connector;

    public function __construct(Site $site, MigrationConnector $connector)
    {
        $this->site = $site;
        $this->connector = $connector;
    }

    public function script(): string
    {
        $path = base_path('/xcloud-migration/dest/xcloud-migration.php.stub');

        $clientBody = file_get_contents($path);

        $replacements = [
            '{{XCLOUD_SITE_ID}}' => $this->site->id,
            '{{XCLOUD_SITE_MIGRATION_ID}}' => $this->site->siteMigration->id,
            '{{XCLOUD_EXISTING_SITE_URL}}' => $this->connector->getBaseurl(),
            '{{XCLOUD_NEW_SITE_URL}}' => $this->site->getSiteUrlAttribute(),
            '{{XCLOUD_REMOTE_BASE_URL}}' => $this->connector->getRestEndpoint(),
            '{{XCLOUD_DOWNLOAD_PATH}}' => $this->site->manager()->siteBasePath(),

            '{{XCLOUD_AUTH_TOKEN}}' => $this->connector->getAuthToken(),
            '{{XCLOUD_ENCRIPTION_KEY}}' => $this->connector->getEncryptionKey(),

            '{{XCLOUD_CALLBACK_ENDPOINT}}' => $this->connector->getCallbackUrl(),

            '{{XCLOUD_DATABASE_HOST}}' => 'localhost',
            '{{XCLOUD_DATABASE_NAME}}' => $this->site->database_name,
            '{{XCLOUD_DATABASE_USERNAME}}' => $this->site->database_user,
            '{{XCLOUD_DATABASE_PASSWORD}}' => $this->site->database_password,

            '{{XCLOUD_SKIP_DATABASE_MIGRATION}}' => !$this->site->siteMigration->shouldMigrateDatabase(),
            '{{XCLOUD_SKIP_DATABASE_IMPORT}}' => false,

            '{{XCLOUD_TMP_STROAGE_PATH}}' => '/tmp',
        ];


        return str_replace(array_keys($replacements), array_values($replacements), $clientBody);
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 30;
    }
}
