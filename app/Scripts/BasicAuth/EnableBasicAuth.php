<?php

namespace App\Scripts\BasicAuth;

use App\Models\Site;
use App\Scripts\Script;

class EnableBasicAuth extends Script
{
    public function __construct(private Site $site, private string $name, private string $password)
    {
    }

    public function name(): string
    {
        return "Enableing Basic Auth for ({$this->site->name})";
    }

    public function script(): string
    {
        return view('scripts.site.enableBasicAuth', [
            'script' => $this,
            'server' => $this->site->server,
            'site' => $this->site,
            'name' => $this->name,
            'password' => escapeshellarg($this->password),
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
