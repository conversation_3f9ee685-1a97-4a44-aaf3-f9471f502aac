<?php

namespace App\Scripts;

use App\Models\Site;

class RunxCloudMigration<PERSON>li extends Script
{
    public $name = 'Run xCloud Migration CLI file';

    private $site;

    public function __construct(Site $site)
    {
        $this->site = $site;
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     */
    public function script(): string
    {
        return view('scripts.site.migration.runXcloudMigrationCli', [
            'site' => $this->site,
        ])->render();
    }

    public function timeout(): ?int
    {
        return -1;
    }
}
