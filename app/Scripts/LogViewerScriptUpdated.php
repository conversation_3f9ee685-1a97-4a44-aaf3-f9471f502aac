<?php

namespace App\Scripts;
class LogViewerScriptUpdated extends Script
{
    public $name = 'Reading log file of a Server';

    /** * @var string */
    private $filePath;

    /** * @var int */
    private $logCount;

    public function __construct(string $filePath, $logCount = 10)
    {
        $this->filePath = $filePath;
        $this->logCount = $logCount;
    }

    public function script(): string
    {
        return sprintf('tail -n %s %s', $this->logCount, $this->filePath);
        return sprintf('tail --bytes=%s %s', $this->sizeInBytes, $this->filePath);
    }

    public function timeout(): ?int
    {
        return 10;
    }
}