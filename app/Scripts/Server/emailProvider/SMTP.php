<?php

namespace App\Scripts\Server\emailProvider;

use App\Models\EmailProvider;
use App\Models\Server;
use App\Scripts\Script;

class SMTP extends Script
{
    public function __construct(public EmailProvider $provider,public string $from,public string $to,public string $subject,public string $body)
    {
    }

    public function name(): string
    {
        return "Sending email from provider {$this->provider->provider}";
    }

    public function script(): string
    {
        return view('scripts.server.emailProvider.smtp', [
            'script' => $this,
            'provider' => $this->provider,
            'from' => $this->from,
            'to' => $this->to,
            'subject' => $this->subject,
            'body' => $this->body,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 600;
    }
}
