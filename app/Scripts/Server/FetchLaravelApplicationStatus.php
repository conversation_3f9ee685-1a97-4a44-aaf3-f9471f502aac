<?php

namespace App\Scripts\Server;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;
use Throwable;

class FetchLaravelApplicationStatus extends Script
{
    public function __construct(public Server $server, public Site $site)
    {
    }
    
    public function name(): string
    {
        return "Fetch Laravel Application Status for {$this->site->name}";
    }

    /**
     * @throws Throwable
     */
    public function script(): string
    {
        return view('scripts.server.fetchLaravelApplicationStatus', [
            'server' => $this->server,
            'site' => $this->site,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
