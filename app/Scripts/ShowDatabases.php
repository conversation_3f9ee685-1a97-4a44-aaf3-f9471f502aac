<?php

namespace App\Scripts;

use App\Models\Server;
use App\Models\Site;

class ShowDatabases extends Script
{
    /** * @var Server */
    private $server;

    public function __construct(Server $server)
    {
        $this->server = $server;
    }

    /**
     * Get the name of the script.
     *
     * @return string
     */
    public function name(): string
    {
        return "Reading database list of ({$this->server->name})";
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     */
    public function script(): string
    {
        return view('scripts.database.showDatabases', [
            'script' => $this,
            'server' => $this->server,
        ])->render();
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 30;
    }
}
