<?php

namespace App\Scripts;

use App\Models\Site;
use App\Scripts\Script;
use Illuminate\Support\Str;

class ConfigurePhpMyAdminAuth extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Configure phpMyAdmin Authentication for {$this->site->name}";
    }

    public function script(): string
    {
        $authScript = file_get_contents(resource_path('views/scripts/phpmyadmin/configure.tpl'));
        $pmaGateway = file_get_contents(resource_path('views/scripts/phpmyadmin/pma-gateway.php'));
        $logoutFile = file_get_contents(resource_path('views/scripts/phpmyadmin/logout.php'));
        $config = file_get_contents(resource_path('views/scripts/phpmyadmin/new_config.inc.php'));

        // Replace placeholders with actual values
        $authScript = str_replace('PMA_GATEWAY_CONTENT_HERE', $pmaGateway, $authScript);
        $authScript = str_replace('LOGOUT_FILE_CONTENT_HERE', $logoutFile, $authScript);
        $authScript = str_replace('NEW_CONFIG_CONTENT_HERE', $config, $authScript);
        $authScript = str_replace('SITE_NAME', $this->site->name, $authScript);
        $authScript = str_replace('SITE_USER', $this->site->site_user, $authScript);
        $authScript = str_replace('SITE_CALLBACK_URL', callback_url('/api/site/phpmyadmin/verify'), $authScript);
        $authScript = str_replace('CALLBACK_URL', callback_url('/api/phpmyadmin/verify'), $authScript);
        $authScript = str_replace('BLOWFISH_SECRET', Str::random(32), $authScript);

        return $authScript;
    }

    public function timeout(): ?int
    {
        return 30;
    }
}
