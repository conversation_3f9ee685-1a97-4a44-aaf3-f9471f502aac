<?php

namespace App\Scripts;


use App\Models\PhpVersion;
use App\Models\Server;
use Illuminate\View\View;

class UpdatePhpSettings extends Script
{
    public function __construct(public Server $server, public array $phpSettings)
    {
    }

    public function name(): string
    {
        return "Updating PHP Settings in {$this->server->name} for PHP {$this->phpSettings['php_version']}";
    }

    function script()
    {
        $version =  $this->phpSettings['php_version'] ?? PhpVersion::DEFAULT;
        return view('scripts.php.updateSettings', [
            'server' => $this->server,
            'max_execution_time' => $this->phpSettings['max_execution_time'] ?? 60,
            'max_input_time' => $this->phpSettings['max_input_time'] ?? 60,
            'upload_max_filesize' => ($this->phpSettings['upload_max_filesize'] ?? 50).'M',
            'memory_limit' => ($this->phpSettings['memory_limit'] ?? 256).'M',
            'post_max_size' => ($this->phpSettings['post_max_size'] ?? 50).'M',
            'max_input_vars' => $this->phpSettings['max_input_vars'] ?? PhpVersion::DEFAULT_MAX_INPUT_VARS,
            'php_opcache_enabled' => $this->phpSettings['php_opcache_enabled'] ?? false,
            'session_gc_maxlifetime' => $this->phpSettings['session_gc_maxlifetime'] ?? PhpVersion::DEFAULT_SESSION_GC_MAXLIFETIME,
            'phpVersions' => [$version],
        ])->render();
    }

    function timeout()
    {
        return 30;
    }
}
