<?php

namespace App\Scripts;

use App\Models\Server;

class UnInstallPHPVersion extends Script
{
    /**
     * Server we are delaing with
     * @var Server
     */
    private $server;

    /**
     *  Version of PHP to install
     *
     * @var string
     */
    private $phpVersion;

    /**
     * @param  Server  $server
     * @param $phpVersion
     */
    public function __construct(Server $server, $phpVersion)
    {
        $this->server = $server;
        $this->phpVersion = $phpVersion;
    }

    /**
     * Get the name of the script.
     *
     * @return string
     */
    public function name(): string
    {
        return "Uninstallin php({$this->phpVersion} on {$this->server->name})";
    }

    function script()
    {
        return view('scripts.php.uninstall', [
            'server' => $this->server,
            'phpVersion' => $this->phpVersion,
        ]);
    }

    function timeout()
    {
        return 600;
    }
}
