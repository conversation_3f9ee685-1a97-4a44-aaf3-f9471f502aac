<?php

namespace App\Scripts;

use App\Models\Server;
use App\Models\Site;

class CreateDatabase extends Script
{
    /** * @var Server */
    private $server;

    /** * @var string */
    private $databaseName, $databaseUser, $databaseUserPassword;

    public function __construct(Server $server, string $databaseName, $databaseUser, $databaseUserPassword)
    {
        $this->server = $server;
        $this->databaseName = $databaseName;
        $this->databaseUser = $databaseUser;
        $this->databaseUserPassword = $databaseUserPassword;
    }

    /**
     * Get the name of the script.
     *
     * @return string
     */
    public function name(): string
    {
        return "Creating database on ({$this->server->name})";
    }

    /**
     * Get the contents of the script.
     *
     * @return string
     */
    public function script(): string
    {
        return view('scripts.database.createDatabase', [
            'script' => $this,
            'server' => $this->server,
            'databaseName' => $this->databaseName,
            'databaseUser' => $this->databaseUser,
            'databaseUserPassword' => $this->databaseUserPassword,
        ])->render();
    }

    /**
     * Get the timeout for the script.
     *
     * @return int|null
     */
    public function timeout(): ?int
    {
        return 15;
    }
}
