<?php

namespace App\Scripts;


use App\Models\Server;
use App\Models\Site;

class PurgeRedisObjectCache extends Script
{

     public function __construct(public Server $server, public Site $site) {}

    public function name(): string
    {
        return "Purging Redis Object Cache for Site";
    }

    public function script(): string
    {
        return view('scripts.site.cache.purgeRedisObjectCache', [
            'script' => $this,
            'server' => $this->server,
            'site' => $this->site,
        ])->render();
    }

    function timeout(): int
    {
        return 30;
    }
}
