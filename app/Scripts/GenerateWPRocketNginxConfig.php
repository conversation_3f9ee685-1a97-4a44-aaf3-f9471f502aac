<?php

namespace App\Scripts;

use App\Models\Server;
use App\Models\Site;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GenerateWPRocketNginxConfig extends Script
{
    /**
     * The base path to the local rocket-nginx directory.
     *
     * @var string
     */
    protected $localBasePath;

    /**
     * Files to upload to the server.
     *
     * @var array
     */
    protected $filesToUpload = [
        'rocket-nginx.ini.disabled',
        'rocket-nginx.tmpl',
        'rocket-parser.php'
    ];

    public function __construct(public Site $site)
    {
        $this->localBasePath = app_path('Services/WordPress/PluginHelpers/WPRocket/rocket-nginx');

        // Ensure we have the latest files before uploading
        $this->ensureLatestFiles();
    }

    /**
     * Ensure we have the latest rocket-nginx files.
     *
     * @return void
     */
    protected function ensureLatestFiles()
    {
        try {
            // Check if the files exist and are not empty
            foreach ($this->filesToUpload as $fileName) {
                $filePath = $this->localBasePath . '/' . $fileName;

                // If the file doesn't exist or is empty, try to find the latest backup
                if (!File::exists($filePath) || File::size($filePath) === 0) {
                    // Find the latest backup file
                    $backupFiles = File::glob($this->localBasePath . '/' . $fileName . '.backup.*');

                    if (!empty($backupFiles)) {
                        // Sort by modification time (newest first)
                        usort($backupFiles, function($a, $b) {
                            return filemtime($b) - filemtime($a);
                        });

                        // Use the latest backup
                        $latestBackup = $backupFiles[0];
                        File::copy($latestBackup, $filePath);
                        Log::info("Restored {$fileName} from backup {$latestBackup}");
                    } else {
                        // If no backup exists, try to download from GitHub
                        $this->downloadFromGitHub($fileName);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error("Error ensuring latest rocket-nginx files: {$e->getMessage()}");
        }
    }

    /**
     * Download a file from GitHub.
     *
     * @param string $fileName
     * @return bool
     */
    protected function downloadFromGitHub($fileName)
    {
        try {
            $githubRawUrl = "https://raw.githubusercontent.com/SatelliteWP/rocket-nginx/master/{$fileName}";
            $response = Http::get($githubRawUrl);

            if ($response->successful()) {
                $content = $response->body();
                File::put($this->localBasePath . '/' . $fileName, $content);
                Log::info("Downloaded {$fileName} from GitHub");
                return true;
            } else {
                Log::warning("Failed to download {$fileName} from GitHub: {$response->status()}");
                return false;
            }
        } catch (\Exception $e) {
            Log::error("Error downloading {$fileName} from GitHub: {$e->getMessage()}");
            return false;
        }
    }

    public function name(): string
    {
        return "Generate WP Rocket Nginx Config";
    }

    public function script(): string
    {
        // Regenerate the Nginx configuration for the site
        $this->site->regenerateNginxConf();

        // Check if the rocket-nginx directory exists on the server
        $checkDirResult = $this->site->server->runInline(new InlineScript("if [ -d /etc/nginx/rocket-nginx ]; then echo 'exists'; else echo 'not_exists'; fi"));

        $dirExists = trim($checkDirResult) === 'exists';

        // Create the directory if it doesn't exist, otherwise just ensure we have write permissions
        if (!$dirExists) {
            $this->site->server->runInline(new InlineScript("mkdir -p /etc/nginx/rocket-nginx"));
        } else {
            // Ensure we have write permissions to the directory
            $this->site->server->runInline(new InlineScript("chmod 755 /etc/nginx/rocket-nginx"));
        }

        // Upload each file
        $uploadedFiles = [];
        $errors = [];

        foreach ($this->filesToUpload as $fileName) {
            $localFilePath = $this->localBasePath . '/' . $fileName;
            $remoteFilePath = '/etc/nginx/rocket-nginx/' . $fileName;

            // Check if the local file exists
            if (!File::exists($localFilePath)) {
                $errors[] = "Local file not found: {$localFilePath}";
                continue;
            }

            try {
                // Upload the file
                $this->site->server->uploadFile([
                    'local_path' => $localFilePath,
                    'remote_path' => $remoteFilePath
                ]);

                // Set appropriate permissions
                $this->site->server->runInline(new InlineScript("chmod 644 {$remoteFilePath}"));

                $uploadedFiles[] = $fileName;
            } catch (\Exception $e) {
                $errors[] = "Failed to upload {$fileName}: {$e->getMessage()}";
                Log::error("Failed to upload rocket-nginx file {$fileName} to server {$this->site->server->id}: {$e->getMessage()}");
            }
        }

        // Log the results
        if (!empty($uploadedFiles)) {
            Log::info("Uploaded rocket-nginx files to server {$this->site->server->id}: " . implode(', ', $uploadedFiles));
        }

        if (!empty($errors)) {
            Log::warning("Errors uploading rocket-nginx files to server {$this->site->server->id}: " . implode('; ', $errors));
        }

        return view('scripts.site.cache.generateWPRocketNginxConfig', [
            'site' => $this->site,
            'uploadedFiles' => $uploadedFiles,
            'errors' => $errors
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
