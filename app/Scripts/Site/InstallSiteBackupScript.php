<?php

namespace App\Scripts\Site;

use App\Models\BackupSetting;
use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class InstallSiteBackupScript extends Script
{
    public function __construct(public Server $server, public Site $site, public BackupSetting $backupSetting,public bool $take_backup=false) {}

    public function name(): string
    {
        return "Installing Backup script inside ({$this->site->name} on {$this->server->name})".($this->take_backup ? ' and taking backup' : '');
    }

    /**
     * @throws \Throwable
     */
    public function script(): string
    {
        if ($this->backupSetting->isFull()){
            return view('scripts.site.backup.installBackUpScript', [
                'script' => $this,
                'server' => $this->server,
                'site' => $this->site,
                'backupSetting' => $this->backupSetting,
                'storageProvider'=>$this->backupSetting->storageProvider,
                'isRemoteBackup'=> !$this->backupSetting->is_local,
                'take_backup'=>$this->take_backup,
            ])->render();
        }
        return view('scripts.site.backup.incremental.installIncrementalBackUpScript', [
            'script' => $this,
            'server' => $this->server,
            'site' => $this->site,
            'backupSetting' => $this->backupSetting,
            'storageProvider'=>$this->backupSetting->storageProvider,
            'isRemoteBackup'=> !$this->backupSetting->is_local,
            'take_backup'=>$this->take_backup,
        ])->render();

    }

    public function timeout(): ?int
    {
        return 600;
    }
}
