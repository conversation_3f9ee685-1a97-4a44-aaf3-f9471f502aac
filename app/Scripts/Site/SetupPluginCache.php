<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class SetupPluginCache extends Script
{
    public function __construct(public Site $site, public string $pluginSlug)
    {
    }

    public function name(): string
    {
        return "Installing Redis Object Cache";
    }

    public function script(): string
    {
        dump('UpdateNginxConf: App\Site: '.$this->site->id);

        $this->site->regenerateNginxConf();
    }

    public function timeout(): ?int
    {
        return 120;
    }
}
