<?php

namespace App\Scripts\Site;

use App\Models\Site;
use App\Scripts\Script;

class Install8GFirewall extends Script
{
    public function __construct(public Site $site)
    {
    }

    public function name(): string
    {
        return "Install 8G Firewall on {$this->site->name}";
    }

    /**
     * @throws \Throwable
     */
    public function script(): string
    {
        return view('scripts.wordpress.7g-firewall.install-8g-firewall', [
            'site' => $this->site
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
