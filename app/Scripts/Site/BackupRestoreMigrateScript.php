<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Models\SiteBackupRestoreMigration;
use App\Models\SiteClone;
use App\Models\SiteMigration;
use App\Models\SshKeyPair;
use App\Models\StorageProvider;
use App\Scripts\Script;
use Illuminate\Support\Facades\Log;

class BackupRestoreMigrateScript extends Script
{
    public function __construct(public Site $site, public SiteBackupRestoreMigration $migration)
    {
    }

    public function name(): string
    {
        return "Backup Restore Migrate for {$this->site->name}";
    }

    /**
     * @throws \Throwable
     */
    public function script(): string
    {
        $storageProvider = $this->migration->getStorageProvider();
        if ($storageProvider?->isGDrive()) {
            $fileName = basename($this->migration->getNonSqlFileName());
            $sqlFileName = basename($this->migration->getSqlFileName());
            $fileID = $storageProvider->getFileId(fileName: $fileName);
            $sqlFileID = $storageProvider->getFileId(fileName: $sqlFileName);
            $domain = $this->getDomainNameFromFile(files: [$fileName, $sqlFileName]);
            return view('scripts.site.migration.gdriveBackupRestoreMigration', [
                'server' => $this->site->server,
                'site' => $this->site,
                'migration' => $this->migration,
                'storageProvider' => $storageProvider,
                'fileID' => $fileID,
                'sqlFileID' => $sqlFileID,
                'domain' => $domain
            ])->render();
        }
        $backupFiles = $this->migration->backupFiles();
        $file = $backupFiles->where('is_sql',false)->first();
        $sqlFile = $backupFiles->where('is_sql',true)->first();
        return view('scripts.site.migration.backupRestoreMigration', [
            'server' => $this->site->server,
            'site' => $this->site,
            'migration' => $this->migration,
            'file' => $file,
            'sqlFile' => $sqlFile,
            'storageProvider' => $storageProvider
        ])->render();
    }

    public function timeout(): ?int
    {
        return 180;
    }

    public function getDomainNameFromFile(array $files)
    {
        $domains = array_map(function ($file) {
            if (preg_match('/^([\w.-]+)_s_/', $file, $matches)) {
                return $matches[1]; // Extract the domain
            }
            return null; // Return null if no match
        }, $files);

        // Filter out null values (in case of no matches)
        $domains = array_filter($domains);

        // Return the first domain
        if ($domains){
            return reset($domains);
        }
        return $this->migration->getSourceDomainName();

    }
}
