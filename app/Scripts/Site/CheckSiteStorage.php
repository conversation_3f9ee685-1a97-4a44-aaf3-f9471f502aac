<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class CheckSiteStorage extends Script
{
    public function __construct(public Site $site, public bool $exclude=false)
    {
    }

    public function name(): string
    {
        return "check how much storage {{$this->site->name}} is occupying";
    }

    public function script(): string
    {
        return view('scripts.site.checkSiteStorage', [
            'site' => $this->site,
            'exclude' => $this->exclude,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 60;
    }
}
