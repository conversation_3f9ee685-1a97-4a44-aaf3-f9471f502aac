<?php

namespace App\Scripts\Site;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Script;

class ExecuteDeployScript extends Script
{
    public function __construct(public Site $site, public string $script)
    {
    }

    public function name(): string
    {
        return "Executing deployment on {$this->site->name}";
    }

    public function script(): string
    {
        return view('scripts.site.deployScript', [
            'site' => $this->site,
            'script' => $this->script,
        ])->render();
    }

    public function timeout(): ?int
    {
        return 300;
    }
}
