<?php

namespace App\Traits;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\CartFormStatuses;
use Illuminate\Support\Facades\Log;

trait InvoiceServiceProvider
{
    public function ensureServiceProvided(): void
    {
        if ($this->status !== BillingStatus::Paid) {
            throw new \Exception('GeneralInvoice is not paid for team # ' . $this->team_id);
        }

        if ($this->bills->count() > 0) {

            $this->bills()->update([
                'status' => BillingStatus::Paid
            ]);

            foreach ($this->bills as $bill) {
                if (!$bill->generator->serviceProvidedAfterPayment($bill->service)) {
                    Log::error('Service not provided for bill # ' . $bill->id . ' for team # ' . $this->team_id . ' for service ' . $bill->service->toReadableSentence());
                    Log::info('Providing service for bill # ' . $bill->id . ' for team # ' . $this->team_id . ' for service ' . $bill->service->toReadableSentence());

                    if ($bill->generator->provideService($bill->service)) {
                        Log::info('Provided service for bill # ' . $bill->id . ' for team # ' . $this->team_id . ' for service ' . $bill->service->toReadableSentence());
                    } else {
                        Log::error('Failed to provide service for bill # ' . $bill->id . ' for team # ' . $this->team_id . ' for service ' . $bill->service->toReadableSentence());
                    }
                }
            }
        }

        if ($this->cartForm->count() > 0) {

            $this->cartForm()->update([
                'status' => CartFormStatuses::Paid
            ]);

            foreach ($this->cartForm as $cartForm) {
                if (!$cartForm->serviceProvidedAfterPayment($cartForm->service)) {
                    Log::error('Service not provided for cart form # ' . $cartForm->id . ' for team # ' . $this->team_id . ' for service ' . $cartForm->service->toReadableSentence());

                    if ($cartForm->provideService($cartForm->service)) {
                        Log::info('Provided service for cart form # ' . $cartForm->id . ' for team # ' . $this->team_id . ' for service ' . $cartForm->service->toReadableSentence());
                    } else {
                        Log::error('Failed to provide service for cart form # ' . $cartForm->id . ' for team # ' . $this->team_id . ' for service ' . $cartForm->service->toReadableSentence());
                    }
                }
            }
        }
    }

    public function cancelProvidedServices() : void
    {
        if ($this->status === BillingStatus::Paid) {
            throw new \Exception('GeneralInvoice is paid for team # ' . $this->team_id);
        }

        if ($this->bills->count() > 0) {
            foreach ($this->bills as $bill) {
                if ($bill->generator->cancelService($bill->service)) {
                    Log::info('Cancelled service for bill # ' . $bill->id . ' for team # ' . $this->team_id . ' for service ' . $bill->service->toReadableSentence());
                } else {
                    Log::error('Failed to cancel service for bill # ' . $bill->id . ' for team # ' . $this->team_id . ' for service ' . $bill->service->toReadableSentence());
                }
            }
        }

        if ($this->cartForm->count() > 0) {
            foreach ($this->cartForm as $cartForm) {
                if ($cartForm->cancelService($cartForm->service)) {
                    Log::info('Cancelled service for cart form # ' . $cartForm->id . ' for team # ' . $this->team_id . ' for service ' . $cartForm->service->toReadableSentence());
                } else {
                    Log::error('Failed to cancel service for cart form # ' . $cartForm->id . ' for team # ' . $this->team_id . ' for service ' . $cartForm->service->toReadableSentence());
                }
            }
        }
    }
}
