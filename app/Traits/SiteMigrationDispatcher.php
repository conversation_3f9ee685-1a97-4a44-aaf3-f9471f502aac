<?php

namespace App\Traits;

use App\Jobs\Site\SiteMigrationProgress;
use App\Models\SiteMigration;
use Illuminate\Support\Facades\Bus;
use Throwable;

trait SiteMigrationDispatcher
{
    /**
     * @param  array  $jobs
     * @param  mixed|null  $nextJob
     * @return void
     */
    private function dispatchSiteMigrationChainJob(array $jobs,mixed $nextJob = null): void
    {
        $jobsChain = [];

        foreach ($jobs as $progress => $job) {
            $jobsChain[] = new SiteMigrationProgress($this->siteMigration, $progress);
            $jobsChain[] = $job;
        }

        $siteMigrationId = $this->siteMigration->id;

        if ($nextJob) {
            $jobsChain[] = $nextJob;
        }

        $migration = SiteMigration::find($siteMigrationId);

        $chainedJob = Bus::chain($jobsChain)->catch(function (Throwable $e) use ($migration) {
            if ($migration && !$migration->isCanceled()) {
                //if migration is not canceled mark it as failed
                $migration->markAsMigrationFailure($e->getMessage());
            }

        })->dispatch();

//        $migration->saveMeta('job_batch_id', $chainedJob->id);
    }

    public function failed($e)
    {
        $this->siteMigration->markAsMigrationFailure($e->getMessage());
    }
}
