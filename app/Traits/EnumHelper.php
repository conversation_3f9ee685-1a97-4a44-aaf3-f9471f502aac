<?php

namespace App\Traits;

use Illuminate\Support\Str;

trait EnumHelper
{
    public function is(self $case): bool
    {
        return $this->value === $case->value;
    }

    public function isNot(self $case): bool
    {
        return !$this->is($case);
    }

    public static function fromValue(string $value): static
    {
        return self::from($value);
    }

    public static function fromSlug(string $slug): ?static
    {
        // Replace hyphens with underscores and convert to uppercase
        $formattedSlug = str_replace('-', '_', $slug);

        // Check if the formatted slug is a valid enum case
        foreach (self::cases() as $case) {
            if ($case->value === $formattedSlug) {
                return $case;
            }
        }
        return null;
    }

    /**
     * Retrieve all enum cases as an array of values.
     *
     * This method uses the `cases` method to get all possible instances of the enum and then
     * maps each instance to its value using a callback function. It effectively converts the
     * enum cases into an array of their corresponding values.
     *
     * @return array An array containing the values of all enum cases.
     */
    public static function asValue(): array
    {
        return array_map(function ($case) use (&$values) {
            return $case->value;
        }, self::cases());
    }

    public static function asSlugValues(): array
    {
        return array_map(function ($case) use (&$values) {
            return Str::slug($case->value);
        }, self::cases());
    }

    public static function asLabel(): array
    {
        return array_map(function ($case) use (&$values) {
            return formatTitle($case->name);
        }, self::cases());
    }

    public static function asValueLabel($addNull = false): array
    {
        $valueLabel = array_combine(self::asValue(), self::asLabel());

        return $addNull ? array_merge([ null => '—' ], $valueLabel) : $valueLabel;
    }

    public static function asReverseValueLabel($addNull = false): array
    {
        $valueLabel = array_combine(self::asLabel(), self::asValue());

        return $addNull ? array_merge([ null => '—' ], $valueLabel) : $valueLabel;
    }

    public function asSlug() : string
    {
        return Str::slug($this->value);
    }

    public static function toSelectArray(): array
    {
        return array_combine(self::asValue(), self::asLabel());
    }

    public static function isValid($value): bool
    {
        if (!is_string($value)) {
            $value = $value?->value;
        }

        return in_array($value, self::asValue());
    }

    public function title() : string
    {
        return _title($this->name);
    }

    public function titleFromValue() : string
    {
        return _title($this->value);
    }

    public function titleFromName() : string
    {
        return _title($this->name);
    }

    public function toReadableString() : string
    {
        return $this->titleFromValue();
    }

    public function toLowerCase() : string
    {
        return strtolower($this->value);
    }

    public function toUpperCase() : string
    {
        return strtoupper($this->value);
    }

    public function toCamelCase() : string
    {
        return Str::camel($this->value);
    }

    public function toSnakeCase() : string
    {
        return Str::snake($this->value);
    }
}
