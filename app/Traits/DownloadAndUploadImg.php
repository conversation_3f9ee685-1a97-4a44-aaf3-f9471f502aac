<?php

namespace App\Traits;

use App\Models\Upload;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

trait DownloadAndUploadImg
{
    /**
     * Download and upload images to the server
     *
     * @param array $imageUrls
     * @return array Array of uploaded image IDs
     */
    protected function downloadAndUploadImages(array $imageUrls): array
    {
        $uploadedImageIds = [];

        foreach ($imageUrls as $imageUrl) {
            try {
                // Make sure the URL is absolute
                if (!Str::startsWith($imageUrl, ['http://', 'https://'])) {
                    if (Str::startsWith($imageUrl, '//')) {
                        $imageUrl = 'https:' . $imageUrl;
                    } else {
                        $imageUrl = 'https://www.gsmoled.com/' . ltrim($imageUrl, '/');
                    }
                }

                $this->info("Downloading image from: {$imageUrl}");

                // Download the image
                $response = Http::withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
                ])->get($imageUrl);

                if (!$response->successful()) {
                    $this->warn("Failed to download image from {$imageUrl}: HTTP status " . $response->status());
                    continue;
                }

                // Get image content
                $imageContent = $response->body();

                // Generate a temporary file
                $tempFile = tempnam(sys_get_temp_dir(), 'img_');
                file_put_contents($tempFile, $imageContent);

                // Get file extension from URL
                $extension = pathinfo(parse_url($imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION);
                if (empty($extension)) {
                    $extension = 'jpg'; // Default to jpg if extension can't be determined
                }

                // Create a new Upload instance
                $upload = new Upload();
                $upload->file_original_name = 'product_image_' . Str::random(5);

                // Store the file
                $filePath = 'uploads/all/' . Str::random(20) . '.' . $extension;
                Storage::disk('public')->put($filePath, $imageContent);
                $upload->file_name = $filePath;

                // Set other properties
                $upload->extension = $extension;
                $upload->type = 'image';
                $upload->file_size = strlen($imageContent);
                $upload->save();

                $uploadedImageIds[] = $upload->id;
                $this->info("Successfully uploaded image with ID: {$upload->id}");

                // Clean up the temporary file
                @unlink($tempFile);

                // Add a small delay to avoid overwhelming the server
                usleep(500000); // 0.5 seconds

            } catch (\Exception $e) {
                $this->error("Error uploading image from {$imageUrl}: " . $e->getMessage());
                Log::error("Image upload error: " . $e->getMessage(), [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        return $uploadedImageIds;
    }
}
