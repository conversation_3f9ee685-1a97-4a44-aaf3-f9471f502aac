<?php

namespace App\Traits;

use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

trait InteractsWithPcloud
{
    /**
     * pCloud API hostname - always use api.pcloud.com for authentication
     */
    private string $pcloudApiHostname = 'api.pcloud.com';

    public static function pcloudTokenResponse(string $code): Response
    {
        return Http::asForm()->post('https://api.pcloud.com/oauth2_token', [
            'code' => $code,
            'client_id' => config('services.pcloud.client_id'),
            'client_secret' => config('services.pcloud.client_secret'),
        ]);
    }

    /**
     * pCloud doesn't support refresh tokens, access tokens are long-lived
     * @throws \Exception
     */
    public function refreshPcloudAccessToken()
    {
        // pCloud access tokens are long-lived and don't need refreshing
        // If the token is invalid, user needs to re-authorize
        throw new Exception('pCloud access tokens do not support refresh. Please re-authorize.');
    }

    /**
     * Check if pCloud connection is working
     */
    public function checkPcloudConnection(): bool
    {
        try {
            $response = Http::timeout(10)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $this->access_key_id
                ])
                ->get("https://{$this->pcloudApiHostname}/userinfo");

            // Log only on failure for debugging
            if (!($response->successful() && $response->json('result') === 0)) {
                Log::error('pCloud connection check failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'hostname' => $this->pcloudApiHostname
                ]);
            }

            return $response->successful() && $response->json('result') === 0;
        } catch (Exception $e) {
            Log::error('pCloud connection check failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get pCloud user information
     * @throws \Exception
     */
    public function getPcloudUserInfo()
    {
        $response = Http::timeout(10)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->access_key_id
            ])
            ->get("https://{$this->pcloudApiHostname}/userinfo");

        if ($response->successful() && $response->json('result') === 0) {
            return $response->json();
        }

        throw new Exception('Failed to get pCloud user info: ' . ($response->json('error') ?? 'Unknown error'));
    }

    /**
     * Create a folder in pCloud
     * @throws \Exception
     */
    public function createPcloudFolder(string $folderName, int $parentFolderId = 0)
    {
        $response = Http::timeout(30)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->access_key_id
            ])
            ->asForm()
            ->post("https://{$this->pcloudApiHostname}/createfolder", [
                'name' => $folderName,
                'folderid' => $parentFolderId
            ]);

        // Log only on failure for debugging
        if (!($response->successful() && $response->json('result') === 0)) {
            Log::error('pCloud createfolder failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'folder_name' => $folderName,
                'parent_folder_id' => $parentFolderId
            ]);
        }

        if ($response->successful() && $response->json('result') === 0) {
            return $response->json('metadata');
        }

        $errorMsg = 'Failed to create pCloud folder';
        if ($response->json('result')) {
            $errorCode = $response->json('result');
            $errorMsg .= " (Error code: {$errorCode})";
        }
        if ($response->json('error')) {
            $errorMsg .= ': ' . $response->json('error');
        }

        throw new \Exception($errorMsg);
    }

    /**
     * Get folder ID by name
     * @throws \Exception
     */
    public function getPcloudFolderId(string $folderName, int $parentFolderId = 0)
    {
        $response = Http::timeout(30)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->access_key_id
            ])
            ->get("https://{$this->pcloudApiHostname}/listfolder", [
                'folderid' => $parentFolderId
            ]);

        // Log only on failure for debugging
        if (!($response->successful() && $response->json('result') === 0)) {
            Log::error('pCloud listfolder failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'folder_name' => $folderName,
                'parent_folder_id' => $parentFolderId
            ]);
        }

        if ($response->successful() && $response->json('result') === 0) {
            $contents = $response->json('metadata.contents', []);

            foreach ($contents as $item) {
                if ($item['isfolder'] && $item['name'] === $folderName) {
                    return $item['folderid'];
                }
            }

            // Folder doesn't exist, create it
            $folderData = $this->createPcloudFolder($folderName, $parentFolderId);
            return $folderData['folderid'];
        }

        $errorMsg = 'Failed to get pCloud folder ID';
        if ($response->json('result')) {
            $errorCode = $response->json('result');
            $errorMsg .= " (Error code: {$errorCode})";
        }
        if ($response->json('error')) {
            $errorMsg .= ': ' . $response->json('error');
        }

        throw new \Exception($errorMsg);
    }

    /**
     * Get file ID by name from pCloud
     * @throws \Exception
     */
    public function getPcloudFileId(string $fileName, int $folderId = null)
    {
        $folderId = $folderId ?? $this->endpoint;

        $response = Http::timeout(30)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->access_key_id
            ])
            ->get("https://{$this->pcloudApiHostname}/listfolder", [
                'folderid' => $folderId
            ]);

        if ($response->successful() && $response->json('result') === 0) {
            $contents = $response->json('metadata.contents', []);

            foreach ($contents as $item) {
                if (!$item['isfolder'] && $item['name'] === $fileName) {
                    return $item['fileid'];
                }
            }
        }

        throw new \Exception("File '{$fileName}' not found in pCloud folder");
    }



    /**
     * Download file from pCloud
     * @throws \Exception
     */
    public function downloadPcloudFile(string $fileId, string $targetPath)
    {
        // Get download link
        $response = Http::timeout(30)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->access_key_id
            ])
            ->get("https://{$this->pcloudApiHostname}/getfilelink", [
                'fileid' => $fileId
            ]);

        if (!($response->successful() && $response->json('result') === 0)) {
            throw new \Exception('Failed to get pCloud download link: ' . $response->body());
        }

        $downloadUrl = $response->json('hosts.0') . $response->json('path');

        // Download the file
        $fileResponse = Http::timeout(300)->get($downloadUrl);

        if (!$fileResponse->successful()) {
            throw new \Exception('Failed to download file from pCloud');
        }

        // Save to target path
        $directory = dirname($targetPath);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        file_put_contents($targetPath, $fileResponse->body());

        if (!file_exists($targetPath)) {
            throw new \Exception('Failed to save downloaded file');
        }
    }

    /**
     * Get file metadata from pCloud folder (equivalent to getGDriveFile)
     * @throws \Exception
     */
    public function getPcloudFile(string $directory, string $file)
    {
        // Get the folder ID for the directory
        $folderId = $this->getPcloudFolderId($directory, $this->endpoint ?: 0);

        $response = Http::timeout(30)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->access_key_id
            ])
            ->get("https://{$this->pcloudApiHostname}/listfolder", [
                'folderid' => $folderId
            ]);

        // Log only on failure for debugging
        if (!($response->successful() && $response->json('result') === 0)) {
            Log::error('pCloud listfolder failed for getPcloudFile', [
                'status' => $response->status(),
                'body' => $response->body(),
                'directory' => $directory,
                'file' => $file,
                'folder_id' => $folderId
            ]);
            return null;
        }

        $contents = $response->json('metadata.contents', []);

        // Find the specific file in the folder contents
        $matchingFiles = [];
        foreach ($contents as $item) {
            if (!$item['isfolder'] && $item['name'] === $file) {
                $matchingFiles[] = [
                    'id' => $item['fileid'],
                    'name' => $item['name'],
                    'size' => $item['size'] ?? 0,
                    'modified' => $item['modified'] ?? null,
                    'created' => $item['created'] ?? null,
                    'contenttype' => $item['contenttype'] ?? null,
                    'hash' => $item['hash'] ?? null
                ];
            }
        }

        return $matchingFiles;
    }

    /**
     * Rename a folder in pCloud
     * @throws \Exception
     */
    public function renamePcloudDirectory(string $folderId, string $newName): bool
    {
        $response = Http::timeout(30)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->access_key_id
            ])
            ->asForm()
            ->post("https://{$this->pcloudApiHostname}/renamefolder", [
                'folderid' => $folderId,
                'toname' => $newName
            ]);

        // Log only on failure for debugging
        if (!($response->successful() && $response->json('result') === 0)) {
            Log::error('pCloud renamefolder failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'folder_id' => $folderId,
                'new_name' => $newName
            ]);
        }

        if ($response->successful() && $response->json('result') === 0) {
            return true;
        }

        $errorMsg = 'Failed to rename pCloud directory';
        if ($response->json('result')) {
            $errorCode = $response->json('result');
            $errorMsg .= " (Error code: {$errorCode})";
        }
        if ($response->json('error')) {
            $errorMsg .= ': ' . $response->json('error');
        }

        throw new \Exception($errorMsg);
    }

    /**
     * Check if this is a pCloud provider
     */
    public function isPcloud(): bool
    {
        return $this->provider === self::PCLOUD;
    }

    /**
     * Get pCloud files with pagination (equivalent to getGoogleDriveFiles)
     * @throws \Exception
     */
    public function getPcloudFiles(string $location, int $perPage = 20, string $nextPageToken = null)
    {
        // Get the folder ID for the location
        $folderId = $this->getPcloudFolderId($location, $this->endpoint ?: 0);

        $response = Http::timeout(30)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->access_key_id
            ])
            ->get("https://{$this->pcloudApiHostname}/listfolder", [
                'folderid' => $folderId
            ]);

        if ($response->successful() && $response->json('result') === 0) {
            $contents = $response->json('metadata.contents', []);

            // Filter out folders, only return files
            $files = collect($contents)
                ->filter(fn($item) => !$item['isfolder'])
                ->map(function ($item) {
                    return [
                        'id' => $item['fileid'],
                        'name' => $item['name'],
                        'size' => $item['size'] ?? 0,
                        'modifiedTime' => isset($item['modified']) ? date('c', $item['modified']) : null,
                        'contenttype' => $item['contenttype'] ?? null
                    ];
                })
                ->values()
                ->toArray();

            return [
                'files' => $files,
                'nextPageToken' => null, // pCloud doesn't use pagination tokens like Google Drive
                'directory' => $location
            ];
        }

        throw new \Exception('Failed to get pCloud files: ' . $response->body());
    }

    /**
     * Filter pCloud files (equivalent to filterGdriveFiles)
     */
    public function filterPcloudFiles(array $files): array
    {
        $pattern = '/(\.tar\.gz$)|(\.sql$)/';
        return collect($files)
            ->filter(fn ($file) => preg_match($pattern, basename($file['name'])))
            ->map(function ($file) {
                $modifiedTime = $file['modifiedTime'];
                if (preg_match('/\d{14}/', $file['name'], $matches)) {
                    $modifiedTime = $matches[0];
                }
                return [
                    'Key' => $file['name'],
                    'Size' => format_bytes($file['size']),
                    'LastModified' => $modifiedTime,
                    'DateTime' => \Illuminate\Support\Carbon::parse($modifiedTime)->format('Y-m-d h:i A'),
                    'is_sql' => \Illuminate\Support\Str::endsWith($file['name'], '.sql')
                ];
            })
            ->groupBy('DateTime')
            ->filter(fn ($files) => $files->count() === 2)
            ->toArray();
    }

    /**
     * Delete a single file from pCloud
     * @throws \Exception
     */
    public function deletePcloudFile(string $fileId): bool
    {
        $response = Http::timeout(30)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->access_key_id
            ])
            ->asForm()
            ->post("https://{$this->pcloudApiHostname}/deletefile", [
                'fileid' => $fileId
            ]);

        // Log only on failure for debugging
        if (!($response->successful() && $response->json('result') === 0)) {
            Log::error('pCloud deletefile failed', [
                'status' => $response->status(),
                'body' => $response->body(),
                'file_id' => $fileId
            ]);
        }

        if ($response->successful() && $response->json('result') === 0) {
            return true;
        }

        $errorMsg = 'Failed to delete pCloud file';
        if ($response->json('result')) {
            $errorCode = $response->json('result');
            $errorMsg .= " (Error code: {$errorCode})";
        }
        if ($response->json('error')) {
            $errorMsg .= ': ' . $response->json('error');
        }

        throw new \Exception($errorMsg);
    }

    /**
     * Delete multiple files from pCloud (equivalent to deleteGoogleDriveFiles)
     * @throws \Exception
     */
    public function deletePcloudFiles(array $fileIds): bool
    {
        foreach ($fileIds as $fileId) {
            $this->deletePcloudFile($fileId);
        }
        return true;
    }

    /**
     * Get pCloud file ID for backup files specifically
     * This method handles backup files where the file ID is stored in the file_path field
     */
    public function getPcloudBackupFileId(string $fileName): ?string
    {
        // For backup files, try to find the file ID from the BackupFile record
        $backupFile = \App\Models\BackupFile::where('file_name', $fileName)
            ->where('storage_provider_id', $this->id)
            ->first();

        if ($backupFile && $backupFile->file_path) {
            $folderId = $backupFile->file_path;

            // The file_path contains the folder ID, we need to search for the individual file within this folder
            try {
                $response = Http::timeout(30)
                    ->withHeaders([
                        'Authorization' => 'Bearer ' . $this->access_key_id
                    ])
                    ->get("https://{$this->pcloudApiHostname}/listfolder", [
                        'folderid' => $folderId
                    ]);

                if ($response->successful() && $response->json('result') === 0) {
                    $contents = $response->json('metadata.contents', []);

                    // Find the file by name
                    foreach ($contents as $item) {
                        if (!$item['isfolder'] && $item['name'] === $fileName) {
                            return (string) $item['fileid'];
                        }
                    }

                    \Log::error('File not found in pCloud folder', [
                        'file_name' => $fileName,
                        'folder_id' => $folderId,
                        'folder_contents' => $contents
                    ]);
                } else {
                    \Log::error('Failed to list pCloud folder contents', [
                        'folder_id' => $folderId,
                        'response' => $response->body()
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('Exception while searching for file in pCloud folder', [
                    'file_name' => $fileName,
                    'folder_id' => $folderId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        try {
            return $this->getPcloudFileId($fileName);
        } catch (\Exception $e) {
            \Log::error('Failed to get pCloud file ID for backup file', [
                'file_name' => $fileName,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
}
