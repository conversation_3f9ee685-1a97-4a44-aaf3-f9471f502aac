<?php

namespace App\Traits;
use App\Models\BackupFile;
use Facades\App\Services\Shell\ShellProcessRunner;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Tightenco\Collect\Support\Arr;

trait InteractsWithGoogleDrive
{
    public static function tokenResponse(string $code): Response
    {
        return Http::asForm()->post('https://oauth2.googleapis.com/token', [
            'code' => $code,
            'client_id' => config('services.google_drive.client_id'),
            'client_secret' => config('services.google_drive.client_secret'),
            'redirect_uri' => config('services.google_drive.redirect_uri'),
            'grant_type' => 'authorization_code',
        ]);
    }


    /**
     * @throws \Exception
     */
    public function doesDirectoryExists(string $folderName): bool
    {
        $response = Http::withToken($this->access_key_id)
            ->get('https://www.googleapis.com/drive/v3/files', [
                'q' => "name = '$folderName' and mimeType = 'application/vnd.google-apps.folder' and trashed = false and 'root' in parents",
                'fields' => 'files(id, name, parents)'
            ]);
        if ($response->successful()) {
            return count($response->json('files')) > 0;
        }
        return throw new \Exception('Failed to check if directory exists');
    }

    /**
     * @throws \Exception
     */
    public function makeDirectory(string $folderName): bool
    {
        if ($this->doesDirectoryExists($folderName)) {
            return true;
        }
        $response = Http::withToken($this->access_key_id)
            ->post('https://www.googleapis.com/drive/v3/files', [
                'name' => $folderName,
                'mimeType' => 'application/vnd.google-apps.folder'
            ]);
        if ($response->successful()) {
            return true;
        }
        return throw new \Exception('Failed to create directory');
    }

    /**
     * @throws \Exception
     */
    public function refreshAccessToken()
    {
        $response = Http::asForm()->post('https://oauth2.googleapis.com/token', [
            'refresh_token' => $this->secret_key,
            'client_id' => config('services.google_drive.client_id'),
            'client_secret' => config('services.google_drive.client_secret'),
            'grant_type' => 'refresh_token',
        ]);
        if ($response->successful()) {
            $this->update([
                'access_key_id' => $response->json('access_token'),
            ]);
            return $this->access_key_id;
        }
        return throw new \Exception('Failed to refresh access token');
    }

    /**
     * @throws \Exception
     */
    public function getDriveFolderId()
    {
        $this->refreshAccessToken();
        if (!$this->doesDirectoryExists($this->bucket)) {
             $this->makeDirectory($this->bucket);
        }
        return $this->getFolderId($this->bucket);
    }

    /**
     * @throws \Exception
     */
    private function getFolderId(string $folder, string $parent = 'root')
    {
        $response = Http::withToken($this->access_key_id)
            ->get('https://www.googleapis.com/drive/v3/files', [
                'q' => "name = '$folder' and mimeType = 'application/vnd.google-apps.folder' and trashed = false and '$parent' in parents",
                'fields' => 'files(id, name, parents)'
            ]);
        if ($response->successful()) {
            $files = $response->json('files');
            return collect($files)
                ->filter(fn($file) => $file['name'] === $folder)
                ->pluck('id')
                ->first();
        }
        return throw new \Exception('Failed to get folder id');
    }

    public function getAllData()
    {
        $response = Http::withToken($this->access_key_id)
            ->get('https://www.googleapis.com/drive/v3/files', [
                'fields' => 'files(id, name, mimeType)'
            ]);
        if ($response->successful()) {
            return $response->json();
        }
        return throw new \Exception('Failed to get all data');
    }

    /**
     * @throws \Exception
     */
    public function renameDirectory(string $folderId, string $newName): bool
    {
        $this->refreshAccessToken();
        $response = Http::withToken($this->access_key_id)
            ->patch("https://www.googleapis.com/drive/v3/files/$folderId", [
                'name' => $newName
            ]);
        if ($response->successful()) {
            return true;
        }
        return throw new \Exception('Failed to rename directory');
    }

    public function checkFolderExists(string $folderId=null): bool
    {
        $folderId = $folderId ?? $this->endpoint;
        $response = Http::withToken($this->access_key_id)
            ->get("https://www.googleapis.com/drive/v3/files/$folderId",[
                'q' => "mimeType = 'application/vnd.google-apps.folder' and trashed = 'false'",
                'fields' => 'id, name, trashed'
            ]);
        if ($response->successful()){
            return $response->json('trashed',true) === false;
        }
        return false;
    }

    public function checkTokenExpiration(): bool
    {
        $response = Http::withToken($this->access_key_id)
            ->get('https://www.googleapis.com/drive/v3/about');
        return $response->successful();
    }

    /**
     * @throws \Exception
     */
    public function generateDownloadLink(BackupFile $file,?string $shareEmail=null): string
    {
        $fileId = $this->getFileId(fileName: $file->file_name);
        $downloadLink = "https://drive.google.com/uc?export=download&id=$fileId";
        if ($shareEmail === null) {
            return $downloadLink;
        }
        $response = Http::withToken($this->access_key_id)
            ->post("https://www.googleapis.com/drive/v3/files/$fileId/permissions", [
                'role' => 'reader',
                'type' => 'user',
                'emailAddress' => $shareEmail
            ]);
        if ($response->successful()) {
            return $downloadLink;
        }
        return throw new \Exception('Failed to generate download link');
    }

    /**
     * @throws \Exception
     */
    public function getFileId(string $fileName)
    {
        $this->refreshAccessToken();
        $response = Http::withToken($this->access_key_id)
            ->get('https://www.googleapis.com/drive/v3/files', [
                'q' => "name = '$fileName' and trashed = false",
                'fields' => 'files(id, name, mimeType)'
            ]);
        if ($response->successful()) {
            return $response->json('files.0.id');
        }
        return throw new \Exception('Failed to get file id');
    }

    /**
     * @throws \Exception
     */
    public function getFileSizeById(string $fileId)
    {
        $response = Http::withToken($this->access_key_id)
            ->get("https://www.googleapis.com/drive/v3/files/$fileId", [
                'fields' => 'size'
            ]);
        if ($response->successful()) {
            return $response->json('size');
        }
        return throw new \Exception('Failed to get file size');
    }

    /**
     * @throws \Exception
     */
    public function getAccessToken()
    {
        try{
            $this->refreshAccessToken();
            return $this->access_key_id;
        }catch (\Exception $e){
            return  null;
        }

    }

    /**
     * @param string|null $folderId The folder id to get files from
     * @param string|int $daysAgo The number of days ago to get files from
     * @throws \Exception
     */
    public function getFiles(string $folderId=null, string|int $daysAgo=null)
    {
        $access_key_id = $this->getAccessToken();
        $folderId = '1V61csOLZCUmN6L3F-LUjMqazehWQ6xVe';
        $modifiedTime = '2024-12-03T05:46:07+00:00';
        $response = Http::withToken($access_key_id)
            ->get('https://www.googleapis.com/drive/v3/files', [
                'q' => "'$folderId' in parents and trashed = false and modifiedTime < '{$modifiedTime}'",
                'fields' => 'files(id, name, modifiedTime)'
            ]);
        if ($response->successful()) {
            return array_map(function($file) use($modifiedTime){
                return [
                    'id' => $file['id'],
                    'name' => $file['name'],
                    'modifiedTime' => Carbon::parse($file['modifiedTime'])->format('Y-m-d h:i:s A'),
                    'compareTime' =>  Carbon::parse($file['modifiedTime'])->gte(Carbon::parse($modifiedTime))
                ];
            }, $response->json('files'));
        }
        return throw new \Exception('Failed to get files');
    }

    /**
     * @throws \Exception
     */
    public function getSiteDirectoryId(string $siteDirectory)
    {
        return $this->getFolderId(folder: $siteDirectory,parent: $this->endpoint);
    }

    public function uploadFile(string $filePath, string $folderId=null): bool
    {
        $folderId = $folderId ?? $this->endpoint;
        $response = Http::withToken($this->access_key_id)
            ->attach('file', file_get_contents($filePath), basename($filePath))
            ->post('https://www.googleapis.com/upload/drive/v3/files', [
                'uploadType' => 'media',
                'name' => basename($filePath),
                'parents' => [$folderId]
            ]);
        if ($response->successful()) {
            return true;
        }
        return throw new \Exception('Failed to upload file');
    }

    public function checkConnection(): bool
    {
        try{
            $driveFolderId = $this->getDriveFolderId();
            return $driveFolderId !== null;
        }catch (\Exception $e){
            return false;
        }
    }

    /**
     * @throws \Exception
     */
    public function deleteDriveFile(string $fileId): bool
    {
        $response = Http::withToken($this->access_key_id)
            ->delete("https://www.googleapis.com/drive/v3/files/$fileId");
        if ($response->successful()) {
            return true;
        }
        return throw new \Exception('Failed to delete files');
    }

    /**
     * @throws \Exception
     */
    public function deleteGoogleDriveFiles(array $fileIds): bool
    {
        $this->refreshAccessToken();
        foreach ($fileIds as $fileId){
            $this->deleteDriveFile($fileId);
        }
        return true;
    }

    /**
     * @throws \Exception
     */
    public function getGoogleDriveFiles(string $location, int $perPage=20, string $nextPageToken=null)
    {
        $this->refreshAccessToken();
        $folderId =$this->getFolderId(folder: $location,parent: $this->endpoint);
        $response = Http::withToken($this->access_key_id)
            ->get('https://www.googleapis.com/drive/v3/files', [
                'q' => "'$folderId' in parents and trashed = false",
                'fields' => 'nextPageToken, files(id, name, mimeType, size, modifiedTime)',
                'pageSize' => $perPage,
                'pageToken' => $nextPageToken
            ]);
        if ($response->successful()) {
            return $response->json();
        }
        return throw new \Exception('Failed to get files');
    }

    public function filterGdriveFiles(array $files): array
    {
        $pattern = '/(\.tar\.gz$)|(\.sql$)/';
        return collect($files)
            ->filter(fn ($file) => preg_match($pattern, basename($file['name'])))
            ->map(function ($file) {
                $modifiedTime = $file['modifiedTime'];
                if (preg_match('/\d{14}/', $file['name'], $matches)) {
                    $modifiedTime = $matches[0];
                }
                return [
                    'Key' => $file['name'],
                    'Size' => format_bytes($file['size']),
                    'LastModified' => $modifiedTime,
                    'DateTime' => Carbon::parse($modifiedTime)->format('Y-m-d h:i A'),
                    'is_sql' => Str::endsWith($file['name'], '.sql')
                ];
            })
            ->groupBy('DateTime')
            ->filter(fn ($files) => $files->count() === 2)
            ->toArray();
    }

    /**
     * @throws \Exception
     */
    public function getGDriveFile(string $directory, string $file)
    {
        $this->refreshAccessToken();
        $folderId =$this->getFolderId(folder: $directory,parent: $this->endpoint);
        $response =  Http::withToken($this->access_key_id)
            ->get('https://www.googleapis.com/drive/v3/files', [
                'q' => "name = '$file' and trashed = false and '$folderId' in parents",
                'fields' => 'files(id, name, mimeType, size, modifiedTime)'
            ]);
        if ($response->successful()) {
            return $response->json('files');
        }
    }
}
