<?php

namespace App\Policies;

use App\Models\ManualInvoice;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ManualInvoicePolicy
{
    use HandlesAuthorization;

    /**
     * @param User $user
     * @return true
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    public function view(User $user, ManualInvoice $invoice): bool
    {
        return $user->current_team_id == $invoice->team->id && $user->can('createBilling', $invoice->team);
    }
}
