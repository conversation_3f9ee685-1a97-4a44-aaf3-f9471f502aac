<?php

namespace App\Policies;

use App\Models\Server;
use App\Models\Team;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Support\Facades\Gate;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class TeamPolicy
{
    use HandlesAuthorization;
    /**
     * Determine whether the user can view any models.
     *
     * @param User $user
     * @return mixed
     */
    public function viewAny(User $user)
    {
        if (isWhiteLabel()) {
            return false;
        }
        return true;
    }

    public function isOwner(User $user, Team $team): bool
    {
        if (isWhiteLabel()) {
            return false;
        }

        return $user->ownsTeam($team);
    }

    /**
     * Determine whether the user can view the model.
     *
     * @param User $user
     * @param Team $team
     * @return mixed
     */
    public function view(User $user, Team $team)
    {
        if (isWhiteLabel()) {
            return false;
        }
        return $user->belongsToTeam($team) && $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]);
    }

    /**
     * Determine whether the user can create models.
     *
     * @param User $user
     * @return bool
     */
    public function create(User $user): bool
    {
        if (isWhiteLabel()) {
            return false;
        }
        return $user->ownedTeams()->count() < $user->maxTeamCount();
    }

    /**
     * Determine whether the user can update the model.
     *
     * @param User $user
     * @param Team $team
     * @return mixed
     */
    public function update(User $user, Team $team)
    {
        if (isWhiteLabel()) {
            return false;
        }
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'team:edit');

    }

    /**
     * Determine whether the user can add team members.
     *
     * @param User $user
     * @param Team $team
     * @return bool
     */
    public function addTeamMember(User $user, Team $team)
    {
        if (isWhiteLabel()) {
            return false;
        }
        Gate::denyIf(!$team->canAddMember(), 'You have reached the maximum number of team members allowed for this team.');

        return ($user->ownsTeam($team) || $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'team:create-member')) && $team->canAddMember();
    }
    /**
     * Determine whether the user can add team members from the invitation link.
     *
     * @param  \App\Models\User  $user
     * @param  \App\Models\Team  $team
     * @return bool
     */
    public function addTeamMemberByInvitation(User $user, Team $team): bool
    {
        if (isWhiteLabel()) {
            return false;
        }
        return ($user->ownsTeam($team) || $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'team:create-member'));
    }

    /**
     * Determine whether the user can add team servers.
     *
     * @param User $user
     * @param Team $team
     * @return bool
     */
    public function addServer(User $user, Team $team): bool
    {
        // $team->canAccessWithBilling(Server::class) TODO: implement this @Arif bro @Billing
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:create');
    }

    public function addFreeServer(User $user, Team $team): bool
    {
        // billing active is not required for free servers
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:create');
    }

    /**
     * Determine whether the user can add team servers.
     *
     * @param User $user
     * @param Team $team
     * @return bool
     */
    public function deleteServer(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN], allowSupport: false) && $user->hasTeamPermission($team, 'server:delete', allowSupport: false);
    }

    public function resizeServer(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:resize-server');
    }

    public function manageProviderServerBackup(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-provider-backup');
    }

    public function addSite(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'site:create');
    }

    public function cloneServer(User $user, Team $team): bool
    {
        //clone server is creating sites in another server, so we are checking site:create permission
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'site:create');
    }

    public function manageDomain(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:manage-domain');
    }

    public function manageCache(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:manage-caching');
    }

    public function reStartServer(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-services');
    }

    public function archiveServer(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:archive');
    }

    public function createDB(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-databases');
    }

    public function deleteDB(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-databases');
    }

    public function createServerMonitors(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-services');
    }

    public function deleteServerMonitors(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'manage-monitoring');
    }

    public function manageServerService(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN],allowSupport: $user->isSupportLevel2()) && $user->hasTeamPermission($team, 'server:manage-services',allowSupport: $user->isSupportLevel2());
    }

    public function managePhp(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-php');
    }

    public function manageLogs(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-logs-and-events');
    }
    public function clearServerLogs(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN],allowSupport: $user->isSupportLevel2()) && $user->hasTeamPermission($team, 'server:manage-logs-and-events',allowSupport: $user->isSupportLevel2());
    }
    public function serverEvents(User $user, Team $team): bool
    {
        // added super admin to access any server events
        return $user->isSuperAdmin() || ($user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-logs-and-events'));
    }

    public function siteEvents(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:manage-events');
    }

    /**
     * Determine whether the user can update team member permissions.
     *
     * @param User $user
     * @param Team $team
     * @return mixed
     */
    public function updateTeamMember(User $user, Team $team)
    {
        if (isWhiteLabel()) {
            return false;
        }
        return $user->ownsTeam($team) || $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'team:create-member');
    }

    /**
     * Determine whether the user can remove team members.
     *
     * @param User $user
     * @param Team $team
     * @return bool
     */
    public function removeTeamMember(User $user, Team $team): bool
    {
        if (isWhiteLabel()) {
            return false;
        }
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'team:delete-member');
    }

    /**
     * Determine whether the user can delete the model.
     *
     * @param User $user
     * @param Team $team
     * @return bool
     */
    public function delete(User $user, Team $team): bool
    {
        return $user->current_team_id !== $team->id && $user->id == $team->user_id && !$team->personal_team && $team->servers()->doesntExist();
    }


    public function manageProvider(User $user, Team $team): bool
    {
        if ($team->getPlan() && !isWhiteLabel()) {
            return $user->hasAnyRole($team, [Team::TEAM_ADMIN,Team::SERVER_ADMIN]) &&
            $user->hasTeamPermission($team, 'server:add-provider') ||
            $user->hasTeamPermission($team, 'server:delete-provider') ||
            $user->hasTeamPermission($team, 'server:edit-provider');
        }
        return false;
    }

    public function manageStorageProvider(User $user, Team $team): bool
    {
        if ($team->getPlan()){
            return $user->hasAnyRole($team, [Team::TEAM_ADMIN,Team::SERVER_ADMIN]) &&
                $user->hasTeamPermission($team, 'server:add-provider') ||
                $user->hasTeamPermission($team, 'server:delete-provider') ||
                $user->hasTeamPermission($team, 'server:edit-provider');
        }
       return false;
    }

    public function manageNotifications(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'account:manage-notifications');
    }

    public function manageEvents(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'account:manage-events');
    }

    public function createBilling(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'billing:create');
    }

    public function manualInvoice(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'billing:create') &&  $team?->activePlan?->support_manual_billing;
    }

    public function updateBilling(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'billing:update');
    }

    public function cancelBilling(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'billing:cancel');
    }

    public function manageAccountLog(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'account:manage-logs');
    }

    public function manageArchiveServers(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'account:manage-archive-servers');
    }

    public function serverAccess(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-access');
    }

    public function mangeCronJob(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:cron-job');
    }

    public function manageSupervisorProcess(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-services');
    }

    public function manageSshKey(User $user, Team $team): bool
    {
        if ($team->getPlan()) {
            return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'account:manage-ssh');
        }
        return false;
    }

    public function deleteServerProvider(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:delete-provider');
    }

    public function viewSite(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:create');
    }

    public function deleteSite(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN], allowSupport: false) && $user->hasTeamPermission($team, 'site:delete',allowSupport: false);
    }
    public function disableSite(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:disable');
    }

    public function siteSettings(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:settings');
    }
    public function editSiteSecuritySettings(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN],allowSupport: $user->isSupportLevel2()) && $user->hasTeamPermission($team, 'site:settings',allowSupport: $user->isSupportLevel2());
    }
    public function siteDatabase(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:manage-database');
    }

    public function siteFileManager(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN],allowSupport: $user->isSupportLevel2()) && $user->hasTeamPermission($team, 'site:manage-file-manager');
    }

    public function siteCustomNginx(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:manage-custom-nginx');
    }

    public function siteUpdate(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN],allowSupport: $user->isSupportLevel2()) && $user->hasTeamPermission($team, 'site:manage-update',allowSupport: $user->isSupportLevel2());
    }

    public function magicLogin(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:access-magic-login');
    }

    public function siteMonitors(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:manage-monitoring');
    }

    public function siteSSL(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:manage-ssl');
    }

    public function siteSSHsFTP(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:manage-ssh-sftp');
    }

    public function siteRedirect(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:manage-redirects');
    }

    public function siteRedirectManage(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN],allowSupport: $user->isSupportLevel2()) && $user->hasTeamPermission($team, 'site:manage-redirects',allowSupport: $user->isSupportLevel2());
    }

    public function manageEmailProvider(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN],allowSupport: false) && $user->hasTeamPermission($team, 'site:manage-email-providers', allowSupport: false);
    }

    public function cloudflareIntegration(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN]) && $user->hasTeamPermission($team, 'account:manage-integrations');
    }

    public function leaveTeam(User $user, Team $team): bool
    {
        return $user->current_team_id !== $team->id && $user->id !== $team->user_id && $team->hasUser($user);
    }

    public function manageFirewallRule(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-firewall');
    }

    public function manageBluePrint(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SITE_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'site:blueprint-manage');
    }

    public function deployStagingSite(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:deploy-staging');
    }

    public function manageStagingSite(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:manage-staging');
    }

    public function vulnerabilityScan(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN, Team::SITE_ADMIN]) && $user->hasTeamPermission($team, 'site:vulnerability-scan');
    }

    public function vulnerabilitySettings(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:vulnerability-settings');
    }

    public function securityUpdate(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:security-update');
    }

    public function manageServerActions(User $user, Team $team): bool
    {
        return $user->hasAnyRole($team, [Team::TEAM_ADMIN, Team::SERVER_ADMIN]) && $user->hasTeamPermission($team, 'server:manage-server-actions');
    }
}
