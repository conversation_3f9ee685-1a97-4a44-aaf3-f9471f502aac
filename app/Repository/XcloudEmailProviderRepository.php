<?php

namespace App\Repository;


use App;
use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\CartFormStatuses;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\xCloudEmailProviderPlanEnum;
use App\Models\CartForm;
use App\Models\EmailProvider;
use App\Models\GeneralInvoice;
use App\Models\PaymentMethod;
use App\Models\Product;
use App\Models\Team;
use Arr;
use GuzzleHttp\Exception\GuzzleException;
use Log;
use mysql_xdevapi\Exception;
use Stripe\Checkout\Session;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\StripeClient;

class XcloudEmailProviderRepository
{
    public function __construct(public EmailProvider $emailProvider, public Team $team)
    {
    }

    public function setupXcloudEmailProvider()
    {
        // check if team has already subaccount on elastic email
        if (!$this->team->hasElasticEmailSubAccount()) {
            $this->createElasticEmailSubAccount();
        }else{
            // update credit on elastic email for existing subaccount
            $this->team->updateElasticEmailSubAccountCredit($this->emailProvider->plan, $this->emailProvider->email_limit);
        }

        $this->emailProvider->update([
           'api_key' => $this->team->getElasticEmailSubAccountApiKey()
        ]);
    }

    /**
     * @throws GuzzleException
     * @throws \Exception
     */
    public function handlePayment()
    {
        $product = Product::where('slug', $this->emailProvider->plan)->first();
        $uniqueId = uniqid();
        $paymentMethod = $this->team->activePaymentMethod()->first();

        if(!team()?->activePlan?->support_manual_billing && empty($paymentMethod)){
            throw new \Exception('No active payment method found for team: ' . $this->team->id);
        }

        if(!$product){
            throw new \Exception('No product found for this email provider');
        }

        // create a cart
        $cart = CartForm::create([
            'model' => $product->service_type->getServiceModel(),
            'email'=> explode('@', $this->team->email)[0] . '_' . $uniqueId . '@' . explode('@', $this->team->email)[1],
            'status'=> CartFormStatuses::Pending,
            'service' => $product->service_type,
            'product_id' => $product->id
        ]);

        try {
            if($product->price > 0){
                $cart->setStatusPaymentProcessing();

                Stripe::setApiKey(config('services.stripe.secret_key'));
                $stripe = new StripeClient(config('services.stripe.secret_key'));

                $customer = Customer::create([
                    'name' => $this->team->owner->name,
                    'email' => explode('@', $this->team->email)[0] . '_' . $uniqueId . '@' . explode('@', $this->team->email)[1],
                    'description' => 'Customer ' . $this->team->name . ' , added when purchasing Email Provider ',
                ]);

                $queryParams = "session_id={CHECKOUT_SESSION_ID}&product_id={$product->id}";

                $session = Session::create([
                    'payment_method_types' => ['card'],
                    'mode' => 'setup',
                    'customer' => $customer->id,
                    'success_url' => route('stripe.product.checkout.success', [], true) . "?{$queryParams}",
                    'cancel_url' =>  route('stripe.product.checkout.cancelled')
                ]);

                $cart->update([
                    'checkout_session_id' => $session->id,
                    'meta->stripe->checkout_session' => [
                        'customer_id' => $customer->id,
                        'checkout_url' => $session->url,
                    ],
                    'team_id' => $this->team->id,
                    'product_id' => $product->id
                ]);

                // take payment and generate invoice if not free package
                if($product->price > 0){
                    // generate bill
                    $bill = $this->emailProvider->cost($product->price)
                        ->title(xCloudEmailProviderPlanEnum::getReadablePlanName()[$this->emailProvider->plan->value])
                        ->prepaid()
                        ->useProduct($product)
                        ->service(BillingServices::EmailProvider)
                        ->description(xCloudEmailProviderPlanEnum::getReadablePlanName()[$this->emailProvider->plan->value])
                        ->renewMonthly()
                        ->generateBill();

                    // generate invoice

                    $invoice = $this->emailProvider->generateInvoice(InvoiceSourceEnum::SinglePurchase);

                    $bill->update([
                        'invoice_id' => $invoice->id
                    ]);


                    if ($this->team->activePlan->support_manual_billing) {
                        return [
                            'sessionId' => $session->id,
                            'productId' => $product->id,
                            'affiliateId' => $this->emailProvider->team->getMeta('affiliate.affiliate_code'),
                            'paymentIntentId' => null,
                            'cartId' => $cart->id,
                            'invoiceId' => $invoice->id,
                        ];
                    }

                    // take payment
                    $paymentIntent = $this->takePayment($invoice);

                    if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
                        // attach invoice to cart form
                        $cart->invoice()->associate($invoice);
                        $cart->save();

                        $bill->setStatusPaid();

                        return [
                            'sessionId' => $session->id,
                            'productId' => $product->id,
                            'affiliateId' => $this->emailProvider->team->getMeta('affiliate.affiliate_code'),
                            'paymentIntentId' => $paymentIntent->id,
                            'cartId' => $cart->id,
                            'invoiceId' => $invoice->id,
                        ];
                    }else{
                        if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
                            $paymentMethod->saveMeta('stripe_3d_secure_authentication', [
                                'requires_3d_secure_authentication' => true
                            ]);

                            $invoice->setStatusRequiresAction();

                            return [
                                'sessionId' => $session->id,
                                'productId' => $product->id,
                                'affiliateId' => $this->emailProvider->team->getMeta('affiliate.affiliate_code'),
                                'paymentIntentId' => $paymentIntent->id,
                                'cartId' => $cart->id,
                                'invoiceId' => $invoice->id,
                            ];
                        }else{
                            $invoice->setStatusPaymentFailed();
                            $invoice->saveLog('payment_failed_' . now()->format('Y-m-d h:m:s'), 'Payment failed');
                            $bill->setStatusUnpaid();

                            return [
                                'sessionId' => $session->id,
                                'productId' => $product->id,
                                'affiliateId' => $this->emailProvider->team->getMeta('affiliate.affiliate_code'),
                                'paymentIntentId' => $paymentIntent->id,
                                'cartId' => $cart->id,
                                'invoiceId' => $invoice->id,
                            ];
                        }
                    }
                }

                // attach product to team
                $this->team->attachProduct($product);

                // update the cart
                $cart->setStatusCompleted();
                $cart->update([
                    'meta->stripe->checkout_session->payment_method_types' => $session->payment_method_types,
                    'meta->stripe->checkout_session->customer' => $session->customer,
                    'meta->stripe->checkout_session->customer_email' => $session->customer_email,
                ]);
            }else{
                // for free email provider
                $cart->update([
                    'team_id' => $this->team->id,
                    'product_id' => $product->id
                ]);

                $bill = $this->emailProvider->cost($product->price)
                    ->title(xCloudEmailProviderPlanEnum::getReadablePlanName()[$this->emailProvider->plan->value])
                    ->prepaid()
                    ->hasOffer()
                    ->useProduct($product)
                    ->service(BillingServices::EmailProvider)
                    ->description(xCloudEmailProviderPlanEnum::getReadablePlanName()[$this->emailProvider->plan->value])
                    ->renewMonthly()
                    ->generateBill();

                // generate invoice
                $invoice = $this->emailProvider->generateInvoice(InvoiceSourceEnum::SinglePurchase);

                $bill->update([
                    'invoice_id' => $invoice->id,
                ]);

                $bill->setStatusPaid();
                $invoice->setStatusPaid();

                // attach product to team
                $this->team->attachProduct($product);

                $cart->invoice()->associate($invoice);
                $cart->save();

                // no need to take payment for free email provider plan
                $cart->setStatusCompleted();

                return [
                    'sessionId' => $paymentMethod->session_id,
                    'productId' => $product->id,
                    'affiliateId' => $this->emailProvider->team->getMeta('affiliate.affiliate_code'),
                    'paymentIntentId' => null, // free provider won't have any payment intent
                    'cartId' => $cart->id,
                    'invoiceId' => $invoice->id,
                ];
            }
        }catch (\Exception $e) {
            return redirect()->back()->with('flash', [
                'message' => 'Payment Failed.',
                'body' => $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }

    private function createElasticEmailSubAccount(): void
    {
        // create subaccount on elastic email
        $this->team->createElasticEmailSubAccount(
            email: app()->isProduction() ? 'team_' . $this->team->id . '@' . 'xcloud.email' : env('APP_ENV') . '_team_' . $this->team->id . '@' . 'xcloud.email',
            credit: $this->emailProvider->email_limit
        );
    }

    /**
     * @throws ApiErrorException
     * @throws CardException
     */
    private function takePayment(GeneralInvoice $invoice)
    {
        try {
            $paymentIntent = (new StripePaymentRepository())->takePayment($invoice);

            if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
                $invoice->setStatusPaid();

                // send email
                $invoice->team->sendInvoiceEmail($invoice);

                // ensure that service is provided after payment
                if(!$this->emailProvider->serviceProvidedAfterPayment(BillingServices::EmailProvider)){
                    Log::error('Service not provided after payment for email provider: ' . $this->emailProvider->id);
                    // provide service
                    $serviceProvided = $this->emailProvider->provideService(BillingServices::EmailProvider);
                    if(!$serviceProvided){
                        Log::error('Failed to provide service after trying again for email provider: ' . $this->emailProvider->id);
                    }
                }
            }else{
                if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
                    $invoice->setStatusRequiresAction();
                }else{
                    $invoice->setStatusPaymentFailed();
                    $invoice->saveLog('payment_error', $paymentIntent->last_payment_error?->message);
                    Log::warning('Payment failed for invoice: ' . $invoice->id . ' with error: ' . $paymentIntent->last_payment_error?->message);
                }
            }

            return $paymentIntent;
        }catch (CardException|InvalidRequestException|ApiErrorException $e) {
            $invoice->setStatusPaymentFailed();
            $invoice->saveLog('payment_error', $e->getMessage());

            $paymentIntent = PaymentIntent::retrieve($invoice->gateway_invoice_or_intent_id);
            $paymentIntent->cancel();
            return $paymentIntent;
        }
    }
}
