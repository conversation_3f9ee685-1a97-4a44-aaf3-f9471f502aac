<?php

namespace App\Repository;

use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Models\Product;
use App\Models\Server;
use App\Models\Site;
use App\Models\SslCertificate;
use App\Scripts\GetDatabasesWithUsers;
use App\Services\CloudServices\DigitalOcean;
use App\Services\CloudServices\Fetchers\Providers\VultrFetcher;
use App\Services\CloudServices\GCP;
use App\Services\DNS\CloudflareDns;
use App\Services\Integrations\CloudflareService;
use Google\ApiCore\ApiException;
use Google\ApiCore\ValidationException;
use Illuminate\Support\Arr;

class ServerRepository
{
//    TODO : This class needs to be refactored, it can be easily interfaced or we can create a wrapper maybe for individual provider
    private Server $server;

    public function __construct(Server $server)
    {
        $this->server = $server;
    }

    /**
     * @throws ApiException
     * @throws ValidationException
     */
    public function getUpgradableServerTypes(): array
    {
        $serverSizeList = array();

        if ($this->server->isDigitalOcean() || $this->server->isXCloudDO()) {

            $serverSizeList = Arr::get((new DigitalOcean())->getServerDetails(), 'droplets');
            if ($this->server->isXCloudDO()) {
                $serverSizeList['unused_bill_amount'] = $this->server->getMonthlyUnusedBillAmount($this->server->getDefaultBillingService());
            }

            // we will disable lower config servers from current plan
            $serverDiskSize = null;
            foreach ($serverSizeList as $serverSizeData) {
                if (!is_array($serverSizeData)) {
                    continue;
                }
                foreach ($serverSizeData as $serverItem) {
                    if ($serverItem['slug'] === $this->server->size) {
                        $serverDiskSize = $serverItem['disk'];
                        break;
                    }
                }
                if ($serverDiskSize) {
                    break;
                }
            }

            foreach ($serverSizeList as &$serverSizeData) {
                if (!is_array($serverSizeData)) {
                    continue;
                }
                foreach ($serverSizeData as &$serverItem) {
                    if ($serverItem['disk'] < $serverDiskSize) {
                        $serverItem['disabled'] = true;
                    }
                }
            }
        }
        else if($this->server->isUnderXCloudOrWhiteLabelVultr()){
//            it will work for both xcloud managed and provider
//            TODO: need to adjust billing here
            $serverSizeList['unused_bill_amount'] = $this->server->getMonthlyUnusedBillAmount($this->server->getDefaultBillingService());

            $serverTypeRepository = new XCloudVultrServerTypeRepository();
//            getting xcloud managed/provider products based on billing service
            $products = $serverTypeRepository->getXCloudProductTypesByBillingService($this->server->getDefaultBillingService());

//            getting upgradable server types for specific instance
            $serverSizeList = $serverTypeRepository->getXCloudUpgradableServerTypes($products, $this->server->resource_id)->toArray();

            $serverCost = null;
            foreach ($serverSizeList as $serverSizeData) {
                foreach ($serverSizeData as $serverItem) {
                    if ($serverItem['slug'] === $this->server->size) {
                        $serverCost = $serverItem['price'];
                        break;
                    }
                }
                if ($serverCost) {
                    break;
                }
            }
        }
        else if ($this->server->isGcp()) {
            $cloudProvider = $this->server->cloudProvider;

            ## Uncomment this if revert back to oauth2
//            $gcp = new GCPOAuth($cloudProvider->getAccessToken());
//            $serverSizeList = $gcp->setProject($cloudProvider->name)->getSizes($this->server->zone);

            ## Comment this if revert back to oauth2
            $gcp = new GCP($cloudProvider->getAccessToken());

            $serverSizeList = $gcp->setProject($cloudProvider->name)->getSizes($this->server->zone);

            // add null value 'price' index to each item
            foreach ($serverSizeList as &$serverSizeData) {
                foreach ($serverSizeData as &$serverItem) {
                    $serverItem['price'] = null;
                }
            }

        }
        else if ($this->server->isVultrOnly()) {
            $apiKey = $this->server->cloudProvider->api_key;

            $vultrFetcher = new VultrFetcher($apiKey);
            $serverSizeList = $vultrFetcher->getUpgradableServerTypes($this->server->resource_id);

//            we are getting upgradable type from Vultr end for specific instance so no need to disable lower instances
//
//            foreach ($serverSizeList as &$serverSizeData) {
//                foreach ($serverSizeData as &$serverItem) {
//                    if ($serverItem['price'] < $serverCost) {
//                        $serverItem['disabled'] = true;
//                    }
//                }
//            }

        }
        return array_filter($serverSizeList);
    }


    /**
     * @throws ValidationException
     * @throws ApiException
     */
    public function getServerInfoByType($serverSizeSlug)
    {
        //        TODO: we have same functionality in BaseServerModificationService.php, need to make them in one
//        TODO: Putting this function doesn't make much sense, this class is serving individual server instance in constructor so we need to put this some other place i think
        $serverSizeList = array();

        if ($this->server->isDigitalOcean() || $this->server->isXCloudDO()) {
            $serverSizeList = Arr::get((new DigitalOcean())->getServerDetails(), 'droplets');
        }
        else if($this->server->isUnderXCloudOrWhiteLabelVultr()) {
            $serverTypeRepository = new XCloudVultrServerTypeRepository();
//            getting xcloud managed/provider products based on billing service
            $products = $serverTypeRepository->getXCloudProductTypesByBillingService($this->server->getDefaultBillingService());
//            getting upgradable server types for specific instance
            $allServerTypeInfo = collect($serverTypeRepository->getXCloudServerTypes($products));

            $serverSizeList = $allServerTypeInfo->toArray()['sizes'];
        }
        else if ($this->server->isGcp()) {
            $cloudProvider = $this->server->cloudProvider;
            $gcp = new GCP($cloudProvider->getAccessToken());
            $serverSizeList = $gcp->setProject($cloudProvider->name)->getSizes($this->server->zone);
        }
        else if ($this->server->isVultrOnly()) {
            $apiKey = $this->server->cloudProvider?->getAccessToken();
            $vultrFetcher = new VultrFetcher($apiKey);
            $serverSizeList = $vultrFetcher->getServerTypes();
        }
        foreach ($serverSizeList as $serverSizeData) {
            foreach ($serverSizeData as $serverItem) {
                if ($serverItem['slug'] === $serverSizeSlug) {
                    return $serverItem;
                }
            }
        }
        return [];
    }

    public function getDatabasesAndUsers(): array
    {
        $task = $this->server->runInline(new GetDatabasesWithUsers($this->server));

        if (!$task->successful()) {
            return [
                'status' => 'failed',
                'message' => 'Failed to fetch databases for server: ' . $this->server->name
            ];
        }

        $responseArr = explode('----------', $task->output);

        $filteredDatabases = [];
        $filteredDatabaseUsers = [];
        ### formatting databases
        if (isset($responseArr[0])) {
            $databases = explode("\n", $responseArr[0]);
            $filteredDatabases = array_filter(array_values(array_filter($databases, function ($db) {
                return !in_array($db, ["information_schema", "mysql", "performance_schema", "sys"]);
            })));
        }

        ### formatting database users
        if (isset($responseArr[1])) {
            // Exclude unwanted database names
            $filteredDatabaseUsers = array_unique(explode("\n", $responseArr[1]));
            $filteredDatabaseUsers = array_values(array_filter(array_filter($filteredDatabaseUsers, function ($db) {
                return !in_array($db, ['debian-sys-maint', 'root', 'mariadb.sys', 'mysql']);
            })));
        }

        $this->server->update([
            'database_info->databases' => $filteredDatabases,
            'database_info->db_users' => $filteredDatabaseUsers,
            'database_info->last_pulled_at' => now()
        ]);

        return [
            'status' => 'success',
            'message' => 'Databases and Users fetched successfully.'
        ];
    }

    /**
     * @throws ValidationException
     * @throws ApiException
     * @throws \Exception
     */
    public function getBackupCost(): float
    {
//        TODO: we have same functionality in BaseServerModificationService.php, need to make them in one

        if ($this->server->isUnderXCloudVultr()) {
            // we will only charge backup for xcloud vultr and xcloud provider
            $billingService = $this->server->getDefaultBillingService();

            $productPrice = Product::where('slug', $this->server->size)
                ->where('service_type', $billingService)
                ->where('renewal_type', BillRenewalPeriod::Monthly)
                ->whereIn('requires_billing_plan', [$this->server?->team?->activePlan?->id, null])
                ->orderByRaw('requires_billing_plan IS NULL') // Prioritize non-null billing plans
                ->first()
                ?->price;

            if (!$productPrice) {
                $serverInfo = $this->getServerInfoByType($this->server->size);
                $productPrice = $serverInfo['price'] ?? null;
            }

            if ($productPrice === null) {
                throw new \Exception("Could not find a price for resizing the server to type: {$this->server->size}.");
            }

            return ceil($productPrice * 0.20);
        }

        if ($this->server->isWhiteLabelVultr()) {
            $whiteLabel = $this->server?->team?->whiteLabel;

            if (!$whiteLabel) {
                throw new \Exception("White label not found for server: {$this->server->id}");
            }

            $productPrice = Product::onlyForWhiteLabelClient()
                    ->where('slug', $this->server->size)
                    ->where('service_type', $this->server->getDefaultBillingService())
                    ->where('renewal_type', BillRenewalPeriod::Monthly)
                    ->where('white_label_id', $whiteLabel->id)
                    ->where('requires_billing_plan', $this->server?->team?->activePlan?->id)
                    ->first()
                    ?->price;

            if (!$productPrice) {
                $productPrice = Product::onlyForWhiteLabelClient()
                    ->where('slug', $this->server->size)
                    ->where('service_type', $this->server->getDefaultBillingService())
                    ->where('renewal_type', BillRenewalPeriod::Monthly)
                    ->where('white_label_id', $whiteLabel->id)
                    ->whereNull('requires_billing_plan')
                    ->first()
                    ?->price;
            }

            return ceil($productPrice * 0.20);
        }

        return 0.00;
    }

    public function removeCloudflareDomainsAndSSLCertificateOnDeletingServer(): void
    {
        // delete domains from cloudflare for all staging sites
        foreach ($this->server->sites()->where('environment', Site::STAGING)->get() as $site) {
            (new CloudflareDns(url: $site->name))->delete(name: $site->name);

            if($site->hasProductionEnvironment()){
                if($site->deploymentPullLogs()->exists()){
                    $site->deploymentPullLogs()->delete();
                }

                if($site->deploymentPushLogs()->exists()){
                    $site->deploymentPushLogs()->delete();
                }
            }
        }

        // delete domains from cloudflare for all demo sites
        foreach ($this->server->sites()->where('environment', Site::DEMO)->get() as $site) {
            (new CloudflareDns(url: $site->name))->delete(name: $site->name);

            if($site->hasProductionEnvironment()){
                if($site->deploymentPullLogs()->exists()){
                    $site->deploymentPullLogs()->delete();
                }

                if($site->deploymentPushLogs()->exists()){
                    $site->deploymentPushLogs()->delete();
                }
            }
        }

        // delete dns records & origin certificates(ssl certificates) from user's cloudflare account if user chooses to delete the dns records
        if($this->server->getMeta('delete_dns_records') && $this->server->team->hasCloudflareIntegration()) {
            foreach ($this->server->sites()->where('ssl_provider', SslCertificate::PROVIDER_CLOUDFLARE)->get() as $site) {
                foreach ($site->team()->cloudflareIntegrations as $cloudflareIntegration) {
                    (new CloudflareService($cloudflareIntegration))
                        ->deleteDns(zoneId: Arr::get($site->meta, 'cloudflare_integration.zone_id'), name: $site->name);
                    if($site->isMultiSite() && $site->isMultiSiteSubdomain()){
                        // delete wildcard dns from user's cloudflare account
                        (new CloudflareService($cloudflareIntegration))
                            ->deleteDns(zoneId: Arr::get($site->meta,'cloudflare_integration.zone_id'), name: '*.' . $site->name);
                    }
                    (new CloudflareService($cloudflareIntegration))
                        ->revokeOriginCertificate(Arr::get($site->meta, 'cloudflare_integration.origin_certificate.certificate_id'));
                }
            }
        }
    }
}
