<?php

namespace App\Repository;

use App\Enums\XcloudBilling\BillingStatus;
use App\Models\GeneralInvoice;
use App\Models\Utm;
use App\Models\WhiteLabel;
use App\Notifications\SendFailedInvoiceNotification;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod;
use Stripe\Refund;
use Stripe\SetupIntent;
use Stripe\Stripe;
use Stripe\StripeClient;

class StripePaymentRepository
{
    public StripeClient $stripe;

    public function __construct()
    {
        $this->stripe = new StripeClient(config('services.stripe.secret_key'));
    }

    /**
     * @throws ApiErrorException
     * @throws \Exception
     */
    public function createPaymentIntent(GeneralInvoice $invoice)
    {
        Stripe::setApiKey(config('services.stripe.secret_key'));

        $data = [
            'amount' => $invoice->amount * 100, // amount in cents
            'currency' => $invoice->currency->value, // currency
            'payment_method' => $invoice->paymentMethod()?->first()?->getPaymentMethod() ?: 'pm_card_visa',
            'customer' => $invoice->paymentMethod()?->first()?->customer_id,
            'automatic_payment_methods' => [
                'enabled' => true,
                'allow_redirects' => 'never'
            ],
            'metadata' => [
                'Invoice ID' => $invoice->id,
                'Invoice Number' => Str::limit($invoice->invoice_number, 495, '...'),
                'Invoice Title' => Str::limit($invoice->title, 495, '...'),
                'Invoice Description' => Str::limit($invoice->description, 495, '...'),
                'xCloud Source' => Str::limit($invoice->source->toReadableString(), 495, '...'),
                'Invoice Team' => Str::limit(url('/admin/resources/general-invoices/'.$invoice->invoice_number), 495, '...'),
                'Payment Method' => Str::limit(url('/admin/resources/payment-methods/'.$invoice->payment_method_id), 495, '...'),
                'Affiliate ID' => Str::limit(Arr::get($invoice->team->meta, 'affiliate.affiliate_code'), 495, '...'),
                'Application Fee Log' => Str::limit($invoice->getMeta('application_fee_log'), 495, '...'),
            ],
        ];

        if ($invoice->team->whiteLabel) {
            ## Suppose xcloud server price is 10$
            ## Reseller set 15$ for the same server
            ## Stripe processing fee is 1$ for example
            ## So, we will receive as usual 10$ for the server
            ## Reseller will receive 5$ - stripe processing fee 1$ = 4$

            $data['application_fee_amount'] = $invoice->application_fee_amount * 100; // application fee in cents
            // if $data['application_fee_amount'] is not a round value, then take the ceil value, otherwise stripe will throw an error
            if($data['application_fee_amount'] != round($data['application_fee_amount'] )){
                $data['application_fee_amount'] = ceil($data['application_fee_amount'] );
            }

            $paymentIntent = PaymentIntent::create($data, [
                'stripe_account' => $invoice->team->whiteLabel->connectedAccount->stripe_account_id
            ]);
        }else{
            $paymentIntent = PaymentIntent::create($data);
        }

        return $paymentIntent;
    }
    /**
     * @throws ApiErrorException
     * @throws GuzzleException
     */
    public function takePayment(GeneralInvoice $invoice): PaymentIntent
    {
        Stripe::setApiKey(config('services.stripe.secret_key'));

        if (!$invoice->gateway_invoice_or_intent_id) {
            $paymentIntent = (new StripePaymentRepository())->createPaymentIntent($invoice);

            $invoice->update([
                'gateway_invoice_or_intent_id' => $paymentIntent->id,
                'gateway_customer_id' => $paymentIntent->customer,
                'gateway_payment_method_id' => $paymentIntent->payment_method,
            ]);
        }

        if($invoice->team->whiteLabel){

            if (!$invoice->team->whiteLabel->connectedAccount) {
                Log::error('Connected account not found for team#' . $invoice->team->id);
                throw new \Exception('Connected account not found for team#' . $invoice->team->id . ' white label id: ' . $invoice->team->whiteLabel->id);
            }

            $paymentIntent = PaymentIntent::retrieve($invoice->gateway_invoice_or_intent_id, [
                'stripe_account' => $invoice->team->whiteLabel->connectedAccount->stripe_account_id
            ]);
        }else{
            $paymentIntent = PaymentIntent::retrieve($invoice->gateway_invoice_or_intent_id);
        }

        if ($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED) {
            // Handle the case where the payment intent has already succeeded (means payment already taken in stripe)
            return $paymentIntent;
        }

        $metadata = [
            'Invoice ID' => $invoice->id,
            'Invoice Number' => $invoice->invoice_number,
            'Invoice Title' => $invoice->title,
            'Invoice Description' => $invoice->description,
            'xCloud Source' => $invoice->source->toReadableString(),
            'Invoice Team' => url('/admin/resources/general-invoices/'.$invoice->invoice_number),
            'Payment Method' => url('/admin/resources/payment-methods/'.$invoice->payment_method_id),
            'Affiliate ID' => Arr::get($invoice->team->meta, 'affiliate.affiliate_code'),
            'Application Fee Amount' => $invoice->application_fee_amount,
            'Application Fee Log' => $invoice->getMeta('application_fee_log'),
        ];

        if ($invoice->team->whiteLabel) {
            $paymentIntent = $this->stripe->paymentIntents->update($invoice->gateway_invoice_or_intent_id, [
                'metadata' => $metadata,
            ], [
                'stripe_account' => $invoice->team->whiteLabel->connectedAccount->stripe_account_id
            ]);
        } else {
            $paymentIntent = $this->stripe->paymentIntents->update($invoice->gateway_invoice_or_intent_id, [
                'metadata' => $metadata,
            ]);
        }

        try {
            // taking the payment
            $paymentIntent = $paymentIntent->confirm();

            if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION){
                Log::info('Payment intent requires action', [
                    'invoice_id' => $invoice->id,
                    'invoice_number' => $invoice->invoice_number,
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_intent_status' => $paymentIntent->status,
                ]);
                // need further action.
                return $paymentIntent;
            }


            // affiliate commission
            if ($invoice->type->isAffiliatable()) {
                if ($invoice?->team?->promoter && !$invoice->promoter_id) {
                    if(!$paymentIntent->status == PaymentIntent::STATUS_SUCCEEDED){
                        Log::warning('Failed to take payment. Affiliate commission unable to generate', [
                            'invoice_id' => $invoice->id,
                            'invoice_number' => $invoice->invoice_number,
                            'payment_intent_id' => $paymentIntent->id,
                            'payment_intent_status' => $paymentIntent->status,
                        ]);
                    }else{
                        $invoice->handleAffiliateCommission();
                    }
                }
            }

            // Utm sales tracking
            if(Utm::ENABLE_UTM_CALCULATION) {
                if (!($paymentIntent->status == PaymentIntent::STATUS_SUCCEEDED)) {
                    Log::warning('Failed to take payment. Utm sales tracking was not generated', [
                        'invoice_id' => $invoice->id,
                        'invoice_number' => $invoice->invoice_number,
                        'payment_intent_id' => $paymentIntent->id,
                        'payment_intent_status' => $paymentIntent->status,
                    ]);
                } else {
                    $invoice->team->addUtmSourceSell($invoice->amount, $invoice);
                }
            }
        }catch (ApiErrorException $e) {
            // send failed invoice notification
            $failedMessage = ($invoice->paymentMethod ? 'We could not take payment using your card ending in **** '
                . $invoice->paymentMethod->cardLast4Digit() : 'No usable payment method found to take payment')
                . '. Please update your card details or try another card.';
            $invoice->team->notify(new SendFailedInvoiceNotification($invoice, message: $failedMessage));

            Log::warning('Failed to take payment. Retrying with other cards..', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'payment_intent_id' => $paymentIntent->id,
                'payment_intent_status' => $paymentIntent->status,
                'error' => $e->getMessage()
            ]);

            ## retry with other cards if payment failed
            if ($paymentIntent->status !== PaymentIntent::STATUS_SUCCEEDED) {
                $paymentMethods = $invoice->team->paymentMethods()
                    ->whereNotIn('id', $invoice->team->getTriedCards())
                    ->withoutDefault()
                    ->get();

                if($paymentMethods->isEmpty()){
                    Log::warning('Failed to take payment. No other cards found', [
                        'invoice_id' => $invoice->id,
                        'invoice_number' => $invoice->invoice_number,
                        'payment_intent_id' => $paymentIntent->id,
                        'payment_intent_status' => $paymentIntent->status,
                    ]);

                    $invoice->saveLog('payment_failed_' . now()->format('Y-m-d h:m:s'), 'Payment failed');

                    $invoice->update([
                        'gateway_invoice_or_intent_id' => $paymentIntent->id,
                        'gateway_payment_method_id' => $invoice->team->activePaymentMethod()->first()->getPaymentMethod()->id
                    ]);

                    return $paymentIntent;
                }

                foreach ($paymentMethods as $paymentMethod) {
                    Log::info('Trying payment method : ' , [
                        'payment_method_id' => $paymentMethod->id
                    ]);

                    $invoice->saveLog('switching_card_' . now()->format('Y-m-d h:m:s'),
                        'Failed to take payment. Switching card from ' . $invoice->paymentMethod()->first()->id . ' to ' . $paymentMethod->id);

                    // update invoice with new payment method(VVI important otherwise will go into infinite loop)
                    $invoice->switchCard($paymentMethod);

                    // if payment method has security protocol, use new architecture, else use old one
                    if($paymentMethod->security_protocol){
                        // V2: single customer with multiple payment methods
                        if ($paymentIntent->payment_method !== Arr::get($paymentMethod->meta, 'stripe.payment_method')) {
                            $updateData = [
                                'payment_method' => Arr::get($paymentMethod->meta, 'stripe.payment_method'),
                            ];

                            $options = $invoice->team->whiteLabel ? [
                                'stripe_account' => $invoice->team->whiteLabel->connectedAccount->stripe_account_id
                            ] : [];

                            $paymentIntent = PaymentIntent::update($paymentIntent->id, $updateData, $options);

                            Log::info('Payment method updated for payment intent: ', [
                                'invoice_id' => $invoice->id,
                                'invoice_number' => $invoice->invoice_number,
                                'payment_intent_id' => $paymentIntent->id,
                                'payment_intent_status' => $paymentIntent->status,
                                'payment_method_id' => $paymentMethod->id,
                                'payment_method_stripe_id' => Arr::get($paymentMethod->meta, 'stripe.payment_method'),
                            ]);
                        }
                    }else{
                        // v1: each customer has only one payment method
                        // cancel previous payment intent since the payment method is changed and customer is also changed
                        if ($invoice->gateway_invoice_or_intent_id) {
                            try {
                                (new StripePaymentRepository())->cancelPaymentIntent($invoice);
                            }catch (\Exception $e) {
                                Log::warning('Payment intent cancellation failed', [
                                    'invoice_id' => $invoice->id,
                                    'invoice_reference_no' => $invoice->reference_no,
                                    'xcloud_team_id' => $invoice->team_id,
                                    'xcloud_payment_method_id' => $invoice->payment_method_id,
                                    'error' => $e->getMessage()
                                ]);
                            }
                        }

                        $paymentIntent = $this->createPaymentIntent($invoice);

                        // update invoice with new payment intent with newly switched card
                        $invoice->update([
                            'gateway_invoice_or_intent_id' => $paymentIntent->id
                        ]);
                    }

                    $paymentMethod->tried();

                    return $this->takePayment($invoice);
                }
            }
        }

        Log::info('Finishing payment intent: ', [
            'invoice_id' => $invoice->id,
            'invoice_number' => $invoice->invoice_number,
            'payment_intent_id' => $paymentIntent->id,
            'payment_intent_status' => $paymentIntent->status,
        ]);

        return $paymentIntent;
    }

    /**
     * Refund the payment for the given invoice.
     *
     * @param GeneralInvoice $invoice
     * @return Refund
     * @throws ApiErrorException
     */
    public static function refundInvoicePayment(GeneralInvoice $invoice, float $refundAmount = null): Refund
    {
        Stripe::setApiKey(config('services.stripe.secret_key'));

        $paymentIntentId = $invoice->gateway_invoice_or_intent_id;

        $refundAmount = $refundAmount ?: ($invoice->refundable_amount ?: $invoice->amount);

        if ($refundAmount > $invoice->amount) {
            Log::error(':|, Refund amount is greater than invoice amount, refunding the full amount. but tanay case korbo apnar name E :)', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'refund_amount' => $refundAmount,
                'invoice_amount' => $invoice->amount,
            ]);

            $refundAmount = $invoice->amount;
        }

        if($refundAmount > $invoice->amount) {
            $refundAmount = $invoice->amount;
        }

        $refundData = [
            'payment_intent' => $paymentIntentId,
            'amount' => $refundAmount * 100 // amount in cents
        ];

        if ($invoice->team->whiteLabel) {
            $refundData['stripe_account'] = $invoice->team->whiteLabel->connectedAccount->stripe_account_id;
        }

        $refund = Refund::create($refundData);

        $invoice->update([
            'status' => $refundAmount < $invoice->amount ? BillingStatus::PartiallyRefunded : BillingStatus::Refunded,
            'refunded_amount' => $refundAmount
        ]);

        // refund affiliate
        $refundData = [
            'email' => $invoice->customer_email,
            'event_id' => $invoice->invoice_number,
            'amount' => $refundAmount * 100, // amount in cents
        ];

        try {
            $response = (new FirstPromoterRepository())->trackingRefunds($refundData);
//            if(empty($response)){
//                Log::error('Refund not tracked on first promoter for invoice id: '. $invoice->id);
//            }
        } catch (GuzzleException $e) {
            Log::error('Error: '. $e->getMessage());
        }

        return $refund;
    }

    /**
     * @throws ApiErrorException
     */
    public function cancelPaymentIntent(GeneralInvoice $invoice)
    {
        // cancel the payment intent
        $stripe = new StripeClient(config('services.stripe.secret_key'));
        if($invoice->team->whiteLabel) {
            $stripe->paymentIntents->cancel($invoice->gateway_invoice_or_intent_id, [
                'cancellation_reason' => 'abandoned'
            ], [
                'stripe_account' => $invoice->team->whiteLabel->connectedAccount->stripe_account_id
            ]);
        }else{
            $stripe->paymentIntents->cancel($invoice->gateway_invoice_or_intent_id, [
                'cancellation_reason' => 'abandoned'
            ]);
        }
    }

    /**
     * @throws ApiErrorException
     */
    public function connectedAccountPaymentTest(): void
    {
        Stripe::setApiKey(config('services.stripe.secret_key'));

        $paymentMethod = \App\Models\PaymentMethod::find(13);

        $whiteLabel = WhiteLabel::find(8);

        $stripeCharge = 3; // 3% tax

        $xcloudAccountId = 'acct_1NVTE8EJyHzisutw';
        $connectedAccountId = Arr::get($whiteLabel->meta, 'stripe.account_id');

        // clone payment method from platform to connected account
//        $payment_method = \Stripe\PaymentMethod::create([
//            'customer' => $paymentMethod->customer_id, // PLATFORM_CUSTOMER_ID
//            'payment_method' => $paymentMethod->getPaymentMethod(), // PLATFORM_PAYMENT_METHOD_ID which will be cloned to connected account
//        ], [
//            'stripe_account' => $connectedAccountId, // CONNECTED_ACCOUNT_ID
//        ]);

        $paymentMethodId = 'pm_1PqgfgE2i52ulO8vjdJHxZVp'; // payment method id from connected account
        $paymentMethod = PaymentMethod::retrieve($paymentMethodId, [
            'stripe_account' => $connectedAccountId,
        ]);

        // clone customer from platform to connected account
//        $customer = \Stripe\Customer::create([
//            'payment_method' => $payment_method->id,
//            'invoice_settings' => [
//                'default_payment_method' => $payment_method->id,
//            ],
//        ], [
//            'stripe_account' => $connectedAccountId,
//        ]);

        $customer = Customer::retrieve($paymentMethod->customer, [
            'stripe_account' => $connectedAccountId,
        ]);

        $paymentIntent = PaymentIntent::create([
            'amount' => 3000,
            'currency' => 'usd',
            'payment_method' => $paymentMethod->id,
            'customer' => $customer->id,
            'application_fee_amount' => 500,
            'automatic_payment_methods' => [
                'enabled' => true,
                'allow_redirects' => 'never'
            ],
            'statement_descriptor' => 'Test Payment',
        ], [
            'stripe_account' => $connectedAccountId
        ]);

        $paymentIntent->confirm();
    }
}
