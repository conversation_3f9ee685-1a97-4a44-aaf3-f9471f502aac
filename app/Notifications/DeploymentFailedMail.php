<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Channels\TelegramChannel;
use App\Channels\WhatsAppChannel;
use App\Enums\AlertLevel;
use App\Models\Site;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class DeploymentFailedMail extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    protected Site $site;
    protected array $mails = [];
    private string $error = '';

    public string $emailFilter = 'site_email';
    public string $slackFilter = 'site_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Site $site, array $mails = [], string $error = '')
    {
        $this->site = $site;
        $this->mails = $mails;
        $this->error = $error;
        $this->defaultChannels = ['mail', 'slack', WhatsAppChannel::class, TelegramChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param $notifiable
     * @return MailMessage
     */
    public function toMail($notifiable): MailMessage
    {
        return $notifiable->mailMessage()
            ->theme('no-subcopy')
            ->subject('Deployment Failed')
            ->cc($this->mails)
            ->greeting(Lang::get('emails.greeting', ['name' => $notifiable->name]))
            ->line("Deployment failed for site {$this->site->name}.")
            ->line("Error: {$this->error}")
            ->action('View Site', $notifiable->url(route('site.events', [$this->site->server_id, $this->site->id])));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param $notifiable
     * @return array
     */
    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::ERROR,
            'type' => 'DeploymentFailed',
            'related_id' => $this->site->id,
            'related_type' => Site::class,
            'meta' => [
                'message' => "Deployment failed for site {$this->site->name}." . PHP_EOL . "Error: {$this->error}",
                'url' => route('site.show', [$this->site->server->id, $this->site->id]),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content("Deployment failed for site {$this->site->name}." . PHP_EOL . "Error: {$this->error}")
                ->attachment(function ($attachment) {
                    $attachment->title('Deployment Failed');
                });
        }
    }

    /**
     * Get the WhatsApp representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return WhatsAppMessage
     */
    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content("Deployment failed for site {$this->site->name}." . PHP_EOL . "Error: {$this->error}")
                ->url(route('site.show', [$this->site->server->id, $this->site->id]));
        }
    }

    /**
     * Get the Telegram representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return TelegramMessage
     */
    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content("Deployment failed for site {$this->site->name}." . PHP_EOL . "Error: {$this->error}")
                ->button('View Site')
                ->url(route('site.show', [$this->site->server->id, $this->site->id]));
        }
    }
}
