<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Models\Server;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Support\Facades\Lang;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ServerProvisionedSuccess extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    protected Server $server;
    protected array $mails = [];

    public string $emailFilter = 'server_email';
    public string $slackFilter = 'server_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Server $server, array $mails = [])
    {
        $this->server = $server;
        $this->mails = $mails;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail(mixed $notifiable): MailMessage
    {
        $hideSensitiveInfo = $notifiable?->owner?->notification && isset($notifiable?->owner?->notification['do_not_sent_sensitive_info']);
        $serverDetails = [
            Lang::get('emails.server_provisioning_success.server_ip').': ' . $this->server->public_ip,
            Lang::get('emails.server_provisioning_success.user').': ' . $this->server->ssh_username,
        ];

        $databaseDetails = [
            Lang::get('emails.server_provisioning_success.database_username').': root',
        ];

        if (!$hideSensitiveInfo) {
            $serverDetails = array_merge($serverDetails, [
                Lang::get('emails.server_provisioning_success.sudo_password').': ' . $this->server->sudo_password,
            ]);

            $databaseDetails = array_merge($databaseDetails, [
                Lang::get('emails.server_provisioning_success.database_password').': ' . $this->server->database_password
            ]);
        }

        $details = array_merge($serverDetails, $databaseDetails);

        //mail with not subcopy
        return $notifiable->mailMessage()
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.server_provisioning_success.subject'))
            ->cc($this->mails)
            ->greeting(Lang::get('emails.server_provisioning_success.greeting', ['name' => $notifiable->name]))
            ->line(Lang::get('emails.greeting', ['name' => $notifiable->name]))
            ->line(Lang::get('emails.server_provisioning_success.line', ['name' => $this->server->name]))
            ->salutation(Lang::get('emails.server_provisioning_success.salutation', ['brand_name' => $notifiable->getBrandName]))
            ->line(join("<br>", $details))
            ->line(Lang::get('emails.server_provisioning_success.salutation', ['brand_name' => $notifiable->getBrandName]))
            ->salutation(Lang::get('emails.salutation', ['brand_name' => $notifiable->getBrandName]))
            ->action(Lang::get('emails.action', ['brand_name' => $notifiable->getBrandName]), $notifiable->url(url('/login')));

    }

    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::SUCCESS,
            'type' => 'ServerProvisioned',
            'related_id' => $this->server->id,
            'related_type' => Server::class,
            'meta' => [
                'message' => Lang::get('emails.server_provisioning_success.notification', ['name' => $this->server->name]),
                'url' => route('server.show', $this->server->id),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.server_provisioning_success.line', ['name' => $this->server->name]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.server_provisioning_success.subject'))
                        ->action(Lang::get('slack.server_provisioning_success.action'), route('server.show', $this->server->id));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(Lang::get('whatsapp.server_provisioning_success.line', ['name' => $this->server->name]))
                ->url("server/{$this->server->id}/sites");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['server_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.server_provisioning_success.line', ['name' => $this->server->name]))
                ->button(Lang::get('telegram.server_provisioning_success.action'))
                ->url(route('server.sites', ['server' => $this->server->id]));
        }
    }
}
