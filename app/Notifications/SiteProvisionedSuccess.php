<?php

namespace App\Notifications;

use App\Channels\Messages\TelegramMessage;
use App\Channels\Messages\WhatsAppMessage;
use App\Enums\AlertLevel;
use App\Models\Site;
use App\Services\Mail\CustomizeMailer;
use App\Traits\VIAxCloudNotificationHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class SiteProvisionedSuccess extends Notification implements ShouldQueue
{
    use Queueable, VIAxCloudNotificationHelper;

    protected Site $site;
    protected array $mails = [];

    public string $emailFilter = 'site_email';
    public string $slackFilter = 'site_slack';

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Site $site, array $mails = [])
    {
        $this->site = $site;
        $this->mails = array_unique($mails);
    }


    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return CustomizeMailer
     */
    public function toMail($notifiable)
    {
        $hideSensitiveInfo = $notifiable?->owner?->notification && isset($notifiable?->owner?->notification['do_not_sent_sensitive_info']);

        $details = [
            Lang::get('emails.site_provisioning_success.url').": {$this->site->site_url}",
        ];

        if ($this->site->admin_user) {
            $details[] = Lang::get('emails.site_provisioning_success.user').": ".$this->site->admin_user;
        }

        if (!$hideSensitiveInfo && $this->site->admin_password) {
            $details[] = Lang::get('emails.site_provisioning_success.password').": ".$this->site->admin_password;
        }

        $details[] = Lang::get('emails.site_provisioning_success.https').": ".($this->site->hasSslCertificate() ? 'Enabled' : 'Disabled');

        if ($this->site?->isWordPress()) {
            $details[] = Lang::get('emails.site_provisioning_success.page_cache').": ".($this->site->isWordPress() ? 'Enabled' : 'Disabled');
        }

        if ($this->site->php_version) {
            $details[] = Lang::get('emails.site_provisioning_success.php_version').": {$this->site->php_version}";
        }

        $details[] = Lang::get('emails.site_provisioning_success.site_user').": {$this->site->site_user}";


        return $notifiable->mailMessage(\App\Services\Mail\CustomizeMailer::class)
            ->theme('no-subcopy')
            ->subject(Lang::get('emails.site_provisioning_success.subject'))
            ->cc($this->mails)
            ->greeting(Lang::get('emails.site_provisioning_success.greeting', ['name' => $this->site->name]))
            ->line(Lang::get('emails.greeting', ['name' => $this->site->getSiteCreatorName($notifiable->owner->name)]))
            ->line(Lang::get('emails.site_provisioning_success.line', ['name' => $this->site->name]))
            ->customAction(Lang::get('emails.site_provisioning_success.custom_action'), $this->site->site_url.'/wp-admin')
            ->line(join("<br>", $details))
            ->line(Lang::get('emails.site_provisioning_success.details', ['brand_name' => $notifiable->get_brand_name]))
            ->action(Lang::get('emails.site_provisioning_success.action', ['brand_name' => $notifiable->get_brand_name]), $notifiable->url(route('site.redirect', $this->site->id)))
            ->salutation(Lang::get('emails.site_provisioning_success.salutation'));
    }

    public function toAlert($notifiable): array
    {
        return [
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => get_class($notifiable),
            'level' => AlertLevel::SUCCESS,
            'type' => 'SiteProvisioned',
            'related_id' => $this->site->id,
            'related_type' => Site::class,
            'meta' => [
                'message' => Lang::get('emails.site_provisioning_success.notification', ['name' => $this->site->name]),
                'url' => route('site.redirect', $this->site->id),
            ],
        ];
    }

    /**
     * Get the Slack representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return SlackMessage
     */
    public function toSlack($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_slack']) && $this->slackIntegration->service()->isConnected()) {
            return (new SlackMessage)
                ->content(Lang::get('slack.site_provisioning_success.line', ['name' => $this->site->name]))
                ->attachment(function ($attachment) {
                    $attachment->title(Lang::get('slack.site_provisioning_success.subject'))
                        ->action(Lang::get('slack.site_provisioning_success.action'), route('site.redirect', $this->site->id));
                });
        }
    }

    public function toWhatsApp($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_whatsapp']) && $this->whatsAppIntegration->service()->isConnected()) {
            return (new WhatsAppMessage)
                ->content(Lang::get('whatsapp.site_provisioning_success.line', ['name' => $this->site->name]))
                ->url("site/{$this->site->id}");
        }
    }

    public function toTelegram($notifiable)
    {
        if ($notifiable?->owner?->notification && isset($notifiable?->owner?->notification['site_telegram']) && $this->telegramIntegration->service()->isConnected()) {
            return (new TelegramMessage)
                ->content(Lang::get('telegram.site_provisioning_success.line', ['name' => $this->site->name]))
                ->button(Lang::get('telegram.site_provisioning_success.action'))
                ->url(route('site.redirect', $this->site->id));
        }
    }
}
