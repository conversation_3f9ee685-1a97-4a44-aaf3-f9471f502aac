<?php

namespace App\Console\Commands;

use App\Models\PatchstackVulnerability;
use App\Models\Team;
use App\Models\VulnerabilitySite;
use Illuminate\Console\Command;

class ScanVulnerabilities extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scan:vulnerabilities';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scan for vulnerabilities in the wordpress sites';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('Scanning for vulnerabilities...');
        $teams = Team::query()
            ->where(function($query){
                $query->where('meta->vulnerability_last_scanned','<',now()->subDays(Team::VULNERABILITY_NOTIFICATION_INTERVAL))
                    ->orWhereNull('meta->vulnerability_last_scanned');
            })
            ->whereHas('servers',fn($query)=>$query->whereHas('vulnerabilitySetting'))->lazy(100);

        foreach ($teams as $team) {
            $this->info('Scanning for team: '.$team->name);
            $sites = $team->sites()->whereNotNull('wordpress_version')->pluck('sites.id')->toArray();
            $patchstackSites = PatchstackVulnerability::where('is_purchase', true)
                ->whereNotNull('vulnerabilities')
                ->whereJsonLength('vulnerabilities', '>', 0)
                ->whereIn('site_id', $sites)
                ->distinct('site_id')
                ->pluck('site_id')
                ->toArray();
            $filteredSites = array_values(array_diff($sites, $patchstackSites));

            $vulnerabilities = VulnerabilitySite::with(['site:id,name,server_id','wordfence_vulnerabilities'])
                ->whereIn('site_id',$filteredSites)
                ->get();

            if ($vulnerabilities->isNotEmpty()) {
                $team->sendVulnerabilityMail($vulnerabilities);
                $team->saveMeta('vulnerability_last_scanned',now());
            }

            #Sending patchstack vulnerability mail
            $patchstackVulnerabilities = PatchstackVulnerability::whereIn('site_id', $patchstackSites)->get();
            
            if ($patchstackVulnerabilities->isNotEmpty()) {
                $team->sendPatchstackVulnerabilityMail($patchstackVulnerabilities);
                $team->saveMeta('vulnerability_last_scanned',now());
            }
        }
        return Command::SUCCESS;
    }
}
