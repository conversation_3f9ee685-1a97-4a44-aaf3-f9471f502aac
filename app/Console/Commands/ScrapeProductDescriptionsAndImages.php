<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Models\HttpResponse;
use App\Models\ScrapableProduct;
use App\Models\Upload;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\DomCrawler\Crawler;

class ScrapeProductDescriptionsAndImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scrape:product-descriptions-images {--limit=0 : Limit the number of products to process}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scrape product descriptions and images from source URLs without login';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to scrape product descriptions and images...');

        try {
            // Get all unscraped products
            $query = ScrapableProduct::where('scraped', false);

            // Apply limit if specified
            $limit = (int)$this->option('limit');
            if ($limit > 0) {
                $query->limit($limit);
                $this->info("Limiting to {$limit} products");
            }

            $scrapableProducts = $query->get();
            $this->info("Found {$scrapableProducts->count()} unscraped products");

            foreach ($scrapableProducts as $scrapableProduct) {
                $this->info("Processing product ID: {$scrapableProduct->product_id}, Source URL: {$scrapableProduct->source_url}");

                try {
                    // Get the product details
                    $product = Product::find($scrapableProduct->product_id);
                    if (!$product) {
                        $this->warn("Product not found with ID: {$scrapableProduct->product_id}");
                        continue;
                    }

                    // Get the HTML content
                    $html = $this->getHTML($scrapableProduct->source_url);
                    if (!$html) {
                        $this->warn("Failed to get HTML content for URL: {$scrapableProduct->source_url}");
                        continue;
                    }

                    $crawler = new Crawler($html);

                    // Extract description
                    $description = $this->extractDescription($crawler);
                    if ($description) {
                        $this->info("Description extracted successfully");
                        // Update product description
                        $product->description = $description;
                        $product->save();
                    } else {
                        $this->warn("Failed to extract description");
                    }

                    // Extract images
                    $images = $this->extractImages($crawler);
                    if (count($images) > 0) {
                        $this->info("Extracted " . count($images) . " images");

                        // Update additional_data in scrapable_product
                        $additionalData = $scrapableProduct->additional_data ?? [];
                        $additionalData['images'] = $images;
                        $scrapableProduct->additional_data = $additionalData;

                        // Download and upload images
                        $uploadedImageIds = $this->downloadAndUploadImages($images);

                        if (!empty($uploadedImageIds)) {
                            $this->info("Successfully uploaded " . count($uploadedImageIds) . " images");
                            // Update product photos with the uploaded image IDs
                            $product->photos = json_encode($uploadedImageIds);
                            $product->save();
                        } else {
                            $this->warn("Failed to upload images");
                        }
                    } else {
                        $this->warn("No images found");
                    }

                    // Mark as scraped
                    $scrapableProduct->scraped = true;
                    $scrapableProduct->save();

                    $this->info("Successfully processed product ID: {$scrapableProduct->product_id}");

                    // Add a delay to avoid overwhelming the server
                    $delay = rand(2, 5);
                    $this->info("Waiting {$delay} seconds before next product...");
                    sleep($delay);

                } catch (\Exception $e) {
                    $this->error("Error processing product ID {$scrapableProduct->product_id}: " . $e->getMessage());
                    Log::error("Scraping error for product ID {$scrapableProduct->product_id}: " . $e->getMessage(), [
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            $this->info('Scraping completed successfully!');
            return 0;
        } catch (\Exception $e) {
            $this->error('An error occurred: ' . $e->getMessage());
            Log::error('Scraping error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * Get HTML content from URL or cache
     *
     * @param string $url
     * @return string|null
     */
    private function getHTML($url)
    {
        if ($product = HttpResponse::where('url', $url)->latest('id')->first()) {
            $this->info("Found product in database");
            $path = resource_path("html" . DIRECTORY_SEPARATOR . "$product->id.html");
            if (file_exists($path)) {
                $this->info("Loading HTML from local file");
                return file_get_contents($path);
            }
            # remove from db
            $product->delete();
        }

        $this->info("Fetching HTML from URL: {$url}");
        $response = Http::withHeaders([
            'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language' => 'en-US,en;q=0.9',
        ])->get($url);

        if (!$response->successful()) {
            $this->warn("Failed to fetch URL: {$url}, Status: {$response->status()}");
            return null;
        }

        $html = $response->body();

        $httpResponse = HttpResponse::create([
            'url' => $url,
            'status_code' => $response->status(),
        ]);
        file_put_contents(resource_path("html" . DIRECTORY_SEPARATOR . "$httpResponse->id.html"), $html);

        return $html;
    }

    /**
     * Extract product description from the page
     *
     * @param Crawler $crawler
     * @return string|null
     */
    protected function extractDescription(Crawler $crawler)
    {
        // Try different selectors for product description
        $descriptionSelectors = [
            '#product-des',
            '.product-description-tab',
            '#tab-description',
            '.tab-description',
            '.product-description',
            '.description',
            '#description',
            '.product-info-description',
            '.product-details',
            '.product-detail',
            '.product-info',
            '.product-text',
            '.product-content',
            '.detail-content',
            '[itemprop="description"]',
        ];

        foreach ($descriptionSelectors as $selector) {
            try {
                if ($crawler->filter($selector)->count() > 0) {
                    return trim($crawler->filter($selector)->html());
                }
            } catch (\Exception $e) {
                continue;
            }
        }

        // If no specific description container found, try to find any div that might contain the description
        try {
            // Look for tabs or sections that might contain description
            $tabSelectors = [
                '.tab-content',
                '.tabs-content',
                '.product-tabs',
                '.product-tab-content',
            ];

            foreach ($tabSelectors as $selector) {
                if ($crawler->filter($selector)->count() > 0) {
                    return trim($crawler->filter($selector)->html());
                }
            }
        } catch (\Exception $e) {
            // Continue to next approach
        }

        return null;
    }

    /**
     * Extract product images from the page
     *
     * @param Crawler $crawler
     * @return array
     */
    protected function extractImages(Crawler $crawler)
    {
        $images = [];

        // Try different selectors for product images
        $imageContainerSelectors = [
            'img.simpleLens-big-image',
            '.product-image-gallery',
            '.product-images',
            '.product-gallery',
            '.gallery',
            '.image-gallery',
            '.thumbnails',
            '.product-thumbnails',
            '.product-image-container',
            '.product-image',
            '.image-additional',
            '.additional-images',
        ];

        foreach ($imageContainerSelectors as $containerSelector) {
            try {
                if ($crawler->filter($containerSelector)->count() > 0) {
                    $crawler->filter($containerSelector)->each(function (Crawler $img) use (&$images) {
                        $src = $img->attr('src') ?: $img->attr('data-src') ?: $img->attr('data-lazy-src');
                        if ($src && !in_array($src, $images)) {
                            $images[] = $src;
                        }
                    });
                    if (count($images) > 0) {
                        return $images;
                    }
                }
            } catch (\Exception $e) {
                continue;
            }
        }

        // If no container found, try to find any images on the page
        try {
            // Look for main product image
            $mainImageSelectors = [
                '.main-image img',
                '.product-image-main img',
                '.product-image img',
                '.product-photo img',
                '#main-image',
                '#product-image',
                '.img-responsive',
                '[itemprop="image"]',
            ];

            foreach ($mainImageSelectors as $selector) {
                if ($crawler->filter($selector)->count() > 0) {
                    $src = $crawler->filter($selector)->attr('src') ?:
                           $crawler->filter($selector)->attr('data-src') ?:
                           $crawler->filter($selector)->attr('data-lazy-src');

                    if ($src && !in_array($src, $images)) {
                        $images[] = $src;
                    }
                }
            }
        } catch (\Exception $e) {
            // Continue to next approach
        }

        return $images;
    }

    /**
     * Download and upload images to the server
     *
     * @param array $imageUrls
     * @return array Array of uploaded image IDs
     */
    protected function downloadAndUploadImages(array $imageUrls)
    {
        $uploadedImageIds = [];

        foreach ($imageUrls as $imageUrl) {
            try {
                // Make sure the URL is absolute
                if (!Str::startsWith($imageUrl, ['http://', 'https://'])) {
                    if (Str::startsWith($imageUrl, '//')) {
                        $imageUrl = 'https:' . $imageUrl;
                    } else {
                        $imageUrl = 'https://www.gsmoled.com/' . ltrim($imageUrl, '/');
                    }
                }

                $this->info("Downloading image from: {$imageUrl}");

                // Download the image
                $response = Http::withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
                ])->get($imageUrl);

                if (!$response->successful()) {
                    $this->warn("Failed to download image from {$imageUrl}: HTTP status " . $response->status());
                    continue;
                }

                // Get image content
                $imageContent = $response->body();

                // Generate a temporary file
                $tempFile = tempnam(sys_get_temp_dir(), 'img_');
                file_put_contents($tempFile, $imageContent);

                // Get file extension from URL
                $extension = pathinfo(parse_url($imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION);
                if (empty($extension)) {
                    $extension = 'jpg'; // Default to jpg if extension can't be determined
                }

                // Create a new Upload instance
                $upload = new Upload();
                $upload->file_original_name = 'product_image_' . Str::random(5);

                // Store the file
                $filePath = 'uploads/all/' . Str::random(20) . '.' . $extension;
                Storage::disk('public')->put($filePath, $imageContent);
                $upload->file_name = $filePath;

                // Set other properties
                $upload->extension = $extension;
                $upload->type = 'image';
                $upload->file_size = strlen($imageContent);
                $upload->save();

                $uploadedImageIds[] = $upload->id;
                $this->info("Successfully uploaded image with ID: {$upload->id}");

                // Clean up the temporary file
                @unlink($tempFile);

                // Add a small delay to avoid overwhelming the server
                usleep(500000); // 0.5 seconds

            } catch (\Exception $e) {
                $this->error("Error uploading image from {$imageUrl}: " . $e->getMessage());
                Log::error("Image upload error: " . $e->getMessage(), [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        return $uploadedImageIds;
    }
}
