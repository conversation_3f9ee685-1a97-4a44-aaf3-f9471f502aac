<?php

namespace App\Console\Commands;

use App\Models\Category;
use App\Models\HttpResponse;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductVariation;
use App\Models\ScrapableProduct;
use App\Models\Shop;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\DomCrawler\Crawler;
use GuzzleHttp\Client;
use GuzzleHttp\Cookie\CookieJar;

class ScrapeGsmOledLoginProducts extends Command
{
    const USER_EMAIL = '<EMAIL>';
    const USER_PASSWORD = 'Bangladesh$1';

    protected array $cookies;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scrape:gsmoled-login-products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scrape products from GSM OLD website with pagination';

    /**
     * The Login URL for the GSM OLED products page.
     *
     * @var string
     */
    protected $loginFormUrl = 'https://www.gsmoled.com/index.php?route=account/login';

    /**
     * The category URL to scrape products from.
     *
     * @var string
     */
    protected $categories = [
        'https://www.gsmoled.com/index.php?route=product/category&path=25_464'
    ];

    /**
     * HTTP Client with cookie jar for maintaining session.
     *
     * @var \GuzzleHttp\Client
     */
    protected $client;

    /**
     * Cookie jar for storing session cookies.
     *
     * @var \GuzzleHttp\Cookie\CookieJar
     */
    protected $cookieJar;

    /**
     * Constructor to initialize the HTTP client with cookie jar.
     */
    public function __construct()
    {
        parent::__construct();
        $this->cookieJar = new CookieJar();
        $this->client = new Client([
            'cookies' => $this->cookieJar,
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language' => 'en-US,en;q=0.5',
            ],
            'allow_redirects' => true,
            'timeout' => 30,
        ]);
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting GSM OLD product scraping...');

        try {
            // Login to the website
            if (!$this->login()) {
                $this->error('Failed to login. Aborting scrape.');
                return 1;
            }

            $this->info('Login successful!');


            foreach ($this->categories as $categoryURL) {
                $this->info("Starting to scrape category ID: {$categoryURL}");
                $this->scrapeCategory($categoryURL);

                // Add a delay between categories to avoid overwhelming the server
                if (next($this->categories) !== false) {
                    $delay = rand(5, 10);
                    $this->info("Waiting {$delay} seconds before scraping next category...");
                    sleep($delay);
                }
            }

            $this->info('Scraping completed successfully!');
            return 0;
        } catch (\Exception $e) {
            $this->error('An error occurred: ' . $e->getMessage());
            Log::error('Scraping error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * Login to the website and maintain session.
     *
     * @return bool
     */
    protected function login(): bool
    {
        $this->info('Attempting to login...');

        try {
           $this->cookies = cache()->remember('gsmOled:login-cookies', 600, function () {
                    // Find the login form - try different selectors
                    $formAction = 'https://www.gsmoled.com/index.php?route=account/login';

                    $loginResponse = Http::asForm()->post(url:$formAction, data: [
                        'email' => self::USER_EMAIL,
                        'password' => self::USER_PASSWORD,
                        'redirect' => 'https://www.gsmoled.com/index.php?route=account/account',
                    ]);
                    if ($loginResponse->successful()){
                        // Step 3: Use the returned cookies for an authenticated session
                        $cookieJar = $loginResponse->cookies();
                        $cookies = [];

                        foreach ($cookieJar as $cookie) {
                            $cookies[$cookie->getName()] = $cookie->getValue();
                        }
                        return $cookies;
                    }else{
                        return [];
                    }
                }
           );

            return true;
        } catch (\Exception $e) {
            $this->error('Login error: ' . $e->getMessage());
            Log::error('Login error: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        return false;
    }

    private function getHTML($url)
    {
        if ($product=HttpResponse::where('url', $url)->latest('id')->first()){
            $this->info("Found product in database");
            $path = resource_path("html".DIRECTORY_SEPARATOR."$product->id.html");
            if (file_exists($path)) {
                $this->info("Loading HTML from local file");
                return file_get_contents($path);
            }
            #remove from db
            $product->delete();
        }

        $response = Http::withCookies($this->cookies, 'www.gsmoled.com')
            ->withHeaders([
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36',
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language' => 'en-US,en;q=0.9',
            ])->get($url);

        $html = $response->getBody();

        $httpResponse = HttpResponse::create([
            'url' => $url,
            'status_code' => $response->status(),
        ]);
        file_put_contents(resource_path("html".DIRECTORY_SEPARATOR."$httpResponse->id.html"), $html);

    }

    /**
     * Scrape products from a specific category.
     *
     * @param string $categoryId
     * @return void
     */
    protected function scrapeCategory($categoryUrl)
    {
        $page = 1;
        $hasMorePages = true;
        $totalPage=1;
        $url = $categoryUrl;

        $categoryName = null;

        while ($hasMorePages) {
            try {
                if (!$this->cookies) {
                    $this->error('No cookies found. Aborting scrape.');
                    return;
                }
                $this->info("Scraping page {$page} of category {$categoryUrl}");
                // Save the category page for debugging
                $html = $this->getHTML($url);

                $crawler = new Crawler($html);
                if ($page==1){
                    $totalPage = $this->totalPagesOfCategory($crawler);
                }
                if ($categoryName==null){
                    $categoryName = $this->extractCategoryName($crawler);
                }

                $this->info("Total page: {$totalPage}");

                // Extract products from the page
                $this->extractProducts($crawler,$page,$categoryName,$categoryUrl);
                if ($totalPage == $page){
                    $hasMorePages = false;
                    $this->info("No more pages to scrape: {$totalPage} = {$page}");
                }else{
                    $page++;
                    $url = $categoryUrl . '&page=' . $page;
                }
            } catch (\Exception $e) {
                $this->error("Error scraping page {$page}: " . $e->getMessage());
            }
        }

    }

    public function extractCategoryName(Crawler $crawler): string
    {
        return  $crawler->filter('.breadcrumb li')->filter('a')->last()->text();
    }
    public function totalPagesOfCategory(Crawler $crawler)
    {
        $paginationExists = $crawler->filter('.pagination')->count() > 0;
        if ($paginationExists){
            $lastPageUrl = $crawler->filter('.pagination li')->filter('a')->last()->attr('href');
            parse_str(parse_url($lastPageUrl, PHP_URL_QUERY), $query);
            return $query['page'] ?? 1;
        }
        return 1;
    }

    /**
     * Extract products from the page and save to database.
     *
     * @param Crawler $crawler
     * @return int Number of products extracted
     */
    protected function extractProducts(Crawler $crawler,$page,$categoryName,$categoryUrl)
    {
        $count = 0;

        // First, try to determine the correct product card selector
        $selectors = [
            '.singel-product',
            '.single-product-col',
            '.product-layout',
            '.product-grid',
            '.product-item',
            '.product-card',
            '.product',
            '.product-thumb',
            '.product-list',
            'div.product',
            '.box-product .product-item',
            '.product-grid .product-item',
            '.product-list .product-item',
        ];

        $productSelector = null;
        foreach ($selectors as $selector) {
            $count = $crawler->filter($selector)->count();
            if ($count > 0) {
                $productSelector = $selector;
                $this->info("Found product selector: {$selector} with {$count} products on page {$page}");
                break;
            }
        }

        // If we still can't find products, try a more generic approach
        if (!$productSelector) {
            // Look for any div that might contain product information
            $possibleContainers = [
                '.category-products',
                '.product-container',
                '.products-container',
                '.product-listing',
                '#content',
                '.main-content',
                '.main',
            ];

            foreach ($possibleContainers as $container) {
                if ($crawler->filter($container)->count() > 0) {
                    // Try to find product elements within this container
                    $containerHtml = $crawler->filter($container)->html();
                    $containerCrawler = new Crawler("<div>{$containerHtml}</div>");

                    // Look for elements with product-related classes or attributes
                    $productElements = $containerCrawler->filter('[class*="product"], [id*="product"], [data-product], a[href*="product"]');

                    if ($productElements->count() > 0) {
                        // We'll handle these elements differently below
                        return $this->extractProductsFromGenericElements($productElements,$categoryName);
                    }
                }
            }

            $this->warn("Could not find any product cards on the page");
            return $count;
        }

        $parent_category_id = Category::query()->where(['name'=>'Accesorios'])->value('id');

        // Find all product cards on the page
        $crawler->filter($productSelector)->each(function (Crawler $node) use (&$count,$categoryName,$categoryUrl,$parent_category_id) {
            try {
                // Try different selectors for product title
                $title = null;
                $titleSelectors = [
                    'h3.product-card__title',
                    'h3.title',
                    'h4.title',
                    'h3.name',
                    'h4.name',
                    '.name',
                    '.caption h4',
                    '.caption h4 a',
                ];

                foreach ($titleSelectors as $selector) {
                    if ($node->filter($selector)->count() > 0) {
                        $title = trim($node->filter($selector)->text());
                        break;
                    }
                }

                if (!$title) {
                    // Try to find any link with a title attribute
                    if ($node->filter('a[title]')->count() > 0) {
                        $title = trim($node->filter('a[title]')->attr('title'));
                    }
                }

                if (!$title) {
                    $this->warn("Could not extract title for a product");
                    return; // Skip this product
                }

                // Try different selectors for product price
                $price = null;
                $priceSelectors = [
                    'div.product-card__price-container div span[data-e2e="price-card"]',
                    '.price',
                    '.price-new',
                    '.price-regular',
                    '.product-price',
                    '.price-container',
                ];

                foreach ($priceSelectors as $selector) {
                    if ($node->filter($selector)->count() > 0) {
                        $price = trim($node->filter($selector)->text());
                        break;
                    }
                }

                // Clean up price (remove currency symbols, etc.)
                if ($price) {
                    $price = preg_replace('/[^0-9.]/', '', $price);
                }

                // Try different selectors for product URL
                $url = null;
                $urlSelectors = [
                    'a.product-card__title-link',
                    'h3.title a',
                    'h4.title a',
                    'h3.name a',
                    'h4.name a',
                    '.name a',
                    '.caption h4 a',
                    'a.product-title',
                    'a.product-name',
                ];

                foreach ($urlSelectors as $selector) {
                    if ($node->filter($selector)->count() > 0) {
                        $url = $node->filter($selector)->attr('href');
                        break;
                    }
                }

                if (!$url) {
                    // Try to find any link with a title that matches the product title
                    $node->filter('a')->each(function (Crawler $linkNode) use (&$url, $title) {
                        if (!$url && (trim($linkNode->attr('title')) == $title || $linkNode->text() == $title)) {
                            $url = $linkNode->attr('href');
                        }
                    });
                }

                if (!$url) {
                    $this->warn("Could not extract URL for product: {$title}");
                    return; // Skip this product
                }

                // Try different selectors for product image
                $imageUrl = null;
                $imageSelectors = [
                    'img.product-card__image',
                    '.image img',
                    '.product-image img',
                    '.product-thumb img',
                    'img.img-responsive',
                ];

                foreach ($imageSelectors as $selector) {
                    if ($node->filter($selector)->count() > 0) {
                        $imageUrl = $node->filter($selector)->attr('src');
                        if (!$imageUrl) {
                            $imageUrl = $node->filter($selector)->attr('data-src');
                        }
                        break;
                    }
                }

                $this->info("Found product: {$title} - Price: {$price}");

                $category = Category::updateOrCreate(
                    ['name' => $categoryName],
                    [
                        'parent_id' => $parent_category_id,
                        'level' => 1,
                        'slug' => Str::slug($categoryName, '-'),
                    ]
                );
                if ($category->wasRecentlyCreated){
                    $this->info("Created new category: {$category->name}");
                    $category->category_translations()->create([
                        'lang' => 'en',
                        'name' => $categoryName
                    ]);
                }
                $slug = Str::slug($title, '-');
                $same_slug_count = Product::where('slug', 'LIKE', $slug . '%')->count();
                $slug_suffix = $same_slug_count > 0 ? '-' . $same_slug_count + 1 : '';
                $slug .= $slug_suffix;
                $product_price = $price + ($price*900);
                // Save to database
                if ( Product::create(['name' => $title, 'lowest_price' => $product_price])->count() == 0) {
                    $product = Product::create([
                        'name' => $title,
                        'shop_id' => Shop::first()->id,
                        'lowest_price' => $product_price,
                        'highest_price' => $product_price,
                        'meta_title' => $title,
                        'slug' => preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', strtolower($slug))) . '-' . Str::random(5),
                    ]);
                    ProductCategory::create([
                        'product_id' => $product->id,
                        'category_id' => $category->id,
                    ]);
                    ProductVariation::create([
                        'product_id' => $product->id,
                        'price' => $product_price,
                    ]);
                    ScrapableProduct::create([
                        'product_id' => $product->id,
                        'source_url' => $url,
                        'source' => 'gsmoled',
                        'additional_data' => [
                            'images' => [
                                $imageUrl
                            ],
                            'source_url' => $categoryUrl,
                        ]
                    ]);

                    $count++;
                }
            } catch (\Exception $e) {
                $this->warn("Error extracting product: " . $e->getMessage());
            }
        });

        $this->info("Extracted {$count} products from current page");
        return $count;
    }

    /**
     * Extract products from generic elements when standard selectors fail.
     *
     * @param Crawler $elements
     * @return int Number of products extracted
     */
    protected function extractProductsFromGenericElements(Crawler $elements,$categoryName)
    {
        $count = 0;
        $processedUrls = [];

        $elements->each(function (Crawler $node) use (&$count, &$processedUrls,$categoryName) {
            try {
                // Try to find a product URL
                $url = null;

                // First, check if the element itself is a link
                if ($node->nodeName() === 'a') {
                    $url = $node->attr('href');
                } else {
                    // Otherwise, look for links inside the element
                    $links = $node->filter('a');
                    if ($links->count() > 0) {
                        // Prefer links that contain "product" in the URL
                        $productLinks = $links->filter('[href*="product"]');
                        if ($productLinks->count() > 0) {
                            $url = $productLinks->first()->attr('href');
                        } else {
                            $url = $links->first()->attr('href');
                        }
                    }
                }

                if (!$url || isset($processedUrls[$url])) {
                    return; // Skip if no URL or already processed
                }

                $processedUrls[$url] = true;

                // Try to find a product title
                $title = null;

                // Look for elements that might contain the title
                $titleElements = $node->filter('h1, h2, h3, h4, h5, .name, .title, [class*="name"], [class*="title"]');
                if ($titleElements->count() > 0) {
                    $title = trim($titleElements->first()->text());
                } else {
                    // Try to get a title from link text or title attribute
                    $links = $node->filter('a');
                    if ($links->count() > 0) {
                        $title = trim($links->first()->attr('title') ?: $links->first()->text());
                    }
                }

                if (!$title) {
                    // As a last resort, use the URL to generate a title
                    $urlParts = parse_url($url);
                    $pathParts = explode('/', trim($urlParts['path'] ?? '', '/'));
                    $lastPart = end($pathParts);
                    $title = str_replace(['-', '_'], ' ', $lastPart);
                    $title = ucwords($title);
                }

                // Try to find a product price
                $price = null;

                // Look for elements that might contain the price
                $priceElements = $node->filter('.price, [class*="price"], .cost, [class*="cost"]');
                if ($priceElements->count() > 0) {
                    $price = trim($priceElements->first()->text());
                    // Clean up price (remove currency symbols, etc.)
                    $price = preg_replace('/[^0-9.]/', '', $price);
                }

                // Try to find a product image
                $imageUrl = null;

                // Look for image elements
                $images = $node->filter('img');
                if ($images->count() > 0) {
                    $imageUrl = $images->first()->attr('src');
                    if (!$imageUrl) {
                        $imageUrl = $images->first()->attr('data-src');
                    }
                }

                // Save to a database
                $product =ScrapedProduct::updateOrCreate(
                    ['url' => $url],
                    [
                        'title' => $title,
                        'price' => $price,
                        'image_url' => $imageUrl,
                        'source' => 'gsmoled',
                        'additional_data' => [
                            'category' => $categoryName
                        ]
                    ]
                );
                $this->info("Saved product to database: {$product->id}");
                $count++;
            } catch (\Exception $e) {
                $this->warn("Error extracting generic product: " . $e->getMessage());
            }
        });

        $this->info("Extracted {$count} products from generic elements");
        return $count;
    }

    /**
     * Parse Set-Cookie headers or cookie strings into a name=>value array for Http::withCookies().
     *
     * @param array|string $cookies
     * @return array
     */
    public function parseCookies(array|string $cookies): array
    {
        $cookieArray = [];

        // If it's a string (e.g. "key1=val1; key2=val2")
        if (is_string($cookies)) {
            foreach (explode(';', $cookies) as $pair) {
                if (str_contains($pair, '=')) {
                    [$name, $value] = explode('=', $pair, 2);
                    $cookieArray[trim($name)] = trim($value);
                }
            }
            return $cookieArray;
        }

        // If it's an array of Set-Cookie headers or strings
        foreach ($cookies as $cookie) {
            // If it's a header array like ['Set-Cookie' => '...'], extract the string
            if (is_array($cookie) && isset($cookie['Set-Cookie'])) {
                $cookie = $cookie['Set-Cookie'];
            }

            // Now, treat it as a string
            if (is_string($cookie)) {
                $parts = explode(';', $cookie);
                $main = trim($parts[0]);
                if (str_contains($main, '=')) {
                    [$name, $value] = explode('=', $main, 2);
                    $cookieArray[trim($name)] = trim($value);
                }
            }
        }

        return $cookieArray;
    }


}
