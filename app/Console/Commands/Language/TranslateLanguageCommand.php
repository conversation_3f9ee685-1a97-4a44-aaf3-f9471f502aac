<?php

namespace App\Console\Commands\Language;

use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use GuzzleHttp\Client;

class TranslateLanguageCommand extends Command
{
    protected $signature = 'translate:language';

    protected $description = 'Translate en.json language to other languages';

    protected $languages = [
        'bn' => 'bn', // Bangla
        'de' => 'de', // German
        'es' => 'es', // Spanish
        'fr' => 'fr', // French
        'hi' => 'hi', // Hindi
        'it' => 'it', // Italian
        'ja' => 'ja', // Japanese
        'nl' => 'nl', // Dutch
        'no' => 'no', // Norwegian
        'pt' => 'pt', // Portuguese
        'ru' => 'ru', // Russian
        'sc' => 'zh', // Simplified Chinese
        'tc' => 'zh-TW', // Traditional Chinese
        'id' => 'id', // Indonesian
        'th' => 'th', // Thai
    ];

    public function handle()
    {
        $apiKey = config('services.chatgpt.api_key');
        if (!$apiKey) {
            $this->error("API key for ChatGPT is not set. Please define it in your .env file as CHATGPT_API_KEY.");
            return;
        }

        $sourcePath = resource_path('lang/en.json');

        if (!File::exists($sourcePath)) {
            $this->error("Source file en.json not found in lang directory.");
            return;
        }

        $sourceContent = json_decode(File::get($sourcePath), true);
        if (!$sourceContent) {
            $this->error("Invalid JSON in en.json.");
            return;
        }

        foreach ($this->languages as $locale => $language) {

            $targetLanguage = $this->languages[$locale];
            $targetPath = resource_path("lang/{$locale}.json");

            File::ensureDirectoryExists(dirname($targetPath));

            $existingTranslations = File::exists($targetPath)
                ? json_decode(File::get($targetPath), true)
                : [];

            if (!is_array($existingTranslations)) {
                $this->error("Invalid JSON in {$locale}.json. Skipping translation.");
                $existingTranslations = [];
            }
            $translatedContent = $existingTranslations;
            foreach ($sourceContent as $key => $text) {
                if (empty($translatedContent[$key])) {
                    $translatedContent[$key] = $this->translateText($text, $targetLanguage, $apiKey);
                }
            }
            File::put($targetPath, json_encode($translatedContent, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            $this->info("Translations saved to {$targetPath}");
        }
    }

    private function translateText(string $text, string $targetLanguage, string $apiKey): string
    {
        try {
            $client = new Client();
            $response = $client->post('https://api.openai.com/v1/chat/completions', [
                'headers' => [
                    'Authorization' => "Bearer {$apiKey}",
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => 'gpt-4o',
                    'messages' => [
                        ['role' => 'system', 'content' => 'You are a professional translator with expertise in adapting text for software and app interfaces. Ensure translations are accurate, concise, and suitable for user-facing interfaces.'],
                        [
                            'role' => 'user',
                            'content' => "Translate the following text into {$targetLanguage}, ensuring it remains suitable for use in a software or app interface: {$text}",
                        ],
                    ],
                    'temperature' => 0.2,
                ],
            ]);

            $data = json_decode($response->getBody(), true);

            if (isset($data['choices'][0]['message']['content'])) {
                return trim($data['choices'][0]['message']['content']);
            }

            $this->error("Translation failed for text: {$text}");
            return $text;
        } catch (Exception $e) {
            $this->error("Error translating text: {$text}. Exception: {$e->getMessage()}");
            return $text;
        }
    }
}
