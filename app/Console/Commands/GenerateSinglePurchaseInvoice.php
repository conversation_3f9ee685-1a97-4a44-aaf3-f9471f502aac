<?php

namespace App\Console\Commands;

use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Jobs\TakePaymentFromInvoiceJob;
use App\Models\Team;
use App\Notifications\SendManualInvoiceNotification;
use App\Services\PaymentGateway\InvoiceServices\InvoiceGenerator;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class GenerateSinglePurchaseInvoice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'xcloud:generate-single-purchase-invoice-for-team';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        // select all the team lazy by id
        $teams = Team::lazyById();

        Log::info('Generating single purchase invoice for '.now()->toDateTimeString());

        $this->info('Generating single purchase invoice for '.now()->toDateTimeString());

        if (now()->day < 5 || now()->day > 28) {
            Log::info('Single purchase invoice generation can only be done from 5 to 29 of the month');
            $this->info('Single purchase invoice generation can only be done from 5 to 29 of the month');
            return;
        }

        foreach ($teams as $team) {
            // looks for bills which created more than 2 day and doesn't have invoice id
            $bills = $team->bills()->unpaid()->withoutOffer()->where('created_at', '>', now()->subDays(2))->whereNull('invoice_id')->get();

            // if there is no bill then look for created_at null
            if ($bills->isEmpty()) {
                $bills = $team->bills()->unpaid()->withoutOffer()->whereNull('created_at')->whereNull('invoice_id')->get();
            }

            // If still not a bill then look for unpaid bills
            if ($bills->isEmpty()) {
                $bills = $team->bills()->unpaid()->withoutOffer()->whereNull('invoice_id')->get();
            }

            // if there is no bill, then return
            if ($bills->isEmpty()) {
                continue;
            }

            $this->info('Generating single purchase invoice for team#' . $team->email);

            Log::info('Generating single purchase invoice for team#' . $team->email);

            $this->info('Sum of bills: ' . $bills->sum('amount_to_pay'));

            // if there is bill, then create invoice for that team the source will be single purchase
            $invoice = InvoiceGenerator::bills($bills)
                            ->team($team)
                            ->source(InvoiceSourceEnum::SinglePurchase)
                            ->generate();


            if ($invoice->type->isRequiredPaymentMethod()) {
                // if there is invoice, then take payment from that invoice
                $this->info('Dispatching job for taking payment for invoice#' . $invoice->id);
                Log::warning('Dispatching job for taking payment for invoice#' . $invoice->id);
                TakePaymentFromInvoiceJob::dispatch($invoice);
            } else {
                // if there is no invoice, then send manual invoice notification
                $this->info('Sending manual invoice notification for invoice#' . $invoice->id);
                Log::warning('Sending manual invoice notification for invoice#' . $invoice->id);
                $invoice->team?->owner->notify(new SendManualInvoiceNotification($invoice));
            }
        }

        Log::info('Single purchase invoice generation completed for '.now()->toDateTimeString());
    }
}
