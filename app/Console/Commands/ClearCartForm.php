<?php

namespace App\Console\Commands;

use App\Enums\XcloudBilling\CartFormStatuses;
use App\Models\CartForm;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ClearCartForm extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clear:cart-form';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('Clearing cart form for '.now()->toDateTimeString());

        CartForm::whereNotIn('status', CartFormStatuses::CompletedStatuses())->whereNull('invoice_id')->where('should_expire_on', '<=', now()->subDays(7)->toDateString())->delete();

        Log::info('Cart form cleared');

        return Command::SUCCESS;
    }
}
