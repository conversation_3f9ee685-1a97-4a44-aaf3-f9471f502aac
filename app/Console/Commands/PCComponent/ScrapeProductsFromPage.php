<?php

namespace App\Console\Commands\PCComponent;

use App\Models\HttpResponse;
use App\Traits\ProductParser;
use App\Traits\StoreHtmlResponse;
use Illuminate\Console\Command;

class ScrapeProductsFromPage extends Command
{
    use StoreHtmlResponse, ProductParser;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scrape:products-from-page';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';



    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $pages = HttpResponse::where('additional_data->type', 'like', 'product-list')->get();
        $this->info("Found " . $pages->count() . " pages");
        foreach ($pages as $page) {
            $this->info("Scraping page: " . $page->url);
            $html = $this->fetchWithCloudflareBypass($page->url);
            $products = $this->parseProductsFromHtml($html);
            foreach ($products as $product) {
                $this->info("Found product: " . $product['title']);
                $this->saveProduct($product);
            }
        }
        return 0;
    }

    public function saveProduct($baseProduct)
    {
        $this->info("Saving product: " . $baseProduct['title']);
        $html = $this->fetchWithCloudflareBypass($baseProduct['url']);
        $productData =$this->parseProductData($html, $baseProduct['url']);
        dd($productData);
    }
}
