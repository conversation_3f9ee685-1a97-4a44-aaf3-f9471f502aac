<?php

namespace App\Console\Commands;

use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ManageSiteTypeFallbackCommand extends Command
{
    protected $signature = 'site:type-fallback {--revert : Revert to original site types}';
    protected $description = 'Convert unsupported site types to custom-php (default) or revert to original types';

    protected $unsupported = [
        'laravel',
        'phpmyadmin',
        'mautic',
        'n8n',
        'uptime-kuma',
        'ghost'
    ];

    protected $backupPath = 'storage/framework/cache/site_type_backup.json';

    public function handle()
    {
        if ($this->option('revert')) {
            $this->revert();
        } else {
            $this->fallback();
        }
    }

    protected function fallback()
    {
        $this->info('Applying fallback: storing original type and updating to custom-php...');

        // Get sites that need to be updated
        $sitesToUpdate = DB::table('sites')
            ->whereIn('type', $this->unsupported)
            ->select('id', 'type')
            ->get();

        if ($sitesToUpdate->isEmpty()) {
            $this->info("No sites found with unsupported types.");
            return;
        }

        // Create backup JSON
        $backupData = $sitesToUpdate->map(fn($site) => [
            'id' => $site->id,
            'original_type' => $site->type
        ])->toArray();

        // Save backup to file
        File::put(base_path($this->backupPath), json_encode($backupData, JSON_PRETTY_PRINT));
        $this->info("Backup created at {$this->backupPath}");

        // Update meta with original type
        DB::table('sites')
            ->whereIn('type', $this->unsupported)
            ->update([
                'meta->original_type' => DB::raw('type')
            ]);

        // Update type to custom-php
        $updated = DB::table('sites')
            ->whereIn('type', $this->unsupported)
            ->update([
                'type' => 'custom-php'
            ]);

        $this->info("Updated $updated site(s) to custom-php.");
    }

    protected function revert()
    {
        $this->info('Reverting fallback: restoring original type and cleaning meta...');

        // Check if backup file exists
        $backupFilePath = base_path($this->backupPath);
        if (File::exists($backupFilePath)) {
            $this->info("Found backup file at {$this->backupPath}");

            try {
                // Load backup data
                $backupData = json_decode(File::get($backupFilePath), true);

                if (empty($backupData)) {
                    $this->warn("Backup file is empty or invalid. Falling back to database meta data.");
                    $this->revertFromMeta();
                    return;
                }

                $updatedCount = 0;

                // Process each site from the backup
                foreach ($backupData as $siteData) {
                    $updated = DB::table('sites')
                        ->where('id', $siteData['id'])
                        ->update([
                            'type' => $siteData['original_type']
                        ]);

                    $updatedCount += $updated;
                }

                // Clean up meta data
                if ($updatedCount > 0) {
                    DB::statement("UPDATE sites SET meta = JSON_REMOVE(meta, '$.original_type') WHERE JSON_CONTAINS_PATH(meta, 'one', '$.original_type')");
                }

                $this->info("Reverted {$updatedCount} site(s) to their original types using backup file.");

                // Rename backup file to indicate it's been used
                File::move($backupFilePath, $backupFilePath . '.used.' . date('Y-m-d-His'));
                $this->info("Backup file renamed to {$this->backupPath}.used." . date('Y-m-d-His'));

            } catch (\Exception $e) {
                $this->error("Error processing backup file: " . $e->getMessage());
                $this->warn("Falling back to database meta data.");
                $this->revertFromMeta();
            }
        } else {
            $this->warn("No backup file found at {$this->backupPath}");
            $this->warn("Falling back to database meta data.");
            $this->revertFromMeta();
        }
    }

    /**
     * Revert site types using meta data stored in the database
     */
    protected function revertFromMeta()
    {
        $updated = DB::table('sites')
            ->whereNotNull('meta->original_type')
            ->update([
                'type' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(meta, '$.original_type'))")
            ]);

        if ($updated) {
            DB::statement("UPDATE sites SET meta = JSON_REMOVE(meta, '$.original_type') WHERE JSON_CONTAINS_PATH(meta, 'one', '$.original_type')");
        }

        $this->info("Reverted {$updated} site(s) to their original types using database meta.");
    }
}
