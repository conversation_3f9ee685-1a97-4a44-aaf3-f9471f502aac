<?php

namespace App\Console;

use App\Console\Commands\AutoDisableFileMangerAdminer;
use App\Console\Commands\CleanupImpersonateActions;
use App\Console\Commands\ClearCartForm;
use App\Console\Commands\UpdateRocketNginxFiles;
use App\Console\Commands\DeletePlayGroundSite;
use App\Console\Commands\FetchPromotersFromFirstPromoter;
use App\Console\Commands\GenerateBillsCommand;
use App\Console\Commands\GenerateMonthlyInvoiceCommand;
use App\Console\Commands\GenerateSinglePurchaseInvoice;
use App\Console\Commands\GetPatchstackVulnerabilitiesCommand;
use App\Console\Commands\GetWordfenceVulnerabilities;
use App\Console\Commands\InvoiceInspector;
use App\Console\Commands\LockSystemIfOverdueOnActiveBilling;
use App\Console\Commands\LockSystemIfPaymentFailedOnGeneralInvoices;
use App\Console\Commands\Maintenance\MarkAsFailedCommand;
use App\Console\Commands\Maintenance\RefreshExpiredOAuthTokens;
use App\Console\Commands\ScanVulnerabilities;
use App\Console\Commands\Server\CheckHeartBit;
use App\Console\Commands\Site\RenewXCloudSslCertificates;
use App\Console\Commands\Slack\SendSlackDailyStatistics;
use App\Console\Commands\SyncUsersDataWithFluentCRM;
use App\Console\Commands\UpdateInvoiceNumberIfNotExists;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('model:prune')->twiceDaily(first: 1, second: 13);
        $schedule->command('telescope:prune --hours=48')->twiceDaily(first: 2, second: 14);
        $schedule->command('queue:prune-failed --hours=48')->twiceDaily(first: 3, second: 15);
        $schedule->command('queue:prune-batches --hours=48')->twiceDaily(first: 4, second: 16);

        $schedule->command(RenewXCloudSslCertificates::class)->twiceDailyAt(first: 5, second: 17);
        $schedule->command(RefreshExpiredOAuthTokens::class)->everySixHours();
        $schedule->command(MarkAsFailedCommand::class)->everyFifteenMinutes();
        $schedule->command(CheckHeartBit::class)->when(fn() => app()->isProduction())->hourly();

//        $schedule->command(GenerateBillsCommand::class)->everyTwoHours();
//        $schedule->command(GenerateMonthlyInvoiceCommand::class)->everyThreeHours();

        // Generate Bills Command
        $schedule->command(GenerateBillsCommand::class)->hourly()->when(function () {
            return now()->day <= 2; // First 2 days of the month
        });

        $schedule->command(GenerateBillsCommand::class)->dailyAt('00:00')->when(function () {
            return now()->day > 2; // After the first 2 days
        });

        // Generate Monthly Invoice Command
        $schedule->command(GenerateMonthlyInvoiceCommand::class)->everyThreeHours()->when(function () {
            return now()->day <= 3; // First 3 days of the month
        });

        // Generate Monthly Invoice Command (Daily till 7th day)
        $schedule->command(GenerateMonthlyInvoiceCommand::class)
            ->dailyAt('00:00')
            ->when(function () {
                return now()->day > 3 && now()->day <= 7; // Days 4 to 7
            });

        $schedule->command(GenerateSinglePurchaseInvoice::class)->everySixHours();

        $schedule->command(InvoiceInspector::class)->daily();

        $schedule->command(UpdateInvoiceNumberIfNotExists::class)->everySixHours();

        $schedule->command(ClearCartForm::class)->twiceDaily(first: 6, second: 18);
        $schedule->command(FetchPromotersFromFirstPromoter::class)->twiceDaily(first: 7, second: 19);
        $schedule->command(DeletePlayGroundSite::class)->everyFifteenMinutes();
        $schedule->command(SendSlackDailyStatistics::class)->dailyAt('03:01');

        // Sync users data with FluentCRM 3 times in a week
        $schedule->command(SyncUsersDataWithFluentCRM::class)->mondays()->at('10:00');
        $schedule->command(SyncUsersDataWithFluentCRM::class)->wednesdays()->at('10:00');
        $schedule->command(SyncUsersDataWithFluentCRM::class)->fridays()->at('10:00');

        // Lock system if overdue on active billing
        $schedule->command(LockSystemIfOverdueOnActiveBilling::class)->daily();
        $schedule->command(LockSystemIfPaymentFailedOnGeneralInvoices::class)->daily();

        //Vulnarability check
        $schedule->command(GetWordfenceVulnerabilities::class)->dailyAt('03:00');

        //Vulnarability check for Patchstack
        $schedule->command(GetPatchstackVulnerabilitiesCommand::class)->dailyAt('04:00');

        // Vulnarability scan
        $schedule->command(ScanVulnerabilities::class)->daily();

        //sync last month vultr bill, run 2nd day of the month
        $schedule->command('sync:last-month-vultr-bill')->monthlyOn(2, '10:00');

        //sync last month vultr bill, run 5th day of the month
        $schedule->command('sync:last-month-vultr-bill')->monthlyOn(5, '02:00');
        # Automatic Disable File Manager and Adminer
        $schedule->command(AutoDisableFileMangerAdminer::class)->hourly();
        # Cleanup old impersonate actions
        $schedule->command(CleanupImpersonateActions::class)->monthlyOn(28);

        # Check for Rocket-Nginx updates weekly
        $schedule->command(UpdateRocketNginxFiles::class)->weekly();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
