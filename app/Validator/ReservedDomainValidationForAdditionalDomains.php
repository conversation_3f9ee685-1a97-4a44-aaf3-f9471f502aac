<?php

namespace App\Validator;

use Arr;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Validator;

/**
 * Class Domain
 * @package dacoto\DomainValidator
 */
class ReservedDomainValidationForAdditionalDomains implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        if(!auth()->user()->isSuperAdmin()){
            $reservedDomainsPattern = implode('|', array_map(function($d) {
                return preg_quote($d, '/');
            }, getReservedDomains()));

            foreach (collect($value) as $domain) {
                if(Arr::get($domain, 'value')){
                    // Check if it's a reserved domain
                    if (preg_match('/\b(?:' . $reservedDomainsPattern . ')\b/i', $domain['value'])) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return trans('The :attribute is not a valid domain.');
    }
}
