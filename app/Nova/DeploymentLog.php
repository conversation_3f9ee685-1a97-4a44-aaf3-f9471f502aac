<?php

namespace App\Nova;

use App\Enums\CustomNginxEnum;
use App\Enums\DeploymentLogsStatusEnum;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class DeploymentLog extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\DeploymentLog>
     */
    public static $model = \App\Models\DeploymentLog::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    public static $group = 'Deployment Logs';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'source_site_id',
        'destination_site_id',
        'sourceSite.name',
        'destinationSite.name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make(__('Source Site'), 'sourceSite', Site::class)->searchable()->readonly(),
            BelongsTo::make(__('Destination Site'), 'destinationSite', Site::class)->searchable()->readonly(),
            Select::make(__('Status'), 'status')->options(
                [
                    DeploymentLogsStatusEnum::PUSHED->value => DeploymentLogsStatusEnum::PUSHED->value,
                    DeploymentLogsStatusEnum::PULLED->value => DeploymentLogsStatusEnum::PULLED->value,
                    DeploymentLogsStatusEnum::PENDING->value => DeploymentLogsStatusEnum::PENDING->value,
                    DeploymentLogsStatusEnum::FAILED->value => DeploymentLogsStatusEnum::FAILED->value,
                ]
            )->displayUsingLabels(),

            Select::make(__('Action'), 'action')->options(
                [
                    DeploymentLogsStatusEnum::PULL->value => DeploymentLogsStatusEnum::PULL->value,
                    DeploymentLogsStatusEnum::PUSH->value => DeploymentLogsStatusEnum::PUSH->value
                ]
            )->displayUsingLabels(),

//            BelongsTo::make(__('Initiated By'), 'initiatedBy', \App\Models\User::class)->searchable(),

            DateTime::make('Updated At')->readonly(function () {
                return $this->resource->exists;
            })->sortable(),

            DateTime::make('Created At')->readonly(function () {
                return $this->resource->exists;
            })->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return $request->user()?->isAdmin() || $request->user()?->isSupportLevel2();
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToDelete(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }
}
