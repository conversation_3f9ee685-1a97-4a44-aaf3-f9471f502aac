<?php

namespace App\Nova\Actions;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Jobs\Server\DeleteServer;
use App\Jobs\Server\DeleteServerBills;
use App\Models\BackupFile;
use App\Models\BackupSetting;
use App\Models\Task;
use App\Notifications\SendManualInvoicePaidNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\ServerDeletedNotification;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Http\Requests\NovaRequest;

class ServerDeleteAction extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models): mixed
    {
        if ($models->count() !== 1) {
            return Action::danger('You can only delete one server at a time.');
        }
        $server = $models->first();

        Log::error('Server Delete Action: '. $fields->delete_bills. ' by '. auth()->user()->email);

        if ($server) {
            $hasPaidInvoice = $server->bills()->whereHas('invoice',function($query){
                    $query->where(['status'=>BillingStatus::Paid]);
                })
                ->exists();
            if ($hasPaidInvoice) {
                return Action::danger('Server has paid invoices. Please cancel or refund the invoices first.');
            }
            #get sites:id of the server
            $sites = $server->sites()->pluck('id')->toArray();



            if (count($sites) > 0) {
                #delete(from db) all the backup files of the sites
                Log::info('DeleteServer: Deleting backup files of sites: by '.user()->name);
                BackupFile::whereHas('backupSetting', fn($q) => $q->whereIn('site_id', $sites))->delete();
                #delete all the backup settings of the sites
                BackupSetting::whereIn('site_id', $sites)->delete();
            }
            //if the server provider is xcloud or digital ocean need to delete the server from the provider
            // Send email notification about server deletion
            try {
                Mail::to('<EMAIL>')
                    ->cc(['<EMAIL>', '<EMAIL>'])
                    ->send(new ServerDeletedNotification($server, auth()->user()->name, $fields->delete_bills));

                Log::info("Server deletion notification email sent for server {$server->name} (ID: {$server->id})");
            } catch (\Exception $e) {
                Log::error("Failed to send server deletion notification email: {$e->getMessage()}");
            }

            if ($server->cloudProvider?->isXCloudOrWhiteLabel() || $fields->confirm) {
                Log::info("DeleteServer: Deleting server  {$server->name} with provider by ".user()->name);
                $task = Task::create([
                    'name' => 'DeleteServer',
                    'status' => 'pending',
                    'server_id' => $server->id,
                    'initiated_by' => auth()->id(),
                    'team_id' => $server->team_id,
                ]);
                Log::error(' $fields->delete_bills'. $fields->delete_bills);
                $jobs = [
                    new DeleteServer($task, $server),
                ];
                if ($fields->delete_bills){
                    Log::info("DeleteServer: Deleting bills of server  {$server->name} with provider by ".user()->name);
                    $jobs[] = new DeleteServerBills($server);
                }
                Bus::chain($jobs)->dispatch();
            }else{
                Log::info("DeleteServer: Deleting server {$server->name} by ".user()->name);
                $server->forceDelete();
                if ($fields->delete_bills) {
                    Log::info("DeleteServer: Deleting bills of server  {$server->name} with provider by ".user()->name);
                    DeleteServerBills::dispatch($server);
                }
            }
            return Action::message($server->name . ' Server Delete Initiated');
        }
        return Action::danger('Server not found');
    }

    /**
     * Get the fields available on the action.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Boolean::make("Delete From Provider", 'confirm')
                ->default(true)
                ->rules('boolean')->hideFromIndex(),
            Boolean::make("Delete Bills Also", 'delete_bills')
                ->default(false)
                ->rules('boolean')->hideFromIndex(),
        ];
    }
}
