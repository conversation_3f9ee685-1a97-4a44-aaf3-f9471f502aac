<?php

namespace App\Nova\Lenses;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\PlansEnum;
use App\Enums\XcloudBilling\TeamBillingStatus;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Date;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\LensRequest;
use Laravel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Lenses\Lens;
use Laravel\Nova\Nova;

class ActiveBillingLockedTeams extends Lens
{
    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'name', 'activePlan.name', 'email'
    ];

    /**
     * Get the query builder / paginator for the lens.
     *
     * @param  \Laravel\Nova\Http\Requests\LensRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return mixed
     */
    public static function query(LensRequest $request, $query)
    {
        return $request->withOrdering($request->withFilters(
            $query->whereHas('activePlan', function ($query) {
                $query->where('name', '!=', PlansEnum::Free)->where('requires_billing', true);
            })->where('billing_status', '!=',TeamBillingStatus::Active)
        ));
    }

    /**
     * Get the fields available to the lens.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make(Nova::__('ID'), 'id')->sortable(),
            Text::make(Nova::__('Name'), 'name'),
            Text::make(Nova::__('Email'), 'email'),
            Text::make(Nova::__('Billing Status'), 'billing_status')->sortable(),
            BelongsTo::make(Nova::__('Plan'), 'activePlan', 'App\Nova\BillingPlan')->sortable(),
            Text::make(Nova::__('Invoice Dues'), function (){
                return '$'.format_billing($this->generalInvoices->whereNotIn('status', [
                    BillingStatus::Paid,
                    BillingStatus::Cancelled
                ])->sum('amount'));
            })->readonly(),
            Text::make(Nova::__('Unpaid Invoices'), function (){
                return $this->generalInvoices->whereNotIn('status', [
                    BillingStatus::Paid,
                    BillingStatus::Cancelled
                ])->count();
            })->readonly(),
            Text::make(Nova::__('Latest Invoice Paid'), function (){
                return $this->generalInvoices->whereNotIn('status', [
                    BillingStatus::Paid,
                    BillingStatus::Cancelled
                ])?->sortByDesc('id')?->first()?->created_at?->format('d M Y') ?: null;
            })->readonly(),
            Date::make('Updated At', 'updated_at')->sortable(),
        ];
    }

    /**
     * Get the cards available on the lens.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the lens.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available on the lens.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return parent::actions($request);
    }

    /**
     * Get the URI key for the lens.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'locked-teams';
    }
}
