<?php

namespace App\Nova;

use App\Nova\Actions\TransferServer;
use App\Nova\Filters\ServerBillHasOffer;
use App\Nova\Filters\ServerBillRenewalPeriod;
use App\Nova\Filters\ServerBillingStatus;
use App\Nova\Filters\ProvisionedWithIP;
use App\Nova\Filters\ServerHasWhiteLabel;
use App\Nova\Filters\ServerIsConnected;
use App\Nova\Filters\provider;
use App\Nova\Lenses\ServerWithMostSites;
use App\Nova\Metrics\ServersPerDay;
use Illuminate\Http\Request;
use Laravel\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Exceptions\HelperNotSupported;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Code;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\MorphMany;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Server extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Server::class;

    public static $displayInNavigation = true;
    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'name', 'public_ip', 'status', 'region'
    ];

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $with = [
        'team', 'team.whiteLabel', 'user', 'cloudProvider',
    ];

    public static $group = 'Main';

    /**
     * Get the fields displayed by the resource.
     *
     * @param  NovaRequest  $request
     * @return array
     * @throws HelperNotSupported
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make('Name', fn() =>
                "<a href='".route('server.show', $this->id)."'
                    class='no-underline dim text-primary font-bold'
                    target=_blank>{$this->name}</a>")
                ->sortable()
                ->rules('required', 'max:255')->asHtml()->exceptOnForms(),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255')->onlyOnForms(),

            BelongsTo::make('User')->searchable(),

            BelongsTo::make('Team')->searchable(),

            // white label
            Text::make('White Label', function () {
                if (!$this->team?->whiteLabel){
                    return '';
                }
                $name = $this->team?->whiteLabel?->domain ?: "whl-{$this->team?->whiteLabel?->id}";
                return "<a href='/admin/resources/white-labels/".$this->team?->whiteLabel?->id."'
                    class='no-underline dim text-primary font-bold'>{$name}</a>";
            })->asHtml()->exceptOnForms(),

            BelongsTo::make('Cloud Provider', 'cloudProvider')->searchable()->nullable(),

            Text::make('Public Ip')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make('Status')
                ->sortable()
                ->rules('required', 'max:255'),

            Boolean::make('Is Connected')->sortable(),

            Text::make('Region')
                ->sortable()
                ->rules('required', 'max:255')->onlyOnDetail(),

            Text::make('Size'),

            Textarea::make('Log')->alwaysShow(),

            Text::make('Server Image')->onlyOnDetail(),
            Text::make('Ubuntu Version')->onlyOnDetail(),
            Text::make('Php Version'),
            Text::make('Database Type')->onlyOnDetail(),
            Text::make('Database Name'),

            Text::make('Total Sites', fn() => $this->sites->count())->onlyOnIndex(),

            DateTime::make('Created At')->onlyOnDetail()->readonly(),

            DateTime::make('Created on', 'created_at')
                ->displayUsing(function ($value) {
                    return $value ? $value->format('d/m/y').' ('.$value->format('M').')' : null;
                })->hideFromDetail()->readonly(),

            DateTime::make('First Bill', 'first_bill_generated_on')
                ->displayUsing(function ($value) {
                    return $value ? $value->format('d/m/y').' ('.$value->format('M').')' : null;
                })->readonly(),

            DateTime::make('Last Visited', 'last_visited_at')->rules([
                'nullable',
                'date',
            ])->hideFromIndex(),

            Text::make('Last Bill', function () {
                $value = $this->latest_bill_generated_on;

                if ($value && $value->format('m') !== now()->format('m')) {
                    return '<b style="color: darkred">' . $value->format('d/m/y') . ' (' . $value->format('M') . ')</b>';
                }

                return $value ? $value->format('d/m/y') . ' (' . $value->format('M') . ')' : null;
            })->asHtml()->readonly(),

            DateTime::make('Updated At')->onlyOnDetail()->readonly(),

            MorphMany::make('Bills'),

            HasMany::make('Sites'),

            HasMany::make('Site Migrations', 'siteMigration'),

            HasMany::make('Manual Site Migration', 'manualMigration'),

            HasMany::make('PHPVersions'),

            HasMany::make('Tasks'),

            Code::make('Meta')->json()->nullable()
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new \App\Nova\Metrics\Servers())->width('1/2'),
            (new ServersPerDay())->width('1/2')
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            new ProvisionedWithIP(),
            new ServerIsConnected(),
            new ServerBillingStatus(),
            new ServerBillRenewalPeriod(),
            new Provider(),
            new ServerBillHasOffer(),
            new ServerHasWhiteLabel()
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [
            new ServerWithMostSites
        ];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        $actions = [];

        if (user()->isAdmin() || user()->isSupportLevel2()){
            $actions = [
                (new Actions\ServerDeleteAction())->confirmText('Are you sure you want to delete this server?'),
                (new Actions\ServerProvisionAction())->confirmText('Are you sure you want to re-run provision script for this server?'),
                (new Actions\ForceDeleteServer())->confirmText('Are you sure you want to force delete this server?'),
                (new Actions\InstallMonitoringAction())->confirmText('Are you sure you want to install monitoring for this server?'),
                (new Actions\RescueAllSites())->confirmText('Are you sure you want to rescue all sites?'),
                (new Actions\SendSlackDataOnDemand())->confirmText('Are you sure you want to send notification on slack to get on demand data?'),
                (new Actions\GenerateBills())->confirmText('Are you sure you want to generate bills for this server?'),
            ];
        }

        if (user()->isSuperAdmin()) {
            $actions = array_merge($actions, [
                (new TransferServer())->canRun(function ($request) {
                    return $request->user()->isSuperAdmin();
                }),
            ]);
        }

        return $actions;
    }

    public function authorizedToDelete(Request $request)
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin();
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToRunAction(NovaRequest $request, Action $action): bool
    {
        return $request->user()?->isSuperAdmin();
    }
}
