<?php

namespace App\Nova;

use App\Enums\PatchstackVulnerabilityStatus;
use App\Nova\Actions\ActivatePatchstackService;
use App\Nova\Metrics\PatchstackPerDay;
use App\Nova\Metrics\PatchstackVulnerabilities;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

class PatchstackVulnerability extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\PatchstackVulnerability>
     */
    public static $model = \App\Models\PatchstackVulnerability::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    public function title()
    {
        return $this->site ? $this->site->domain : 'Patchstack #' . $this->id;
    }

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'site_id', 'patchstack_site_id'
    ];

    /**
     * Build an "index" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query;
    }

    public static $group = 'Security';

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Site'),

            Text::make('Patchstack Site ID', 'patchstack_site_id')
                ->sortable(),

            Boolean::make('Is Purchase', 'is_purchase')
                ->sortable(),

            Select::make('Status')
                ->options(PatchstackVulnerabilityStatus::toSelectArray())
                ->displayUsingLabels()
                ->sortable(),

            DateTime::make('Created At')
                ->sortable()
                ->hideFromIndex(),

            DateTime::make('Updated At')
                ->sortable()
                ->hideFromIndex(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new PatchstackVulnerabilities())->width('1/2'),
            (new PatchstackPerDay())->width('1/2')
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            (new ActivatePatchstackService)
                ->onlyOnTableRow()
                ->canSee(function ($resource) {
                    return is_null($resource->patchstack_site_id);
                })
                ->canRun(function ($resource) {
                    return is_null($resource->patchstack_site_id);
                })
                ->confirmButtonText('Activate')
                ->confirmText('Are you sure you want to activate Patchstack for this site?'),
        ];
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return false;
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return false;
    }

    public function authorizedToDelete(Request $request): bool
    {
        return false;
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return false;
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->hasAnySupportRole();
    }
}
