<?php

namespace App\Nova;

use App\Nova\Actions\AssignInvitationCodeToUsers;
use App\Nova\Actions\ExportWhiteLabelUsersToCSV;
use App\Nova\Actions\SyncWithFluentCRM;
use App\Nova\Lenses\LatestUsers;
use App\Nova\Lenses\UserWithMostTeams;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Code;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\Gravatar;
use <PERSON>vel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Password;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Http\Requests\NovaRequest;
use function Symfony\Component\Translation\t;

class WhiteLabelUser extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static string $model = \App\Models\User::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'name', 'email',
    ];

    /**
     * Build an "index" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->whereNotNull('white_label_id');
    }

    public static $group = 'White Labels';

    /**
     * Get the fields displayed by the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('White Label', 'whiteLabel', WhiteLabel::class)
                ->nullable()
                ->sortable(),

            Gravatar::make()->maxWidth(50),

            Text::make('Name')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make('Email')
                ->sortable()
                ->rules('required', 'email', 'max:254')
                ->creationRules('unique:users,email')

//                ->updateRules('unique:users,email,{{resourceId}}')
                ->updateRules(function (\Laravel\Nova\Http\Requests\NovaRequest $request) {
                    $userId = $this->resource->id; // Current user ID
                    $whiteLabelId = $this->resource->white_label_id; // Current white label ID from request

                    return [
                        Rule::unique('users', 'email')
                            ->ignore($userId) // Ignore the current user's record
                            ->where(function ($query) use ($whiteLabelId) {
                                if ($whiteLabelId) {
                                    $query->where('white_label_id', $whiteLabelId);
                                } else {
                                    $query->whereNull('white_label_id');
                                }
                            }),
                    ];
                })
            ,

            Text::make('Fluent CRM', function () {

                if ($this->resource->personalTeam()?->fluent_crm_contact_id) {
                    return "<a class='link-default'
                        target='_blank'
                        href=".config('app.fluent_crm_hosted_wp').'/wp-admin/admin.php?page=fluentcrm-admin#/subscribers/'.$this->resource->personalTeam()->fluent_crm_contact_id.">
                        Check Customer #{$this->resource->personalTeam()->fluent_crm_contact_id}
                    </a>";
                }

                return 'N/A';

            })->onlyOnDetail()->asHtml(),

            //show how many teams a user belongs to
            Text::make('Teams', function () {
                return $this->allTeams()->count();
            })->exceptOnForms(),

            HasMany::make('Owned Teams', 'ownedTeams', Team::class)->onlyOnDetail(),
            BelongsToMany::make('Others Teams', 'teams', Team::class)->fields(function(){
                //role and permissions of the user in the team
                return [
                    Select::make(
                        'Role',
                        'role'
                    )->options([
                        \App\Models\Team::TEAM_ADMIN => 'Team Admin',
                        \App\Models\Team::SERVER_ADMIN => 'Server Admin',
                        \App\Models\Team::SITE_ADMIN => 'Site Admin',
                    ])
                        ->rules('required', Rule::in([\App\Models\Team::TEAM_ADMIN, \App\Models\Team::SERVER_ADMIN, \App\Models\Team::SITE_ADMIN]))
                        ->sortable(),
                    Code::make('Permissions', 'permissions')->json(),
                ];
            }),


            //role of the user in the team
            Text::make('Current Team', function () {
                return $this->currentTeam?->name;
            })->onlyOnDetail(),


            HasMany::make('User Team Permissions', 'userTeamPermissions', TeamUser::class),

            Text::make('Team Max Count','meta->team_max_count')
                ->default(function () {
                    return 10;
                })->sortable()
                ->rules('required', 'numeric','min:1'),

            Code::make('Meta', 'meta')->readonly(function (){
                return true; //!auth()->user()->isSuperAdmin();
            })->json()->hideFromIndex(),

            Select::make('Role')
                ->options([
                    \App\Models\User::ADMIN => 'Admin',
                    \App\Models\User::SUPER_ADMIN => 'Super Admin',
                    \App\Models\User::USER => 'User',
                    \App\Models\User::SUPPORT_LEVEL_1 => 'Support Level 1',
                    \App\Models\User::SUPPORT_LEVEL_2 => 'Support Level 2',
                ])
                ->rules('required', Rule::in([\App\Models\User::SUPER_ADMIN, \App\Models\User::ADMIN, \App\Models\User::USER, \App\Models\User::SUPPORT_LEVEL_1, \App\Models\User::SUPPORT_LEVEL_2]))
                ->sortable(),

            DateTime::make('Email Verified At'),

            Boolean::make('Is Active'),

            Boolean::make('2FA', 'two_factor_enabled')->exceptOnForms(),

            HasMany::make('Bills'),

            HasMany::make('Impersonated To', 'impersonators', ImpersonateAction::class)->onlyOnDetail(),
            HasMany::make('Impersonated By', 'impersonated', ImpersonateAction::class)->onlyOnDetail(),

            Password::make('Password')
                ->onlyOnForms()
                ->creationRules('required', 'string', 'min:8')
                ->updateRules('nullable', 'string', 'min:8'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function cards(NovaRequest $request): array
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function filters(NovaRequest $request): array
    {
        return [

        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function lenses(NovaRequest $request): array
    {
        return [
            new UserWithMostTeams,
            new LatestUsers
        ];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function actions(NovaRequest $request): array
    {
        return [
            // no longer needed
            // (new AssignInvitationCodeToUsers())
            //     ->confirmText('Are you sure you want to generate invitations to selected users?')
            //     ->confirmButtonText('Generate Invitation Code')
            //     ->cancelButtonText("Cancel"),

            (new Actions\RemoveTwoFAFromUser())->canSee(fn($request) => $request->user()?->isAdmin() || $request->user()->isSupportLevel2()),
            (new SyncWithFluentCRM()),
            (new ExportWhiteLabelUsersToCSV())
                ->canSee(function ($request) {
                    return $request->user()->isAdmin() || $request->user()->isSupportLevel2();
                })
        ];
    }

    public function authorizedToForceDelete(Request $request)
    {
        return $request->user()?->isSuperAdmin();
    }

    public function authorizedToDelete(Request $request)
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToRestore(Request $request)
    {
        return $request->user()?->isSuperAdmin();
    }

    public static function authorizedToCreate(Request $request)
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToUpdate(Request $request)
    {
        return $request->user()?->isAdmin();
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->hasAnySupportRole();
    }

    public function authorizedToRunAction(NovaRequest $request, Action $action): bool
    {
        return $request->user()?->isSuperAdmin();
    }
}
