<?php

namespace App\Nova;

use <PERSON><PERSON><PERSON>\DependencyContainer\DependencyContainer;
use App\Enums\ProductProvider;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\ProductSources;
use App\Enums\XcloudBilling\XcloudProductStatuses;
use App\Enums\XcloudBilling\XcloudProductType;
use App\Nova\Filters\ProductServiceType;
use App\Nova\Filters\ProductTypeFilter;
use App\Traits\XcSoftDelete;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\HasManyThrough;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Lara<PERSON>\Nova\Http\Requests\NovaRequest;

class WhiteLabelProduct extends Resource
{

    use XcSoftDelete;

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Models\Product::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'title';
    public static $group = 'Payment Gateway';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'title', 'slug', 'sku'
    ];

    /**
     * Build an "index" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->whereNotNull('white_label_id');
    }

    public function title()
    {
        return $this->title . ' ~ ['.$this->currency?->symbol().$this->price. ' - ' . $this->service_type?->toShortIdentifier() . ']';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()
                ->sortable(),

            Text::make('Title')
                ->rules('required')
                ->help('The title will be shown on the checkout page')
                ->sortable(),

            BelongsTo::make('Source', 'source', WhiteLabelProduct::class)
                ->nullable()
                ->help('The source product will be used to identify the product')
                ->sortable(),

            Currency::make('Price')
                ->rules('required', 'numeric', 'min:0')
                ->nullable(),

            BelongsTo::make('White Label', 'whiteLabel', WhiteLabel::class)
                ->nullable()
                ->sortable(),

            Textarea::make('Description')->rules('required')->nullable(),

            Select::make('Type')
                ->rules('nullable')
                ->options(
                    XcloudProductType::asValueLabel(true)
                )->help(
                    'The type will be used to determine the product type when the user purchases this package.
                        Keep empty if you do not want to set the type.'
                )->sortable(),

            Text::make('Slug')
                ->rules('required', 'alpha_dash')
                ->help('The slug will be used to identify the product')
                ->sortable(),

            Text::make('Sku')
                ->rules('required', 'alpha_dash')
                ->help('The sku will be used to identify the product')
                ->sortable(),

            Select::make('Source Model')
                ->rules('required')
                ->options([
                    \App\Models\Server::class => \App\Models\Server::class,
                    \App\Models\EmailProvider::class => \App\Models\EmailProvider::class,
                ])
                ->help('The source model will be used to identify the product')
                ->default(\App\Models\Server::class)->sortable(),

            Select::make('Provider')
                ->rules('required')
                ->options(
                    ProductProvider::asValueLabel(true)
                )->help('The provider will be used to determine the product provider. Most of the cases we are taking from Vultr.
                              If there is no 3rd party provider please choose xCloud as own provider.'
                )->default(ProductProvider::Vultr)->sortable(),

            Text::make('Flag')
                ->rules('nullable')
                ->hideFromIndex()
                ->help('The flag will be used to identify the product')
                ->sortable(),

            Text::make('Product Image URL')
                ->rules('url', 'nullable')
                ->hideFromIndex()
                ->help('The profile photo will be shown on the checkout page')
                ->nullable(),

            Text::make('Checkout URL')
                ->rules('url', 'nullable')
                ->hideFromIndex()
                ->help('The checkout URL will be used to redirect the user to the payment gateway')
                ->readonly()
                ->nullable(),

            Text::make('Checkout With Access Code', 'checkout_url')
                ->resolveUsing(function ($value) {
                    if ($this->resource->coupons) {
                        return $value . '&access_key=' . implode('/', $this->resource->coupons->pluck('code')->toArray());
                    }
                })
                ->rules('url', 'nullable')
                ->hideFromIndex()
                ->help('The checkout URL will be used to redirect the user to the payment gateway')
                ->readonly()
                ->nullable()
                ->copyable(),

            Boolean::make('Is Active')->sortable(),

            Boolean::make('Allow White Label', 'available_for_white_label')->sortable(),

            Boolean::make('Show On Display')->default(true)->sortable(),

            Select::make('Expected Product Source')
                ->hideFromIndex()
                ->rules('nullable')
                ->options(
                    ProductSources::asValueLabel(true)
                )->help(
                    'The expected product source will be used to determine the product source when the user
                          purchases this package. Keep empty if you do not want to set the expected product source.'
                )->sortable(),

            Select::make('Renewal Type')
                ->rules('required')
                ->options(
                    BillRenewalPeriod::asValueLabel()
                )->sortable(),

            Select::make('Service Type')
                ->rules('required')
                ->hideFromIndex()
                ->options(
                    BillingServices::asValueLabel()
                )->sortable(),

            DependencyContainer::make([
                Number::make('Unit')
                    ->default(1)
                    ->canSee(function () {
                        return $this->resource->service_type === BillingServices::EmailProvider->value;
                    })
                    ->rules('nullable', 'integer', 'min:1')
                    ->help('The unit will be used to determine the product unit when the user purchases this product.'),
            ])->dependsOn('service_type', BillingServices::EmailProvider->value),

            Number::make('Max Purchase Limit', 'max_purchase_limit')
                ->rules('integer', 'min:1', 'nullable')
                ->placeholder('e.g. 1, 2, 3, 4, 5, 6, 7, 8, 9, 10')
                ->help('The maximum number of times a team can purchase this product. Keep empty if there is no limit.')
                ->hideFromIndex()
                ->nullable(),

            BelongsTo::make('Requires Billing Plan', 'requiredBillingPlan', BillingPlan::class)
                ->nullable()
                ->readonly(function () {
                    return $this->resource->teams->count() > 0;
                })
                ->help('If this package requires a billing plan, select the billing plan here. Unmodifiable unless there is no team attached to this package.'),

            BelongsToMany::make('Teams', 'teams', Team::class)->fields(function () {
                return [
                    DateTime::make('Attached At', 'attached_at')
                        ->default(now())
                        ->rules('required')
                ];
            })->allowDuplicateRelations(),

            HasMany::make('Bills', 'bills', Bill::class),

            DateTime::make('Created At')->onlyOnDetail(),

            DateTime::make('Updated At')->exceptOnForms(),
            DateTime::make('Deleted At')->hideFromIndex()->exceptOnForms(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            new ProductServiceType()
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public function authorizedToForceDelete(Request $request)
    {
        return false;
    }

    public function authorizedToDelete(Request $request) : bool
    {
        if ($this->resource->teams->count() > 0) {
            return false;
        }

        return $request->user() && $request->user()->isAdmin();
    }

    public function authorizedToRestore(Request $request) : bool
    {
        return $request->user() && $request->user()->isSuperAdmin();
    }

    public static function authorizedToCreate(Request $request) : bool
    {
        return $request->user() && $request->user()->isAdmin();
    }

    public function authorizedToUpdate(Request $request) : bool
    {
        return $request->user() && $request->user()->isAdmin();
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->hasAnySupportRole();
    }

//    public function authorizedToAttach(NovaRequest $request, $model)
//    {
//        return app()->environment('production') ? ($request->user() && $request->user()->isSuperAdmin()) : true;
//    }
//
//    public function authorizedToDetach(NovaRequest $request, $model)
//    {
//        return app()->environment('production') ? ($request->user() && $request->user()->isSuperAdmin()) : true;
//    }
//
//    public function authorizedToAttachAny(NovaRequest $request, $model)
//    {
//        return app()->environment('production') ? ($request->user() && $request->user()->isSuperAdmin()) : true;
//    }
}
