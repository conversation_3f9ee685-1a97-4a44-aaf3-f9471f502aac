<?php

namespace App\Nova\Dashboards;

use App\Nova\Metrics\ActivePlaygroundSites;
use App\Nova\Metrics\ArchivedServersCount;
use App\Nova\Metrics\BillCountPerBillingPlan;
use App\Nova\Metrics\BillCountPerPackage;
use App\Nova\Metrics\BillCountPerProduct;
use App\Nova\Metrics\BillsHasOffer;
use App\Nova\Metrics\BillsPaidUsingPlan;
use App\Nova\Metrics\BillsUnpaidByUser;
use App\Nova\Metrics\FailedInvoicesBalance;
use App\Nova\Metrics\LifeTimeUsage;
use App\Nova\Metrics\ManualSiteMigrationPerDay;
use App\Nova\Metrics\ManualSiteMigrations;
use App\Nova\Metrics\Overview;
use App\Nova\Metrics\PatchstackAddonPerSite;
use App\Nova\Metrics\PatchstackPerDay;
use App\Nova\Metrics\ServerCountPerProvider;
use App\Nova\Metrics\ServerPerProvider;
use App\Nova\Metrics\Servers;
use App\Nova\Metrics\ServersPerDay;
use App\Nova\Metrics\SiteCountByEnvironment;
use App\Nova\Metrics\SiteCountBySiteType;
use App\Nova\Metrics\SiteMigrations;
use App\Nova\Metrics\SiteMigrationsPerDay;
use App\Nova\Metrics\Sites;
use App\Nova\Metrics\SitesPerDay;
use App\Nova\Metrics\StackPerServer;
use App\Nova\Metrics\StackPerSite;
use App\Nova\Metrics\StatusPerServer;
use App\Nova\Metrics\StatusPerSite;
use App\Nova\Metrics\SystemOverview;
use App\Nova\Metrics\TeamsCount;
use App\Nova\Metrics\TypePerSite;
use App\Nova\Metrics\UsingSystemWithoutPayingTeam;
use App\Nova\Metrics\TotalPackageBills;
use App\Nova\Metrics\TotalPackageSales;
use App\Nova\Metrics\TotalProductBills;
use App\Nova\Metrics\Users;
use App\Nova\Metrics\UsersPerDay;
use Laravel\Nova\Cards\Help;
use Laravel\Nova\Dashboards\Main as Dashboard;

class Statistics extends Dashboard
{
    /**
     * Get the cards for the dashboard.
     *
     * @return array
     */
    public function cards() : array
    {
        return [

            (new Overview())->width('1/2'),
            (new SystemOverview())->width('1/2'),
            // (new Servers)->width('1/4'),
            // (new ArchivedServersCount)->width('1/4'),
            // (new Sites)->width('1/4'),
            // // (new ActivePlaygroundSites())->width('1/4'),
            //
            // (new Users)->width('1/4'),
            // (new TeamsCount())->width('1/4'),
            // (new SiteMigrations)->width('1/4'),

            (new UsersPerDay)->width('1/4'),
            (new SiteMigrationsPerDay)->width('1/4'),

            // new ManualSiteMigrations,
            (new ServersPerDay)->width('1/4'),
            (new SitesPerDay)->width('1/4'),
            (new PatchstackPerDay)->width('1/4'),

            (new ServerPerProvider)->width('1/4'),
            (new ServerCountPerProvider)->width('1/4'),
            (new StackPerServer)->width('1/4'),
            (new StatusPerServer)->width('1/4'),

            (new SiteCountByEnvironment)->width('1/4'),
            (new SiteCountBySiteType)->width('1/4'),
            (new StackPerSite)->width('1/4'),
            (new StatusPerSite)->width('1/3'),

            (new BillCountPerBillingPlan)->width('1/3'),
            (new BillCountPerProduct)->width('1/3'),
            (new BillCountPerPackage)->width('1/3'),

            (new PatchstackAddonPerSite)->width('1/3'),

            // new ManualSiteMigrationPerDay,
            (new UsingSystemWithoutPayingTeam)->canSee(fn() => auth()->user()->isSuperAdmin())->width('1/3'),
            (new BillsPaidUsingPlan)->canSee(fn() => auth()->user()->isSuperAdmin())->width('1/4'),
            (new TotalPackageSales)->canSee(fn() => auth()->user()->isSuperAdmin())->width('1/4'),
            (new TotalProductBills)->canSee(fn() => auth()->user()->isSuperAdmin())->width('1/4'),
            (new BillsUnpaidByUser)->canSee(fn() => auth()->user()->isSuperAdmin())->width('1/4'),
            (new BillsHasOffer)->canSee(fn() => auth()->user()->isSuperAdmin())->width('1/4'),
            (new FailedInvoicesBalance)->canSee(fn() => auth()->user()->isSuperAdmin())->width('1/4'),
            (new TotalPackageBills)->canSee(fn() => auth()->user()->isSuperAdmin())->width('1/4'),

            (new LifeTimeUsage)->canSee(fn() => auth()->user()->isSuperAdmin())->width('1/4'),
        ];
    }
}
