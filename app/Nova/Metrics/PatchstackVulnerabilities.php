<?php

namespace App\Nova\Metrics;

use App\Models\PatchstackVulnerability;
use DateTimeInterface;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;
use <PERSON><PERSON>\Nova\Metrics\Value;
use <PERSON><PERSON>\Nova\Metrics\ValueResult;

class PatchstackVulnerabilities extends Value
{
    /**
     * The displayable name of the metric.
     *
     * @var string
     */
    public $name = 'Total Patchstack Vulnerabilities';

    /**
     * Calculate the value of the metric.
     *
     * @param NovaRequest $request
     * @return ValueResult
     */
    public function calculate(NovaRequest $request): ValueResult
    {
        return $this->count($request, PatchstackVulnerability::class);
    }

    /**
     * Get the ranges available for the metric.
     *
     * @return array
     */
    public function ranges(): array
    {
        return [
            'ALL' => __('ALL'),
            30 => __('30 Days'),
            60 => __('60 Days'),
            90 => __('90 Days')
        ];
    }

    /**
     * Determine for how many minutes the metric should be cached.
     *
     * @return  DateTimeInterface
     */
    public function cacheFor(): DateTimeInterface
    {
         return now()->addMinutes(5);
    }

    /**
     * Get the URI key for the metric.
     *
     * @return string
     */
    public function uriKey(): string
    {
        return 'patchstack-vulnerabilities';
    }
}
