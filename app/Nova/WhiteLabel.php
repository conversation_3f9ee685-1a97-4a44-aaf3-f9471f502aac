<?php

namespace App\Nova;

use App\Enums\DeploymentLogsStatusEnum;
use App\Enums\WhiteLabel\WhiteLabelStatus;
use App\Nova\Filters\ActiveWhiteLabelFilter;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Code;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\HasManyThrough;
use Laravel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class WhiteLabel extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\WhiteLabel>
     */
    public static $model = \App\Models\WhiteLabel::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    public function title()
    {
        return $this->domain ?: $this->sub_domain;
    }

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'domain', 'sub_domain', 'owner.name', 'connectedAccount.account_email'
    ];

    /**
     * Build an "index" query for the given resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function indexQuery(NovaRequest $request, $query)
    {
        return $query->withCount('servers');
    }

    public static $group = 'White Labels';

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make(__('Owner Team'), 'owner', Team::class)->searchable()->readonly(),
            BelongsTo::make(__('Connected Account'), 'connectedAccount', ConnectedAccount::class)
                ->searchable()
                ->readonly()
                ->displayUsing(function ($connectedAccount) {
                    return $connectedAccount->account_email;
                }),
            Select::make(__('Status'), 'status')->options(
                WhiteLabelStatus::asValueLabel()
            )->displayUsingLabels(),

            // server count
            Text::make(__('Servers'), function () {
                return $this->servers_count;
            })->onlyOnIndex()->textAlign('center'),

            Text::make(__('Domain'), 'domain')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            }),
            Text::make(__('Sub Domain'), 'url')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            }),
            Text::make(__('SMTP Password'), 'smtp_password')->hideFromIndex()->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            }),
            Code::make('Branding', 'branding')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->json()->hideFromIndex(),
            Code::make('Billing Details', 'billing_details')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->json()->hideFromIndex(),
            Code::make('Payment Info', 'payment_info')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->json()->hideFromIndex(),
            Code::make('Settings', 'settings')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->json()->hideFromIndex(),
            Code::make('Meta', 'meta')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->json()->hideFromIndex(),
            Code::make('Settings', 'settings')->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            })->json()->hideFromIndex(),
            Textarea::make('Terms of Service', 'tos')->nullable()->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            }),
            Textarea::make('Privacy Policy', 'privacy_policy')->nullable()->readonly(function () {
                return !auth()->user()->isSuperAdmin();
            }),
            HasMany::make(__('Users'), 'users', WhiteLabelUser::class),
            HasMany::make(__('Teams'), 'teams', Team::class),
            HasMany::make(__('Bills'), 'bills', Bill::class),
            HasMany::make(__('Invoices'), 'invoices', GeneralInvoice::class),
            HasMany::make(__('Products'), 'products', WhiteLabelProduct::class),
            HasManyThrough::make(__('Servers'), 'servers', Server::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            new ActiveWhiteLabelFilter(),
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin();
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToDelete(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin() || (bool) $request->user()?->hasAnySupportRole();
    }
}
