<?php

namespace App\Nova;

use App\Enums\XcloudBilling\PaymentGateway;
use App\Enums\XcloudBilling\PaymentMethodStatus;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Code;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use function Clue\StreamFilter\fun;

class PaymentMethod extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\PaymentMethod>
     */
    public static $model = \App\Models\PaymentMethod::class;

    public static $group = 'Payment Gateway';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'card_no';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'team.name', 'team.email', 'user.name', 'user.email', 'card_no'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Select::make(__('Payment Gateway'), 'payment_gateway')->options([
                PaymentGateway::Stripe->value => 'Stripe',
                PaymentGateway::PayPal->value => 'PayPal',
            ])->readonly(),
            Select::make(__('Status'), 'status')->options([
                PaymentMethodStatus::ACTIVE->value => 'Active',
                PaymentMethodStatus::INACTIVE->value => 'Inactive',
            ])->readonly(),
            Text::make(__('Session ID'), 'session_id')->hideFromIndex()->readonly(),
            Text::make(__('Customer ID'), 'customer_id')->readonly(),
            Text::make(__('Card No'), 'card_no')->readonly(),
            BelongsTo::make(__('Team'), 'team')->readonly(),
            BelongsTo::make(__('User'), 'user')->readonly(),
            Boolean::make(__('Default Card'), 'default_card'),
            Code::make(__('Meta'), 'meta')->json()->readonly(),
            Code::make(__('Logs'), 'logs')->readonly(),
            DateTime::make(__('Created At'), 'created_at')->readonly(),
            DateTime::make(__('Updated At'), 'updated_at')->readonly(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [

        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public function authorizedToUpdate(Request $request)
    {
        return $request->user() && $request->user()->isAdmin();
    }

    public function authorizedToDelete(Request $request)
    {
        return $request->user() && $request->user()->isAdmin();
    }

    public function authorizedToForceDelete(Request $request)
    {
        return $request->user() && $request->user()->isSuperAdmin();
    }

    public function authorizedToRestore(Request $request)
    {
        return $request->user() && $request->user()->isSuperAdmin();
    }

    public static function authorizedToCreate(Request $request)
    {
        return $request->user()?->isAdmin();
    }

    public static function authorizedToViewAny(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }
}
