<?php

namespace App\Nova;

use App\Enums\CustomNginxEnum;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\Code;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class CustomNginx extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\CustomNginx>
     */
    public static $model = \App\Models\CustomNginx::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'template',
        'file',
        'type',
        'site.name'
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            BelongsTo::make(__('Site'), 'site', Site::class)->searchable(),
            Select::make(__('Template'), 'template')->options(
                [
                    'use_my_own_template' =>  CustomNginxEnum::USE_MY_OWN_CONFIG->value,
                    CustomNginxEnum::HIDE_MY_WP->value => CustomNginxEnum::HIDE_MY_WP->value,
                    CustomNginxEnum::RANKMATH_REWRITE_RULES->value => CustomNginxEnum::RANKMATH_REWRITE_RULES->value
                ]
            )->displayUsingLabels(),
            Text::make(__('File Name'), 'file'),
            Select::make(__('Nginx Config Type'), 'type')->options(
                [
                    CustomNginxEnum::BEFORE_SERVER_BLOCK->value =>  CustomNginxEnum::BEFORE_SERVER_BLOCK->value,
                    CustomNginxEnum::INSIDE_SERVER_BLOCK->value => CustomNginxEnum::INSIDE_SERVER_BLOCK->value,
                    CustomNginxEnum::AFTER_SERVER_BLOCK->value => CustomNginxEnum::AFTER_SERVER_BLOCK->value
                ]
            )->displayUsingLabels(),
            Code::make('Content')->json()->hideFromIndex(),
            Code::make('Response')->json()->hideFromIndex(),

            DateTime::make('Updated At')->readonly(function () {
                return $this->resource->exists;
            })->sortable(),

            DateTime::make('Created At')->readonly(function () {
                return $this->resource->exists;
            })->sortable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return (bool) $request->user()?->isAdmin();
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToDelete(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }

    public function authorizedToReplicate(Request $request): bool
    {
        return $request->user()?->isAdmin();
    }
}
