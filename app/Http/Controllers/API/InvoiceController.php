<?php

namespace App\Http\Controllers\API;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Http\Controllers\Controller;
use App\Models\Bill;
use App\Models\GeneralInvoice;
use App\Models\PaymentMethod;
use App\Models\SubscriptionProduct;
use App\Models\WhiteLabel;
use App\Repository\StripePaymentRepository;
use App\Services\PaymentGateway\API\InvoicePaymentService;
use App\Services\PaymentGateway\InvoiceServices\InvoiceGenerator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\StripeClient;
use Stripe\Subscription;

class InvoiceController extends Controller
{
    public function getInvoice(Request $request, $invoiceId)
    {
        $invoice = GeneralInvoice::findOrFail($invoiceId);

        $this->authorize('view', $invoice);

        return response()->json($invoice->toArray());
    }

    /**
     * @throws ApiErrorException
     */
    public function payInvoice(Request $request, GeneralInvoice $invoice) : JsonResponse
    {
        // Authorize the user to pay the invoice
        $this->authorize('can-pay', $invoice);

        // Authorize payment method usage
        $paymentMethod = $request->has('paymentMethodId') ? PaymentMethod::find($request->get('paymentMethodId')) : team()->activePaymentMethod()->first();

        // If any payment method is found, authorize its usage
        if ($paymentMethod) {
            $this->authorize('can-use', $paymentMethod);
        } else {
            return response()->json([
                'message' => 'No payment method found.'
            ], 400);
        }

        // Use the shared service to process payment
        $result = InvoicePaymentService::forInvoice($invoice)
                    ->withPaymentMethod($paymentMethod)
                    ->redirectOnPaymentSuccess($request->get('success_redirect'))
                    ->redirectOnPaymentFail($request->get('failure_redirect'))
                    ->process();

        return response()->json($result);
    }

    /**
     * @throws ApiErrorException
     */
    public function payInvoiceOld(Request $request)
    {
        $this->authorize('createBilling', team());

        Stripe::setApiKey(config('services.stripe.secret_key'));
        $stripe = new StripeClient(config('services.stripe.secret_key'));

        $invoice = GeneralInvoice::findOrFail(Arr::get($request->get('invoice'), 'id'));

        if ($invoice->status->isFailed() && $invoice?->gateway_invoice_or_intent_id) {
            $invoice->fetchPaymentWebhookFromStripe();
        }

        $paymentMethodId = $request->get('paymentMethodId');

        if(empty($paymentMethodId)){
            return back()->with('flash', [
                'error' => 'Please select a payment method!'
            ]);
        }

        if (team()->activePaymentMethod()->isEmpty()) {
            return back()->with('flash', [
                'error' => 'No active payment method found!'
            ]);
        }

        if ($invoice->status === BillingStatus::Paid) {
            return back()->with('flash', [
                'success' => 'Invoice already paid!'
            ]);
        }

        $paymentMethod = PaymentMethod::findOrFail($paymentMethodId);

        $unpaidBills = $invoice->unpaidBills()->get();

        ## need to take payment with user's specific payment method
        // switch card with specified payment method
        $invoice->switchToPaymentMethod($paymentMethod);

        if($invoice->team->whiteLabel) {
            if ($invoice->gateway_invoice_or_intent_id) {
                $paymentIntent = $stripe->paymentIntents->retrieve($invoice->gateway_invoice_or_intent_id, [], [
                    'stripe_account' => $invoice->team->whiteLabel->connectedAccount->stripe_account_id
                ]);
            } else {
                $paymentIntent = (new StripePaymentRepository())->createPaymentIntent($invoice);

                $invoice->update([
                    'gateway_invoice_or_intent_id' => $paymentIntent->id,
                    'gateway_customer_id' => $paymentIntent->customer,
                    'gateway_payment_method_id' => $paymentIntent->payment_method
                ]);

            }
        }elseif($invoice->gateway_invoice_or_intent_id){
            $paymentIntent = $stripe->paymentIntents->retrieve($invoice->gateway_invoice_or_intent_id);
        } else {
            $paymentIntent = (new StripePaymentRepository())->createPaymentIntent($invoice);
        }

        // if invoice->customer_id and paymentMethod->customer_id is not same then need to cancel the payment intent and create a new one
        // since stripe does not allow to change customer to existing payment intent (This only requires for v1 since from v2, we use single customer for all payment methods)
        if($invoice->gateway_customer_id !== $paymentMethod->customer_id){
            if($invoice->gateway_invoice_or_intent_id){
                try {
                    if($paymentIntent->status !== PaymentIntent::STATUS_CANCELED){
                        (new StripePaymentRepository())->cancelPaymentIntent($invoice);
                    }

                }catch (\Exception $e) {
                    Log::warning('Payment intent cancellation failed', [
                        'invoice_id' => $invoice->id,
                        'invoice_reference_no' => $invoice->reference_no,
                        'xcloud_team_id' => $invoice->team_id,
                        'xcloud_payment_method_id' => $invoice->payment_method_id,
                        'error' => $e->getMessage()
                    ]);

                    return back()->with('flash', [
                        'error' => 'Failed to process payment. Please try with another card.'
                    ]);
                }

                // update invoice customer with payment method's customer
                $invoice->update([
                    'gateway_customer_id' => $paymentMethod->customer_id
                ]);
                // create a new payment intent(v1)
                try {
                    $paymentIntent = (new StripePaymentRepository())->createPaymentIntent($invoice);
                }catch (\Exception $e) {
                    Log::warning('Payment intent creation failed', [
                        'invoice_id' => $invoice->id,
                        'invoice_reference_no' => $invoice->reference_no,
                        'xcloud_team_id' => $invoice->team_id,
                        'xcloud_payment_method_id' => $invoice->payment_method_id,
                        'error' => $e->getMessage()
                    ]);
                    return back()->with('flash', [
                        'error' => 'Failed to process payment. Please try with another card.'
                    ]);
                }

                // update invoice with new payment intent with newly switched card
                $invoice->update([
                    'gateway_invoice_or_intent_id' => $paymentIntent->id
                ]);
            }
        }else{
            if ($paymentMethod->security_protocol) {
                // v2 payment method(update existing payment method instead of creating new one)
                $stripeAccount = $invoice->team->whiteLabel ? $invoice->team->whiteLabel->connectedAccount->stripe_account_id : null;

                if ($paymentIntent->payment_method !== Arr::get($paymentMethod->meta, 'stripe.payment_method')) {
                    $updateParams = [
                        'payment_method' => Arr::get($paymentMethod->meta, 'stripe.payment_method'),
                    ];

                    $paymentIntent = PaymentIntent::update($paymentIntent->id, $updateParams, $stripeAccount ? [
                        'stripe_account' => $stripeAccount
                    ] : []);
                }
            }
        }

        // take charge with selected payment method
        try {
            $paymentIntent = $paymentIntent->confirm();
        }catch (\Exception $e){
            Log::warning('Payment intent creation failed', [
                'invoice_id' => $invoice->id,
                'invoice_reference_no' => $invoice->reference_no,
                'xcloud_team_id' => $invoice->team_id,
                'xcloud_payment_method_id' => $invoice->payment_method_id,
                'error' => $e->getMessage()
            ]);
        }


        if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
            $invoice->team->onPaymentSuccess($invoice);

            $invoice->setStatusPaid();

            $unpaidBills->each(function (Bill $bill) {
                $bill->setStatusPaid();
            });

            if (!$invoice->team->billingIsActive() && !$invoice->team->hasDuesToPay()) {
                $invoice->team->setBillingStatusActive();
            }

            // send email
            $invoice->team->sendInvoiceEmail($invoice);

            if($invoice->isWhiteLabelPurchase()){
                $whiteLabelID = Arr::get($invoice->meta, 'whitelabel_id');
                if($whiteLabelID){
                    $whiteLabel = WhiteLabel::findOrFail($whiteLabelID);
                    $whiteLabel->update([
                        'payment_info' => [
                            'invoice_id' => $invoice->id,
                            'payment_intent_id' => $paymentIntent->id,
                            'payment_status' => PaymentIntent::STATUS_SUCCEEDED,
                        ],
                        'billing_activated_from' => now()
                    ]);

                    $subscriptionProductID = Arr::get($invoice->meta, 'subscription_product_id');
                    $stripeSubscriptionID = Arr::get($invoice->meta, 'stripe_subscription_id');

                    if($subscriptionProductID && $stripeSubscriptionID){
                        $subscriptionProduct = SubscriptionProduct::findOrFail($subscriptionProductID);
                        if($invoice->team->whiteLabel){
                            $stripeSubscription = Subscription::retrieve($stripeSubscriptionID, [
                                'stripe_account' => $invoice->team->whiteLabel->connectedAccount->stripe_account_id
                            ]);
                        }else{
                            $stripeSubscription = Subscription::retrieve($stripeSubscriptionID);
                        }

                        $subscriptionProduct->teams()->attach($invoice->team, [
                            'team_id' => $invoice->team->id,
                            'subscription_product_id' => $subscriptionProduct->id,
                            'stripe_subscription_id' => $stripeSubscription->id,
                        ]);
                    }

                    return redirect()->route('white-label.onboarding.brand-setup')->with('flash', [
                        'success' => 'Your payment processed successfully!'
                    ]);
                }
            }

            return back()->with('flash', [
                'success' => 'Your payment processed successfully!'
            ]);
        }else {
            if ($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION) {
                $paymentMethod->saveMeta('stripe_3d_secure_authentication', [
                    'requires_3d_secure_authentication' => true
                ]);

                $invoice->setStatusRequiresAction();

                if($invoice->isWhiteLabelPurchase()){
                    $whiteLabelID = Arr::get($invoice->meta, 'whitelabel_id');
                    if($whiteLabelID){
                        $subscriptionProductID = Arr::get($invoice->meta, 'subscription_product_id');
                        $stripeSubscriptionID = Arr::get($invoice->meta, 'stripe_subscription_id');

                        if($subscriptionProductID && $stripeSubscriptionID){
                            $subscriptionProduct = SubscriptionProduct::findOrFail($subscriptionProductID);
                            if($invoice->team->whiteLabel){
                                $stripeSubscription = Subscription::retrieve($stripeSubscriptionID, [
                                    'stripe_account' => $invoice->team->whiteLabel->connectedAccount->stripe_account_id
                                ]);
                            }else{
                                $stripeSubscription = Subscription::retrieve($stripeSubscriptionID);
                            }

                            return redirect()->route('stripe.subscription-product.checkout.requires3dsecure', [
                                'affiliateId' => null,
                                'paymentIntentId' => $paymentIntent->id,
                                'cartId' => null,
                                'invoiceId' => $invoice->id,
                                'nextRoute' => 'white-label.onboarding.brand-setup',
                                'routeParam' => [
                                    'model' => WhiteLabel::class,
                                    'id' => $whiteLabelID
                                ],
                                'subscriptionProductId' => $subscriptionProduct->id,
                                'stripeSubscriptionId' => $stripeSubscription->id,
                            ]);
                        }
                    }
                }

                return redirect()->route('stripe.checkout.requires3dsecure', [
                    'paymentIntentId' => $paymentIntent->id,
                    'sessionId' => $paymentMethod->session_id,
                    'affiliateId' => team()->getMeta('affiliate.affiliate_code'),
                    'invoiceId' => $invoice->id,
                ]);
            } else {
                $invoice->setStatusPaymentFailed();
                $invoice->saveLog('payment_failed_' . now()->format('Y-m-d h:m:s'), 'Payment failed');

                $invoiceBills = $invoice->bills()->get();

                foreach ($invoiceBills as $bill) {
                    $bill->setStatusUnpaid();
                }

                return back()->with('flash', [
                    'error' => 'Failed to process payment'
                ]);
            }
        }
    }

    public function cancelInvoice(GeneralInvoice $invoice)
    {
        $this->authorize('createBilling', team());

        if ($invoice->status === BillingStatus::Paid) {
            return back()->with('flash', [
                'warning' => 'You can not cancel a paid invoice!'
            ]);
        }

        $errorMessage = '';
        $successMessage = 'Invoice cancelled successfully!';

        try {
            $invoice->markAsCancelled();
        }catch (\Exception $e){
            $errorMessage = $e->getMessage();
        }

        if($errorMessage){
            return [
                'status' =>  'error',
                'message' => $errorMessage
            ];
        } else {
            return [
                'status' => 'success',
                'message' => $successMessage
            ];
        }
    }

    public function generateInvoiceFromBills(Request $request)
    {
        $this->authorize('createBilling', team());

        $request->validate([
            'bills' => 'required',
            'confirmed_to_convert_free_bills' => 'boolean'
        ]);

        if (is_string($request->get('bills')) && str_contains($request->get('bills'), ',')) {
            $request->merge([
                'bills' => explode(',', $request->get('bills')),
            ]);
        } else {
            $request->merge([
                'bills' => [$request->get('bills')]
            ]);
        }

        if ($request->confirmed_to_convert_free_bills) {
            $bills = team()->bills()->where('status', BillingStatus::Unpaid)
                ->where('has_offer', true)
                ->whereNull('invoice_id')
                ->whereIn('id', $request->bills)->get();

            if ($bills) {
                $bills->each(function ($bill) {
                    $bill->adjustPreviousOffer();
                });
            }
        } else {
            $bills = team()->bills()->where('status', BillingStatus::Unpaid)
                ->where('has_offer', false)
                ->whereNull('invoice_id')
                ->whereIn('id', $request->bills)->get();
        }

        if($bills->isEmpty()){
            return response()->json([
                'message' => 'No bills found to generate invoice.'
            ], 201);
        }

        if ($bills->sum('amount_to_pay') < 0.50) {
            return response()->json([
                'message' => 'Total amount to pay is less than $0.50. So, no invoice generated.'
            ], 201);
        }

        return response()->json(InvoiceGenerator::bills($bills)
            ->team(team())
            ->source(InvoiceSourceEnum::SinglePurchase)
            ->generate()->toArray());
    }
}
