<?php

namespace App\Http\Controllers\API;

use App\Enums\SiteStatus;
use App\Http\Controllers\Controller;
use App\Models\Server;
use App\Models\Site;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    /**
     * Display a listing of server and site by search
     *
     * @return JsonResponse
     */
    public function search(Request $request)
    {
        $search = $request->string('query');
        $servers = team()->servers()
            ->with('cloudProvider')
            ->accessFilter()
            ->select(['id', 'name', 'is_connected', 'status', 'cloud_provider_id'])
            ->search($search)
            ->orderBy('id', 'desc')
            ->take(10)
            ->get()->map(function ($server) {
                return [
                    'id' => $server->id,
                    'name' => $server->name,
                    'is_connected' => $server->is_connected,
                    'status' => $server->status->value,
                    'provider' => !blank($server->cloud_provider_id) ? $server->cloud_provider_name->value : 'other'
                ];
            });
        $sites = team()->sites()->accessFilter()->search($search)->with(['server:id,is_connected,name'])->orderBy('id', 'desc')->take(10)->get();

        return response()->json([
            'servers' => $servers,
            'sites' => $sites
        ]);
    }

    /**
     * Display a listing of server and site by search
     *
     * @return JsonResponse
     */
    public function searchServerSiteBackup(Request $request)
    {
        $search = $request->string('query');
        $servers = team()->servers()->accessFilter()->select(['id','name'])
            ->where([
                'is_connected' => true,
                'is_provisioned' => true
            ])
            ->whereHas('sites', fn ($q)=> $q->whereHas('backupFiles'))
            ->when($search, fn ($q, $search) => $q->search($search))
            ->orderBy('id', 'desc')
            ->take(10)->get()->toArray();

        return response()->json($servers);
    }

    public function searchSitesBackup(Request $request)
    {
        $server = $request->string('server');
        $search = $request->string('query');
        $sites = team()->sites()->accessFilter()->select(['sites.id','sites.name'])
            ->where(['server_id' => $server, 'sites.status' => SiteStatus::PROVISIONED])
            ->when($search, fn ($q, $search) => $q->search($search))
            ->orderBy('id', 'desc')
            ->take(10)->get()->toArray();

        return response()->json($sites);
    }
    public function searchBucket(Request $request)
    {
        $search = $request->string('query');
        $sites = team()->storageProviders()->select(['id','bucket'])
            ->when($search, fn ($q, $search) => $q->search($search))
            ->orderBy('id', 'desc')
            ->take(10)->get()->toArray();

        return response()->json($sites);
    }
}
