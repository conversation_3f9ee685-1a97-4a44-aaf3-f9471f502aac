<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\PcloudStorageProviderRequest;
use App\Models\StorageProvider;
use App\Models\WhiteLabel;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PcloudController extends Controller
{
    public function pcloudAuthorize(PcloudStorageProviderRequest $request)
    {
        $this->authorize('manageStorageProvider', team());

        if(team()->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }

        $clientId = config('services.pcloud.client_id');
        $redirectUri = config('services.pcloud.redirect_uri');
        $stateData = [
            'team_id' => team()->id,
            'label' => $request->get('label'),
            'user_id' => user()->id
        ];

        if (currentWhiteLabel()){
            $stateData['white_label_id'] = currentWhiteLabel()->id;
        }

        $state = urlencode(json_encode($stateData));
        $authUrl = "https://my.pcloud.com/oauth2/authorize?response_type=code&client_id=$clientId&redirect_uri=$redirectUri&state=$state";

        return response()->json($authUrl);
    }

    /**
     * @throws \Exception
     */
    public function pcloudCallback(Request $request)
    {
        // Ensure the 'code' query parameter exists
        if (!$code = $request->query('code')) {
            return redirect()->route('user.storage-provider')->with('flash', [
                'message' => 'Authorization Failed.',
                'type' => 'error',
            ]);
        }

        // Decode and validate the state parameter
        $stateData = json_decode($request->query('state', '{}'));

        $label = $stateData->label ?? null;
        $teamId = $stateData->team_id ?? null;
        $userId = $stateData->user_id ?? null;
        $locationId = $request->query('locationid');
        $hostname = $request->query('hostname');
        $whiteLabelId = $stateData->white_label_id ?? null;

        // Redirect to the proper white-label domain if needed
        if ($whiteLabelId) {
            $whiteLabelDomain = WhiteLabel::query()->where('id', $whiteLabelId)->value('domain');

            if ($whiteLabelDomain && $request->getHost() !== $whiteLabelDomain) {
                $redirectUrl = Str::replace(
                    search: $request->getHost(),
                    replace: $whiteLabelDomain,
                    subject: $request->fullUrl()
                );
                return redirect()->away($redirectUrl);
            }
        }

        // Exchange code for access/refresh tokens
        $response = StorageProvider::pcloudTokenResponse(code: $code);

        if (!$response->successful()) {
            return redirect()->route('user.storage-provider')->with('flash', [
                'message' => 'pCloud Linked Failed.',
                'type' => 'error'
            ]);
        }

        $tokens = $response->json();

        // Validate pCloud response
        if ($tokens['result'] !== 0) {
            return redirect()->route('user.storage-provider')->with('flash', [
                'message' => 'pCloud authorization failed: ' . ($tokens['error'] ?? 'Unknown error'),
                'type' => 'error'
            ]);
        }

        // Create storage provider with temporary endpoint
        $storageProvider = StorageProvider::create([
            'provider'       => StorageProvider::PCLOUD,
            'access_key_id'  => $tokens['access_token'],
            'secret_key'     => $hostname ?? 'api.pcloud.com', // Store hostname in secret_key for API calls
            'bucket'         => $label,
            'team_id'        => $teamId,
            'user_id'        => $userId,
            'region'         => $locationId,
            'endpoint'       => '0', // Will be updated with folder ID
        ]);

        // Create the folder in pCloud and update endpoint with folder ID
        try {
            $folderId = $storageProvider->getPcloudFolderId($label);
            $storageProvider->update(['endpoint' => $folderId]);
        } catch (Exception $e) {
            // If folder creation fails, we'll still save the provider but log the error
            Log::error('Failed to create pCloud folder: ' . $e->getMessage());
        }

        return redirect()->route('user.storage-provider')->with('flash', [
            'message' => 'pCloud Linked Successfully.',
        ]);
    }

    /**
     * @throws \Exception
     * @throws \Throwable
     */

    public function update(PcloudStorageProviderRequest $request, StorageProvider $provider)
    {
        $this->authorize('manageStorageProvider', team());

        if(team()->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }

        DB::beginTransaction();
        try{
            $provider->update([
                'bucket' => $request->get('label')
            ]);

            $provider->renamePcloudDirectory($provider->endpoint, $request->get('label'));

            DB::commit();
            return redirect()->back()->with('flash', ['message' => 'Storage Provider Updated Successfully.']);
        }catch (Exception $e){
            DB::rollBack();
            return redirect()->back()->with('flash', ['message' => 'Failed to update storage provider.', 'type' => 'error']);
        }
    }
}
