<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\SiteSSLHttpsRequest;
use App\Jobs\Site\EnableSsl;
use App\Models\Server;
use App\Models\Site;
use App\Models\SslCertificate;
use App\Services\Integrations\CloudflareService;
use Illuminate\Support\Arr;

class SslCertificateController extends Controller
{
    public function update(Server $server, Site $site, SiteSSLHttpsRequest $request)
    {
        $this->authorize('ssl', $site);

        $site->update(['ssl_provider' => $request->get('ssl_provider')]);

        if ($request->get('ssl_provider') === SslCertificate::PROVIDER_CLOUDFLARE && $request->get('domain_active_on_cloudflare')){
            $site->update([
                'environment' => Site::PRODUCTION,
                'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                'meta->cloudflare_integration->site_name' => $request->get('site_name'),
            ]);
        }

        // create or update ssl certificate
        $site->sslCertificates()->updateOrCreate(
            [
                'site_id' => $site->id,
                'provider' => $request->input('ssl_provider'),
            ],
            [
                'status' => 'new',
                'ssl_certificate' => $request->input('ssl_certificate'),
                'ssl_private_key' => $request->input('ssl_private_key'),
            ]
        );

        EnableSsl::dispatch($site, $reloadNginx = true);

        return redirect()
            ->route('site.ssl', [$server, $site])
            ->with('flash', [
                'type' => 'success',
                'message' => 'Updating SSL certificate. It will be reflected in a while.'
            ]);
    }

    function destroy(Server $server, Site $site)
    {
        $this->authorize('ssl', $site);

        $site->update(['ssl_provider' => null]);

        $site->regenerateNginxConf();

        return redirect()
            ->back()
            ->with('flash', ['message' => 'SSL Certificate removed successfully.', 'type' => 'success']);
    }
}
