<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\Server\XCloudServerReboot;
use App\Models\Server;
use App\Models\Task;
use App\Scripts\Reboot;
use App\Services\CloudServices\Actions\Providers\VultrActions;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class RebootController extends Controller
{
    /**
     * @param Server $server
     * @return JsonResponse|RedirectResponse
     */
    public function __invoke(Server $server): JsonResponse|RedirectResponse
    {
        $this->authorize('re-start-server', $server);
        if ($server->isConnected()){
            $server->update([
                'server_info' => Arr::except($server->server_info, ['upgradable_packages','reboot_require']),
                'reboot_required' => false,
            ]);
            $task = $server->runInline(new Reboot);
            // $task->exit_code == 255 added to handle rebooting of servers that are not connected
            //sometime rebooting server and get exit code 255 with text ssh: connect to host *************** port 22: Connection refused
            $message = $task->successful() || $task->exit_code == 255 ? 'Server Rebooted successfully' : 'Failed to reboot server';
            $type = $task->successful() || $task->exit_code == 255 ? 'success' : 'error';
        }else{
            $message = 'Server is not connected';
            $type = 'error';
        }
        if (request()->expectsJson()) {
            return response()->json([
                'message' => $message,
                'type' => $type
            ]);
        }

        return back()->with('flash', ['message' => $message, 'type' => $type]);
    }

    /**
     * @throws \Exception
     */
    public function hardRebootServerFromVultr(Request $request, Server $server): JsonResponse|RedirectResponse
    {
        $this->authorize('service', $server);

        // create a new task
        $task = Task::create([
            'status' => 'pending',
            'name' =>  "Hard rebooting server {$server->ipAddress()}",
            'server_id' => $server->id,
            'team_id' => $server->team_id,
        ]);

         XCloudServerReboot::dispatch($task, $server);

//        (new VultrActions($server, $task))->serverReboot();

        return back()->with('flash', [
            'message' => 'Please wait your server is rebooting',
            'type' => 'success'
        ]);
    }
}
