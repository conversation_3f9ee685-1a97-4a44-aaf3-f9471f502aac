<?php

namespace App\Http\Controllers\API;
use App\Enums\CloudProviderEnums;
use App\Enums\Stack;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Http\Controllers\Controller;
use App\Http\Resources\CloudProviderResource;
use App\Models\CloudProvider;
use App\Models\PhpVersion;
use App\Models\Product;
use App\Models\Server;
use App\Models\SshKeyPair;
use App\Models\Utm;
use App\Repository\ServerBillingRepository;
use App\Repository\ServerRepository;
use App\Rules\DatabasePassword;
use App\Services\CloudServices\GCP;
use App\Services\CloudServices\Validations\APIKeyValidationFactory;
use App\Services\Database\DatabaseEngine;
use App\Services\Server\Api\ServerCreation\CloudServerCreationServiceFactory;
use App\Services\Shell\SshConnector;
use App\Validator\ServerNameValidation;
use App\Validator\TagValidation;
use Arr;
use Exception;
use Google\ApiCore\ApiException;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Inertia\Response;

class ServerCreationController extends Controller
{
//    TODO: Need to clear out individual provider functions here and move them to universal function
    /**
     * @throws \Throwable
     */
    public function customProvider(Request $request)
    {
        if(!team()->canAccessWithBilling(BillingServices::SelfManagedHosting)){
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Add a payment method to active your account.'
            ]);
        }

        $this->authorize('create', Server::class);

        $validated = $request->validate([
            'name' => ServerNameValidation::serverName(),
            'public_ip' => ['required', 'max:16', 'ip'],
            'ssh_port' => ['required', 'numeric', 'max:99999'],
            'ssh_username' => ['required', 'max:30', 'string'],

            'php_version' => ['nullable', 'string', PhpVersion::asRule()],
            'database_type' => ['nullable', 'string', DatabaseEngine::asRule()],
            'database_name' => ['nullable', 'max:32', 'string'],

            'ssh_authentication_mode' => ['required', 'in:public_key,password'],
            'ssh_password' => ['required_if:ssh_authentication_mode,password'],
            'ssh_key_pair_id' => ['required_if:ssh_authentication_mode,public_key'],
            'stack' => ['nullable', Rule::in(Stack::availableStacks())],
            'tags' => TagValidation::tagsRules(),
            'tags.*' => TagValidation::tagRules(),
            'consent' => ['required', 'accepted']
        ],
            [
                ...TagValidation::tagMessage(),
                'consent.required' => 'You must create a server on your provider in order to connect to your existing server.',
                'consent.accepted' => 'You must create a server on your provider in order to connect to your existing server.',
            ]
        );

        // don't need to pass consent for server creation
        unset($validated['consent']);

        if ($validated['ssh_username'] !== 'root') {
            throw ValidationException::withMessages([
                'ssh_username' => 'Custom provider requires root user.',
            ]);
        }

        app(SshConnector::class)->executeScript(
            validated: $validated,
            script: view('scripts.server.versionCompatibility', [
                'stack' => $validated['stack'],
            ])->render()
        );

        return $this->createServer($validated, $request);
    }


    /**
     * @throws Exception
     */
    public function storeXCloud(Request $request)
    {
        $this->authorize('create', Server::class);

        if(!team()->canAccessWithBilling(BillingServices::xCloudManagedHosting)){
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Add a payment method to active your account.'
            ]);
        }

        $cloudProvider = CloudProvider::where('provider', CloudProviderEnums::XCLOUD->value)->firstOrFail();

        // we don't need to authorize here because we are using the default cloud provider
        // $this->authorize('view', $cloudProvider);

        $serverData = $request->validate([
            'name' => ServerNameValidation::serverName(),
            'size' => ['required', 'max:255'],
            'region' => ['required', 'max:255'],
            'database_type' => ['string', DatabaseEngine::asRule()],
            'stack' => ['nullable', Rule::in(Stack::availableStacks())],
            'tags' => TagValidation::tagsRules(),
            'tags.*' => TagValidation::tagRules(),
            'backups' => ['boolean']
        ],[
            ...TagValidation::tagMessage(),
        ]);

        // default authentication mode
        $serverData['ssh_authentication_mode'] = 'public_key';
        $serverData['ssh_port'] = '22';
        $serverData['ssh_username'] = 'root';
        $serverData['ubuntu_version'] = '22.04';
        $serverData['server_image'] = 'ubuntu-22-04-x64';
        $serverData['cloud_provider_id'] = $cloudProvider->id;
        $serverData['ssh_key_pair_id'] = SshKeyPair::getSystemDefaulKeyPair()->id;

        return $this->createServer($serverData, $request);
    }

    /**
     * @throws Exception
     */
    public function storeXCloudVultr(Request $request)
    {
        if(team()->billingIsActive() && !team()->hasActivePaymentMethod()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Please add a card to create servers.'
            ]);
        }

        $this->authorize('create', Server::class);

        if(!team()->canAccessWithBilling(BillingServices::xCloudManagedHosting)){
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Add a payment method to active your account.'
            ]);
        }

        $cloudProvider = CloudProvider::where('provider', CloudProviderEnums::XCLOUD_VULTR->value)->first();

        if (!$cloudProvider) {
            $cloudProvider = CloudProvider::createXcloudVultr();
        }

        $serverData = $request->validate([
            'name' => ServerNameValidation::serverName(),
            'product_id' => [
                'required',
                'exists:products,id',
                function ($attribute, $value, $fail) {
                    $product = \App\Models\Product::withCount(['teams'=>fn($q)=>$q->where(['teams.id' => team()->id])])
                        ->where([
                            'id' => $value,
                            'is_active' => true,
                            'service_type' => BillingServices::xCloudManagedHosting,
                            'available_for_white_label' => false,
                            'show_on_display' => true,
                        ])->where(fn ($q)=> $q->whereRequiresBillingPlan(false)
                            ->orWhereNull('max_purchase_limit')
                            ->orWhere('max_purchase_limit','<','max_purchase_limit')
                        )->where(fn ($q)=> $q->whereRequiresBillingPlan(false)
                            ->orWhereNull('requires_billing_plan')
                            ->orWhere('requires_billing_plan', team()->active_plan_id)
                        )->first();
                    if(!$product){
                        return $fail('Invalid product selected.');
                    }
                    if (!($product->max_purchase_limit == null || $product->max_purchase_limit > $product->teams_count)){
                        return $fail('Product purchase limit reached.');
                    }
                }
            ],
            'size' => ['required', 'max:255'],
            'region' => ['required', 'max:255'],
            'stack' => ['nullable', Rule::in(Stack::availableStacks())],
            'database_type' => ['string', DatabaseEngine::asRule()],
            'tags' => TagValidation::tagsRules(),
            'tags.*' => TagValidation::tagRules(),
            'backups' => ['boolean'],
        ],[
            ...TagValidation::tagMessage(),
        ]);

        // default authentication mode
        $serverData['ssh_authentication_mode'] = 'public_key';
        $serverData['ssh_port'] = '22';
        $serverData['ssh_username'] = 'root';
        $serverData['ubuntu_version'] = '22.04';
        $serverData['server_image'] = 'Ubuntu 22.04 LTS x64';
        $serverData['cloud_provider_id'] = $cloudProvider->id;
        $serverData['ssh_key_pair_id'] = SshKeyPair::getSystemDefaulKeyPair()->id;

        return $this->createServer($serverData, $request);
    }


    /**
     * @throws Exception
     */
    public function storeWhiteLabelVultr(Request $request)
    {
        if(team()->billingIsActive() && !team()->hasActivePaymentMethod()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Please add a card to create servers.'
            ]);
        }

        $this->authorize('create', Server::class);

        if(!team()->canAccessWithBilling(BillingServices::ManagedHosting)){
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Add a payment method to active your account.'
            ]);
        }

        $cloudProvider = CloudProvider::where('provider', CloudProviderEnums::WHITE_LABEL_VULTR->value)->first();

        if (!$cloudProvider) {
            $cloudProvider = CloudProvider::createWhiteLabelVultr();
        }

        $serverData = $request->validate([
            'name' => ServerNameValidation::serverName(),
            'product_id' => [
                'required',
                'exists:products,id',
                function ($attribute, $value, $fail) {
                    $product = \App\Models\Product::withCount(['teams'=>fn($q)=>$q->where(['teams.id' => team()->id])])
                        ->where([
                            'id' => $value,
                            'is_active' => true,
                            'service_type' => BillingServices::ManagedHosting,
                            'available_for_white_label' => false,
                            'show_on_display' => true,
                        ])->first();
                    if(!$product){
                        return $fail('Invalid product selected.');
                    }
                }
            ],
            'size' => ['required', 'max:255'],
            'region' => ['required', 'max:255'],
            'stack' => ['nullable', Rule::in(Stack::availableStacks())],
            'database_type' => ['string'],
            'tags' => TagValidation::tagsRules(),
            'tags.*' => TagValidation::tagRules(),
            'backups' => ['boolean'],
        ],[
            ...TagValidation::tagMessage(),
        ]);

        // default authentication mode
        $serverData['ssh_authentication_mode'] = 'public_key';
        $serverData['ssh_port'] = '22';
        $serverData['ssh_username'] = 'root';
        $serverData['ubuntu_version'] = '22.04';
        $serverData['server_image'] = 'Ubuntu 22.04 LTS x64';
        $serverData['cloud_provider_id'] = $cloudProvider->id;
        $serverData['ssh_key_pair_id'] = SshKeyPair::getSystemDefaulKeyPair()->id;

        return $this->createServer($serverData, $request);
    }

    /**
     * @throws Exception
     */
    public function storeXcloudLTD(Request $request)
    {
        if(team()->billingIsActive() && !team()->hasActivePaymentMethod()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Please add a card to create servers.'
            ]);
        }

        $this->authorize('create', Server::class);

        if(!team()->canAccessWithBilling(BillingServices::xCloudProviderHosting)){
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Add a payment method to active your account.'
            ]);
        }

        $cloudProvider = CloudProvider::where('provider', CloudProviderEnums::XCLOUD_PROVIDER)->first();

        if (!$cloudProvider) {
            $cloudProvider = CloudProvider::createXcloudProviderHosting();
        }

        $serverData = $request->validate([
            'name' => ServerNameValidation::serverName(),
            'product_id' => [
                'required',
                'exists:products,id',
                function ($attribute, $value, $fail) {
                    $product = \App\Models\Product::withCount(['teams'=>fn($q)=>$q->where(['teams.id' => team()->id])])
                        ->where([
                            'id' => $value,
                            'is_active' => true,
                            'service_type' => BillingServices::xCloudProviderHosting,
                            'available_for_white_label' => false,
                            'show_on_display' => true,
                        ])->where(fn ($q)=> $q->whereRequiresBillingPlan(false)
                            ->orWhereNull('max_purchase_limit')
                            ->orWhere('max_purchase_limit','<','max_purchase_limit')
                        )->where(fn ($q)=> $q->whereRequiresBillingPlan(false)
                            ->orWhereNull('requires_billing_plan')
                            ->orWhere('requires_billing_plan', team()->active_plan_id)
                        )->first();
                    if(!$product){
                        return $fail('Invalid product selected.');
                    }
                    if (!($product->max_purchase_limit == null || $product->max_purchase_limit > $product->teams_count)){
                        return $fail('Product purchase limit reached.');
                    }
                }
            ],
            'size' => ['required', 'max:255'],
            'region' => ['required', 'max:255'],
            'database_type' => ['string', DatabaseEngine::asRule()],
            'stack' => ['nullable', Rule::in(Stack::availableStacks())],
            'tags' => TagValidation::tagsRules(),
            'tags.*' => TagValidation::tagRules(),
            'backups' => ['boolean'],
        ],[
            ...TagValidation::tagMessage(),
        ]);

        $serverData['tags'] = array_merge($serverData['tags'] ?? [], ['xcloud_white_label_hosting']);

        // default authentication mode
        $serverData['ssh_authentication_mode'] = 'public_key';
        $serverData['ssh_port'] = '22';
        $serverData['ssh_username'] = 'root';
        $serverData['ubuntu_version'] = '22.04';
        $serverData['server_image'] = 'Ubuntu 22.04 LTS x64';
        $serverData['cloud_provider_id'] = $cloudProvider->id;
        $serverData['ssh_key_pair_id'] = SshKeyPair::getSystemDefaulKeyPair()->id;

        return $this->createServer($serverData, $request);
    }

    /**
     * @param array $validated
     * @param Request $request
     * @return JsonResponse|RedirectResponse
     * @throws Exception
     * @throws GuzzleException
     */
    private function createServer(array $validated, Request $request): RedirectResponse|JsonResponse
    {
        $this->authorize('create', Server::class);

        $validated['team_id'] = $request->user()->current_team_id;

        unset($validated['server_size']);
        unset($validated['tags']);

        $validated['sudo_password'] ??= Str::random(32);
        $validated['database_password'] = DatabasePassword::randomPassword();
        $validated['database_name'] ??= 'xcloud';
        $validated['database_type'] ??= DatabaseEngine::DEFAULT;
        $validated['php_version'] ??= PhpVersion::DEFAULT;
        $validated['ssh_key_pair_id'] ??= SshKeyPair::createServerDefaultKey(Str::slug($validated['name'])."@xcloud")->id;
        $validated['stack'] ??= Stack::Nginx;
        $validated['redis_version'] = Server::REDIS_SEVEN;
        $validated['redis_password'] ??= Str::random(32);

        if(Utm::getUtmIdFromCookie()){
            $validated['meta']['utm_id'] = Utm::getUtmIdFromCookie();
            team()->setUtmSource(Utm::getUtmIdFromCookie(), optional(session())->getId(), $request?->ip());
        }

        $product = isset($validated['product_id']) ? Product::where('id', $validated['product_id'])->first() : null;

        if ($product) {
            $this->authorize('canUse', $product);
        }

        unset($validated['product_id']);

        /** @var Server $server */
        $server = $request->user()->servers()->create($validated);

        if($server->backups){
            $server->saveMeta('backupCost', (new ServerRepository($server))->getBackupCost() ?? 0);
        }

        if(!app()->environment('production') && $request->has('demo')){
            $server->saveMeta('is_demo', $request->boolean('demo'));
        }

        // generate bill
        (new ServerBillingRepository($server, $product))->generateBills();

        // Generate invoice if billing is active
        if ($server->team->billingIsActive() && $server->bills()->unpaid()->count() > 0 && !app()->runningUnitTests()) {
            $invoice = $server->generateInvoice(InvoiceSourceEnum::SinglePurchase);
            if ($invoice){
                $server->saveMeta('first_creation_invoice_id', $invoice?->id);
            }
        }

        if ($request->has('tags')) {
            $server->syncTags($request->get('tags'));
        }

        $server->provision();

        if ($request->expectsJson()) {
            return response()->json($server, 201);
        }

        return to_route('server.show', $server)->with('flash', ['message' => 'Server Connected Successfully.']);
    }


    /**
     * @throws Exception
     */
    public function storeDigitalOcean(Request $request, CloudProvider $cloudProvider)
    {
        $this->authorize('view', $cloudProvider);
        $this->authorize('create', Server::class);

        if(!team()->canAccessWithBilling(BillingServices::SelfManagedHosting)){
            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'warning',
                'message' => 'Add a payment method to active your account.'
            ]);
        }

        $serverData = $request->validate([
            'name' => ServerNameValidation::serverName(),
            'size' => ['required', 'max:255'],
            'region' => ['required', 'max:255'],
            'database_type' => ['string', DatabaseEngine::asRule()],
            'stack' => ['nullable', Rule::in(Stack::availableStacks())],
            'tags' => TagValidation::tagsRules(),
            'tags.*' => TagValidation::tagRules(),
            'backups' => ['boolean'],
            'consent' => ['required', 'accepted']
        ],
            [
                ...TagValidation::tagMessage(),
                'consent.required' => 'Please accept this consent before creating server.',
                'consent.accepted' => 'Please accept this consent before creating server.',
            ]
        );

        // don't need to pass consent for server creation
        unset($serverData['consent']);

        // default authentication mode
        $serverData['ssh_authentication_mode'] = 'public_key';
        $serverData['ssh_port'] = '22';
        $serverData['ssh_username'] = 'root';
        $serverData['ubuntu_version'] = '22.04';
        $serverData['server_image'] = 'ubuntu-22-04-x64';
        $serverData['cloud_provider_id'] = $cloudProvider->id;

        return $this->createServer($serverData, $request);
    }

    /**
     *
     * @throws \Google\ApiCore\ValidationException
     */
    function verifyApiTokenGCP(Request $request)
    {
        $this->authorize('create', CloudProvider::class);
        //TODO: need to rename this function,
        // need a different function or modification of this function to verify access token (OAuth)
        $data = $request->validate([
            'project_id' => 'regex:/^[a-z][-a-z0-9]+$/',
            'credential' => 'required'
        ]);

        $keyFile = base64_decode($data['credential']);
        $gcp = new GCP($keyFile);

        $validationKeyFile = $gcp->validateKeyFile();
        if (!$validationKeyFile['isVerified']) {
            throw ValidationException::withMessages([
                'title' => 'Authorization Failed',
                'message' => $validationKeyFile['message'],
                'api_token' => 'Authorization Failed.'
            ]);
        }

        $cloudProvider = team()->cloudProviders()->updateOrCreate(
            [
                'provider' => CloudProviderEnums::GCP->value,
                'name' => Arr::get(json_decode($keyFile, true), 'project_id')
            ], [
                'gcp_access' => json_decode($keyFile),
                'user_id' => user()->id
            ]
        );

        try {
            $regionsAndZones = $gcp->getRegions();
        } catch (ApiException|\Google\ApiCore\ValidationException $e) {
            throw ValidationException::withMessages([
                'title' => 'Failed to get regions',
                'message' => 'Unable to get regions from Google Cloud. Please check your API token and try again.',
            ]);
        }

        $zones = [];
        $regions = [];

        foreach ($regionsAndZones as $regionsAndZone) {

            $filterZones = $regionsAndZone['zones'];

            $regionsAndZone['zones'] = [];

            foreach ($filterZones as $region) {
                $regionsAndZone['zones'][] = [
                    'name' => get_region($region)['region-name'],
                    'region' => get_region($region)['region']
                ];
            }

            $zones[$regionsAndZone['region']] = $regionsAndZone['zones'];
            $regions[] = $regionsAndZone;
        }

        return back()->with('flash', [
            'status' => 'success',
            'message' => 'Successfully Authorized!',
            'provider' => CloudProviderResource::make($cloudProvider),
            'zones' => $zones,
            'regions' => $regions,
            'providers' => CloudProviderResource::make($cloudProvider)
        ]);
    }


    /**
     * @throws AuthorizationException|ValidationException
     */
    function verifyProvider(Request $request, CloudProvider $cloudProvider)
    {
        $this->authorize('view', $cloudProvider);

        $gcp = new GCP($cloudProvider->getAccessToken());

        $validationKeyFile = $gcp->validateKeyFile();
        if (!$validationKeyFile['isVerified']) {
            throw ValidationException::withMessages([
                'title' => 'Authorization Failed',
                'message' => $validationKeyFile['message'],
                'api_token' => 'Authorization Failed.'
            ]);
        }

        try {
            $regionsAndZones = $gcp->getRegions();
        } catch (ApiException|\Google\ApiCore\ValidationException $e) {
            throw ValidationException::withMessages([
                'title' => 'Failed to get regions',
                'message' => 'Unable to get regions from Google Cloud. Please check your API token and try again.',
            ]);
        }

        $zones = [];
        $regions = [];

        foreach ($regionsAndZones as $regionsAndZone) {

            $filterZones = $regionsAndZone['zones'];

            $regionsAndZone['zones'] = [];

            foreach ($filterZones as $region) {
                $regionsAndZone['zones'][] = [
                    'name' => get_region($region)['region-name'],
                    'region' => get_region($region)['region']
                ];
            }

            $zones[$regionsAndZone['region']] = $regionsAndZone['zones'];
            $regions[] = $regionsAndZone;
        }

        return back()->with('flash', [
            'status' => 'success',
            'message' => 'Successfully Authorized!',
            'zones' => $zones,
            'regions' => $regions,
            'provider' => $cloudProvider
        ]);
    }

    /**
     * @throws \Google\ApiCore\ValidationException
     * @throws ApiException
     */
    public function getZones(Request $request)
    {
        $this->authorize('create', CloudProvider::class);
        $keyFile = team()->cloudProviders()->where('provider', CloudProviderEnums::GCP->value)->first()->gcp_access;
        $cloudProvider = CloudProvider::findOrFail(Arr::get($request->get('provider'),'id'));

        $accessToken = $cloudProvider->getAccessToken();

        $gcp = new GCP($accessToken);

        return back()->with('flash', [
            'status' => 'success',
            'zones' => $gcp->setProject($cloudProvider->name)->getZones(request('region'))
        ]);
    }

    /**
     * @throws \Google\ApiCore\ValidationException
     * @throws ApiException
     */
    public function getSizes()
    {
        // $this->authorize('create', CloudProvider::class);

        ### Uncomment this for gcp oAuth ###
//        $keyFile = team()->cloudProviders()->where('provider', CloudProviderEnums::GCP->value)->first()->gcp_access;
//        $cloudProvider = CloudProvider::findOrFail($providerId);
//
//        $accessToken = $cloudProvider->getAccessToken();
//
//        $gcp = new GCPOAuth($accessToken);
//
//        return response()->json([
//            'status' => 'success',
//            'sizes' => $gcp->setProject($cloudProvider->name)->getSizes($zone)
//        ]);

        $keyFile = team()->cloudProviders()->where('provider', CloudProviderEnums::GCP->value)->first()->gcp_access;

        $gcp = new GCP(json_encode($keyFile));

        return back()->with('flash', [
            'status' => 'success',
            'sizes' => $gcp->getSizes(request('zone'))
        ]);
    }

//    function verifyAWSCredentials(Request $request, CloudProvider $cloudProvider)
//    {
//        $this->authorize('view', $cloudProvider);
//        $data = $request->validate([
//            'key' => 'required',
//            'secret' => 'required',
//            'region' => 'required',
//        ]);
//
//        $aws = new AWS($data['key'], $data['secret'], $data['region']);
//
//        if (!$aws->verifyAWSCredentials()) {
//            throw ValidationException::withMessages([
//                'title' => 'Authorization Failed',
//                'message' => 'Unable to connect to AWS. Please check your Key/Secret and try again.',
//                'api_token' => 'Authorization Failed.'
//            ]);
//        }
//
//        return back()->with('flash', [
//            'status' => 'success',
//            'message' => 'Successfully Authorized!',
//            'regions' => $aws->getCachedRegions(),
//        ]);
//    }

//    /**
//     * @throws Exception
//     */
//    public function storeAWS(Request $request, CloudProvider $cloudProvider)
//    {
//        $this->authorize('view', $cloudProvider);
//        $this->authorize('create', Server::class);
//
//        $serverData = $request->validate([
//            'name' => ServerNameValidation::serverName(),
//            'size' => ['required', 'max:255'],
//            'region' => ['required', 'max:255'],
//            'database_type' => ['string', DatabaseEngine::asRule()],
//            'database_password' => ['nullable', 'string'],
//            'tags' => TagValidation::tagsRules(),
//            'tags.*' => TagValidation::tagRules(),
//        ],[
//            ...TagValidation::tagMessage(),
//        ]);
//
//        // default authentication mode
//        $serverData['ssh_authentication_mode'] = 'public_key';
//        $serverData['ssh_port'] = '22';
//        $serverData['ssh_username'] = 'root';
//
//        $serverData['server_image'] = 'ubuntu-focal-20.04'; // ubuntu-focal-20.04-amd64-server-20210119
//        $serverData['cloud_provider_id'] = $cloudProvider->id;
//
//        return $this->createServer($serverData, $request);
//    }


    /**
     * @throws Exception
     */
    public function storeGCP(Request $request, CloudProvider $cloudProvider)
    {
        $this->authorize('view', $cloudProvider);

        $serverData = $request->validate([
            'name' => ServerNameValidation::serverName(),
            'size' => ['required', 'max:255'],
            'region' => ['required', 'max:255'],
            'zone' => ['required', 'max:255'],
            'database_type' => ['string', DatabaseEngine::asRule()],
            'stack' => ['nullable', Rule::in(Stack::availableStacks())],
            'tags' => TagValidation::tagsRules(),
            'tags.*' => TagValidation::tagRules(),
            'consent' => ['required', 'accepted']
        ],
            [
                ...TagValidation::tagMessage(),
                'consent.required' => 'Please accept this consent before creating server.',
                'consent.accepted' => 'Please accept this consent before creating server.',
            ]
        );

        // don't need to pass consent for server creation
        unset($serverData['consent']);

        // default authentication mode
        $serverData['ssh_authentication_mode'] = 'public_key';
        $serverData['ssh_port'] = '22';

        // gcp doesn't allow root login, so we use xcloud user and later add sudo access from SetupSshKey
        $serverData['ssh_username'] = 'xcloud';
        $serverData['ubuntu_version'] = '22.04';
        $serverData['server_image'] = 'ubuntu-2204-lts'; // 'ubuntu-2004-lts'
        $serverData['cloud_provider_id'] = $cloudProvider->id;

        return $this->createServer($serverData, $request);
    }


    // interfacing all server creation

    /**
     * @throws AuthorizationException
     */
    public function storeServer(Request $request): JsonResponse|RedirectResponse {
        $this->authorize('create', Server::class);

//        UNDER XCLOUD servers and ANY server will be handled differently or we pass null provider class to them (will handle later)

        $validatedData = $request->validate([
            'cloudProvider' => ['required', 'integer']
        ]);

        $cloudProvider = CloudProvider::find($validatedData['cloudProvider']);
//        checking if the user is allowed to use this cloudProvider
        $this->authorize('view', $cloudProvider);

//        we can pass blance CloudProvider class to the factory getservice method to run it smoothly for the other providers
//        that do not need any relationship with the provider

        $service = CloudServerCreationServiceFactory::getService($request, $cloudProvider);
        return $service->createServer();
    }

    // interfacing all cloud API key validation

    /**
     * @throws AuthorizationException
     * @throws ValidationException
     */
    public function validateAPICredential(Request $request): RedirectResponse {
        $this->authorize('create', CloudProvider::class);

        $validatedData = $request->validate([
            'cloud_provider_name' => ['required', Rule::in(CloudProviderEnums::asValue())],
        ]);

        $cloudProviderEnum = CloudProviderEnums::tryFrom($validatedData['cloud_provider_name']);

        if (is_null($cloudProviderEnum)) {
            // the enum value is not valid so returning an error response
            throw ValidationException::withMessages([
                'label' => 'Invalid cloud provider',
                'verified' => false
            ]);
        }

        $service = APIKeyValidationFactory::getService($request, $cloudProviderEnum);
        return $service->validateSaveOrUpdateCredential();

    }


    /**
     * Handles dynamic requests to cloud providers by calling a specified method on the provider's service.
     *
     * @param Request $request
     * @return JsonResponse|RedirectResponse
     * @throws AuthorizationException
     */
    public function executeProviderFunction(Request $request): JsonResponse|RedirectResponse {
        // Authorize the user to create a server
        $this->authorize('create', Server::class);

        // Validate the request data
        $validatedData = $request->validate([
            'cloudProvider' => ['required', 'integer'],
            'functionName' => ['required', 'string'],
            'parameters' => ['sometimes', 'array'] // Optional parameters
        ]);

        // Find the specified cloud provider
        $cloudProvider = CloudProvider::find($validatedData['cloudProvider']);
        // Authorize the user to view the cloud provider
        $this->authorize('view', $cloudProvider);

        // Retrieve the service for the specified cloud provider
        $service = CloudServerCreationServiceFactory::getService($request, $cloudProvider);

        // Get the function name from the validated data
        $functionName = $validatedData['functionName'];

        // Check if the function exists in the service
        if (!method_exists($service, $functionName)) {
            return response()->json(['error' => 'Invalid function name provided.'], 400);
        }

        // Get the parameters if provided
        $parameters = $validatedData['parameters'] ?? [];

        // Dynamically call the specified function on the service with parameters
        return call_user_func_array([$service, $functionName], $parameters);
    }
}
