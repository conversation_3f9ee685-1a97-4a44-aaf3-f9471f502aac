<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\ApiEndpoint;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ApiEndpointController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = ApiEndpoint::query();

        // Filter by status if provided
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('url', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $endpoints = $query->orderBy('created_at', 'desc')->paginate(15);

        return response()->json([
            'data' => $endpoints->items(),
            'meta' => [
                'current_page' => $endpoints->currentPage(),
                'last_page' => $endpoints->lastPage(),
                'per_page' => $endpoints->perPage(),
                'total' => $endpoints->total(),
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'nullable|string|max:255',
            'url' => 'required|url|max:2048',
            'api_key' => 'required|string|max:1000',
            'active' => 'boolean',
            'count' => 'integer|min:0',
            'description' => 'nullable|string|max:1000',
        ]);

        $endpoint = ApiEndpoint::create($validated);

        return response()->json([
            'message' => 'API Endpoint created successfully.',
            'data' => $endpoint
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ApiEndpoint $apiEndpoint): JsonResponse
    {
        return response()->json([
            'data' => $apiEndpoint
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ApiEndpoint $apiEndpoint): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'nullable|string|max:255',
            'url' => 'required|url|max:2048',
            'api_key' => 'required|string|max:1000',
            'active' => 'boolean',
            'count' => 'integer|min:0',
            'description' => 'nullable|string|max:1000',
        ]);

        $apiEndpoint->update($validated);

        return response()->json([
            'message' => 'API Endpoint updated successfully.',
            'data' => $apiEndpoint->fresh()
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ApiEndpoint $apiEndpoint): JsonResponse
    {
        $apiEndpoint->delete();

        return response()->json([
            'message' => 'API Endpoint deleted successfully.'
        ]);
    }

    /**
     * Toggle the active status of an endpoint.
     */
    public function toggleStatus(ApiEndpoint $apiEndpoint): JsonResponse
    {
        $apiEndpoint->update(['active' => !$apiEndpoint->active]);

        $status = $apiEndpoint->active ? 'activated' : 'deactivated';

        return response()->json([
            'message' => "API Endpoint {$status} successfully.",
            'data' => $apiEndpoint->fresh()
        ]);
    }

    /**
     * Increment the count for an endpoint.
     */
    public function incrementCount(ApiEndpoint $apiEndpoint): JsonResponse
    {
        $apiEndpoint->incrementCount();

        return response()->json([
            'message' => 'Count incremented successfully.',
            'data' => $apiEndpoint->fresh()
        ]);
    }

    /**
     * Reset the count for an endpoint.
     */
    public function resetCount(ApiEndpoint $apiEndpoint): JsonResponse
    {
        $apiEndpoint->resetCount();

        return response()->json([
            'message' => 'Count reset successfully.',
            'data' => $apiEndpoint->fresh()
        ]);
    }
}
