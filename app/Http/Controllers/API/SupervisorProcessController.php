<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Jobs\SupervisorProcessSyncJob;
use App\Models\Server;
use App\Models\SupervisorProcess;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class SupervisorProcessController extends Controller
{
    function store(Server $server, Request $request)
    {
        $this->authorize('manageSupervisorProcess', $server);
        $data = $request->validate([
            'user' => [
                'required',
                'max:64',
                'not_in:xcloud',
                function ($attribute, $value, $fail) use ($server) {
                    if ($value === 'xcloud') {
                        $fail('The user is not allowed.');
                    } elseif (!$server->doesUserExist(user: $value)) {
                        $fail('The user does not exist on the server.');
                    }
                }
            ],
            'command' => ['required', 'max:255', 'min:1'],
            'directory' => ['nullable', 'max:255'],
            'numprocs' => ['required', 'integer', 'min:1', 'max:100'],
            'startsecs' => ['nullable', 'integer', 'min:0', 'max:3600'],
            'stopsecs' => ['nullable', 'integer', 'min:0', 'max:3600'],
            'stopsignal' => ['nullable', 'max:10'],
        ]);
        try {
            $supervisorProcess = $server->supervisorProcesses()->create([
                ...$data,
                'status' => SupervisorProcess::INSTALLING,
                'user_id' => user()->id,
            ]);
            if (!$supervisorProcess->installSupervisorProcess()) {
                $supervisorProcess->update(['status' => SupervisorProcess::INACTIVE]);
            } else {
                $supervisorProcess->syncInQueue();
            }
            return to_route('server.supervisor', $server)->with('flash', [
                'type' => 'success',
                'message' => 'Supervisor process has been successfully added.'
            ]);
        } catch (Exception $e) {
            Log::error($e);
            return to_route('server.supervisor', $server)->with('flash', [
                'type' => 'error',
                'message' => 'Failed to add supervisor process.'
            ]);
        }
    }

    function update(Server $server, SupervisorProcess $supervisorProcess, Request $request)
    {
        $this->authorize('manageSupervisorProcess', $server);

        $data = $request->validate([
            'user' => [
                'required',
                'max:64',
                'not_in:xcloud',
                function ($attribute, $value, $fail) use ($server) {
                    if ($value === 'xcloud') {
                        $fail('The user is not allowed.');
                    } elseif (!$server->doesUserExist(user: $value)) {
                        $fail('The user does not exist on the server.');
                    }
                }
            ],
            'command' => ['required', 'max:255', 'min:1'],
            'directory' => ['nullable', 'max:255'],
            'numprocs' => ['required', 'integer', 'min:1', 'max:100'],
            'startsecs' => ['nullable', 'integer', 'min:0', 'max:3600'],
            'stopsecs' => ['nullable', 'integer', 'min:0', 'max:3600'],
            'stopsignal' => ['nullable', 'max:10'],
        ]);

        try {
            $supervisorProcess->update([
                ...$data,
                'status' => SupervisorProcess::INSTALLING
            ]);
            if (!$supervisorProcess->installSupervisorProcess()) {
                $supervisorProcess->update(['status' => SupervisorProcess::INACTIVE]);
            } else {
                $supervisorProcess->syncInQueue();
            }

            return to_route('server.supervisor', $server)->with('flash', [
                'type' => 'success',
                'message' => 'Supervisor process has been successfully updated.'
            ]);
        } catch (Exception $e) {
            Log::error($e);
            return to_route('server.supervisor', $server)->with('flash', [
                'type' => 'error',
                'message' => 'Failed to update supervisor process.'
            ]);
        }
    }

    public function output(Server $server, SupervisorProcess $supervisorProcess)
    {
        $this->authorize('manageSupervisorProcess', $server);
        return response()->json($supervisorProcess->getOutput());
    }

    function destroy(Server $server, SupervisorProcess $supervisorProcess)
    {
        $this->authorize('manageSupervisorProcess', $server);
        if ($supervisorProcess->removeSupervisorProcess()) {
            $supervisorProcess->delete();
            return redirect()->back()->with('flash', [
                'type' => 'success',
                'message' => 'Supervisor process has been successfully deleted.'
            ]);
        }
        return redirect()->back()->with('flash', [
            'type' => 'error',
            'message' => 'Failed to delete supervisor process.'
        ]);
    }

    function restart(Server $server, SupervisorProcess $supervisorProcess)
    {
        $this->authorize('manageSupervisorProcess', $server);
        $supervisorProcess->update(['status' => SupervisorProcess::RESTARTING, 'log' => null]);

        $status = $supervisorProcess->restartSupervisorProcess();

        $supervisorProcess->syncInQueue();

        if ($status) {
            return redirect()->back()->with('flash', [
                'type' => 'success',
                'message' => 'Supervisor process has been successfully restarted.'
            ]);
        }

        $supervisorProcess->update(['status' => SupervisorProcess::INACTIVE]);

        return redirect()->back()->with('flash', [
            'type' => 'error',
            'message' => 'Failed to restart supervisor process.'
        ]);
    }

    function pause(Server $server, SupervisorProcess $supervisorProcess)
    {
        $this->authorize('manageSupervisorProcess', $server);
        $supervisorProcess->update(['status' => SupervisorProcess::STOPPING, 'log' => null]);

        $status = $supervisorProcess->pauseSupervisorProcess();

        $supervisorProcess->syncInQueue();

        if ($status) {
            return redirect()->back()->with('flash', [
                'type' => 'success',
                'message' => 'Supervisor process has been successfully paused.'
            ]);
        }

        $supervisorProcess->update(['status' => SupervisorProcess::INACTIVE]);

        return redirect()->back()->with('flash', [
            'type' => 'error',
            'message' => 'Failed to pause supervisor process.'
        ]);
    }

    function checkStatus(Server $server, SupervisorProcess $supervisorProcess)
    {
        $this->authorize('manageSupervisorProcess', $server);
        $supervisorProcess->syncStatus();
        return response()->json([
            'status' => $supervisorProcess->status,
            'log' => $supervisorProcess->log
        ]);
    }

    function checkAllStatus(Server $server, Request $request)
    {
        $this->authorize('manageSupervisorProcess', $server);

        // Get all supervisor processes for this server
        $processes = $server->supervisorProcesses()->get();

        // Get all statuses at once using a single supervisorctl status command
        $statusOutput = $server->checkAllSupervisorProcessStatus();
        $parsedStatuses = SupervisorProcess::parseMultipleStatusLines($statusOutput);

        // Update each process with its status
        foreach ($processes as $process) {
            if (isset($parsedStatuses[$process->id])) {
                $statusInfo = $parsedStatuses[$process->id];

                // Update the log field with the full status line for tooltip display
                $process->update(['log' => $statusInfo['name'].' '.$statusInfo['status'].' '.$statusInfo['details']]);

                // Map supervisor status to our application status
                if (!empty($statusInfo['status'])) {
                    $supervisorStatus = strtolower($statusInfo['status']);

                    switch ($supervisorStatus) {
                        case 'running':
                            $process->update(['status' => SupervisorProcess::RUNNING]);
                            break;
                        case 'fatal':
                            $process->update(['status' => SupervisorProcess::FATAL]);
                            break;
                        case 'backoff':
                            $process->update(['status' => SupervisorProcess::BACKOFF]);
                            break;
                        case 'starting':
                            $process->update(['status' => SupervisorProcess::STARTING]);
                            break;
                        case 'stopping':
                            $process->update(['status' => SupervisorProcess::STOPPING]);
                            break;
                        case 'exited':
                            $process->update(['status' => SupervisorProcess::EXITED]);
                            break;
                        case 'stopped':
                            $process->update(['status' => SupervisorProcess::STOPPED]);
                            break;
                        default:
                            $process->update(['status' => SupervisorProcess::UNKNOWN]);
                    }
                } else {
                    // If we can't parse the status, set it to inactive
                    $process->update(['status' => SupervisorProcess::INACTIVE]);
                }
            } else {
                // If the process is not found in the supervisor output, set it to inactive
                $process->update(['status' => SupervisorProcess::INACTIVE, 'log' => 'Process not found in supervisor']);
            }
        }

        if ($request->wantsJson()) {
            return response()->json([
                'message' => 'All supervisor processes status updated successfully'
            ]);
        }

        return back()->with('flash', ['message' => 'All supervisor processes status updated successfully']);
    }
}
