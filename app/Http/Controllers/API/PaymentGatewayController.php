<?php

namespace App\Http\Controllers\API;

use App\Enums\ServerModificationActions;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\CartFormStatuses;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Http\Controllers\Controller;
use App\Models\CartForm;
use App\Models\GeneralInvoice;
use App\Models\Package;
use App\Models\PaymentMethod;
use App\Models\Product;
use App\Models\SubscriptionProduct;
use App\Models\Utm;
use App\Models\WhiteLabel;
use App\Repository\StripePaymentRepository;
use App\Services\Server\Api\ServerModification\Actions\ServerProviderBackup;
use App\Services\Server\Api\ServerModification\Actions\ServerResize;
use Arr;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Log;
use PHPUnit\Util\Exception;
use Stripe\Checkout\Session;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\StripeClient;

class PaymentGatewayController extends Controller
{
    /**
     * @throws ApiErrorException
     */
    public function addPaymentMethod($paymentGateway, Request $request)
    {
        $this->authorize('createBilling', team());
        $this->authorize('addPaymentMethod', PaymentMethod::class);

        if($paymentGateway === PaymentGateway::Stripe->value){
            Stripe::setApiKey(config('services.stripe.secret_key'));

            // check if customer already exists
            $customer = null;
            if(team()->stripe_customer_id){
                if(currentWhiteLabel()){
                    $customer = Customer::retrieve(team()->stripe_customer_id, [
                        'stripe_account' => currentWhiteLabel()->connectedAccount?->stripe_account_id
                    ]);
                }else{
                    $customer = Customer::retrieve(team()->stripe_customer_id);
                }


                if(!$customer || $customer->deleted){
                    // create new customer
                    $customerData = [
                        'name' => team()->owner->name,
                        'email' => team()->email,
                        'description' => (currentWhiteLabel() ? 'Whitelabel Client ' : 'Customer ') . team()->name . ' , added by ' . auth()->user()->name,
                    ];

                    $customerOptions = currentWhiteLabel() ? ['stripe_account' => currentWhiteLabel()->connectedAccount?->stripe_account_id] : [];

                    $customer = Customer::create($customerData, $customerOptions);

                    team()->update(['stripe_customer_id' => $customer->id]);
                }
            }

            if(!$customer){
                $customerData = [
                    'name' => team()->owner->name,
                    'email' => team()->email,
                    'description' => (currentWhiteLabel() ? 'Whitelabel Client ' : 'Customer ') . team()->name . ' , added by ' . auth()->user()->name,
                ];

                $customerOptions = currentWhiteLabel() ? ['stripe_account' => currentWhiteLabel()->connectedAccount?->stripe_account_id] : [];

                $customer = Customer::create($customerData, $customerOptions);

                team()->update(['stripe_customer_id' => $customer->id]);
            }

            $sessionData = [
                'payment_method_types' => ['card'],
                'mode' => 'setup',
                'customer' => $customer->id,
                'success_url' => route('stripe.card_add.success', [], true) . "?session_id={CHECKOUT_SESSION_ID}",
                'cancel_url' => route('stripe.card_add.cancelled'),
            ];

            $sessionOptions = currentWhiteLabel() ? ['stripe_account' => currentWhiteLabel()->connectedAccount?->stripe_account_id] : [];

            $session = Session::create($sessionData, $sessionOptions);

            return redirect()->away($session->url);
        }

        return back();
    }

    public function setAsDefault(PaymentMethod $paymentMethod)
    {
        $this->authorize('createBilling', team());
        $this->authorize('update', $paymentMethod);

        // make existing default card to false
        PaymentMethod::where('team_id', team()->id)->where('default_card', true)->first()?->setAsNonDefault();

        $paymentMethod->setAsDefault();

        if (request()->expectsJson()) {
            return response()->json($paymentMethod);
        }

        return redirect(route('user.bills-payment'))->with('flash', ['message' => 'Card set as default']);
    }

    public function deletePaymentMethod(PaymentMethod $paymentMethod)
    {
        $this->authorize('createBilling', team());
        $this->authorize('delete', $paymentMethod);

        $paymentMethod->delete();

        if (request()->expectsJson()) {
            return response()->json($paymentMethod);
        }

        return redirect(route('user.bills-payment'))->with('flash', ['message' => 'Card deleted successfully']);
    }
    /**
     * @throws ApiErrorException
     * @throws GuzzleException
     */
    public function confirm3DSecurePayment(Request $request)
    {
        $invoice = GeneralInvoice::where('id', $request->get('invoiceId'))->first();

        $this->authorize('can-pay', $invoice);

        $confirmPaymentIntent = StripePaymentRepository::confirmPayment($invoice);

        $nextRoute = $request->get('nextRoute');
        $routeParam = $request->get('routeParam');

        if($routeParam && isset($routeParam['model'])){
            $model = $routeParam['model']::findOrFail($routeParam['id']);
        }

        if($confirmPaymentIntent->status === PaymentIntent::STATUS_SUCCEEDED) {
            return redirect()->route($nextRoute ?? 'user.detailedInvoices', $model ?? $routeParam ?? $invoice->id)
                ->with('flash', [
                    'message' => 'Payment completed successfully'
                ]);
        } else {
            // affiliate log
            Log::warning('Failed to take payment. Affiliate commission unable to generate', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'payment_intent_id' => $confirmPaymentIntent->id,
                'payment_intent_status' => $confirmPaymentIntent->status,
            ]);

            return redirect()->route($nextRoute ?? 'user.detailedInvoices', $model ?? $routeParam ?? $invoiceId)
                ->with('flash', [
                    'message' => 'Failed to take payment. Please try again',
                    'type' => 'error'
                ]);
        }
    }


    /**
     * @throws ApiErrorException
     * @throws GuzzleException
     */
    public function confirmPackage3DSecurePayment(Request $request)
    {
        $paymentIntentId = $request->get('paymentIntentId');
        $sessionId = $request->get('sessionId');
        $packageId = $request->get('packageId');
        $affiliateId = $request->get('affiliateId');
        $cartId = $request->get('cartId');
        $invoiceId = $request->get('invoiceId');

        if(empty($paymentIntentId)){
            throw new Exception('Payment intent id is missing!');
        }

        $invoice = GeneralInvoice::findOrFail($invoiceId);

        Stripe::setApiKey(config('services.stripe.secret_key'));

        if($invoice->team->whiteLabel) {
            $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId, [
                'stripe_account' => $invoice->team->whiteLabel->connectedAccount?->stripe_account_id
            ]);
        }else{
            $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId);
        }

        $cart = CartForm::findOrFail($cartId);
        $invoice = GeneralInvoice::findOrFail($invoiceId);

        if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
            $package = Package::findOrFail($packageId);
            if($invoice->team->whiteLabel) {
                $session = Session::retrieve($sessionId, [
                    'stripe_account' => $invoice->team->whiteLabel->connectedAccount?->stripe_account_id
                ]);
            }else {
                $session = Session::retrieve($sessionId);
            }

            // attach invoice to cart form
            $cart->invoice()->associate($invoice);
            $cart->save();

            $invoice->setStatusPaid();

            // make bill as paid
            $unpaidBills = $invoice->unpaidBills()->get();
            $unpaidBills->each(function ($bill) {
                $bill->setStatusPaid();
            });

            // affiliate commission
            if ($invoice->type->isAffiliatable()) {
                if ($invoice?->team?->promoter && !$invoice->promoter_id) {
                    if(!$paymentIntent->status == PaymentIntent::STATUS_SUCCEEDED){
                        Log::warning('Failed to take payment. Affiliate commission unable to generate', [
                            'invoice_id' => $invoice->id,
                            'invoice_number' => $invoice->invoice_number,
                            'payment_intent_id' => $paymentIntent->id,
                            'payment_intent_status' => $paymentIntent->status,
                        ]);
                    }else{
                        $invoice->handleAffiliateCommission();
                    }
                }
            }

            // attach package to team
            team()->attachPackage($package);

            // update the cart
            $cart->setStatusCompleted();
            $cart->update([
                'meta->stripe->checkout_session->payment_method_types' => $session->payment_method_types,
                'meta->stripe->checkout_session->customer' => $session->customer,
                'meta->stripe->checkout_session->customer_email' => $session->customer_email,
            ]);

            // send email
            $invoice->team->sendInvoiceEmail($invoice);
        }else{
            // affiliate log
            Log::warning('Failed to take payment. Affiliate commission unable to generate', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'payment_intent_id' => $paymentIntent->id,
                'payment_intent_status' => $paymentIntent->status,
            ]);
        }

        return redirect()->route('user.detailedInvoices', $invoiceId)
            ->with('flash', [
                'message' => 'Payment completed successfully'
            ]);
    }

    /**
     * @throws ApiErrorException
     * @throws GuzzleException
     * @throws \Exception
     */
    public function confirmProduct3DSecurePayment(Request $request)
    {
        $paymentIntentId = $request->get('paymentIntentId');
        $sessionId = $request->get('sessionId');
        $productId = $request->get('productId');
        $affiliateId = $request->get('affiliateId');
        $cartId = $request->get('cartId');
        $invoiceId = $request->get('invoiceId');

        if(empty($paymentIntentId)){
            throw new Exception('Payment intent id is missing!');
        }

        $invoice = GeneralInvoice::findOrFail($invoiceId);

        Stripe::setApiKey(config('services.stripe.secret_key'));

        if($invoice->team->whiteLabel) {
            $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId, [
                'stripe_account' => $invoice->team->whiteLabel->connectedAccount?->stripe_account_id
            ]);
        }else {
            $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId);
        }

        $cart = CartForm::findOrFail($cartId);
        $invoice = GeneralInvoice::findOrFail($invoiceId);

        if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
            $product = Product::findOrFail($productId);
            if($invoice->team->whiteLabel) {
                $session = Session::retrieve($sessionId, [
                    'stripe_account' => $invoice->team->whiteLabel->connectedAccount?->stripe_account_id
                ]);
            }else {
                $session = Session::retrieve($sessionId);
            }

            // attach invoice to cart form
            $cart->invoice()->associate($invoice);
            $cart->save();

            $invoice->setStatusPaid();

            $invoice->unpaidBills()->update(['status' => BillingStatus::Paid]);

            // affiliate commission
            if ($invoice->type->isAffiliatable()) {
                if ($invoice?->team?->promoter && !$invoice->promoter_id) {
                    if(!$paymentIntent->status == PaymentIntent::STATUS_SUCCEEDED){
                        Log::warning('Failed to take payment. Affiliate commission unable to generate', [
                            'invoice_id' => $invoice->id,
                            'invoice_number' => $invoice->invoice_number,
                            'payment_intent_id' => $paymentIntent->id,
                            'payment_intent_status' => $paymentIntent->status,
                        ]);
                    }else{
                        $invoice->handleAffiliateCommission();
                    }
                }
            }

            // attach package to team
            team()->attachProduct($product);

            // update the cart
            $cart->setStatusCompleted();
            $cart->update([
                'meta->stripe->checkout_session->payment_method_types' => $session->payment_method_types,
                'meta->stripe->checkout_session->customer' => $session->customer,
                'meta->stripe->checkout_session->customer_email' => $session->customer_email,
            ]);

            // send email
            $invoice->team->sendInvoiceEmail($invoice);
        }else{
            // affiliate log
            Log::warning('Failed to take payment. Affiliate commission unable to generate', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'payment_intent_id' => $paymentIntent->id,
                'payment_intent_status' => $paymentIntent->status,
            ]);
        }

        $data = $request->get('requestData');
        if(Arr::get($data, 'serviceType') === ServerModificationActions::RESIZE->value && $invoice->isPaid()){
            $model = Arr::get($data, 'sourceModel')::findOrFail(Arr::get($data, 'sourceId'));
            return (new ServerResize($model))->modify($request);
        }

        if(Arr::get($data, 'serviceType') === ServerModificationActions::BACKUP->value && $invoice->isPaid()){
            $model = Arr::get($data, 'sourceModel')::findOrFail(Arr::get($data, 'sourceId'));
            return (new ServerProviderBackup($model))->modify($request);
        }

        return redirect()->route('user.detailedInvoices', $invoiceId)
            ->with('flash', [
                'message' => $invoice->isPaid() ? 'Payment completed successfully' : 'Failed to take payment. Please try again',
                'type' => $invoice->isPaid() ? 'success' : 'error'
            ]);
    }

    /**
     * @throws GuzzleException
     * @throws ApiErrorException
     */
    public function confirmSubscriptionProduct3DSecurePayment(Request $request)
    {
        $paymentIntentId = $request->get('paymentIntentId');
        $affiliateId = $request->get('affiliateId');
        $invoiceId = $request->get('invoiceId');
        $nextRoute = $request->get('nextRoute');
        $routeParam = $request->get('routeParam');
        $subscriptionProductId = $request->get('subscriptionProductId');
        $stripeSubscriptionId = $request->get('stripeSubscriptionId');

        if(empty($paymentIntentId)){
            throw new Exception('Payment intent id is missing!');
        }

        if(!$subscriptionProductId || !$stripeSubscriptionId){
            throw new Exception('Subscription ID is missing!');
        }

        Stripe::setApiKey(config('services.stripe.secret_key'));

        $subscriptionProduct = SubscriptionProduct::findOrFail($subscriptionProductId);

        if(currentWhiteLabel()) {
            $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId, [
                'stripe_account' => currentWhiteLabel()->connectedAccount?->stripe_account_id
            ]);

            $stripeSubscription = \Stripe\Subscription::retrieve($stripeSubscriptionId, [
                'stripe_account' => currentWhiteLabel()->connectedAccount?->stripe_account_id
            ]);
        }else{
            $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId);

            $stripeSubscription = \Stripe\Subscription::retrieve($stripeSubscriptionId);
        }

        $model = null;
        if($routeParam && isset($routeParam['model'])){
            $model = $routeParam['model']::findOrFail($routeParam['id']);
        }

        $invoice = GeneralInvoice::findOrFail($invoiceId);

        if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
            $invoice->setStatusPaid();

            // make bill as paid
            $unpaidBills = $invoice->unpaidBills()->get();
            $unpaidBills->each(function ($bill) {
                $bill->setStatusPaid();
            });

            // affiliate commission
            if ($invoice->type->isAffiliatable()) {
                if ($invoice?->team?->promoter && !$invoice->promoter_id) {
                    if(!$paymentIntent->status == PaymentIntent::STATUS_SUCCEEDED){
                        Log::warning('Failed to take payment. Affiliate commission unable to generate', [
                            'invoice_id' => $invoice->id,
                            'invoice_number' => $invoice->invoice_number,
                            'payment_intent_id' => $paymentIntent->id,
                            'payment_intent_status' => $paymentIntent->status,
                        ]);
                    }else{
                        $invoice->handleAffiliateCommission();
                    }
                }
            }

            // Utm sales tracking
            if(Utm::ENABLE_UTM_CALCULATION) {
                if (!($paymentIntent->status == PaymentIntent::STATUS_SUCCEEDED)) {
                    Log::warning('Failed to take payment. Utm sales tracking was not generated', [
                        'invoice_id' => $invoice->id,
                        'invoice_number' => $invoice->invoice_number,
                        'payment_intent_id' => $paymentIntent->id,
                        'payment_intent_status' => $paymentIntent->status,
                    ]);
                } else {
                    $invoice->team->addUtmSourceSell($invoice->amount, $invoice);
                }
            }

            // send email
            $invoice->team->sendInvoiceEmail($invoice);

            // check if this is whitelabel purchase
            if($invoice->isWhitelabelPurchase()){
                if($model instanceof WhiteLabel){
                    // update whitelabel payment status
                    $model->update([
                        'payment_info' => [
                            'invoice_id' => $invoice->id,
                            'payment_intent_id' => $paymentIntent->id,
                            'payment_status' => PaymentIntent::STATUS_SUCCEEDED,
                        ],
                        'billing_activated_from' => now()
                    ]);
                }

                $invoice->cartForm()->update([
                    'status' => CartFormStatuses::Paid,
                    'meta->payment_status' => PaymentIntent::STATUS_SUCCEEDED
                ]);

                // attach subscription product to team
                $subscriptionProduct->teams()->attach(team(), [
                    'team_id' => team()->id,
                    'subscription_product_id' => $subscriptionProduct->id,
                    'stripe_subscription_id' => $stripeSubscription->id,
                ]);
            }

            return redirect()->route($nextRoute ?? 'user.detailedInvoices', $model ?? $routeParam ?? $invoiceId)
                ->with('flash', [
                    'message' => 'Payment completed successfully'
                ]);
        }else{
            // affiliate log
            Log::warning('Failed to take payment. Affiliate commission unable to generate', [
                'invoice_id' => $invoice->id,
                'invoice_number' => $invoice->invoice_number,
                'payment_intent_id' => $paymentIntent->id,
                'payment_intent_status' => $paymentIntent->status,
            ]);

            return redirect()->route($nextRoute ?? 'user.detailedInvoices', $model ?? $routeParam ?? $invoiceId)
                ->with('flash', [
                    'message' => 'Failed to take payment. Please try again',
                    'type' => 'error'
                ]);
        }
    }
}
