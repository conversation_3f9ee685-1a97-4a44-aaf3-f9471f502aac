<?php

namespace App\Http\Controllers\API;

use App\Enums\ServerStatus;
use App\Enums\SiteCloneStatus;
use App\Enums\SiteStatus;
use App\Enums\SiteType;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\PlansEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\SiteCloneDatabaseStoreRequest;
use App\Models\AutoSiteClone;
use App\Models\ManualSiteMigration;
use App\Models\Server;
use App\Models\Site;
use App\Models\AutoSiteMigration;
use App\Models\SiteClone;
use App\Models\SiteMigration;
use App\Models\SslCertificate;
use App\Models\Task;
use App\Rules\DatabasePassword;
use App\Rules\SiteNameRule;
use App\Rules\SiteUserRule;
use App\Rules\ValidateCommaSeperatedMails;
use App\Scripts\CreateSiteDirectory;
use App\Scripts\TestDatabaseConnection;
use App\Services\Database\DatabaseProvider;
use App\Services\Migration\MigrationConnector;
use App\Models\PhpVersion;
use App\Services\XcloudProduct\ActiveOffers;
use App\Validator\AliasesValidation;
use App\Validator\Domain;
use App\Validator\ReservedDomainValidationForAdditionalDomains;
use Arr;
use Exception;
use Faker\Factory;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use function PHPUnit\Framework\isEmpty;

class SiteCloneController extends Controller
{
    /**
     * @throws AuthorizationException
     */
    function storeDestination(Request $request, $siteClone = null)
    {
        $data = $this->validate($request, ['destination_server' => ['required', 'integer', 'exists:servers,id,team_id,'.team()->id]]);
        $server = Server::findOrFail($data['destination_server']);

        #white label can't clone site
        abort_if(isWhiteLabel() || (team()->ownedWhiteLabel && $server->team->white_label_id == team()->ownedWhiteLabel?->id),404);

        if ($siteClone) {
            $siteClone = SiteClone::findOrFail($siteClone);
            $this->authorize('hasAccessToModifyCloneDestination', $siteClone);
        }

        $sourceSite = Site::findOrFail($request->get('site'));

        if ($sourceSite){
            $this->authorize('view', $sourceSite);
        }

        // need to check if selected server has enough storage to clone the site
        $task = $this->eligibleForCloning($server, $sourceSite);

        if($task->output !== 'Server has enough storage to clone site'){
            return redirect()->back()->with('response', [
                'message' => $task->output,
                'status' => ServerStatus::LOW_STORAGE
            ]);
        }

        $this->authorize('view', $server);

        $latestBill = $server->bills()->where('service', $server->getDefaultBillingService())->orderBy('id', 'desc')->first();

        if ($latestBill && $latestBill->billingPlan->name === PlansEnum::Free) {
            $quantityAvailed = $server->sites()->count(); //$server->team->availed_offers[BillingServices::Site->value] ?? 0;

            $canAvailInTotal = ActiveOffers::matchGetQuantity(BillingServices::Site, $latestBill->billingPlan?->name);

            if ($quantityAvailed >= $canAvailInTotal) {
                return redirect()->back()->with('flash', [
                    'type' => 'warning',
                    'message' => $canAvailInTotal.' site limit reached, please consider upgrading the server bill from free to paid plan to avail more sites.'
                ]);
            }
        }

        if ($siteClone) {
            $siteClone->update([
                'server_id' => $server->id,
                'form->'.SiteClone::DESTINATION => ['server' => $server->id],
                'form->next_step' => SiteClone::DOMAINS,
            ]);
           $route =  $siteClone->getStepRouteNames()[SiteClone::DOMAINS];
        } else {
            $route = app(AutoSiteClone::class)->getStepRouteNames()[SiteClone::DOMAINS];
        }

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteClone::DESTINATION,
            'status' => SiteCloneStatus::FILLING->value,
        ]) : redirect()->route($route, [
            'server' => $server->id,
            'site' => $request->get('site') ?? 'new',
            'siteClone' => $siteClone ?? 'new'
        ]);
    }

    public function eligibleForCloning(Server $server, Site $site): Task
    {
        #white label can't clone site
        abort_if(isWhiteLabel() || (team()->ownedWhiteLabel && $server->team->white_label_id == team()->ownedWhiteLabel?->id),404);

        if($site->server->ipAddress() !== $server->ipAddress()) {
            // clone to remote
            // check how much storage the source site is occupying
            $task = $site->checkSiteStorage();
            if($task->successful()) {
                $sourceSiteStorage = $task->output;
                // check if destination server has enough storage to clone the site
                $task = $server->checkServerHasRequiredStorageToCloneSite($site, $sourceSiteStorage);
            }
        }else{
            $task = $site->checkSiteStorageAvailability( oldSiteDomain: $site->name);
        }

        return $task;
    }


    /**
     * @throws AuthorizationException
     * @throws BindingResolutionException
     */
    function storeAutoDomain(Request $request, Server $server)
    {
        #white label can't clone site
        abort_if(isWhiteLabel() || (team()->ownedWhiteLabel && $server->team->white_label_id == team()->ownedWhiteLabel?->id),404);
        $this->authorize('addSite', $server);

        $domainName = '';
        if($request->get('domain_parking_method') !== 'staging_env'){
            $domainName = $request->get('domain_name');
        }

        if($request->get('domain_active_on_cloudflare')){
            $request['ssl_provider'] = SslCertificate::PROVIDER_CLOUDFLARE;
        }

        $request->request->add(['domain_name' => $domainName ?? null]); // updating domain_name for request validation

        // Convert the array into a string suitable for the regular expression
        $reservedDomainsPattern = implode('|', array_map('preg_quote', getReservedDomains()));

        $validationRules = [
            'domain_parking_method' => 'required|string|in:staging_env,migrate_into_new_domain',
            'ssl_provider' =>[
                'nullable', 'in:xcloud,custom,staging,cloudflare', function ($attribute, $value, $fail) use ($server, $request, $domainName) {
                    if ($value == 'xcloud' && !$server->hasDnsRecord($domainName)) {
                        $fail(__('dns.verification_failed'));
                    }
                }
            ],
            'ssl_certificate' => 'required_if:ssl_provider,custom|string|nullable',
            'ssl_private_key' => 'required_if:ssl_provider,custom|string|nullable',
            'additional_domains' => [
                'nullable',
                new AliasesValidation,
                $request->get('domain_parking_method') === 'migrate_into_new_domain' ? new ReservedDomainValidationForAdditionalDomains : null
            ]
        ];


        if(request('siteClone') !== 'new'){
            $siteClone = SiteClone::findOrFail(request('siteClone'));

            if($siteClone->status === SiteCloneStatus::FILLING){
                $validationRules['domain_name'] = [
                    'required_if:domain_parking_method,migrate_into_new_domain',
                    'nullable',
                    $request->get('domain_parking_method') === 'migrate_into_new_domain'
                        ? 'not_regex:/\b(?:' . $reservedDomainsPattern . ')\b/'
                        : null,
                    ...SiteNameRule::rules(serverId: $server->id, ignoreSiteId: optional($siteClone)->site_id),
                ];
            }else{
                $validationRules['domain_name'] = [
                    'required_if:domain_parking_method,migrate_into_new_domain',
                    'nullable',
                    $request->get('domain_parking_method') === 'migrate_into_new_domain'
                        ? 'not_regex:/\b(?:' . $reservedDomainsPattern . ')\b/'
                        : null,
                    ...SiteNameRule::rules(serverId: $server->id, ignoreSiteId: optional($siteClone)->site_id),
                ];
            }
        }else{
            $duplicateCount = Site::query()
                        ->whereServerId($server->id)
                        ->whereName($request->get('domain_name'))
                        ->count();

            if($duplicateCount){
                throw ValidationException::withMessages([
                    'domain_name' => ['This domain already exists on this server: ' . $server->name]
                ]);
            }else if($request->get('domain_parking_method') === 'migrate_into_new_domain' && empty($request->get('domain_name'))){
                $validationRules['domain_name'] = [
                    'required_if:domain_parking_method,migrate_into_new_domain',
                    $request->get('domain_parking_method') === 'migrate_into_new_domain'
                        ? 'not_regex:/\b(?:' . $reservedDomainsPattern . ')\b/'
                        : null,
                    ...SiteNameRule::rules(serverId: $server->id),
                ];
            }
        }

        $data = $this->validate($request, $validationRules);

        if($data['ssl_provider'] === 'xcloud'){
            $data['ssl_certificate'] = null;
            $data['ssl_private_key'] = null;
        }

        if(request('siteClone') !== 'new'){
            $cloneFrom = Site::findOrFail(Arr::get(SiteClone::findOrFail(request('siteClone'))->meta, 'cloneInfo.cloneFromSiteId'));
        }else{
            $cloneFrom = Site::findOrFail(request('site'));
        }

        $this->authorize('view', $cloneFrom);

        if(request('siteClone') === 'new'){
            # create meta for cloning site
            $meta = [
                'cloneInfo' =>[
                    'cloneFromSiteId' => $cloneFrom->id,
                    'cloningStatus' => SiteCloneStatus::FILLING->value,
                ]
            ];
            # check if the source site is multisite or not
            if ($cloneFrom->isMultiSite()){
                $meta['enable_multisite'] = true;
                $meta['multisite_subdomain'] = $cloneFrom->getMeta('multisite_subdomain',false);
            }
            # check if the source site has 7g firewall enabled
            if ($cloneFrom->getMeta('enable_7g_firewall',false)){
                $meta['enable_7g_firewall'] = true;
            }

            # check if the source site has redis object cache enabled
            if ($cloneFrom->hasRedisObjectCaching()){
                $meta['has_redis_object_caching'] = true;
            }

            $site = Site::create([
                'name' => 'clone@'.$server->name_slug,
                'title' => 'clone@'.$server->name_slug,
                'type' => SiteType::WORDPRESS,
                'server_id' => $server->id,
                'status' => SiteStatus::CLONE_INIT,
                'php_version' => PhpVersion::DEFAULT,
                'meta' => $meta,
                'redis_password' => $server->isRedisSeven() ? Str::random(32) : null
            ]);

            // update site domain with staging site name
            if($request->get('domain_parking_method') === 'staging_env') {
                $domainName = $site->generateStagingSiteName();
                $site->updateDomain($domainName);
            }

            $siteClone = AutoSiteClone::create([
                'site_id' => $site->id,
                'source_site_id' => $cloneFrom->id,
                'type' => SiteClone::DEFAULT,
                'user_id' => auth()->id(),
                'team_id' => team()->id,
                'server_id' => $server->id,
                'existing_site_url' => $cloneFrom->name,
                'domain_name' => $domainName,
                'form->'. SiteClone::DOMAINS => $data,
                'form->next_step' => SiteClone::DATABASE,
            ]);

            $siteClone->saveMeta('cloneInfo', [
                'cloneFromSiteId' => $cloneFrom->id
            ]);

            if($request->get('domain_active_on_cloudflare')){
                $site->update([
                    'ssl_provider' => canGenerateSslCertificateOnCloudflare($request->get('domain_name'))
                        ? SslCertificate::PROVIDER_CLOUDFLARE
                        : SslCertificate::PROVIDER_XCLOUD,
                    'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                    'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                    'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                    'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                    'meta->cloudflare_integration->site_name' => $request->get('site_name'),
                ]);
            }
        }else{
            $siteClone = SiteClone::findOrFail(request('siteClone'));

            // update site domain with staging site name
            if($request->get('domain_parking_method') === 'staging_env') {
                $domainName = $siteClone->site->generateStagingSiteName();
                $siteClone->site->updateDomain($domainName);
            }

            $siteClone->update([
                'existing_site_url' => $cloneFrom->name,
                'domain_name' => $domainName,
                'form->'. SiteClone::DOMAINS => $data,
                'form->next_step' => SiteClone::DATABASE,
            ]);
        }

        $siteClone->updateSite($cloneFrom);

        if($request->get('domain_active_on_cloudflare')){
            $siteClone->site->update([
                'ssl_provider' => canGenerateSslCertificateOnCloudflare($request->get('domain_name'))
                    ? SslCertificate::PROVIDER_CLOUDFLARE
                    : SslCertificate::PROVIDER_XCLOUD,
                'meta->cloudflare_integration->domain_active_on_cloudflare' => $request->get('domain_active_on_cloudflare'),
                'meta->cloudflare_integration->account_id' => $request->get('cloudflare_account_id'),
                'meta->cloudflare_integration->zone_id' => $request->get('cloudflare_zone_id'),
                'meta->cloudflare_integration->subdomain' => $request->get('subdomain'),
                'meta->cloudflare_integration->site_name' => $request->get('site_name'),
            ]);
        }

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteClone::DOMAINS,
            'status' => $siteClone->status,
        ]) : redirect()->route('site.clone.database', [
            'server' => $siteClone->server->id,
            'site' => $siteClone->site->id,
            'siteClone' => $siteClone->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    function storeSettings(Request $request, SiteClone $siteClone)
    {
        $this->authorize('canSetupCloneSettings', $siteClone);
        #white label can't clone site
        abort_if(team()->ownedWhiteLabel && $siteClone->server->team->white_label_id == team()->ownedWhiteLabel?->id,404);
        $data = Validator::make(
            $request->all(),
            [
                'php_version' => ['required', PhpVersion::asRule()],
                'site_user' => SiteUserRule::rules($siteClone->server_id, $siteClone->site_id),
            ],
            $messages = [
                'site_user.regex' => 'The value must start with a lowercase letter, followed by lowercase letters, digits, or underscores.'
            ]
        )->validate();

        // update site clone
        $siteClone->update([
            'form->'.SiteClone::SETTINGS => $data,
            'form->next_step' => SiteClone::CONFIRM,
        ]);


        // update site
        $siteClone->site->update([
            'php_version' => $data['php_version'],
            'site_user' => $data['site_user'],
        ]);

        if ($request->filled('deploy_script')) {
            $siteClone->site->saveMeta('deploy_script', $request->input('deploy_script'));
        }

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteClone::SETTINGS,
            'status' => $siteClone->status,
        ]) : redirect()->route('site.clone.confirm', [
            'server' => $siteClone->server->id,
            'site' => $siteClone->site->id,
            'siteClone' => $siteClone->id,
        ]);
    }

    public function storeDatabase(SiteCloneDatabaseStoreRequest $request, AutoSiteClone $siteClone)
    {
        #white label can't clone site
        abort_if(team()->ownedWhiteLabel && $siteClone->server->team->white_label_id == team()->ownedWhiteLabel?->id,404);
        $this->authorize('canSetupCloneDatabase', $siteClone);
        $sourceSite = Site::find(Arr::get($siteClone->site->meta, 'cloneInfo.cloneFromSiteId'));
        $data = $this->validate($request, [
            'database_provider' => ['required', DatabaseProvider::asRule(!$sourceSite->hasDatabase())],
            'database_name' => ['nullable', 'max:32','lowercase','alpha_dash'],
            'database_user' => ['nullable', 'max:32','lowercase','alpha_dash'],
            'database_password' => DatabasePassword::rules(),
            'database_host' => ['required_if:database_provider,custom', 'nullable', 'max:256'],
            'database_port' => ['required_if:database_provider,custom', 'nullable', 'max:5'],
        ],[
            ...DatabasePassword::messages()
        ]);
        $data['database_password'] = $data['database_password'] ?? DatabasePassword::randomPassword();
        if ($request->input('database_provider') === DatabaseProvider::CUSTOM) {
            $testCustomDatabaseConnection = $siteClone->server->runInline(new TestDatabaseConnection(
                $data['database_host'],
                $data['database_port'],
                $data['database_name'],
                $data['database_user'],
                $data['database_password']
            ))->successful();

            if (!$testCustomDatabaseConnection) {
                throw ValidationException::withMessages([
                    'db_connection_error' => 'Unable to connect to database. Please check your credentials.',
                ]);
            }
        }

        // update site clone
        $siteClone->update([
            'form->'.SiteClone::DATABASE => $data,
            'form->next_step' => SiteClone::SETTINGS,
        ]);
        // update site
        $siteClone->site->update([
            'database_provider' => $data['database_provider'],
            'database_user' => $data['database_user'],
            'database_name' => $data['database_name'],
            'database_password' => $data['database_password'],
        ]);
        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteClone::DATABASE,
            'status' => $siteClone->status,
        ]) : redirect()->route('site.clone.settings', [
            'server' => $siteClone->server->id,
            'site' => $siteClone->site->id,
            'siteClone' => $siteClone->id,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function confirmClone(Request $request, SiteClone $siteClone)
    {
        #white label can't clone site
        abort_if(team()->ownedWhiteLabel && $siteClone->server->team->white_label_id == team()->ownedWhiteLabel?->id,404);
        $this->authorize('cloneConfirmation', $siteClone);

        $data = $this->validate($request, [
            'notification_mails' => ['nullable', new ValidateCommaSeperatedMails],
        ]);

        if (isset($data['notification_mails'])) {
            $data['notification_mails'] = is_array($data['notification_mails'])
                ? $data['notification_mails']
                : explode(',', $data['notification_mails']);
        }

        $siteClone->update([
            'form->'.SiteMigration::CONFIRM => $data,
            'notification_mails' => $data['notification_mails'] ?? null,
        ]);

        $siteClone->site->saveMeta('disable_search_engine_visibility', $siteClone->site->environment !== Site::PRODUCTION);

        $siteClone->startCloning();

        return $request->expectsJson() ? response()->json([
            'completed_step' => SiteClone::CONFIRM,
            'status' => $siteClone->status,
        ]) : redirect()->route('site.progress', [
            'server' =>$siteClone->server->id,
            'site' => $siteClone->site->id,
        ]);
    }
}
