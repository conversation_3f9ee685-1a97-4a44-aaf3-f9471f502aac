<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\GoogleDriveStorageProviderRequest;
use App\Models\StorageProvider;
use App\Models\WhiteLabel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;

class GoogleDriveController extends Controller
{
    public function googleDriveAuthorize(GoogleDriveStorageProviderRequest $request)
    {
        $this->authorize('manageStorageProvider', team());
        if(team()->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }

        $clientId = config('services.google_drive.client_id');
        $redirectUri = config('services.google_drive.redirect_uri');
        $scope = urlencode('https://www.googleapis.com/auth/drive.file');
        $stateData = [
            'team_id' => team()->id,
            'label' => $request->get('label'),
            'user_id' => user()->id
        ];
        if (currentWhiteLabel()){
            $stateData['white_label_id'] = currentWhiteLabel()->id;
        }
        $state = urlencode(json_encode($stateData));
        $authUrl = "https://accounts.google.com/o/oauth2/auth?response_type=code&client_id=$clientId&redirect_uri=$redirectUri&scope=$scope&state=$state&access_type=offline&prompt=consent";

        return response()->json($authUrl);
    }

    /**
     * @throws \Exception
     */
    public function googleDriveCallback(Request $request)
    {
        // Ensure the 'code' query parameter exists
        if (!$code = $request->query('code')) {
            return redirect()->route('user.storage-provider')->with('flash', [
                'message' => 'Authorization Failed.',
                'type' => 'error',
            ]);
        }

        // Decode and validate the state parameter
        $stateData = json_decode($request->query('state', '{}'));

        $label = $stateData->label ?? null;
        $teamId = $stateData->team_id ?? null;
        $userId = $stateData->user_id ?? null;
        $whiteLabelId = $stateData->white_label_id ?? null;

        // Redirect to the proper white-label domain if needed
        if ($whiteLabelId) {
            $whiteLabelDomain = WhiteLabel::query()->where('id', $whiteLabelId)->value('domain');

            if ($whiteLabelDomain && $request->getHost() !== $whiteLabelDomain) {
                $redirectUrl = Str::replace(
                    search: $request->getHost(),
                    replace: $whiteLabelDomain,
                    subject: $request->fullUrl()
                );
                return redirect()->away($redirectUrl);
            }
        }

        // Exchange code for access/refresh tokens
        $response = StorageProvider::tokenResponse(code: $code);

        if (!$response->successful()) {
            return redirect()->route('user.storage-provider')->with('flash', [
                'message' => 'Google Drive Linked Failed.',
                'type' => 'error'
            ]);
        }

        $tokens = $response->json();

        // Create storage provider
        $storageProvider = StorageProvider::create([
            'provider'       => StorageProvider::GOOGLE_DRIVE,
            'access_key_id'  => $tokens['access_token'],
            'secret_key'     => $tokens['refresh_token'],
            'bucket'         => $label,
            'team_id'        => $teamId,
            'user_id'        => $userId,
        ]);

        // Fetch and update Drive folder ID
        $directoryId = $storageProvider->getDriveFolderId();
        $storageProvider->update(['endpoint' => $directoryId]);

        return redirect()->route('user.storage-provider')->with('flash', [
            'message' => 'Google Drive Linked Successfully.',
        ]);
    }


    /**
     * @throws \Exception
     */
    public function googleDriveUpdate(GoogleDriveStorageProviderRequest $request, StorageProvider $provider)
    {
        $this->authorize('manageStorageProvider', team());
        if(team()->isTrailMode()){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'You need to upgrade your plan to access this feature.',
                'type' => 'error'
            ]);
        }
        DB::beginTransaction();
        try{
            $provider->update([
                'bucket' => $request->get('label')
            ]);
            $provider->renameDirectory($provider->endpoint,$request->get('label'));
            DB::commit();
            return redirect()->back()->with('flash', ['message' => 'Storage Provider Updated Successfully.']);
        }catch (\Exception $e){
            DB::rollBack();
            return redirect()->back()->with('flash', ['message' => 'Failed to update storage provider.', 'type' => 'error']);
        }
    }


    /**
     * @throws \Exception
     */
    public function refreshAccessToken(string $hashId)
    {
        $id = hashid_decode($hashId);
        $storageProvider = StorageProvider::find($id);
        try{
            $access_token =  $storageProvider->getAccessToken();
            return response()->json(['access_token' => $access_token]);
        }catch (\Exception $e){
            return response()->json(['error' => $e->getMessage()]);
        }

    }
}
