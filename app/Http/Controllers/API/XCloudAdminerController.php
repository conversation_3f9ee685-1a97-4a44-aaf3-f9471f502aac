<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Site;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class XCloudAdminerController extends Controller
{
    function redirect(Request $request, Site $site)
    {
        $this->authorize('database', $site);

        // prefer phpMyAdmin if available
        if ($request->has('prefer_pma') && $site->server->getPhpMyAdminSite()) {
            return redirect()->route('site.phpmyadmin-login', $site);
        }

        // Block access for Node.js applications
        if ($site->isNodeApp()) {
            return redirect()->route('site.database', [$site->server, $site])->with('flash', [
                'type' => 'error',
                'message' => 'Adminer is not compatible with Node.js applications. Please use phpMyAdmin instead.'
            ]);
        }

        // Check if phpMyAdmin is enabled on this server
        $enable_adminer = $site->getMeta('enable_adminer', false);
        // abort_unless($enable_adminer, 403, 'Adminer is not enabled');

        if(!$enable_adminer){
            return redirect()->route('site.database', [$site->server, $site])->with('flash', [
                'type' => 'error',
                'message' => 'Adminer is not enabled. Please enable it first.'
            ]);
        }

        if (!$site->server->isConnected()) {
            return redirect()->back()->with('flash', [
                'type' => 'error',
                'message' => 'Server is not connected'
            ]);
        }
        $secret = Str::random(30);

        $token = encrypt(json_encode([
            'token' => $secret,
            'site' => $site->id,
            'user' => user()->id,
        ]));

        Cache::put('XCloudAdminer:' . user()->id . ':' . $site->id . ':', $secret, 60);
        Log::info('XCloudAdminer Token: ' . $token);
        return redirect()->away($site->getSiteUrlAttribute() . '/'.$enable_adminer.'?xcloud-token=' . $token);
    }

    function verify(Request $request)
    {
        $token = json_decode(decrypt(request('token')), true);
        Log::info('XCloudAdminer Verify: ' . json_encode($token));
        if (empty($token['token']) || empty($token['site']) || empty($token['user'])) {
            return ['success' => false];
        }
        $site = Site::find($token['site']);
        $user = User::find($token['user']);

        if (is_null($site) || is_null($user) || Gate::forUser($user)->denies('database', $site)) {
            return ['success' => false];
        }
        $_token = Cache::get('XCloudAdminer:' . $user->id . ':' . $site->id . ':');
        $tries = Cache::get("{$_token}:try",0);
        if ($_token == $token['token'] && $tries <2) {
            Cache::put("{$_token}:try",$tries+1,30);
            $data = [
                'db_name' => $site->database_name,
                'db_user' => $site->database_user,
                'db_password' => $site->database_password
            ];
            if ($tries == 1){
                Cache::forget("{$_token}:try");
                Cache::forget('XCloudAdminer:' . $user->id . ':' . $site->id . ':');
            }
            return [
                'success' => true,
                'data' => $data,
            ];
        }
        return ['success' => false, 'message' => 'Something went wrong'];
    }

    function failed(Request $request, Site $site)
    {
        return redirect()->route('site.index')->with('flash', [
            'type' => 'error',
            'message' => 'Remote Login failed'
        ]);
    }
}
