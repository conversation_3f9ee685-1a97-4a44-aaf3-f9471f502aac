<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Server;
use App\Models\Site;
use App\Models\Task;
use App\Scripts\ClearLogScript;
use App\Scripts\LogViewerScript;
use App\Scripts\LogViewerScriptUpdated;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class LogViewerController extends Controller
{
    /**
     * @param  Site  $site
     * @param  Request  $request
     * @return JsonResponse
     * @throws Exception
     */
    public function siteLog(Site $site, Request $request): JsonResponse
    {
        $this->authorize('logs', $site);

        $request->validate([
            'type' => 'required|in:lsws,nginx,access,error,wp-debug-log,laravel-log,pm2-log'
        ]);

        $logFile = match ($request->get('type')) {
            'nginx' => $site->manager()->getNginxLogPath(),
            'lsws' => $site->manager()->getLswsLogPath(),
            'access' => $site->manager()->getAccessLogPath(),
            'error' => $site->manager()->getErrorLogPath(),
            'pm2-log', 'wp-debug-log', 'laravel-log' => $site->manager()->geLogFilePaths(),
        };

        $logs = str($site->runInline(new LogViewerScript($logFile))->output);

        if ($request->get('type') == 'laravel-log' && $site->isLaravel()) {
            $logs = $logs->explode(PHP_EOL)->filter();
        } else {
            $logs = $logs->explode(PHP_EOL)->reverse()->filter();
        }

        return response()->json([
            'file' => $logFile,
            'log' => $logs->values()
        ]);
    }

    /**
     * @param  Server  $server
     * @param  Request  $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function nginxLog(Server $server, Request $request): JsonResponse
    {
        $this->authorize('manageLogs', $server);

        $path = $this->getServerLogFilePath($request, $server);

        $logs = str($server->runInline(new LogViewerScript($path))->output)->explode(PHP_EOL)->reverse()->filter();

        return response()->json([
            'file' => $path,
            'log' => $logs->values(),
        ]);
    }

    public function clearServerLog(Server $server, Request $request): JsonResponse
    {
        $this->authorize('clear-logs', $server);

        $path = $this->getServerLogFilePath($request, $server);

        $task = $server->runInline(new ClearLogScript($path));

        if(!$task->successful()){
            return response()->json([
                'file' => $path,
                'log' => str($server->runInline(new LogViewerScript($path))->output)->explode(PHP_EOL)->reverse()->filter()->values(),
                'status' => Task::STATUS_FAILED
            ]);
        }

        return response()->json([
            'file' => $path,
            'status' => Task::STATUS_FINISHED,
            'log' => []
        ]);
    }

    /**
     * @throws Exception
     */
    public function clearSiteLog(Site $site, Request $request): JsonResponse
    {
        $this->authorize('clear-logs', $site);

        $request->validate([
            'type' => 'required|in:lsws,nginx,access,error,wp-debug-log,laravel-log,pm2-log'
        ]);

        $logFile = match ($request->get('type')) {
            'nginx' => $site->manager()->getNginxLogPath(),
            'lsws' => $site->manager()->getLswsLogPath(),
            'access' => $site->manager()->getAccessLogPath(),
            'error' => $site->manager()->getErrorLogPath(),
            'pm2-log', 'wp-debug-log', 'laravel-log' => $site->manager()->geLogFilePaths(),
        };

        $task = $site->runInline(new ClearLogScript($logFile));

        if(!$task->successful()){
            return response()->json([
                'file' => $logFile,
                'log' => str($site->runInline(new LogViewerScript($logFile))->output)->explode(PHP_EOL)->reverse()->filter()->values(),
                'status' => Task::STATUS_FAILED
            ]);
        }

        return response()->json([
            'file' => $logFile,
            'status' => Task::STATUS_FINISHED,
            'log' => []
        ]);
    }

    /**
     * @param  Request  $request
     * @param  Server  $server
     * @return string
     */
    private function getServerLogFilePath(Request $request, Server $server): string
    {
        $request->validate([
            'type' => 'required|in:access,error,fail2ban,auth_log'
        ]);

        $path = ($server->stack->isNginx() ? '/var/log/nginx' : '/var/log/lsws')."/*-{$request->get('type')}.log";

        if ($request->get('type') === 'fail2ban') {
            $path = '/var/log/fail2ban.log';
        }

        if ($request->get('type') === 'auth_log') {
            $path = ' /var/log/auth.log';
        }

        return $path;
    }
}
