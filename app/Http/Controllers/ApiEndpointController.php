<?php

namespace App\Http\Controllers;

use App\Models\ApiEndpoint;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class ApiEndpointController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = ApiEndpoint::query();

        // Filter by status if provided
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('url', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $endpoints = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('api-endpoints.index', compact('endpoints'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        return view('api-endpoints.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'nullable|string|max:255',
            'url' => 'required|url|max:2048',
            'api_key' => 'required|string|max:1000',
            'active' => 'boolean',
            'count' => 'integer|min:0',
            'description' => 'nullable|string|max:1000',
        ]);

        ApiEndpoint::create($validated);

        return redirect()->route('api-endpoints.index')
            ->with('success', 'API Endpoint created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ApiEndpoint $apiEndpoint): View
    {
        return view('api-endpoints.show', compact('apiEndpoint'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ApiEndpoint $apiEndpoint): View
    {
        return view('api-endpoints.edit', compact('apiEndpoint'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ApiEndpoint $apiEndpoint): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'nullable|string|max:255',
            'url' => 'required|url|max:2048',
            'api_key' => 'required|string|max:1000',
            'active' => 'boolean',
            'count' => 'integer|min:0',
            'description' => 'nullable|string|max:1000',
        ]);

        $apiEndpoint->update($validated);

        return redirect()->route('api-endpoints.index')
            ->with('success', 'API Endpoint updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ApiEndpoint $apiEndpoint): RedirectResponse
    {
        $apiEndpoint->delete();

        return redirect()->route('api-endpoints.index')
            ->with('success', 'API Endpoint deleted successfully.');
    }

    /**
     * Toggle the active status of an endpoint.
     */
    public function toggleStatus(ApiEndpoint $apiEndpoint): RedirectResponse
    {
        $apiEndpoint->update(['active' => !$apiEndpoint->active]);

        $status = $apiEndpoint->active ? 'activated' : 'deactivated';
        return redirect()->back()
            ->with('success', "API Endpoint {$status} successfully.");
    }

    /**
     * Increment the count for an endpoint.
     */
    public function incrementCount(ApiEndpoint $apiEndpoint): RedirectResponse
    {
        $apiEndpoint->incrementCount();

        return redirect()->back()
            ->with('success', 'Count incremented successfully.');
    }

    /**
     * Reset the count for an endpoint.
     */
    public function resetCount(ApiEndpoint $apiEndpoint): RedirectResponse
    {
        $apiEndpoint->resetCount();

        return redirect()->back()
            ->with('success', 'Count reset successfully.');
    }
}
