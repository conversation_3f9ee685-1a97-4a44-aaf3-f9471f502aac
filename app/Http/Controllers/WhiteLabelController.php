<?php

namespace App\Http\Controllers;

use App\Enums\WhiteLabel\ClientStatus;
use App\Http\Resources\ServerResource;
use App\Http\Resources\SiteResource;
use App\Models\Server;
use App\Models\Site;
use App\Models\WhiteLabel;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Inertia\Inertia;

class WhiteLabelController extends Controller
{
    public function dashboard(Request $request)
    {
        $whiteLabel = currentWhiteLabel() ?? team()->ownedWhiteLabel;
        abort_unless($whiteLabel,Response::HTTP_NOT_FOUND);
        $this->authorize('view', $whiteLabel);
        $totalClients = user()->can('manageClients',$whiteLabel) ? $whiteLabel->clients()->count() : 0;
        $totalServers = user()->can('manageServers',$whiteLabel) ? $whiteLabel->servers()->count() : 0;
        $totalSites = user()->can('manageSites',$whiteLabel) ? $whiteLabel->sites()->count() : 0;
        $totalProducts = user()->can('manageProducts',$whiteLabel) ? $whiteLabel->products->count() : 0;

        $totalActiveClients = user()->can('manageClients',$whiteLabel) ? team()->ownedWhiteLabel?->clients()->where('is_active', ClientStatus::ACTIVE->value)->count() : 0;
        $totalInactiveClients = user()->can('manageClients',$whiteLabel) ? team()->ownedWhiteLabel?->clients()->where('is_active', ClientStatus::INACTIVE->value)->count() : 0;
        $totalInvoices = user()->can('manageBilling',$whiteLabel) ?  team()->ownedWhiteLabel?->invoices()->count() : 0;

        $servers = user()->can('manageServers',$whiteLabel) ? $whiteLabel->servers()
            ->withCount('sites')
            ->with([
                'tags:id,name,slug',
                'latestMonitor',
                'team:id,name,user_id,active_plan_id,white_label_id',
                'cloudProvider:id,name,provider',
                'user:id,name',
            ])
            ->orderBy('id', 'desc')
            ->take(6)
            ->get()
            ->map(function ($server) {
                $serverResource = ServerResource::globalResource($server);
                $user = $server->user;
                if ($user) {
                    $serverResource['user'] = [
                        'name' => $user->name,
                        'profile_photo_url' => $user->profile_photo_url,
                    ];
                }
                return $serverResource;
            }) : [];

        $sites = user()->can('manageSites',$whiteLabel) ? $whiteLabel->sites()
            ->with(['siteMigration','tags:id,name', 'server', 'server.monitors'=>fn($q)=>$q->latest('id')->take(1), 'server.team:id,name,user_id,active_plan_id', 'server.cloudProvider'])
            ->orderBy('id', 'desc')
            ->take(6)
            ->get()
            ->map(fn($site) => SiteResource::globalResource($site)) : [];

        return Inertia::render('WhiteLabel/Dashboard/Dashboard', [
            'totalClients' => $totalClients,
            'totalServers' => $totalServers,
            'totalSites' => $totalSites,
            'totalProducts' => $totalProducts,
            'totalActiveClients' => $totalActiveClients,
            'totalInactiveClients' => $totalInactiveClients,
            'totalInvoices' => $totalInvoices,
            'servers' => $servers,
            'sites' => $sites,
            'whiteLabel' => $whiteLabel
        ]);
    }

    public function AllServers(Request $request)
    {
        abort_unless(team()->ownedWhiteLabel,Response::HTTP_NOT_FOUND);
        $this->authorize('manageServers', team()->ownedWhiteLabel);
        $search = $request->get('search');
        $servers = $request->user()->currentTeam->ownedWhiteLabel->servers()
            ->withCount('sites')
            ->with([
                'tags:id,name,slug',
                'latestMonitor',
                'team:id,name,user_id,active_plan_id',
                'cloudProvider:id,name,provider',
                'user:id,name',
            ])
            ->orderBy($request->sortBy && $request->sortBy !== 'Sort By' ? str_replace(' ', '_', strtolower($request->sortBy)) : 'id', $request->sortOrder ?? 'desc')
            ->search($search)
            ->filter($request)
            ->paginate(10)
            ->through(function ($server) {
                //return ServerResource::globalResource($server);
                $serverResource = ServerResource::globalResource($server);
                $user = $server->user;
                if ($user) {
                    $serverResource['user'] = [
                        'name' => $user->name,
                        'profile_photo_url' => $user->profile_photo_url,
                    ];
                }

                return $serverResource;
            });

        $clients = team()->ownedWhiteLabel
            ->clients()
            ->get()
            ->pluck('name', 'id')
            ->toArray();

        return Inertia::render('WhiteLabel/AllServer', [
            'servers' => $servers,
            'clients' => $clients,
            'tags' => $request->user()->siteTags(),
            'filter' => $request->filter ?? 'All Servers',
            'sortBy' => $request->sortBy ?? 'Sort By',
            'sortByWebServer' => $request->sortByWebServer ?? 'All Web Servers',
            'sortOrder' => $request->sortOrder ?? 'desc',
            'search' => $search,
            'client' => intval($request->client) ?? 0
        ]);
    }

    public function AllSites(Request $request)
    {
        abort_unless(team()->ownedWhiteLabel,Response::HTTP_NOT_FOUND);
        $this->authorize('manageSites', team()->ownedWhiteLabel);

        $search = $request->get('search');
        $sites = $request->user()->currentTeam->ownedWhiteLabel->sites()
            ->with([
                'server',
                'server.user:id,name',
                'server.team:id,name,user_id'
            ])
            ->selectRaw('sites.*, DATE_FORMAT(sites.created_at, "%D %M, %Y") as created_at_readable')
            ->orderBy($request->sortBy ? str_replace(' ', '_', strtolower($request->sortBy)) : 'id', $request->sortOrder ?? 'desc')
            ->search($search)
            ->filter($request)
            ->paginate(10);

        $clients = team()->ownedWhiteLabel
            ->clients()
            ->get()
            ->pluck('name', 'id')
            ->toArray();

        return Inertia::render('WhiteLabel/AllSite', [
            'sites' => $sites,
            'clients' => $clients,
            'tags' => $request->user()->siteTags(),
            'search' => $search,
            'filter' => $request->filter ?? 'All Sites',
            'sortBy' => $request->sortBy ?? 'Sort By',
            'sortOrder' => $request->sortOrder ?? 'desc',
            'client' => intval($request->client) ?? 0

        ]);
    }

    public function termsAndServices()
    {
        $whiteLabel = team()->ownedWhiteLabel ?? currentWhiteLabel();

        $this->authorize('manageLandingPage', $whiteLabel);

        return Inertia::render('WhiteLabel/TOS', [
            'whiteLabel' => $whiteLabel,
            'landing_page_navbar_photo_url' => $whiteLabel->landing_page_navbar_photo_url,
            'products' => $whiteLabel->activeProducts
        ]);
    }

    public function landing()
    {
        $whiteLabel = team()->ownedWhiteLabel ?? currentWhiteLabel();
        $this->authorize('manageLandingPage', $whiteLabel);
        return Inertia::render('WhiteLabel/Landing', [
            'whiteLabel' => $whiteLabel,
            'landing_page_navbar_photo_url' => $whiteLabel->landing_page_navbar_photo_url,
            'products' => $whiteLabel->activeProducts,
        ]);
    }
}
