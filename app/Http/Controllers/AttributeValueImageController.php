<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAttributeValueImageRequest;
use App\Http\Requests\UpdateAttributeValueImageRequest;
use App\Models\AttributeValueImage;

class AttributeValueImageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAttributeValueImageRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(AttributeValueImage $attributeValueImage)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AttributeValueImage $attributeValueImage)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAttributeValueImageRequest $request, AttributeValueImage $attributeValueImage)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AttributeValueImage $attributeValueImage)
    {
        //
    }
}
