<?php

namespace App\Http\Controllers;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Models\Bill;
use App\Models\GeneralInvoice;
use App\Models\ManualInvoice;
use App\Models\PaymentMethod;
use App\Models\User;
use App\Repository\StripePaymentRepository;
use App\Services\PaymentGateway\InvoiceServices\Invoiceable;
use App\Services\PaymentGateway\InvoiceServices\InvoiceGenerator;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Inertia\Inertia;
use Log;
use Stripe\PaymentIntent;
use Stripe\Stripe;

class InvoiceController extends Controller
{
    public function downloadInvoice(GeneralInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->download($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }
    public function previewDownloadInvoice(GeneralInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->stream($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }

    public function downloadManualInvoice(ManualInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->download($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }
    public function previewDownloadManualInvoice(ManualInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        return $invoice->invoicePdfView(user())->stream($invoice->invoice_number. ".pdf" ?? 'Invoice' . ".pdf");
    }

    public function previewAsHtml(GeneralInvoice $invoice)
    {
        if (!user()->isSuperAdmin()) {
            abort(404);
        }

        $invoice->load(['paymentMethod', 'cartForm']);

        $bills = $invoice->bills->groupBy(function ($bill) {
            return $bill->service->toReadableSentence();  // Assuming 'service' is an enum, use 'value' to group by
        });

        $cartForms = $invoice->cartForm->groupBy(function ($cartForm) use ($invoice) {
            return $cartForm->service->toReadableSentence();  // Assuming 'service' is an enum, use 'value' to group by
        })->map(function ($cartFormGroup) use ($invoice) {
            return $cartFormGroup->filter(function ($cartForm) use ($invoice) {
                // Check if the cart form product/package id matches any bill's product/package id
                return !$invoice->bills->contains(function ($bill) use ($cartForm) {
                    return $bill->product_id === $cartForm->product_id || $bill->package_id === $cartForm->package_id;
                });
            });
        })->filter(function ($group) {
            // Ensure we only keep non-empty groups
            return $group->isNotEmpty();
        });;

        $cartFormAmount = 0;

        foreach ($cartForms as $cartForm) {
            foreach ($cartForm as $cart) {
                $cartFormAmount += $cart?->package?->price ?: $cart?->product?->price ?: $cart?->subscriptionProduct?->price;
            }
        }

        return view('Invoice.general', [
            'invoice' => $invoice,
            'billsGroup' => $bills,
            'cartFormsGroup' => $cartForms,
            'isPaid' => $invoice->status->is(BillingStatus::Paid),
            'currentMonth' => Carbon::now()->format('F'),
            'cartFormAmount' => $cartFormAmount,
            'billableAmount' => ($invoice->bills->sum('amount_to_pay') + $cartFormAmount) ?: $invoice->amount,
            'paymentMethod' => $invoice->paymentMethod,
        ]);
    }

    public function payInvoice(Request $request, GeneralInvoice $invoice)
    {
        $this->authorize('canPay', $invoice);

        Stripe::setApiKey(config('services.stripe.secret_key'));

        if ($invoice->status->is(BillingStatus::Paid) || ($invoice->status->isFailed() && $invoice?->gateway_invoice_or_intent_id)) {
            $invoice->fetchPaymentWebhookFromStripe();
        }

        return Inertia::render('Invoice/PayInvoice', [
            'invoice' => $invoice,
            'paymentMethods' => team()->paymentMethods,
            'defaultPaymentMethod' => team()->paymentMethods->where('is_default', true)->first(),
        ]);

        $paymentMethodId = $request->get('paymentMethodId');

        if(empty($paymentMethodId)){
            return back()->with('flash', [
                'error' => 'Please select a payment method!'
            ]);
        }

        if (team()->activePaymentMethod()->isEmpty()) {
            return back()->with('flash', [
                'error' => 'No active payment method found!'
            ]);
        }
//
//        if ($invoice->status === BillingStatus::Paid) {
//            return back()->with('flash', [
//                'success' => 'Invoice already paid!'
//            ]);
//        }
//
//        $paymentMethod = PaymentMethod::findOrFail($paymentMethodId);
//
//        $unpaidBills = $invoice->unpaidBills()->get();
//
//
//        ## need to take payment with user's specific payment method
//        // switch card with specified payment method
//        $invoice->switchCard($paymentMethod);
//
//        // cancel previous payment intent
//        if ($invoice->gateway_invoice_or_intent_id) {
//            try {
//                (new StripePaymentRepository())->cancelPaymentIntent($invoice);
//            }catch (\Exception $e) {
//                Log::warning('Payment intent cancellation failed', [
//                    'invoice_id' => $invoice->id,
//                    'invoice_reference_no' => $invoice->reference_no,
//                    'xcloud_team_id' => $invoice->team_id,
//                    'xcloud_payment_method_id' => $invoice->payment_method_id,
//                    'error' => $e->getMessage()
//                ]);
//
//                return back()->with('flash', [
//                    'error' => 'Failed to process payment. Please try with another card.'
//                ]);
//            }
//        }
//
//        // create a new payment intent
//        try {
//            $paymentIntent = (new StripePaymentRepository())->createPaymentIntent($invoice);
//        }catch (\Exception $e) {
//            Log::warning('Payment intent creation failed', [
//                'invoice_id' => $invoice->id,
//                'invoice_reference_no' => $invoice->reference_no,
//                'xcloud_team_id' => $invoice->team_id,
//                'xcloud_payment_method_id' => $invoice->payment_method_id,
//                'error' => $e->getMessage()
//            ]);
//            return back()->with('flash', [
//                'error' => 'Failed to process payment. Please try with another card.'
//            ]);
//        }
//
//
//        // update invoice with new payment intent with newly switched card
//        $invoice->update([
//            'gateway_invoice_or_intent_id' => $paymentIntent->id
//        ]);
//
//        // take charge with selected payment method
//        try {
//            $paymentIntent = $paymentIntent->confirm();
//        }catch (\Exception $e){
//            Log::warning('Payment intent creation failed', [
//                'invoice_id' => $invoice->id,
//                'invoice_reference_no' => $invoice->reference_no,
//                'xcloud_team_id' => $invoice->team_id,
//                'xcloud_payment_method_id' => $invoice->payment_method_id,
//                'error' => $e->getMessage()
//            ]);
//        }
//
//
//        if($paymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
//            $invoice->team->onPaymentSuccess($invoice);
//
//            $invoice->setStatusPaid();
//
//            $unpaidBills->each(function (Bill $bill) {
//                $bill->setStatusPaid();
//            });
//
//            if (!$invoice->team->billingIsActive() && !$invoice->team->hasDuesToPay()) {
//                $invoice->team->setBillingStatusActive();
//            }
//
//            // send email
//            $invoice->team->sendInvoiceEmail($invoice);
//
//            return back()->with('flash', [
//                'success' => 'Your payment processed successfully!'
//            ]);
//        }else {
//            if ($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION) {
//                $paymentMethod->saveMeta('stripe_3d_secure_authentication', [
//                    'requires_3d_secure_authentication' => true
//                ]);
//
//                $invoice->setStatusRequiresAction();
//
//                return redirect()->route('stripe.checkout.requires3dsecure', [
//                    'paymentIntentId' => $paymentIntent->id,
//                    'sessionId' => $paymentMethod->session_id,
//                    'affiliateId' => team()->getMeta('affiliate.affiliate_code'),
//                    'invoiceId' => $invoice->id,
//                ]);
//            } else {
//                $invoice->setStatusPaymentFailed();
//                $invoice->saveLog('payment_failed_' . now()->format('Y-m-d h:m:s'), 'Payment failed');
//
//                $invoiceBills = $invoice->bills()->get();
//
//                foreach ($invoiceBills as $bill) {
//                    $bill->setStatusUnpaid();
//                }
//
//                return back()->with('flash', [
//                    'error' => 'Failed to process payment'
//                ]);
//            }
//        }
    }

    public function invoiceDetails(GeneralInvoice $invoice)
    {
        $this->authorize('view', $invoice);

        $invoice->load([
            'paymentMethod',
            'bills',
            'cartForm'
        ])->loadCount(['bills', 'cartForm']);

        return response()->json([
            'invoice' => $invoice->toArray(),
            'bills' => $invoice->bills()->with('generator')->get()->toArray(),
            'paymentMethod' => $invoice->paymentMethod->toArray(),
            'cartForm' => $invoice->cartForm()->get()->toArray(),
        ]);
    }

//    public function invoiceDetails(GeneralInvoice $invoice)
//    {
//        $this->authorize('view', $invoice);
//
//        $invoice->load(['paymentMethod', 'bills', 'cartForm'])->loadCount(['bills', 'cartForm']);
//
//        $invoiceData = $invoice->only([
//            'id', 'reference_no', 'invoice_number', 'title', 'description',
//            'amount', 'currency', 'status', 'type', 'source', 'team_id',
//            'due_date', 'created_at', 'updated_at', 'status_readable', 'number'
//        ]);
//
//        $paymentMethodData = $invoice->paymentMethod->only([
//            'id', 'payment_gateway', 'status', 'card_no', 'created_at', 'updated_at'
//        ]);
//        $paymentMethodData['meta'] = [
//            'stripe' => [
//                'brand' => $invoice->paymentMethod->meta['stripe']['brand'],
//                'last4digit' => $invoice->paymentMethod->meta['stripe']['last4digit'],
//                'expiry_year' => $invoice->paymentMethod->meta['stripe']['expiry_year'],
//                'expiry_month' => $invoice->paymentMethod->meta['stripe']['expiry_month'],
//            ]
//        ];
//
//        return response()->json([
//            'invoice' => $invoiceData,
//            'bills' => $invoice->bills()->with('generator')->get(['id', 'amount', 'description'])->toArray(),
//            'paymentMethod' => $paymentMethodData,
//            'cartForm' => $invoice->cartForm()->get(['id', 'name', 'quantity', 'price'])->toArray(),
//        ]);
//    }

}
