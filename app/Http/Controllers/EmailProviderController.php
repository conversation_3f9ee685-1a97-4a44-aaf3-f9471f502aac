<?php

namespace App\Http\Controllers;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\xCloudEmailProviderPlanEnum;
use App\Models\EmailProvider;
use App\Models\GeneralInvoice;
use App\Models\Product;
use App\Repository\XcloudEmailProviderRepository;
use App\Scripts\Server\emailProvider\SMTP;
use App\Services\Integrations\ElasticEmailService;
use App\Services\PaymentGateway\InvoiceServices\Invoiceable;
use Arr;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Psr\Http\Client\ClientExceptionInterface;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Symfony\Component\Process\Process;

class EmailProviderController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        $this->authorize('manageEmailProvider', team());
        $providers = EmailProvider::query()
            ->where(['team_id' => team()->id]);

        return Inertia::render('Profile/EmailProvider', [
            'user' => auth()->user(),
            'team' => team(),
            'providers' => $providers->paginate(10),
            'title' => 'Email Provider',
            'can_delete_server_provider' => user()->can('deleteServerProvider', user()->currentTeam),
            'access_permissions' => team()->permissions(['account:', 'billing:', 'server:']),
            'uniqueProviders' => $providers->get()->unique('provider'),
            'xcloudEmailProviderPlans' => xCloudEmailProviderPlanEnum::getReadablePlanName(),
            'subAccountEmail' => team()->getElasticEmailSubAccountEmail(),
        ]);

    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $team = team();

        $this->authorize('manageEmailProvider', team());


        return Inertia::render('Profile/EmailProvider', [
            'team' => $team,
            'access_permissions' => team()->permissions(['account:', 'billing:', 'server:']),
            'email_provider' => $team->emailProviders()->first(),
            'title' => $team->name.' - Smtp Settings',
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     * @throws ApiErrorException
     * @throws \Exception
     * @throws GuzzleException
     */
    public function store(Request $request)
    {
        $this->authorize('manageEmailProvider', team());

        // if team is split payment method and no active card, redirect to billing page (only for xcloud managed email provider)
        if(!team()->canAccessWithBilling(BillingServices::EmailProvider) && $request->get('provider') === \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER->value){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'Warning',
                'body' => 'Please add a payment method to purchase email provider.',
                'type' => 'warning'
            ]);
        }

        $data = $request->validate([
            'provider_type' => 'required|in:'.\App\Enums\EmailProvider::OWN_SMTP->value.','.\App\Enums\EmailProvider::XCLOUD_SMTP->value . \App\Enums\EmailProvider::ELASTIC_EMAIL->value,
            'provider' => 'required_if:provider_type,==,'.\App\Enums\EmailProvider::OWN_SMTP->value,
            'host' => 'required_if:provider,'.\App\Enums\EmailProvider::OTHER->value,
            'port' => 'required_if:provider,'.\App\Enums\EmailProvider::OTHER->value,
            'username' => 'required_if:provider,'.\App\Enums\EmailProvider::SENDGRID->value.','.\App\Enums\EmailProvider::OTHER->value.','.\App\Enums\EmailProvider::MAILGUN->value,
            'password' => 'required_if:provider,'.\App\Enums\EmailProvider::OTHER->value.','.\App\Enums\EmailProvider::MAILGUN->value,
            'domain' => 'required_if:provider,'.\App\Enums\EmailProvider::MAILGUN_API->value,
            'api_key' => 'required_if:provider,'.\App\Enums\EmailProvider::MAILGUN_API->value.','.\App\Enums\EmailProvider::SENDGRID->value,
            'selected_plan' => 'required_if:provider,' . \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER->value,
            'label' => 'required',
        ]);

        if ($data['provider'] == \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER->value) {
            $data['username'] = team()->getElasticEmailSubAccountEmail() ?? $request->get('label');
            $data['provider_type'] = \App\Enums\EmailProvider::ELASTIC_EMAIL->value;
            $data['provider'] =\App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER->value;
            $data['domain'] = 'xcloud.email';
            $data['email_limit'] = xCloudEmailProviderPlanEnum::getPlanLimit(Arr::get($data, 'selected_plan'));
            $data['plan'] = Arr::get($data, 'selected_plan');
            $data['price'] = xCloudEmailProviderPlanEnum::getPlanPrice(Arr::get($data, 'selected_plan'));
            $data['label'] = $request->get('label');
        }

        if($data['provider'] === \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER->value &&
            !team()->canAccessWithBilling(BillingServices::EmailProvider)){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'Error',
                'body' => 'Please add a Payment Method to purchase email provider.',
                'type' => 'error'
            ]);
        }

        // if team has already availed 100 free plan, and tries to get that again, return error
        if($data['selected_plan'] === xCloudEmailProviderPlanEnum::XCLOUD_100_EMAILS->value
            && team()->hasAvailedFreeXcloudEmailProviderPlan()){
            return redirect()->back()->with('flash', [
                'message' => 'Error',
                'body' => 'You have already availed xCloud Email Provider free plan(100 emails).',
                'type' => 'error'
            ]);
        }

        unset($data['selected_plan']);

        // add new data to email_providers table in this team
        $provider = team()->emailProviders()->create($data);

        $provider->setHostAndPort();

        if($provider->provider === \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER){
            // setup xcloud email provider on elastic email
            (new XcloudEmailProviderRepository($provider, team()))->setupXcloudEmailProvider();
            // handle payment
            $response = (new XcloudEmailProviderRepository($provider, team()))->handlePayment();

            $model = team()->activePlan?->supported_invoice_type?->model();
            $invoice = $model::findOrFail(Arr::get($response, 'invoiceId'));
            if($invoice->requiresAction()){
                return redirect()->route('stripe.product.checkout.requires3dsecure', $response);
            }
        }

        return redirect()->back()->with('flash', [
            'message' => 'Email Provider Added.',
            'body' => 'New email provider has been added to your team.',
            'type' => 'success'
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param \App\Models\EmailProvider $emailProvider
     * @return \Illuminate\Http\RedirectResponse
     * @throws \Exception
     */
    public function update(EmailProvider $emailProvider, Request $request)
    {
        $team = team();
        $this->authorize('manageEmailProvider', $team);

        if($emailProvider->provider === \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER
            && $request->get('provider') !== \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER->value)
        {
            return redirect()->back()->with('flash', [
                'message' => 'Error',
                'body' => 'You cannot change xCloud Email Provider to other provider.',
                'type' => 'error'
            ]);
        }

        if($request->get('provider') === \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER->value &&
            !team()->canAccessWithBilling(BillingServices::EmailProvider)){
            return redirect()->route('user.bills-payment')->with('flash', [
                'message' => 'Error',
                'body' => 'Please add a Payment Method to purchase email provider.',
                'type' => 'error'
            ]);
        }

        $data = $request->validate([
            'provider_type' => 'required|in:'.\App\Enums\EmailProvider::OWN_SMTP->value.','.\App\Enums\EmailProvider::XCLOUD_SMTP->value . ',' . \App\Enums\EmailProvider::ELASTIC_EMAIL->value,
            'provider' => 'required_if:provider_type,==,'.\App\Enums\EmailProvider::OWN_SMTP->value,
            'host' => 'required_if:provider,'.\App\Enums\EmailProvider::OTHER->value,
            'port' => 'required_if:provider,'.\App\Enums\EmailProvider::OTHER->value,
            'username' => 'required_if:provider,'.\App\Enums\EmailProvider::SENDGRID->value.','.\App\Enums\EmailProvider::OTHER->value.','.\App\Enums\EmailProvider::MAILGUN->value,
            'password' => 'required_if:provider,'.\App\Enums\EmailProvider::OTHER->value.','.\App\Enums\EmailProvider::MAILGUN->value,
            'domain' => 'required_if:provider,'.\App\Enums\EmailProvider::MAILGUN_API->value,
            'api_key' => 'required_if:provider,'.\App\Enums\EmailProvider::MAILGUN_API->value.','.\App\Enums\EmailProvider::SENDGRID->value,
            'selected_plan' => 'required_if:provider,' . \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER->value,
            'label' => 'required',
        ]);

        if ($data['provider'] == \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER->value) {
            $data['username'] = $team->getElasticEmailSubAccountEmail() ?? $request->get('label');
            $data['provider_type'] = \App\Enums\EmailProvider::ELASTIC_EMAIL->value;
            $data['provider'] =\App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER->value;
            $data['domain'] = 'xcloud.email';
            $data['email_limit'] = xCloudEmailProviderPlanEnum::getPlanLimit(Arr::get($data, 'selected_plan'));
            $data['plan'] = Arr::get($data, 'selected_plan');
            $data['price'] = xCloudEmailProviderPlanEnum::getPlanPrice(Arr::get($data, 'selected_plan'));
            $data['label'] = $request->get('label');
        }

        unset($data['selected_plan']);

        $emailProvider = $team->emailProviders()->findOrFail($emailProvider->id);
        $emailProvider->update($data);

        $emailProvider->setHostAndPort();

        return redirect()->back()->with('flash', [
            'message' => 'Email Provider Updated.',
            'body' => 'Email provider has been updated.',
            'type' => 'success'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmailProvider  $emailProvider
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(EmailProvider $emailProvider)
    {
        $team = team();
        $this->authorize('manageEmailProvider', $team);

        if($emailProvider->provider === \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER){
            // delete xcloud email provider on elastic email
            $team->subtractCreditFromSubaccount(credit: -$emailProvider->email_limit);
        }


        $team->emailProviders()->where('id', $emailProvider->id)->first()->delete();


        return redirect()->back()->with('flash', [
            'message' => 'Email Provider Deleted.',
            'body' => 'Email provider has been deleted.',
            'type' => 'success'
        ]);

    }


    /**
     * @throws GuzzleException
     * @throws ClientExceptionInterface
     */
    public function sendTestEmail(EmailProvider $email_provider, Request $request)
    {
        return 'This feature is disabled for now. User will test his email provider from fluent smtp settings';

        $team = team();
        $this->authorize('manageProvider', $team);

        $data = $request->validate([
            'from_email' => 'required|email',
            'email' => 'required|email',
        ]);

        if ($email_provider->provider == \App\Enums\EmailProvider::MAILGUN_API) {
            $mg = \Mailgun\Mailgun::create($email_provider->api_key);
            // Now, compose and send your message.
            try {
                $mg->messages()->send($email_provider->domain, [
                    'from' => $data['from_email'],
                    'to' => $data['email'],
                    'subject' => 'xCloud is awesome!',
                    'text' => 'This is a test email from xCloud. Please ignore.'
                ]);
            } catch (Exception $e) {
                return redirect()->back()->with('flash', [
                    'message' => 'Your credentials are incorrect.',
                    'body' => 'Please check your credentials.',
                    'type' => 'error'
                ]);
            }

        }else if($email_provider->provider === \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER){
            (new ElasticEmailService(team()->getElasticEmailSubAccountApiKey()))->sendEmail([
                'apikey' => $email_provider->api_key,
                'from' => $data['from_email'],
                'fromName' => $team->name,
                'to' => $data['email'],
                'subject' => 'xCloud is awesome!',
                'body' => 'This is a test email from xCloud. Please ignore.',
            ]);
        }else {
            $script = (new SMTP($email_provider,$data['from_email'], $data['email'], 'Test Email',
                'This is a test email from xCloud. Please ignore.'))->script();

            //run shell script php
            $process = Process::fromShellCommandline($script);
            $process->setTimeout(600);
            $process->run();

        }


        return redirect()->back()->with('flash', [
            'message' => 'Test Email Sent.',
            'body' => 'Please check your inbox.',
            'type' => 'success'
        ]);
    }
}
