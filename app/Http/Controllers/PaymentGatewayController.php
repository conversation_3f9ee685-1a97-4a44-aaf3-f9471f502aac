<?php

namespace App\Http\Controllers;

use App\Enums\WebhookTypes;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\XcloudBilling\PaymentCardSecurityProtocol;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Enums\XcloudBilling\PaymentMethodStatus;
use App\Enums\XcloudBilling\PlansEnum;
use App\Models\GeneralInvoice;
use App\Models\PaymentMethod;
use App\Models\Team;
use App\Models\Webhook;
use App\Repository\ServerBillingRepository;
use App\Repository\StripePaymentRepository;
use Arr;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Log;
use PHPUnit\Util\Exception;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\StripeClient;

class PaymentGatewayController extends Controller
{
    /**
     * @throws ApiErrorException
     * @throws GuzzleException
     */
    public function stripeCardAddSuccess(Request $request)
    {
        $sessionId = $request->get('session_id');

        Stripe::setApiKey(config('services.stripe.secret_key'));

        $stripe = new StripeClient(config('services.stripe.secret_key'));

        // if card is not saved in out system, then get the card
        try {
            if(currentWhiteLabel()){
                $session = $stripe->checkout->sessions->retrieve(
                    $sessionId,
                    [],
                    ['stripe_account' => currentWhiteLabel()?->connectedAccount?->stripe_account_id]
                );
            }else{
                $session = $stripe->checkout->sessions->retrieve(
                    $sessionId,
                    []
                );
            }

            if(!$session){
                throw new InvalidRequestException('Session not found!');
            }

            if(currentWhiteLabel()){
                $customer = Customer::retrieve($session->customer, [
                    'stripe_account' => currentWhiteLabel()?->connectedAccount?->stripe_account_id
                ]);
            }else{
                $customer = Customer::retrieve($session->customer);
            }

            if(!$customer){
                throw new InvalidRequestException('Customer not found!');
            }

            $paymentMethod = PaymentMethod::create([
                'team_id' => team()->id,
                'user_id' => auth()->user()->id,
                'payment_gateway' => PaymentGateway::Stripe,
                'customer_id' => $customer->id,
                'default_card' => !(count(team()->paymentMethods) > 0),
                'status' => PaymentMethodStatus::ACTIVE,
                'session_id' => $session->id,
            ]);


            if(!$paymentMethod){
                throw new InvalidRequestException('Failed to add payment method!');
            }

            if(empty($paymentMethod->card_no)){
                if(!team()->hasDuesToPay()) {
                    // set billing status to active
                    team()->setBillingStatusActive();
                }
                team()->setPlan(PlansEnum::getPlanByServerCount(Team::where('id', team()->id)->first()->countSelfManagedHostingForPlanBasedBilling()));


                $customerMeta['customer_id'] = $customer->id;
                $customerMeta['email'] = $customer->email;
                $customerMeta['description'] = $customer->description;
                $customerMeta['discount'] = $customer->discount;

                $paymentMethod->saveMeta('customer', $customerMeta);

                // get payment method
                if(currentWhiteLabel()){
                    $setupIntent = $stripe->setupIntents->retrieve($session->setup_intent, [], [
                        'stripe_account' => currentWhiteLabel()?->connectedAccount?->stripe_account_id
                    ]);
                    $stripePaymentMethod = $stripe->paymentMethods->retrieve(
                        $setupIntent->payment_method,
                        [],
                        ['stripe_account' => currentWhiteLabel()?->connectedAccount?->stripe_account_id]
                    );
                }else {
                    $setupIntent = $stripe->setupIntents->retrieve($session->setup_intent, []);

                    $stripePaymentMethod = $stripe->paymentMethods->retrieve(
                        $setupIntent->payment_method,
                        []
                    );
                }

                if(!$stripePaymentMethod){
                    throw new InvalidRequestException('Payment method not found on stripe!');
                }

                $paymentMethod->update([
                    'card_no' => '**** ' . $stripePaymentMethod->card->last4
                ]);

                $meta['payment_method'] = $stripePaymentMethod->id;
                $meta['setup_intent'] = $session->setup_intent;
                $meta['payment_method_types'] = $session->payment_method_types;
                $meta['last4digit'] = $stripePaymentMethod->card->last4;
                $meta['brand'] = $stripePaymentMethod->card->brand;
                $meta['expiry_month'] = $stripePaymentMethod->card->exp_month;
                $meta['expiry_year'] = $stripePaymentMethod->card->exp_year;

                $paymentMethod->saveMeta('stripe', $meta);

                // verify card
                if(!$paymentMethod->paymentMethodVerified()){
                    $paymentMethod->setStatusInactive();
                }
            }
        }catch (\Exception $exception){
            team()->setBillingStatusInactive();
//            $paymentMethod->setStatusInactive();
            $paymentMethod->saveLog('error', $exception->getMessage());

            return redirect()->route('user.bills-payment')->with('flash', [
                'type' => 'danger',
                'message' => $exception->getMessage(),
            ]);
        }

        return redirect()->route('user.bills-payment');
    }

    public function stripeCardAddCancelled(Request $request)
    {
        return redirect()->route('user.bills-payment')->with('flash', [
            'type' => 'warning',
            'message' => 'You had cancelled adding your payment method!',
        ]);
    }

    /**
     * @throws ApiErrorException
     * @throws GuzzleException
     */
    public function webhook(Request $request)
    {
        $stripe = new StripeClient(config('services.stripe.secret_key'));

        // This is your Stripe CLI webhook secret for testing your endpoint locally.
        $endpoint_secret = config('services.stripe.webhook_secret');

        $payload = @file_get_contents('php://input');
        $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
        $event = null;

        try {
            $event = \Stripe\Webhook::constructEvent(
                $payload, $sig_header, $endpoint_secret
            );
        } catch(\UnexpectedValueException $e) {
            // Invalid payload
            return response('', 400);
        } catch(\Stripe\Exception\SignatureVerificationException $e) {
            // Invalid signature
            return response('', 400);
        }

        $cases = [
            'customer.subscription.created' => 'handleSubscriptionCreated',
            'customer.subscription.updated' => 'handleSubscriptionUpdated',
            'customer.subscription.deleted' => 'handleSubscriptionDeleted',
            'customer.subscription.paused' => 'handleSubscriptionPaused',
            'customer.subscription.pending_update_applied' => 'handlePendingUpdateApplied',
            'customer.subscription.pending_update_expired' => 'handlePendingUpdateExpired',
            'customer.subscription.resumed' => 'handleSubscriptionResumed',
            'customer.subscription.trial_will_end' => 'handleTrialWillEnd',
        ];

        if (in_array($event->type, array_keys($cases))) {
            return (new TeamSubscriptionProductWebhook)->__invoke($request);
        }

//            return (new TeamSubscriptionProductWebhook())->$this->{$cases[$event->type]}($event);

        // Handle the event
        switch ($event->type) {
            case 'checkout.session.async_payment_failed':
                $session = $event->data->object;
                $teamId = Arr::get($session, 'metadata.xcloud_team_id');
                if($teamId){
                    $team = Team::findOrFail($teamId);
                    $team->setBillingStatusInactive();
                }
                Webhook::create([
                    'type' => WebhookTypes::payment,
                    'config' => $event,
                    'team_id' => $teamId ?? null,
                ]);
                break;
            case 'checkout.session.async_payment_succeeded':
                $session = $event->data->object;
                $teamId = Arr::get($session, 'metadata.xcloud_team_id');
                if($teamId){
                    $team = Team::findOrFail($teamId);
                    $team->setBillingStatusActive();
                    $team->setPlan(PlansEnum::getPlanByServerCount(Team::where('id', team()->id)->first()->countSelfManagedHostingForPlanBasedBilling()));
                }
                Webhook::create([
                    'type' => WebhookTypes::payment,
                    'config' => $event,
                    'team_id' => $teamId ?? null,
                ]);
                break;
            case 'checkout.session.completed':
                $session = $event->data->object;
                $paymentMethod = PaymentMethod::whereSessionId($session->id)->first();

                $teamId = Arr::get($session, 'metadata.xcloud_team_id');

                Webhook::create([
                    'type' => WebhookTypes::payment,
                    'config' => $event,
                    'team_id' => $teamId ?? null,
                ]);

                if(empty($paymentMethod->card_no)){
                    if($teamId){
                        $team = Team::findOrFail($teamId);
                        $team->setBillingStatusActive();
                        $team->setPlan(PlansEnum::getPlanByServerCount(Team::where('id', team()->id)->first()
                            ->countSelfManagedHostingForPlanBasedBilling()));

                        // if card is not saved in out system, then get the card
                        if(currentWhiteLabel()){
                            $customer = Customer::retrieve($session->customer, [
                                'stripe_account' => currentWhiteLabel()?->connectedAccount?->stripe_account_id
                            ]);
                        }else {
                            $customer = Customer::retrieve($session->customer);
                        }

                        if(!$customer){
                            throw new InvalidRequestException('Customer not found!');
                        }

                        $customerMeta['customer_id'] = $customer->id;
                        $customerMeta['email'] = $customer->email;
                        $customerMeta['description'] = $customer->description;
                        $customerMeta['discount'] = $customer->discount;

                        $paymentMethod->saveMeta('customer', $customerMeta);

                        // get payment method
                        if(currentWhiteLabel()) {
                            $setupIntent = $stripe->setupIntents->retrieve($session->setup_intent, [], [
                                'stripe_account' => currentWhiteLabel()?->connectedAccount?->stripe_account_id
                            ]);
                            $stripePaymentMethod = $stripe->paymentMethods->retrieve(
                                $setupIntent->payment_method,
                                [],
                                ['stripe_account' => currentWhiteLabel()?->connectedAccount?->stripe_account_id]
                            );
                        }else{
                            $setupIntent = $stripe->setupIntents->retrieve($session->setup_intent, []);

                            $stripePaymentMethod = $stripe->paymentMethods->retrieve(
                                $setupIntent->payment_method,
                                []
                            );
                        }

                        if(!$stripePaymentMethod){
                            throw new InvalidRequestException('Payment method not found on stripe!');
                        }

                        // store payment method data to meta
                        $paymentMethod->update([
                            'card_no' => '**** ' . $stripePaymentMethod->card->last4
                        ]);

                        $meta['payment_method'] = $stripePaymentMethod->id;
                        $meta['setup_intent'] = $session->setup_intent;
                        $meta['payment_method_types'] = $session->payment_method_types;
                        $meta['last4digit'] = $stripePaymentMethod->card->last4;
                        $meta['brand'] = $stripePaymentMethod->card->brand;
                        $meta['expiry_month'] = $stripePaymentMethod->card->exp_month;
                        $meta['expiry_year'] = $stripePaymentMethod->card->exp_year;

                        $paymentMethod->saveMeta('stripe', $meta);
                    }
                }
                break;
            case 'checkout.session.expired':
                $session = $event->data->object;
                break;
            case 'charge.succeeded':
                $charge = $event->data->object;
                $teamId = Arr::get($charge, 'metadata.xcloud_team_id');
                if($teamId){
                    $team = Team::findOrFail($teamId);
                    $team->setBillingStatusActive();

                    $xcloudInvoiceId = Arr::get($charge, 'metadata.invoice_id');
                    $invoice = GeneralInvoice::findOrFail($xcloudInvoiceId);

                    $invoice->bills()->update([
                        'status' => BillingStatus::Paid
                    ]);

                    $invoice->paid();

                    $logs = [
                        'event_id' => $event->id,
                        'event_type' => $event->type,
                        'charge_id' => $charge['id'],
                        'status' => $charge['status'],
                        'amount' => $charge['amount'] / 100,
                        'currency' => $charge['currency'],
                        'paid' => $charge['paid'],
                        'balance_transaction' => $charge['balance_transaction'],
                        'charged_at' => Carbon::createFromTimestamp($charge['created']),
                        'customer' => $charge['customer'],
                        'receipt_url' => $charge['receipt_url'],
                        'metadata' => $charge['metadata'],
                        'outcome' => $charge['outcome'],
                        'payment_intent' => $charge['payment_intent'],
                        'payment_method' => $charge['payment_method'],
                        'three_d_secure' => Arr::get($charge, 'payment_method_details.card.three_d_secure'),
                    ];

                    $data = [];
                    if(Arr::get($logs, 'payment_intent')){
                        $data['gateway_invoice_or_intent_id'] = Arr::get($logs, 'payment_intent') ?? $invoice->gateway_invoice_or_intent_id;
                    }

                    if(Arr::get($logs, 'payment_method')){
                        $data['gateway_payment_method_id'] = Arr::get($logs, 'payment_method') ?? $invoice->gateway_payment_method_id;
                    }

                    $invoice->update([
                        'gateway_invoice_or_intent_id' => $data['gateway_invoice_or_intent_id'],
                        'gateway_payment_method_id' => $data['gateway_payment_method_id'],
                        'logs' => $logs
                    ]);
                }

                Webhook::create([
                    'type' => WebhookTypes::payment,
                    'config' => $event,
                    'team_id' => $teamId ?? null,
                ]);
                break;
            case 'charge.failed':
                $charge = $event->data->object;
                $teamId = Arr::get($charge, 'metadata.xcloud_team_id');
                if($teamId){
                    $team = Team::findOrFail($teamId);

                    $xcloudInvoiceId = Arr::get($charge, 'metadata.invoice_id');
                    $invoice = GeneralInvoice::findOrFail($xcloudInvoiceId);
                    $invoice->bills()->update([
                        'status' => BillingStatus::Unpaid
                    ]);

                    $invoice->setStatusPaymentFailed();

                    $logs = [
                        'event_id' => $event->id,
                        'event_type' => $event->type,
                        'charge_id' => $charge['id'],
                        'status' => $charge['status'],
                        'amount' => $charge['amount'] / 100,
                        'currency' => $charge['currency'],
                        'paid' => $charge['paid'],
                        'balance_transaction' => $charge['balance_transaction'],
                        'charged_at' => Carbon::createFromTimestamp($charge['created']),
                        'customer' => $charge['customer'],
                        'receipt_url' => $charge['receipt_url'],
                        'metadata' => $charge['metadata'],
                        'outcome' => $charge['outcome'],
                        'payment_intent' => $charge['payment_intent'],
                        'payment_method' => $charge['payment_method'],
                        'three_d_secure' => Arr::get($charge, 'payment_method_details.card.three_d_secure'),
                    ];

                    $data = [];
                    if(Arr::get($logs, 'payment_intent')){
                        $data['gateway_invoice_or_intent_id'] = Arr::get($logs, 'payment_intent') ?? $invoice->gateway_invoice_or_intent_id;
                    }

                    if(Arr::get($logs, 'payment_method')){
                        $data['gateway_payment_method_id'] = Arr::get($logs, 'payment_method') ?? $invoice->gateway_payment_method_id;
                    }

                    $invoice->update([
                        'gateway_invoice_or_intent_id' => $data['gateway_invoice_or_intent_id'],
                        'gateway_payment_method_id' => $data['gateway_payment_method_id'],
                        'logs' => $logs
                    ]);
                }

                Webhook::create([
                    'type' => WebhookTypes::payment,
                    'config' => $event,
                    'team_id' => $teamId ?? null,
                ]);
                break;
            case 'refund.created':
            case 'charge.refund.updated':
                $refund = $event->data->object;
                $teamId = Arr::get($refund, 'metadata.xcloud_team_id');

                Webhook::create([
                    'type' => WebhookTypes::payment,
                    'config' => $event,
                    'team_id' => $teamId ?? null,
                ]);
                break;
            case 'charge.refunded':
                $charge = $event->data->object;
                $teamId = Arr::get($charge, 'metadata.xcloud_team_id');
                if($teamId){
                    $team = Team::findOrFail($teamId);
                    $team->setBillingStatusActive();

                    $xcloudInvoiceId = Arr::get($charge, 'metadata.invoice_id');
                    $invoice = GeneralInvoice::findOrFail($xcloudInvoiceId);
                    $invoice->bills()->update([
                        'status' => BillingStatus::Refunded
                    ]);
                    $invoice->refunded();

                    $logs = [
                        'event_id' => $event->id,
                        'event_type' => $event->type,
                        'charge_id' => $charge['id'],
                        'status' => $charge['status'],
                        'amount' => $charge['amount'] / 100,
                        'currency' => $charge['currency'],
                        'paid' => $charge['paid'],
                        'balance_transaction' => $charge['balance_transaction'],
                        'charged_at' => Carbon::createFromTimestamp($charge['created']),
                        'customer' => $charge['customer'],
                        'receipt_url' => $charge['receipt_url'],
                        'metadata' => $charge['metadata'],
                        'outcome' => $charge['outcome'],
                        'payment_intent' => $charge['payment_intent'],
                        'payment_method' => $charge['payment_method'],
                        'three_d_secure' => Arr::get($charge, 'payment_method_details.card.three_d_secure'),

                    ];
                    $data = [];
                    if(Arr::get($logs, 'payment_intent')){
                        $data['gateway_invoice_or_intent_id'] = Arr::get($logs, 'payment_intent') ?? $invoice->gateway_invoice_or_intent_id;
                    }

                    if(Arr::get($logs, 'payment_method')){
                        $data['gateway_payment_method_id'] = Arr::get($logs, 'payment_method') ?? $invoice->gateway_payment_method_id;
                    }

                    $invoice->update([
                        'gateway_invoice_or_intent_id' => $data['gateway_invoice_or_intent_id'],
                        'gateway_payment_method_id' => $data['gateway_payment_method_id'],
                        'logs' => $logs
                    ]);
                }

                Webhook::create([
                    'type' => WebhookTypes::payment,
                    'config' => $event,
                    'team_id' => $teamId ?? null,
                ]);
                break;
            case 'payment_intent.succeeded':
            case 'payment_intent.canceled':
                $paymentIntent = $event->data->object;
                $teamId = Arr::get($paymentIntent, 'metadata.xcloud_team_id');
                Webhook::create([
                    'type' => WebhookTypes::payment,
                    'config' => $event,
                    'team_id' => $teamId ?? null,
                ]);
                break;
            case 'payment_intent.payment_failed':
                $paymentIntent = $event->data->object;
                $teamId = Arr::get($paymentIntent, 'metadata.xcloud_team_id');
                if($teamId){
                    $team = Team::findOrFail($teamId);

                    $xcloudInvoiceId = Arr::get($paymentIntent, 'metadata.invoice_id');
                    $invoice = GeneralInvoice::findOrFail($xcloudInvoiceId);
                    $invoice->bills()->update([
                        'status' => BillingStatus::Unpaid
                    ]);
                    $invoice->setStatusPaymentFailed();

                    $logs = [
                        'event_id' => $event->id,
                        'event_type' => $event->type,
                        'payment_intent_id' => $paymentIntent['id'],
                        'status' => $paymentIntent['status'],
                        'amount' => $paymentIntent['amount'] / 100,
                        'currency' => $paymentIntent['currency'],
                        'customer' => $paymentIntent['customer'],
                        'client_secret' => $paymentIntent['client_secret'],
                        'metadata' => $paymentIntent['metadata'],
                        'three_d_secure' => Arr::get($paymentIntent, 'payment_method_details.card.three_d_secure'),
                        'last_payment_error' => Arr::get($paymentIntent, 'last_payment_error')
                    ];

                    $data = [];
                    if(Arr::get($logs, 'payment_intent_id')){
                        $data['gateway_invoice_or_intent_id'] = Arr::get($logs, 'payment_intent_id') ?? $invoice->gateway_invoice_or_intent_id;
                    }

                    $invoice->update([
                        'gateway_invoice_or_intent_id' => $data['gateway_invoice_or_intent_id'],
                        'logs' => $logs
                    ]);
                }
                Webhook::create([
                    'type' => WebhookTypes::payment,
                    'config' => $event,
                    'team_id' => $teamId ?? null,
                ]);
                break;
            case 'payment_intent.requires_action':
                $paymentIntent = $event->data->object;
                break;
            case 'payment_method.detached':
            case 'payment_method.attached':
                $paymentMethod = $event->data->object;
                break;
            case 'refund.updated':
                $refund = $event->data->object;
                break;
            default:
                echo 'Received unknown event type ' . $event->type;
        }

        return response('', 200);
    }

    /**
     * @throws ApiErrorException
     */
    public function stripeCardRequires3DSecurity(Request $request)
    {
        $paymentIntentId = $request->get('paymentIntentId');
        $affiliateId = $request->get('affiliateId');
        $invoiceId = $request->get('invoiceId');
        $nextRoute = $request->get('nextRoute');
        $routeParam = $request->get('routeParam');

        $invoice = GeneralInvoice::findOrFail($invoiceId);

        if(empty($paymentIntentId)){
            throw new Exception('Payment intent id is missing!');
        }

        Stripe::setApiKey(config('services.stripe.secret_key'));

        if($invoice->team->whiteLabel){
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId, [
                'stripe_account' => $invoice->team->whiteLabel->connectedAccount?->stripe_account_id
            ]);
        }else {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
        }

        return Inertia::render('Bill/Stripe3DSecure', [
            'clientSecret' => $paymentIntent->client_secret,
            'paymentIntentStatus' => $paymentIntent->status,
            'paymentIntentId' => $paymentIntent->id,
            'affiliateId' => $affiliateId,
            'invoiceId' => $invoiceId,
            'nextRoute' => $nextRoute,
            'routeParam' => $routeParam,
            'stripePublishableKey' => $invoice->team->whiteLabel ? $invoice->team->whiteLabel->connectedAccount?->publishable_key : config('services.stripe.publish_key'),
        ]);
    }

    /**
     * @throws ApiErrorException
     */
    public function packageStripeCardRequires3DSecurity(Request $request)
    {
        $paymentIntentId = $request->get('paymentIntentId');
        $sessionId = $request->get('sessionId');
        $packageId = $request->get('packageId');
        $affiliateId = $request->get('affiliateId');
        $cartId = $request->get('cartId');
        $invoiceId = $request->get('invoiceId');

        if(empty($paymentIntentId)){
            throw new Exception('Payment intent id is missing!');
        }

        $invoice = GeneralInvoice::findOrFail($invoiceId);

        Stripe::setApiKey(config('services.stripe.secret_key'));

        if($invoice->team->whiteLabel){
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId, [
                'stripe_account' => $invoice->team->whiteLabel->connectedAccount?->stripe_account_id
            ]);
        }else {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
        }

        return Inertia::render('Bill/Stripe3DSecurePackage', [
            'clientSecret' => $paymentIntent->client_secret,
            'paymentIntentStatus' => $paymentIntent->status,
            'paymentIntentId' => $paymentIntent->id,
            'sessionId' => $sessionId,
            'packageId' => $packageId,
            'affiliateId' => $affiliateId,
            'cartId' => $cartId,
            'invoiceId' => $invoiceId,
            'stripePublishableKey' => $invoice->team->whiteLabel ? $invoice->team->whiteLabel->connectedAccount?->publishable_key : config('services.stripe.publish_key'),
        ]);
    }

    /**
     * @throws ApiErrorException
     */
    public function productStripeCardRequires3DSecurity(Request $request)
    {
        $paymentIntentId = $request->get('paymentIntentId');
        $sessionId = $request->get('sessionId');
        $productId = $request->get('productId');
        $affiliateId = $request->get('affiliateId');
        $cartId = $request->get('cartId');
        $invoiceId = $request->get('invoiceId');


        if(empty($paymentIntentId)){
            throw new Exception('Payment intent id is missing!');
        }

        Stripe::setApiKey(config('services.stripe.secret_key'));

        $invoice = GeneralInvoice::findOrFail($invoiceId);

        if($invoice->team->whiteLabel){
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId, [
                'stripe_account' => $invoice->team->whiteLabel->connectedAccount?->stripe_account_id
            ]);
        }else {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
        }

        return Inertia::render('Bill/Stripe3DSecureProduct', [
            'clientSecret' => $paymentIntent->client_secret,
            'paymentIntentStatus' => $paymentIntent->status,
            'paymentIntentId' => $paymentIntent->id,
            'sessionId' => $sessionId,
            'productId' => $productId,
            'affiliateId' => $affiliateId,
            'cartId' => $cartId,
            'invoiceId' => $invoiceId,
            'data' => $request->all(),
            'stripePublishableKey' => $invoice->team->whiteLabel ? $invoice->team->whiteLabel->connectedAccount?->publishable_key : config('services.stripe.publish_key'),
        ]);
    }

    /**
     * @throws ApiErrorException
     */
    public function subscriptionProductStripeCardRequires3DSecurity(Request $request)
    {
        $paymentIntentId = $request->get('paymentIntentId');
        $sessionId = $request->get('sessionId');
        $productId = $request->get('productId');
        $affiliateId = $request->get('affiliateId');
        $cartId = $request->get('cartId');
        $invoiceId = $request->get('invoiceId');
        $subscriptionProductId = $request->get('subscriptionProductId');
        $stripeSubscriptionId = $request->get('stripeSubscriptionId');
        $nextRoute = $request->get('nextRoute');
        $routeParam = $request->get('routeParam');


        if(empty($paymentIntentId)){
            throw new Exception('Payment intent id is missing!');
        }

        if(!$subscriptionProductId || !$stripeSubscriptionId){
            throw new Exception('Subscription ID is missing!');
        }

        $invoice = GeneralInvoice::findOrFail($invoiceId);

        Stripe::setApiKey(config('services.stripe.secret_key'));

        if($invoice->team->whiteLabel){
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId, [
                'stripe_account' => $invoice->team->whiteLabel->connectedAccount?->stripe_account_id
            ]);
        }else {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
        }

        return Inertia::render('Bill/Stripe3DSecureSubscriptionProduct', [
            'clientSecret' => $paymentIntent->client_secret,
            'paymentIntentStatus' => $paymentIntent->status,
            'paymentIntentId' => $paymentIntent->id,
            'sessionId' => $sessionId,
            'productId' => $productId,
            'affiliateId' => $affiliateId,
            'cartId' => $cartId,
            'invoiceId' => $invoiceId,
            'subscriptionProductId' => $subscriptionProductId,
            'stripeSubscriptionId' => $stripeSubscriptionId,
            'data' => $request->all(),
            'stripePublishableKey' => $invoice->team->whiteLabel ? $invoice->team->whiteLabel->connectedAccount?->publishable_key : config('services.stripe.publish_key'),
            'nextRoute' => $nextRoute,
            'routeParam' => $routeParam,
        ]);
    }
}
