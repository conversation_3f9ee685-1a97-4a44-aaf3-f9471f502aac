<?php

namespace App\Http\Controllers\WhiteLabel;

use App\Enums\WhiteLabel\ProductStatus;
use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\XcloudProductType;
use App\Http\Controllers\Controller;
use App\Http\Requests\ProductDuplicateRequest;
use App\Http\Requests\ProductRequest;
use App\Http\Requests\ProductUpdateRequest;
use App\Models\Bill;
use App\Models\GeneralInvoice;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel,Response::HTTP_NOT_FOUND);
        $this->authorize('manageProducts',$whiteLabel);
        $sortByStatus = $request->get('sort_by_status');
        $whiteLabelProducts = Product::allowedForWhiteLabel()
            ->active()
            ->withoutDependency()
            ->usableForWhiteLabel()
            ->get()
            ->map(function ($product) {
                $descriptions = explode("\n", trim($product->description));
                foreach ($descriptions as $description) {
                    if (str_contains($description, 'RAM')) {
                        $product['memory'] = trim(str_replace('RAM -', '', $description));
                    } elseif (str_contains($description, 'SSD')) {
                        $product['disk'] = trim(str_replace('SSD -', '', $description));
                    } elseif (str_contains($description, 'vCPU')) {
                        $product['cpu'] = trim(str_replace('vCPU -', '', $description));
                    } elseif (str_contains($description, 'Bandwidth')) {
                        $product['bandwidth'] = trim(str_replace('Bandwidth -', '', $description));
                    }
                }

                return $product;
            })
            ->groupBy(function ($product) {
                return (string) $product->type->value;
            });

        $products = $whiteLabel ? $whiteLabel->products()
            ->where('depends_on_product_id', null)
            ->when($sortByStatus, function ($query) use ($sortByStatus) {
                return $query->where('is_active', $sortByStatus === 'Active' ? ProductStatus::ACTIVE->value : ProductStatus::INACTIVE->value);
            })
            ->with('source')
            ->latest()
            ->paginate(10)
            ->through(function (Product $product) {
                if($product->source){
                    $descriptions = explode("\n", trim($product->source->description));
                    foreach ($descriptions as $description) {
                        if (str_contains($description, 'RAM')) {
                            $product['source']['memory'] = trim(str_replace('RAM -', '', $description));
                        } elseif (str_contains($description, 'SSD')) {
                            $product['source']['disk'] = trim(str_replace('SSD -', '', $description));
                        } elseif (str_contains($description, 'vCPU')) {
                            $product['source']['cpu'] = trim(str_replace('vCPU -', '', $description));
                        } elseif (str_contains($description, 'Bandwidth')) {
                            $product['source']['bandwidth'] = trim(str_replace('Bandwidth -', '', $description));
                        }
                    }
                }
                return $product;
            })
            : collect()->paginate(10);
        $skuPrefix = $whiteLabel ? 'whl-'.$whiteLabel->id : '';

        return Inertia::render('WhiteLabel/Product/Index', [
            'products' => $products,
            'whiteLabelProducts' => $whiteLabelProducts->toArray(),
            'skuPrefix' => $skuPrefix,
            'sort_by_status' => $sortByStatus ?? 'Sort By Status'
        ]);
    }

    public function details(Request $request, Product $product)
    {
        $whiteLabel = $product->whiteLabel;
        abort_unless($whiteLabel,Response::HTTP_NOT_FOUND);
        $this->authorize('manageProducts',$whiteLabel);
        $product->load('source');

        $invoiceIds = Bill::where('product_id', $product->id)->pluck('invoice_id');
        $invoices = GeneralInvoice::query()
            ->when($invoiceIds, fn ($query) =>  $query->whereIn('id', $invoiceIds))
            ->when($request->filled('invoiceDateFilter'), function($query) use ($request) {
                $query->when($request->get('invoiceDateFilter') === 'This Month', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ]);
                })->when($request->get('invoiceDateFilter') === 'Last Month', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->subMonth()->startOfMonth(),
                        Carbon::now()->subMonth()->endOfMonth()
                    ]);
                })->when($request->get('invoiceDateFilter') === 'Last 6 Months', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->subMonths(6)->startOfMonth(),
                        Carbon::now()->endOfMonth()
                    ]);
                })->when($request->get('invoiceDateFilter') === 'This Year', function ($query) {
                    $query->whereBetween('created_at', [
                        Carbon::now()->startOfYear(),
                        Carbon::now()->endOfYear()
                    ]);
                });
            })
            ->when($request->filled('invoiceStatusFilter'), function($query) use($request) {
                if ($request->get('invoiceStatusFilter') === 'Paid Invoices') {
                    $query->whereStatus(BillingStatus::Paid);
                } else if ($request->get('invoiceStatusFilter') === 'Unpaid Invoices') {
                    $query->whereStatus(BillingStatus::Unpaid);
                } else if ($request->get('invoiceStatusFilter') === 'Failed Invoices') {
                    $query->where(function ($query) {
                        $query->whereStatus(BillingStatus::Failed)
                            ->orWhere('status', BillingStatus::PaymentFailed);
                    });
                }
            })
            ->selectRaw('invoices.*, date_format(invoices.created_at, "%d %b %Y") as date')
            ->latest('id')
            ->paginate(10);

        return Inertia::render('WhiteLabel/Product/Details', [
            'product' => $product,
            'invoices' => $invoices,
            'invoiceDateFilter' => request('invoiceDateFilter','All Invoices'),
            'invoiceStatusFilter' => request('invoiceStatusFilter', 'All Invoices'),
            'isStaging' => !app()->environment('production'),
            'isAdmin' => user()->isAdmin(),
            'isImpersonating' => session()->has('impersonate'),
        ]);
    }

    public function store(ProductRequest $request)
    {
        $whiteLabel = team()->ownedWhiteLabel;
        abort_unless($whiteLabel,Response::HTTP_NOT_FOUND);
        $this->authorize('manageProducts', team()->ownedWhiteLabel);

        $productId = $request->get('product_id');
        $product = Product::find($productId);
        $title = !blank($request->get('plan_name')) ? $request->get('plan_name') : $product?->title;

        $dependentProducts = $product->where('depends_on_product_id', $productId)->get();

        $product = team()->ownedWhiteLabel->products()->create([
            'source_product_id' => $productId,
            'title' => $title,
            'slug' => $product?->slug ?: Str::slug($title),
            'sku' => $request->get('sku'),
            'type' => $request->get('server_type') ?: XcloudProductType::General->value,
            'description' => $product?->description ?? null,
            'is_active' => $request->get('is_active'),
            'show_on_display' => $product?->show_on_display ?: false,
            'renewal_type' => $product?->renewal_type ?: BillRenewalPeriod::Monthly->value,
            'service_type' => $product?->service_type ?: BillingServices::ManagedHosting->value,
            'price' => $request->get('custom_price'),
            'unit' => $product?->unit ?: 1,
            'currency' => $product?->currency ?: 'usd'
        ]);

        foreach ($dependentProducts as $dependentProduct) {
            if ($dependentProduct->service_type === BillingServices::BackupManagedHosting) {
                team()->ownedWhiteLabel->products()->create([
                    'source_product_id' => $dependentProduct->id,
                    'depends_on_product_id' => $product->id,
                    'title' => $title . ' - (' . BillingServices::BackupManagedHosting->toShortTitle().')',
                    'slug' => $dependentProduct->slug,
                    'sku' => $request->get('sku'),
                    'type' => $request->get('server_type') ?: XcloudProductType::General->value,
                    'description' => $dependentProduct->description ?? null,
                    'is_active' => $request->get('is_active'),
                    'show_on_display' => $dependentProduct->show_on_display ?: false,
                    'renewal_type' => $dependentProduct->renewal_type ?: BillRenewalPeriod::Monthly->value,
                    'service_type' => $dependentProduct->service_type ?: BillingServices::ManagedHosting->value,
                    'price' => $request->get('custom_price') * 0.2,
                    'unit' => $dependentProduct->unit ?: 1,
                    'currency' => $dependentProduct->currency ?: 'usd'
                ]);
            }
        }

        return back()->with('flash', [
            'message' => 'Product Created Successfully.'
        ]);
    }

    public function update(ProductUpdateRequest $request, Product $product)
    {
        $whiteLabel = $product->whiteLabel;
        abort_unless($whiteLabel,Response::HTTP_NOT_FOUND);
        $this->authorize('manageProducts', $whiteLabel);

        $product->update([
            'title' => $request->get('plan_name') !== $product->title ? $request->get('plan_name') : $product->title,
            'sku' => $request->get('sku') !== $product->sku ? $request->get('sku') : $product->sku,
            'price' => $request->get('custom_price') !== $product->price ? $request->get('custom_price') : $product->price,
            'is_active' => $request->get('is_active')
        ]);

        return back()->with('flash', [
            'message' => 'Product Updated Successfully.'
        ]);
    }

    public function delete(Product $product)
    {
        $whiteLabel = $product->whiteLabel;

        if($product->depends_on_product_id && $product->where('id', $product->depends_on_product_id)->exists()) {
            return back()->with('flash', [
                'message' => 'This product can not be deleted. It is a dependent product please delete the main product first',
                'type' => 'error'
            ]);
        }

        // if the product attached to any team then we can't delete it
        if ($product->teams()->exists() || Bill::where('product_id', $product->id)->exists()) {
            return back()->with('flash', [
                'message' => 'Product is purchased by someone. You can\'t delete it. Please deactivate it instead.',
                'type' => 'error'
            ]);
        }

        abort_unless($whiteLabel,Response::HTTP_NOT_FOUND);
        $this->authorize('manageProducts', $whiteLabel);

        $product->delete();
        return back()->with('flash', [
            'message' => 'Product Deleted Successfully.'
        ]);
    }

    public function duplicate(ProductDuplicateRequest $request, Product $product)
    {
        $whiteLabel = $product->whiteLabel;
        abort_unless($whiteLabel,Response::HTTP_NOT_FOUND);
        $this->authorize('manageProducts', $whiteLabel);
        $title = !blank($request->get('plan_name')) ? $request->get('plan_name') : $product?->title;

        if ($product->depends_on_product_id) {
            return back()->with('flash', [
                'message' => 'This product can not be duplicated.',
                'type' => 'error'
            ]);
        }

        $newProduct = team()->ownedWhiteLabel->products()->create([
            'source_product_id' => $product?->source?->id,
            'title' => $title,
            'slug' => $product?->slug ?: Str::slug($title),
            'sku' => $request->get('sku'),
            'type' => $request->get('server_type') ?: XcloudProductType::General->value,
            'description' => $product?->description ?? null,
            'is_active' => $request->get('is_active'),
            'show_on_display' => $product?->show_on_display ?: false,
            'renewal_type' => $product?->renewal_type ?: BillRenewalPeriod::Monthly->value,
            'service_type' => $product?->service_type ?: BillingServices::ManagedHosting->value,
            'price' => $request->get('custom_price'),
            'unit' => $product?->unit ?: 1,
            'currency' => $product?->currency ?: 'usd'
        ]);

        $dependentProducts = $product->where('depends_on_product_id', $product->id)->get();

        foreach ($dependentProducts as $dependentProduct) {
            if ($dependentProduct->service_type === BillingServices::BackupManagedHosting) {
                team()->ownedWhiteLabel->products()->create([
                    'source_product_id' => $dependentProduct->source->id,
                    'depends_on_product_id' => $newProduct->id,
                    'title' => $title . ' - (' . BillingServices::BackupManagedHosting->toShortTitle().')',
                    'slug' => $dependentProduct->slug,
                    'sku' => $request->get('sku'),
                    'type' => $request->get('server_type') ?: XcloudProductType::General->value,
                    'description' => $dependentProduct->description ?? null,
                    'is_active' => $request->get('is_active'),
                    'show_on_display' => $dependentProduct->show_on_display ?: false,
                    'renewal_type' => $dependentProduct->renewal_type ?: BillRenewalPeriod::Monthly->value,
                    'service_type' => $dependentProduct->service_type ?: BillingServices::ManagedHosting->value,
                    'price' => $request->get('custom_price') * 0.2,
                    'unit' => $dependentProduct->unit ?: 1,
                    'currency' => $dependentProduct->currency ?: BillingCurrency::USD->value
                ]);
            }
        }

        return back()->with('flash', [
            'message' => 'Product Duplicate Successfully.'
        ]);
    }
}
