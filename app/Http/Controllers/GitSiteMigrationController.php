<?php

namespace App\Http\Controllers;

use App\Enums\SiteType;
use App\Enums\ServerStatus;
use App\Models\GitSiteMigration;
use App\Models\Server;
use App\Models\Site;
use App\Models\SiteMigration;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Inertia\Inertia;

class GitSiteMigrationController extends SiteMigrationController
{
    /**
     * @throws AuthorizationException
     * @throws Exception
     */
    public function destination($model = null,$siteMigration=null)
    {
        $server = Server::find($model);
        if (!$server->isProvisioned() || !$server->isConnected() || !in_array($server->status, ServerStatus::successStates(), true)) {
            return back()->with('flash', [
                'message' => 'Server is not in a Provisioned state, current status is : '.title($server->isConnected() ? $server->status->value : 'Disconnected'),
                'type' => 'error'
            ]);
        }
        $this->authorize('create', Site::class);

        $siteMigration = intval($siteMigration)>0 ? GitSiteMigration::find($siteMigration) : new GitSiteMigration();
        return parent::destination($siteMigration);
    }

        /**
     * @throws AuthorizationException
     */
    public function domains(Server $server,Request $request)
    {
        $this->authorize('addSite', $server);
        $siteMigration= app(GitSiteMigration::class);
        if ($request->filled('siteMigration') &&  intval($request->siteMigration) > 0){
            $siteMigration = GitSiteMigration::findOrFail($request->siteMigration);
            $this->authorize('canSetupDomain', $siteMigration);
            $siteMigration->load('site');
        }
        $site_type = $request->input('siteType',SiteType::WORDPRESS->value);
        if (!in_array($site_type,SiteType::asValue(), true)) {
            $site_type = SiteType::WORDPRESS->value;
        }
        return Inertia::render('Migration/Domain', [
            'site_migration' => $siteMigration,
            'steps' => app(GitSiteMigration::class)->getFormSteps(),
            'post_route' =>app(GitSiteMigration::class)->getStepPostRouteNames()[SiteMigration::DOMAINS],
            'previous_route' =>route('site.migrate.git', [$server, $siteMigration->id ?? 'new', $site_type]),
            'previous_step' => SiteMigration::DESTINATION,
            'current_step' => SiteMigration::DOMAINS,
            'next_step' => SiteMigration::SETTINGS,
            'server' => $server,
            'is_git'=>true,
            'form' => optional($siteMigration)->form[SiteMigration::DOMAINS] ?? null,
            'hasCloudflareIntegration' => team()->hasCloudflareIntegration(),
            'site_type' => $site_type,
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function git_repo(Server $server, GitSiteMigration $siteMigration)
    {
        $this->authorize('canUseGitRepo', $siteMigration);
        $this->authorize('addSite', $server);
        $site = $siteMigration?->site;
        $git_deployment_url =  route('git-site.deploy', [hashid_encode($siteMigration->site_id)]);

        $form = optional($siteMigration)->form[GitSiteMigration::GIT_REPO] ?? null;
        $git_info = $site->getMeta('git_info');
        $git_info['deploy_script'] = $site->getMeta('deploy_script');

        if (!($git_info['deploy_script'] ?? null) && $site?->isLaravel()){
            $git_info['deploy_script'] = "{$site->php_short} $(which composer) install --no-interaction --prefer-dist --optimize-autoloader --no-dev".PHP_EOL;
            if ($site?->hasDatabase()){
                $git_info['deploy_script'] .= "{$site->php_short} artisan migrate --force".PHP_EOL;
            }
            $git_info['deploy_script'] .= "{$site->php_short} artisan optimize:clear".PHP_EOL;
        }

        return Inertia::render('Migration/GitRepoConfig', [
            'site_migration' => $siteMigration,
            'steps' => $siteMigration->getFormSteps(),
            'previous_step' => SiteMigration::SETTINGS,
            'current_step' => GitSiteMigration::GIT_REPO,
            'next_step' => SiteMigration::DATABASE,
            'post_route' =>app(GitSiteMigration::class)->getStepPostRouteNames()[GitSiteMigration::GIT_REPO],
            'server' => $server,
            'previous_route' => route('site.migrate.settings', [$server, $siteMigration->id ?? 'new']),
            'form' => $form,
            'git_deployment_url' => $git_deployment_url,
            'git_info'=> $git_info,
            'ssh_pub_key' => $siteMigration?->site?->sshKeyPair?->public_key ?? null,
        ]);
    }

}
