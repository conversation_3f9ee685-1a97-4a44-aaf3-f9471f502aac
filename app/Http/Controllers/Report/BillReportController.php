<?php

namespace App\Http\Controllers\Report;

use App\Enums\NotificationIntegrationTypes;
use App\Enums\Stack;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Http\Controllers\Controller;
use App\Http\Resources\BillReportResource;
use App\Jobs\Report\ExportBillsJob;
use App\Jobs\Report\UnpaidTeamsExportJob;
use App\Jobs\Report\UnpaidXcloudmanagedTeamsExportJob;
use App\Jobs\Server\DeleteServer;
use App\Jobs\Server\DeleteServerBills;
use App\Jobs\UnpaidInvoiceExportJob;
use App\Models\BackupFile;
use App\Models\BackupSetting;
use App\Models\Bill;
use App\Models\BillingPlan;
use App\Models\GeneralInvoice;
use App\Models\ManualInvoice;
use App\Models\Package;
use App\Models\Product;
use App\Models\Server;
use App\Models\Task;
use App\Models\Team;
use App\Models\TeamSubscription;
use App\Models\WhiteLabel;
use App\Services\Report\ReportService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Str;
use Inertia\Inertia;

class BillReportController extends Controller
{
    protected ReportService $reportService;
    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    public function reports(Request $request)
    {
        $this->authorize('createBilling', team());
        $currentMonth = Carbon::now()->format('F');
        $currentYear = Carbon::now()->format('Y');
        $filterByMonth = $request->get('filter_by_month') ?? $currentMonth;
        $filterByYear = $request->get('filter_by_year') ?? $currentYear;

        $generalInvoiceSale = GeneralInvoice::query()
                                    ->whereIn('status', [
                                        BillingStatus::Paid
                                    ])->select([
                                        'amount'
                                    ])->sum('amount');

        $manualInvoiceSale = ManualInvoice::query()
                                    ->where('status', BillingStatus::Paid)
                                    ->select([
                                        'amount'
                                    ])->sum('amount');

        $totalSaleXcloudManaged = Bill::whereNotNull('invoice_id')
                                    ->where('service', BillingServices::xCloudManagedHosting)
                                    /*->when($filterByMonth, function ($query) use ($filterByMonth) {
                                        $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                                        return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
                                    })
                                    ->when($filterByYear, function ($query) use ($filterByYear) {
                                        return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
                                    })*/
                                    ->whereNull('package_id')
                                    ->whereIn('status', [
                                        BillingStatus::Paid
                                    ])->select([
                                        'amount_to_pay'
                                    ])->sum('amount_to_pay');

        $totalSaleXcloudProvider = Bill::whereNotNull('invoice_id')
                                    ->where('service', BillingServices::xCloudProviderHosting)
                                    ->whereNull('package_id')
                                    ->whereIn('status', [
                                        BillingStatus::Paid
                                    ])->select([
                                        'amount_to_pay'
                                    ])->sum('amount_to_pay');

        $totalSaleSelfManaged = Bill::whereNotNull('invoice_id')->where('service', BillingServices::SelfManagedHosting)
                                    ->whereNull('package_id')
                                    /*->when($filterByMonth, function ($query) use ($filterByMonth) {
                                        $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                                        return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
                                    })
                                    ->when($filterByYear, function ($query) use ($filterByYear) {
                                        return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
                                    })*/
                                    ->whereIn('status', [
                                        BillingStatus::Paid
                                    ])->select([
                                        'amount_to_pay'
                                    ])->sum('amount_to_pay');

        $totalSaleEmailProvider = Bill::whereNotNull('invoice_id')->where('service', BillingServices::EmailProvider)
                                        ->whereNull('package_id')
                                        /*->when($filterByMonth, function ($query) use ($filterByMonth) {
                                            $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                                            return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
                                        })
                                        ->when($filterByYear, function ($query) use ($filterByYear) {
                                            return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
                                        })*/
                                        ->whereIn('status', [
                                            BillingStatus::Paid
                                        ])->select([
                                            'amount_to_pay'
                                        ])->sum('amount_to_pay');

        $totalSaleLtdPackages = GeneralInvoice::whereHas('cartForm', function ($query) {
            return $query->whereNotNull(['package_id']);
        })->where('status', BillingStatus::Paid)->sum('amount');

        $totalRefunds = GeneralInvoice::whereIn('status', [
                            BillingStatus::Refundable,
                            BillingStatus::Refunded,
                            BillingStatus::PartiallyRefunded,
                            BillingStatus::RefundPermissionPending,
                            BillingStatus::RefundPermitted
                        ])->sum('amount');

        $totalInvoiceSale = $generalInvoiceSale + $manualInvoiceSale;

        $xcloudMRR = Bill::query()
                    ->whereNotNull('invoice_id')
                    ->where('service', BillingServices::xCloudManagedHosting)
                    ->where('renewal_period', BillRenewalPeriod::Monthly)
                    ->where('status', BillingStatus::Paid)
                    ->when($filterByMonth, function ($query) use ($filterByMonth) {
                        $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                        return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
                    })
                    ->when($filterByYear, function ($query) use ($filterByYear) {
                        return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
                    })
                    //->where('created_at', '>=', Carbon::now()->subMonth()) // Last 30 days
                    ->sum('amount_to_pay');

        $xcloudProviderMRR = Bill::query()
                    ->whereNotNull('invoice_id')
                    ->where('service', BillingServices::xCloudProviderHosting)
                    ->where('renewal_period', BillRenewalPeriod::Monthly)
                    ->where('status', BillingStatus::Paid)
                    ->when($filterByMonth, function ($query) use ($filterByMonth) {
                        $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                        return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
                    })
                    ->when($filterByYear, function ($query) use ($filterByYear) {
                        return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
                    })
                    //->where('created_at', '>=', Carbon::now()->subMonth()) // Last 30 days
                    ->sum('amount_to_pay');

        $selfManagedMRR = Bill::query()
                    ->whereNotNull('invoice_id')
                    ->where('service', BillingServices::SelfManagedHosting)
                    ->where('renewal_period', BillRenewalPeriod::Monthly)
                    ->where('status', BillingStatus::Paid)
                    ->when($filterByMonth, function ($query) use ($filterByMonth) {
                        $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                        return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
                    })
                    ->when($filterByYear, function ($query) use ($filterByYear) {
                        return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
                    })
                    //->where('created_at', '>=', Carbon::now()->subMonth()) // Last 30 days
                    ->sum('amount_to_pay');

        $emailProviderMRR = Bill::query()
                    ->whereNotNull('invoice_id')
                    ->where('service', BillingServices::EmailProvider)
                    ->where('renewal_period', BillRenewalPeriod::Monthly)
                    ->where('status', BillingStatus::Paid)
                    ->when($filterByMonth, function ($query) use ($filterByMonth) {
                        $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                        return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
                    })
                    ->when($filterByYear, function ($query) use ($filterByYear) {
                        return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
                    })
                    //->where('created_at', '>=', Carbon::now()->subMonth()) // Last 30 days
                    ->sum('amount_to_pay');
        $patchstackMRR = Bill::query()
                    ->whereNotNull('invoice_id')
                    ->where('service', BillingServices::PatchstackAddon)
                    ->where('renewal_period', BillRenewalPeriod::Monthly)
                    ->where('status', BillingStatus::Paid)
                    ->when($filterByMonth, function ($query) use ($filterByMonth) {
                        $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                        return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
                    })
                    ->when($filterByYear, function ($query) use ($filterByYear) {
                        return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
                    })
                    //->where('created_at', '>=', Carbon::now()->subMonth()) // Last 30 days
                    ->sum('amount_to_pay');

        $totalMRR = $xcloudMRR + $xcloudProviderMRR + $selfManagedMRR + $emailProviderMRR + $patchstackMRR;
        $totalEmailSold = $this->reportService->getTotalEmailSold($filterByMonth, $filterByYear);
        $totalPatchstackSold = $this->reportService->getTotalPatchstackSold($filterByMonth, $filterByYear);
        $totalFreeEmail = $this->reportService->getTotalFreeEmail($filterByMonth, $filterByYear);

        return Inertia::render('Reports/Report', [
            'total_sales' => format_billing($totalInvoiceSale),
            'total_sale_xcloud_managed' => format_billing($totalSaleXcloudManaged),
            'total_sale_xcloud_provider' => format_billing($totalSaleXcloudProvider),
            'total_sale_self_managed' => format_billing($totalSaleSelfManaged),
            'total_sale_email_provider' => format_billing($totalSaleEmailProvider),
            'total_sale_refunds' => format_billing($totalRefunds),
            'general_invoice_sale' => $generalInvoiceSale,
            'total_sale_ltd_packages' => $totalSaleLtdPackages,
            'manual_invoice_sale' => $manualInvoiceSale,
            'total_monthly_recurring_revenue' => format_billing($totalMRR),
            'xcloud_mrr' => format_billing($xcloudMRR),
            'xcloud_provider_mrr' => format_billing($xcloudProviderMRR),
            'self_managed_mrr' => format_billing($selfManagedMRR),
            'email_provider_mrr' => format_billing($emailProviderMRR),
            'patchstack_mrr' => format_billing($patchstackMRR),
            'total_email_sold' => format_billing($totalEmailSold),
            'total_free_email' => $totalFreeEmail,
            'total_patchstack_sold' => format_billing($totalPatchstackSold),
            'filter_by_month' => $filterByMonth ?? $currentMonth,
            'filter_by_year' => $filterByYear ?? $currentYear,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    public function othersReport(Request $request)
    {
        $this->authorize('createBilling', team());
        $currentMonth = Carbon::now()->format('F');
        $currentYear = Carbon::now()->format('Y');
        $filterByMonth = $request->get('filter_by_month') ?? $currentMonth;
        $filterByYear = $request->get('filter_by_year') ?? $currentYear;

        $totalServers = $this->reportService->getTotalServers($filterByMonth, $filterByYear);
        $totalXcloudManagedServer = $this->reportService->getServerCount(BillingServices::xCloudManagedHosting->value, $filterByMonth, $filterByYear);
        $totalXcloudProviderServer = $this->reportService->getServerCount(BillingServices::xCloudProviderHosting->value, $filterByMonth, $filterByYear);
        $totalSelfManagedServer = $this->reportService->getServerCount(BillingServices::SelfManagedHosting->value, $filterByMonth, $filterByYear);
        $totalNginxServer = $this->reportService->getWebServerCount(Stack::Nginx->value, $filterByMonth, $filterByYear);
        $totalOlsServer = $this->reportService->getWebServerCount(Stack::OpenLiteSpeed->value, $filterByMonth, $filterByYear);
        $totalServerDeleted = $this->reportService->getServerDeletedCount($filterByMonth, $filterByYear);
        $totalServerFailed = $this->reportService->getServerFailedCount($filterByMonth, $filterByYear);
        $totalLtdServer = $this->reportService->getLtdServerCount($filterByMonth, $filterByYear);
        $totalSelfManagedLtdServer = $this->reportService->getSelfManagedLtdServerCount($filterByMonth, $filterByYear);
        $totalXcloudProviderLtdServer = $this->reportService->getXcloudProviderLtdServerCount($filterByMonth, $filterByYear);
        $totalXcloudManagedWithoutLtdServer = $this->reportService->getXcloudManagedWithoutLtdServerCount($filterByMonth, $filterByYear);
        $totalSelfManagedWithoutLtdServer = $this->reportService->getSelfManagedWithoutLtdServerCount($filterByMonth, $filterByYear);
        $totalSites = $this->reportService->getTotalSitesCount($filterByMonth, $filterByYear);
        $totalMainSites = $this->reportService->getTotalMainSitesCount($filterByMonth, $filterByYear);
        $totalStagingSites = $this->reportService->getTotalStagingSitesCount($filterByMonth, $filterByYear);
        $totalPlaygroundSites = $this->reportService->getTotalPlaygroundSitesCount($filterByMonth, $filterByYear);
        $totalNginxSites = $this->reportService->getTotalSiteWebServerCount(Stack::Nginx->value, $filterByMonth, $filterByYear);
        $totalOlsSites = $this->reportService->getTotalSiteWebServerCount(Stack::OpenLiteSpeed->value, $filterByMonth, $filterByYear);
        $totalSiteFailed = $this->reportService->getTotalSiteFailedCount($filterByMonth, $filterByYear);
        $totalSlack = $this->reportService->getTotalIntegrationCount(NotificationIntegrationTypes::Slack->value, $filterByMonth, $filterByYear);
        $totalWhatsApp = $this->reportService->getTotalIntegrationCount(NotificationIntegrationTypes::WhatsApp->value, $filterByMonth, $filterByYear);
        $totalTelegram = $this->reportService->getTotalIntegrationCount(NotificationIntegrationTypes::Telegram->value, $filterByMonth, $filterByYear);

        return Inertia::render('Reports/OthersReport', [
            'total_servers' => $totalServers,
            'total_xcloud_managed_server' => $totalXcloudManagedServer,
            'total_xcloud_provider_server' => $totalXcloudProviderServer,
            'total_self_managed_server' => $totalSelfManagedServer,
            'total_nginx_server' => $totalNginxServer,
            'total_ols_server' => $totalOlsServer,
            'total_server_deleted' => $totalServerDeleted,
            'total_server_failed' => $totalServerFailed,
            'total_ltd_server' => $totalLtdServer,
            'total_self_managed_ltd_server' => $totalSelfManagedLtdServer,
            'total_xcloud_provider_ltd_server' => $totalXcloudProviderLtdServer,
            'total_xcloud_managed_without_ltd_server' => $totalXcloudManagedWithoutLtdServer,
            'total_self_managed_without_ltd_server' => $totalSelfManagedWithoutLtdServer,
            'total_sites' => $totalSites,
            'total_main_sites' => $totalMainSites,
            'total_staging_sites' => $totalStagingSites,
            'total_playground_sites' => $totalPlaygroundSites,
            'total_nginx_sites' => $totalNginxSites,
            'total_ols_sites' => $totalOlsSites,
            'total_site_failed' => $totalSiteFailed,
            'total_slack' => $totalSlack,
            'total_whatsapp' => $totalWhatsApp,
            'total_telegram' => $totalTelegram,
            'filter_by_month' => $filterByMonth ?? $currentMonth,
            'filter_by_year' => $filterByYear ?? $currentYear,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    public function bill(Request $request)
    {
        $this->authorize('createBilling', team());

        $currentMonth = Carbon::now()->format('F');
        $currentYear = Carbon::now()->format('Y');
        $filterByMonth = $request->get('filter_by_month') ?? $currentMonth;
        $filterByYear = $request->get('filter_by_year') ?? $currentYear;

        $filters = $request->only(['billing_plan','bill_status','bill_type','offer_filter',
                                   'period_filter','start_date','end_date','package_filter','product_filter', 'team_filter', 'filter_by_month', 'filter_by_year']);

        $inputStartDate = $filters['start_date'] ?? '';
        $inputEndDate = $filters['end_date'] ?? '';

        if ($inputStartDate) {
            $startDate = Carbon::parse($inputStartDate);
            $startDayMain = $startDate->addDay();
            $filters['start_date'] = Carbon::parse($startDayMain)->format('Y-m-d');
        }

        if ($inputEndDate) {
            $endDate = Carbon::parse($inputEndDate);
            $endDayMain = $endDate->addDay();
            $filters['end_date'] = Carbon::parse($endDayMain)->format('Y-m-d');
        }

        $billsHasOffer = Bill::where('has_offer', true)
            ->billFilter($filters)
            ->sum('billing_amount');
        $billsUnpaidByUser = Bill::where('has_offer', false)
            ->where('status', BillingStatus::Unpaid)
            ->billFilter($filters)
            ->sum('amount_to_pay');
        $billsRefundable = Bill::where('has_offer', false)
            ->where('status', BillingStatus::Paid)
            ->billFilter($filters)
            ->sum('refundable_amount');
        $billsPaidUsingPlan = Bill::where('has_offer', false)
            ->whereNull('package_id')
            ->whereNull('product_id')
            ->where('status', BillingStatus::Paid)
            ->billFilter($filters)
            ->sum('amount_to_pay');

        $totalProductBills = Bill::where('has_offer', false)
            ->whereNotNull('product_id')
            ->where('status', BillingStatus::Paid)
            ->billFilter($filters)
            ->sum('amount_to_pay');

        $bills = Bill::query()
            ->billFilter($filters)
            ->billSearchFilter($request->search ?? '')
            ->latest()
            ->paginate()->through(fn($bill) => BillReportResource::make($bill));

        $billingPlans = collect(BillingPlan::pluck('name', 'id')->toArray())->map(function ($value, $key) {
            return [
                'label' => $value->value,
                'value' => $value->value
            ];
        })->values()->toArray();
        $billStatus = collect(BillingStatus::asValueLabel())->map(function ($value, $key) {
            return [
                'label' => $value,
                'value' => $key
            ];
        })->values()->toArray();
        $billType = collect(BillingServices::asValueLabel())->map(function ($value, $key) {
            return [
                'label' => $value,
                'value' => $key
            ];
        })->values()->toArray();
        $products = array_merge([
            'NONE' => null,
        ], Product::pluck('title', 'id')->toArray());
        $packages = array_merge([
            'NONE' => null,
        ], Package::pluck('name', 'id')->toArray());

        $teams = Team::select('id', 'name')->take(10)->get();
        if ($request->has('team_filter') && !$teams->contains('name', $request->team_filter)) {
            $team = Team::select('id', 'name')
                ->where('id', $request->team_filter)
                ->first();

            if ($team) {
                $teams->push($team);
            }
        }

        $teams = $teams->map(function ($team, $key) {
            return [
                'label' => $team->id.'# '.$team->name,
                'value' => $team->id
            ];
        })->values()->toArray();

        if ($request->start_date || $request->end_date) {
            $totalGeneralInvoicePaidSales = GeneralInvoice::query()
                ->whereBetween('created_at', [$request->start_date, $request->end_date])
                ->whereHas('bills', function ($query) use ($filters){
                    return $query->billFilter($filters);
                })
                ->where('status', BillingStatus::Paid)
                ->select([
                    'amount'
                ])->sum('amount');
        } else {
            $totalGeneralInvoicePaidSales = GeneralInvoice::query()
                ->where('status', BillingStatus::Paid)
                ->whereHas('bills', function ($query) use ($filters){
                    return $query->billFilter($filters);
                })
                ->select([
                    'amount'
                ])->sum('amount');
        }

        if ($request->start_date || $request->end_date) {
            $totalManualInvoicePaidSales = ManualInvoice::query()
                ->whereBetween('created_at', [$request->start_date, $request->end_date])
                ->whereHas('bills', function ($query) use ($filters){
                    return $query->billFilter($filters);
                })
                ->where('status', BillingStatus::Paid)
                ->select([
                    'amount'
                ])->sum('amount');
        } else {
            $totalManualInvoicePaidSales = ManualInvoice::query()
                ->whereHas('bills', function ($query) use ($filters){
                    return $query->billFilter($filters);
                })
                ->where('status', BillingStatus::Paid)
                ->select([
                    'amount'
                ])->sum('amount');
        }

        if ($request->start_date || $request->end_date) {
            $totalGeneralInvoiceUnpaid = GeneralInvoice::query()
                ->whereBetween('created_at', [$request->start_date, $request->end_date])
                ->whereHas('bills', function ($query) use ($filters){
                    return $query->billFilter($filters);
                })
                ->whereIn('status', [
                    BillingStatus::Unpaid,
                    BillingStatus::Failed,
                    BillingStatus::PaymentFailed
                ])->select([
                    'amount'
                ])->sum('amount');
        } else {
            $totalGeneralInvoiceUnpaid = GeneralInvoice::query()
                ->whereHas('bills', function ($query) use ($filters){
                    return $query->billFilter($filters);
                })
                ->whereIn('status', [
                    BillingStatus::Unpaid,
                    BillingStatus::Failed,
                    BillingStatus::PaymentFailed
                ])->select([
                    'amount'
                ])->sum('amount');
        }

        if ($request->start_date || $request->end_date) {
            $totalManualInvoiceUnpaid = ManualInvoice::query()
                ->whereBetween('created_at', [$request->start_date, $request->end_date])
                ->whereHas('bills', function ($query) use ($filters){
                    return $query->billFilter($filters);
                })
                ->whereIn('status', [
                    BillingStatus::Unpaid,
                    BillingStatus::Failed,
                    BillingStatus::PaymentFailed
                ])
                ->select([
                    'amount'
                ])->sum('amount');
        } else {
            $totalManualInvoiceUnpaid = ManualInvoice::query()
                ->whereIn('status', [
                    BillingStatus::Unpaid,
                    BillingStatus::Failed,
                    BillingStatus::PaymentFailed
                ])
                ->whereHas('bills', function ($query) use ($filters){
                    return $query->billFilter($filters);
                })
                ->select([
                    'amount'
                ])->sum('amount');
        }

        $totalRefunds = GeneralInvoice::whereIn('status', [
            BillingStatus::Refundable,
            BillingStatus::Refunded,
            BillingStatus::PartiallyRefunded,
            BillingStatus::RefundPermissionPending,
            BillingStatus::RefundPermitted
        ])->sum('amount');

        $totalInvoicePaidSale = $totalGeneralInvoicePaidSales + $totalManualInvoicePaidSales;
        $totalInvoiceUnpaidSales = $totalGeneralInvoiceUnpaid + $totalManualInvoiceUnpaid;

        return Inertia::render('Reports/BillReport', [
            'total_paid_sales' => format_billing($totalInvoicePaidSale),
            'total_unpaid_sales' => format_billing($totalInvoiceUnpaidSales),
            'bills_has_offer' => format_billing($billsHasOffer),
            'bills_unpaid_by_user' => format_billing($billsUnpaidByUser),
            'bills_refundable' => format_billing($billsRefundable),
            'bills_paid_using_plan' => format_billing($billsPaidUsingPlan),
            'total_product_bills' => format_billing($totalProductBills),
            'total_refunds' => format_billing($totalRefunds),
            'bills' => $bills,
            'billing_plan' => $billingPlans,
            'bill_status' => $billStatus,
            'bill_type' => $billType,
            'products' => $products,
            'packages' => $packages,
            'teams' => $teams,
            'billing_plan_filter' => $request->billing_plan ?? [],
            'bill_status_filter' => $request->bill_status ?? [],
            'bill_type_filter' => $request->bill_type ?? [],
            'offer_filter' => $request->offer_filter ?? 'Bill Has Offer Filter',
            'period_filter' => $request->period_filter ?? 'Bill Period Filter',
            'package_filter' => $request->package_filter ?? 'No Package',
            'team_filter' => $request->team_filter ?? [],
            'product_filter' => $request->product_filter ?? 'Product Filter',
            'start_date_filter' => $filters['start_date'] ?? '',
            'end_date_filter' => $filters['end_date'] ?? '',
            'search_filter' => $request->search ?? '',
            'is_filter_apply' => $request->is_filter_apply ?? false,
            'filter_by_month' => $filterByMonth ?? $currentMonth,
            'filter_by_year' => $filterByYear ?? $currentYear,
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:'])
        ]);
    }

    public function billExport(Request $request)
    {
        $exportType = $request->export_type ?? 'xlsx';
        $filters = $request->only(['billing_plan','bill_status','bill_type','offer_filter',
            'period_filter','start_date','end_date','package_filter','product_filter','team_filter','filter_by_month','filter_by_year']);

        ExportBillsJob::dispatch($filters, $exportType);
        return response()->json('initiate');
    }

    public function billExportUrl()
    {
        $billExportFileName = Cache::get('bill_export_file_name');
        $billExportUrl = Cache::get('bill_export_url');
        return response()->json([
            'bill_export_file_name' => $billExportFileName,
            'bill_export_url' => $billExportUrl
        ]);
    }

    public function getAllTeam(Request $request)
    {
        $this->authorize('createBilling', team());

        $query = Team::select('id', 'name')->latest();

        $search = $request->get('team_name');
        if (!blank($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%');
            });
        }

        $teams = $query->take(10)->get()->map(function ($team, $key) {
            return [
                'label' => $team->id.'# '.$team->name,
                'value' => $team->id
            ];
        })->values()->toArray();

        return response()->json($teams);
    }

    public function getPackageWiseSold()
    {
        $cartForms = DB::table('cart_forms')
            ->join('packages', 'cart_forms.package_id', '=', 'packages.id')
            ->join('invoices', 'cart_forms.invoice_id', '=', 'invoices.id')
            ->select('packages.id as package_id', 'packages.name as package_name',
                DB::raw('SUM(CASE WHEN invoices.status = "paid" THEN invoices.amount ELSE 0 END) as total_paid_amount'),
                DB::raw('SUM(CASE WHEN invoices.status != "paid" THEN invoices.amount ELSE 0 END) as total_unpaid_amount'),
                DB::raw('COUNT(CASE WHEN invoices.status = "paid" THEN 1 ELSE NULL END) as paid_invoice_count'),
                DB::raw('COUNT(CASE WHEN invoices.status != "paid" THEN 1 ELSE NULL END) as unpaid_invoice_count')
            )
            ->groupBy('packages.id', 'packages.name')
            ->orderByRaw('GREATEST(total_paid_amount, total_unpaid_amount) DESC')
            ->get();

        $cartForms->transform(function ($item) {
            $item->total_paid_amount = format_billing($item->total_paid_amount);
            $item->total_unpaid_amount = format_billing($item->total_unpaid_amount);
            return $item;
        });

        return response()->json($cartForms);
    }

    public function getUnpaidInvoice(Request $request)
    {
        $this->authorize('createBilling', team());

        $baseUrl = config('app.url');
        $invoices = GeneralInvoice::query()
            ->with('team')
            ->invoiceSearchFilter($request->search ?? '')
            ->whereNotIn('status', [
                BillingStatus::Paid,
                BillingStatus::Cancelled,
                BillingStatus::Refunded,
                BillingStatus::PartiallyRefunded,
                BillingStatus::RefundPermissionPending,
                BillingStatus::RefundPermitted
            ])->selectRaw('invoices.*, date_format(invoices.created_at, "%d %b %Y") as date, date_format(invoices.due_date, "%d %b %Y") as format_due_date');

        $totalReceivableAmount = GeneralInvoice::query()
            ->whereNotIn('status', [
                BillingStatus::Paid,
                BillingStatus::Cancelled,
                BillingStatus::Refunded,
                BillingStatus::PartiallyRefunded,
                BillingStatus::RefundPermissionPending,
                BillingStatus::RefundPermitted
            ])->sum('amount');

        $invoices =  $invoices->latest('id')->paginate(10);

        $invoices->getCollection()->transform(function ($invoice) use ($baseUrl) {
            $invoice->team_url = $invoice->team
                ? $baseUrl . '/admin/resources/teams/' . $invoice->team->id
                : null;
            $invoice->invoice_url = $invoice->id
                ? $baseUrl . '/admin/resources/general-invoices/' . $invoice->id
                : null;
            return $invoice;
        });

        return Inertia::render('Reports/UnpaidGeneralInvoice', [
            'invoices' => $invoices,
            'search_filter' => $request->search ?? '',
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'count' => $invoices->total(),
            'receivable_amount' => format_billing($invoices->sum('amount')),
            'total_receivable_amount' => format_billing($totalReceivableAmount)
        ]);
    }

    public function unpaidInvoiceExport(Request $request)
    {
        $exportType = $request->export_type ?? 'xlsx';
        $filters = $request->only(['search']);

        UnpaidInvoiceExportJob::dispatch($filters, $exportType);
        return response()->json('initiate');
    }

    public function unpaidInvoiceExportUrl()
    {
        $billExportFileName = Cache::get('invoice_export_file_name');
        $billExportUrl = Cache::get('invoice_export_url');
        return response()->json([
            'invoice_export_file_name' => $billExportFileName,
            'invoice_export_url' => $billExportUrl
        ]);
    }

    public function getUnpaidTeams(Request $request)
    {
        $filterByMonth = $request->get('filter_by_month');
        $filterByYear = $request->get('filter_by_year');
        $filterBySort = $request->get('filter_by_sort');

        $teams = Team::query()
            ->with('owner')
            ->searchFilter($request->search ?? '')
            ->where(function ($query) use ($filterByMonth, $filterByYear, $filterBySort) {
                $query->whereHas('generalInvoices', function ($query) use ($filterByMonth, $filterByYear, $filterBySort) {
                    $query->whereNotIn('status', [
                        BillingStatus::Paid,
                        BillingStatus::Cancelled,
                        BillingStatus::Refunded,
                        BillingStatus::PartiallyRefunded,
                        BillingStatus::RefundPermissionPending,
                        BillingStatus::RefundPermitted
                    ])
                    ->when($filterByMonth, function ($query) use ($filterByMonth) {
                        $monthNumbers = collect($filterByMonth)->map(function ($monthName) {
                            return date('m', strtotime($monthName));
                        })->toArray();

                        return $query->whereRaw('MONTH(created_at) IN (' . implode(',', $monthNumbers) . ')');
                    })
                    ->when($filterByYear, function ($query) use ($filterByYear) {
                        return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
                    })
                    ->when($filterBySort === 'Low to High', function ($query) use ($filterBySort) {
                        return $query->orderBy('amount', 'asc');
                    })
                    ->when($filterBySort === 'High to Low', function ($query) use ($filterBySort) {
                        return $query->orderBy('amount', 'desc');
                    });
                })->orWhereHas('manualInvoices', function ($query) use ($filterByMonth, $filterByYear, $filterBySort) {
                    $query->whereNotIn('status', [
                        BillingStatus::Paid,
                        BillingStatus::Cancelled,
                        BillingStatus::Refunded,
                        BillingStatus::PartiallyRefunded,
                        BillingStatus::RefundPermissionPending,
                        BillingStatus::RefundPermitted
                    ])
                    ->when($filterByMonth, function ($query) use ($filterByMonth) {
                        $monthNumbers = collect($filterByMonth)->map(function ($monthName) {
                            return date('m', strtotime($monthName));
                        })->toArray();

                        return $query->whereRaw('MONTH(created_at) IN (' . implode(',', $monthNumbers) . ')');
                    })
                    ->when($filterByYear, function ($query) use ($filterByYear) {
                        return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
                    })
                    ->when($filterBySort === 'Low to High', function ($query) use ($filterBySort) {
                        return $query->orderBy('amount', 'asc');
                    })
                    ->when($filterBySort === 'High to Low', function ($query) use ($filterBySort) {
                        return $query->orderBy('amount', 'desc');
                    });
                })->orWhereHas('bills', function ($query) use ($filterByMonth, $filterByYear, $filterBySort) {
                    $query->where('has_offer', false)->whereNotIn('status', [
                        BillingStatus::Paid,
                        BillingStatus::Cancelled,
                        BillingStatus::Refunded,
                        BillingStatus::PartiallyRefunded,
                        BillingStatus::RefundPermissionPending,
                        BillingStatus::RefundPermitted
                    ])
                    ->when($filterByMonth, function ($query) use ($filterByMonth) {
                        $monthNumbers = collect($filterByMonth)->map(function ($monthName) {
                            return date('m', strtotime($monthName));
                        })->toArray();

                        return $query->whereRaw('MONTH(bill_from) IN (' . implode(',', $monthNumbers) . ')');
                    })
                    ->when($filterByYear, function ($query) use ($filterByYear) {
                        return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
                    })
                    ->when($filterBySort === 'Low to High', function ($query) use ($filterBySort) {
                        return $query->orderBy('amount_to_pay', 'asc');
                    })
                    ->when($filterBySort === 'High to Low', function ($query) use ($filterBySort) {
                        return $query->orderBy('amount_to_pay', 'desc');
                    });
                });
            })
            ->withCount([
                'generalInvoices as unpaid_general_invoices_count' => function ($query) {
                    $query->whereNotIn('status', [
                        BillingStatus::Paid,
                        BillingStatus::Cancelled,
                        BillingStatus::Refunded,
                        BillingStatus::PartiallyRefunded,
                        BillingStatus::RefundPermissionPending,
                        BillingStatus::RefundPermitted
                    ]);
                },
                'manualInvoices as unpaid_manual_invoices_count' => function ($query) {
                    $query->whereNotIn('status', [
                        BillingStatus::Paid,
                        BillingStatus::Cancelled,
                        BillingStatus::Refunded,
                        BillingStatus::PartiallyRefunded,
                        BillingStatus::RefundPermissionPending,
                        BillingStatus::RefundPermitted
                    ]);
                },
                'bills as unpaid_bills_count' => function ($query) {
                    $query->whereNotIn('status', [
                        BillingStatus::Paid,
                        BillingStatus::Cancelled,
                        BillingStatus::Refunded,
                        BillingStatus::PartiallyRefunded,
                        BillingStatus::RefundPermissionPending,
                        BillingStatus::RefundPermitted
                    ]);
                }
            ])->withSum([
                'generalInvoices as unpaid_general_invoices_sum' => function ($query) {
                    $query->whereNotIn('status', [
                        BillingStatus::Paid,
                        BillingStatus::Cancelled,
                        BillingStatus::Refunded,
                        BillingStatus::PartiallyRefunded,
                        BillingStatus::RefundPermissionPending,
                        BillingStatus::RefundPermitted
                    ]);
                },
                'manualInvoices as unpaid_manual_invoices_sum' => function ($query) {
                    $query->whereNotIn('status', [
                        BillingStatus::Paid,
                        BillingStatus::Cancelled,
                        BillingStatus::Refunded,
                        BillingStatus::PartiallyRefunded,
                        BillingStatus::RefundPermissionPending,
                        BillingStatus::RefundPermitted
                    ]);
                }
            ], 'amount')
//            ->withSum('generalInvoices as unpaid_general_invoices_sum', 'amount')
//            ->withSum('manualInvoices as unpaid_manual_invoices_sum', 'amount')
//            ->withSum('bills as unpaid_bills_sum', 'amount_to_pay')
            ->orderBy('unpaid_general_invoices_sum', 'desc')->paginate(12);

        $baseUrl = config('app.url');
        $teams->getCollection()->transform(function ($team) use ($baseUrl) {
            $team->team_url = $baseUrl . '/admin/resources/teams/' . $team->id;
            $team->user_url = $baseUrl . '/admin/resources/users/' . $team->user_id;

            $team->unpaid_general_invoices_sum = format_billing($team->unpaid_general_invoices_sum ?: 0);
            $team->unpaid_manual_invoices_sum = format_billing($team->unpaid_manual_invoices_sum ?: 0);
            $team->unpaid_bills_sum = format_billing($team->bills->whereNotIn('status', [
                BillingStatus::Paid,
                BillingStatus::Cancelled,
                BillingStatus::Refunded,
                BillingStatus::PartiallyRefunded,
                BillingStatus::RefundPermissionPending,
                BillingStatus::RefundPermitted
            ])->where('has_offer', false)->sum('amount_to_pay') ?: 0);

            $withoutInvoiceBillAmount = format_billing($team->bills->whereNotIn('status', [
                BillingStatus::Paid,
                BillingStatus::Cancelled,
                BillingStatus::Refunded,
                BillingStatus::PartiallyRefunded,
                BillingStatus::RefundPermissionPending,
                BillingStatus::RefundPermitted
            ])->where('has_offer', false)->whereNull('invoice_id')->sum('amount_to_pay') ?: 0);

            $team->total_unpaid_amount = format_billing(($team->unpaid_general_invoices_sum + $team->unpaid_manual_invoices_sum + $withoutInvoiceBillAmount) ?: 0);

            return $team;
        });

        $totalReceivableAmount = GeneralInvoice::whereNotIn('status', [
            BillingStatus::Paid,
            BillingStatus::Cancelled,
            BillingStatus::Refunded,
            BillingStatus::PartiallyRefunded,
            BillingStatus::RefundPermissionPending,
            BillingStatus::RefundPermitted
        ])->sum('amount') +

        ManualInvoice::whereNotIn('status', [
            BillingStatus::Paid,
            BillingStatus::Cancelled,
            BillingStatus::Refunded,
            BillingStatus::PartiallyRefunded,
            BillingStatus::RefundPermissionPending,
            BillingStatus::RefundPermitted
        ])->sum('amount') +

        Bill::whereNull('invoice_id')->where('has_offer', false)->whereNotIn('status', [
            BillingStatus::Paid,
            BillingStatus::Cancelled,
            BillingStatus::Refunded,
            BillingStatus::PartiallyRefunded,
            BillingStatus::RefundPermissionPending,
            BillingStatus::RefundPermitted
        ])->sum('amount_to_pay');

        return Inertia::render('Reports/UnpaidTeam', [
            'teams' => $teams,
            'count' => $teams->total(),
            'receivable_amount' => format_billing($teams->sum('total_unpaid_amount')),
            'total_receivable_amount' => format_billing($totalReceivableAmount),
            'search_filter' => $request->search ?? '',
            'filter_by_month' => $filterByMonth ?? [],
            'filter_by_year' => $filterByYear ?? '',
            'filter_by_sort' => $filterBySort ?? 'Filter by Sort',
        ]);
    }

    public function unpaidTeamsExport(Request $request)
    {
        $exportType = $request->export_type ?? 'xlsx';
        $filters = $request->only(['search', 'filter_by_month', 'filter_by_year', 'filter_by_sort']);

        UnpaidTeamsExportJob::dispatch($filters, $exportType);
        return response()->json('initiate');
    }

    public function unpaidTeamsExportUrl()
    {
        $teamsExportFileName = Cache::get('teams_export_file_name');
        $teamsExportUrl = Cache::get('teams_export_url');
        return response()->json([
            'teams_export_file_name' => $teamsExportFileName,
            'teams_export_url' => $teamsExportUrl
        ]);
    }

    public function getXcloudManagedUnpaidTeams(Request $request)
    {
        $teams = Team::with('owner')
            ->searchFilter($request->search ?? '')
            ->whereHas('bills', function ($bills) {
            $bills->whereIn('service', [
                BillingServices::xCloudManagedHosting,
                BillingServices::xCloudProviderHosting,
                BillingServices::BackupXCloudManagedHosting,
                BillingServices::BackupXCloudProviderHosting
            ])->whereNotIn('status', [
                BillingStatus::Paid,
                BillingStatus::Cancelled,
                BillingStatus::Refunded,
                BillingStatus::PartiallyRefunded,
                BillingStatus::RefundPermissionPending,
                BillingStatus::RefundPermitted
            ]);
        })->withCount([
            'bills as unpaid_bills_count' => function ($query) {
                $query->where('service', BillingServices::xCloudManagedHosting)
                    ->whereNotIn('status', [
                        BillingStatus::Paid,
                        BillingStatus::Cancelled,
                        BillingStatus::Refunded,
                        BillingStatus::PartiallyRefunded,
                        BillingStatus::RefundPermissionPending,
                        BillingStatus::RefundPermitted
                    ]);
            }
        ])->withSum([
            'bills as unpaid_bills_sum' => function ($query) {
                $query->where('service', BillingServices::xCloudManagedHosting)
                    ->whereNotIn('status', [
                        BillingStatus::Paid,
                        BillingStatus::Cancelled,
                        BillingStatus::Refunded,
                        BillingStatus::PartiallyRefunded,
                        BillingStatus::RefundPermissionPending,
                        BillingStatus::RefundPermitted
                    ]);
            }
        ], 'amount_to_pay')->orderBy('unpaid_bills_sum', 'desc')->paginate(12);

        $baseUrl = config('app.url');
        $teams->getCollection()->transform(function ($team) use ($baseUrl) {
            $team->team_url = $baseUrl . '/admin/resources/teams/' . $team->id;
            $team->user_url = $baseUrl . '/admin/resources/users/' . $team->user_id;
            $team->unpaid_bills_sum = format_billing($team->unpaid_bills_sum?: 0);
            return $team;
        });

        return Inertia::render('Reports/UnpaidxCloudManagedTeam', [
            'teams' => $teams,
            'count' => $teams->total(),
            'search_filter' => $request->search ?? '',
            'receivable_amount' => format_billing($teams->sum('unpaid_bills_sum')),
            'total_receivable_amount' => format_billing(Bill::whereIn('service', [
                BillingServices::xCloudManagedHosting,
                BillingServices::xCloudProviderHosting,
                BillingServices::BackupXCloudManagedHosting,
                BillingServices::BackupXCloudProviderHosting
            ])->whereNotIn('status', [
                BillingStatus::Paid,
                BillingStatus::Cancelled,
                BillingStatus::Refunded,
                BillingStatus::PartiallyRefunded,
                BillingStatus::RefundPermissionPending,
                BillingStatus::RefundPermitted
            ])->sum('amount_to_pay')),
            'total_active_receivable_amount' => format_billing(Bill::whereIn('service', [
                BillingServices::xCloudManagedHosting,
                BillingServices::xCloudProviderHosting,
                BillingServices::BackupXCloudManagedHosting,
                BillingServices::BackupXCloudProviderHosting
            ])->whereNotIn('status', [
                BillingStatus::Paid,
                BillingStatus::Cancelled,
                BillingStatus::Refunded,
                BillingStatus::PartiallyRefunded,
                BillingStatus::RefundPermissionPending,
                BillingStatus::RefundPermitted
            ])->where('service_is_active', true)->sum('amount_to_pay')),
        ]);
    }

    public function xcloudManagedUnpaidTeamsExport(Request $request)
    {
        $exportType = $request->export_type ?? 'xlsx';
        $filters = $request->only(['search']);

        UnpaidXcloudmanagedTeamsExportJob::dispatch($filters, $exportType);
        return response()->json('initiate');
    }

    public function xcloudManagedUnpaidTeamsExportUrl()
    {
        $xcloudManagedTeamsExportFileName = Cache::get('xcloud_managed_teams_export_file_name');
        $xcloudManagedTeamsExportUrl = Cache::get('xcloud_managed_teams_export_url');
        return response()->json([
            'xcloud_managed_teams_export_file_name' => $xcloudManagedTeamsExportFileName,
            'xcloud_managed_teams_export_url' => $xcloudManagedTeamsExportUrl
        ]);
    }

    public function getRefundedInvoice(Request $request)
    {
        $this->authorize('createBilling', team());

        $baseUrl = config('app.url');
        $invoices = GeneralInvoice::query()
            ->with('team')
            ->invoiceSearchFilter($request->search ?? '')
            ->whereIn('status', [
                BillingStatus::Refunded,
                BillingStatus::PartiallyRefunded,
                BillingStatus::RefundPermissionPending,
                BillingStatus::RefundPermitted
            ])->selectRaw('invoices.*, date_format(invoices.created_at, "%d %b %Y") as date, date_format(invoices.due_date, "%d %b %Y") as format_due_date');

        $totalReceivableAmount = GeneralInvoice::query()
            ->whereIn('status', [
                BillingStatus::Refunded,
                BillingStatus::PartiallyRefunded,
                BillingStatus::RefundPermissionPending,
                BillingStatus::RefundPermitted
            ])->sum('amount');

        $invoices =  $invoices->latest('id')->paginate(10);

        $invoices->getCollection()->transform(function ($invoice) use ($baseUrl) {
            $invoice->team_url = $invoice->team
                ? $baseUrl . '/admin/resources/teams/' . $invoice->team->id
                : null;
            $invoice->invoice_url = $invoice->id
                ? $baseUrl . '/admin/resources/general-invoices/' . $invoice->id
                : null;
            return $invoice;
        });

        return Inertia::render('Reports/RefundedInvoice', [
            'invoices' => $invoices,
            'search_filter' => $request->search ?? '',
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'count' => $invoices->total(),
            'receivable_amount' => format_billing($invoices->sum('amount')),
            'total_receivable_amount' => format_billing($totalReceivableAmount)
        ]);
    }

    public function getCancelledInvoice(Request $request)
    {
        $this->authorize('createBilling', team());

        $baseUrl = config('app.url');
        $invoices = GeneralInvoice::query()
            ->with('team')
            ->invoiceSearchFilter($request->search ?? '')
            ->whereIn('status', [
                BillingStatus::Cancelled,
            ])->selectRaw('invoices.*, date_format(invoices.created_at, "%d %b %Y") as date, date_format(invoices.due_date, "%d %b %Y") as format_due_date');

        $totalReceivableAmount = GeneralInvoice::query()
            ->whereIn('status', [
                BillingStatus::Cancelled,
            ])->sum('amount');

        $invoices =  $invoices->latest('id')->paginate(10);

        $invoices->getCollection()->transform(function ($invoice) use ($baseUrl) {
            $invoice->team_url = $invoice->team
                ? $baseUrl . '/admin/resources/teams/' . $invoice->team->id
                : null;
            $invoice->invoice_url = $invoice->id
                ? $baseUrl . '/admin/resources/general-invoices/' . $invoice->id
                : null;
            return $invoice;
        });

        return Inertia::render('Reports/CancelledInvoice', [
            'invoices' => $invoices,
            'search_filter' => $request->search ?? '',
            'access_permissions' => team()->permissions(['account:', 'billing:','server:','site:']),
            'count' => $invoices->total(),
            'receivable_amount' => format_billing($invoices->sum('amount')),
            'total_receivable_amount' => format_billing($totalReceivableAmount)
        ]);
    }

    public function deleteServerBills(Server $server)
    {
        if ($server) {
            $hasPaidInvoice = $server->bills()->whereHas('invoice',function($query){
                $query->where(['status'=>BillingStatus::Paid]);
            })->exists();

            if ($hasPaidInvoice) {
                return back()->with('flash', [
                    'type' => 'error',
                    'message' => 'Server has paid invoices. Please cancel or refund the invoices first.'
                ]);
            }

            #get sites:id of the server
            $sites = $server->sites()->pluck('id')->toArray();

            if (count($sites) > 0) {
                #delete(from db) all the backup files of the sites
                Log::info('DeleteServer: Deleting backup files of sites: by '.user()->name);
                BackupFile::whereHas('backupSetting', fn($q) => $q->whereIn('site_id', $sites))->delete();
                #delete all the backup settings of the sites
                BackupSetting::whereIn('site_id', $sites)->delete();
            }

            //if the server provider is xcloud or digital ocean need to delete the server from the provider
            if ($server->cloudProvider?->isXCloudOrWhiteLabel()) {
                Log::info("DeleteServer: Deleting server  {$server->name} with provider by ".user()->name);
                $task = Task::create([
                    'name' => 'DeleteServer',
                    'status' => 'pending',
                    'server_id' => $server->id,
                    'initiated_by' => auth()->id(),
                    'team_id' => $server->team_id,
                ]);

                $jobs = [
                    new DeleteServer($task, $server),
                ];

                Log::info("DeleteServer: Deleting bills of server  {$server->name} with provider by ".user()->name);
                $jobs[] = new DeleteServerBills($server);

                Bus::chain($jobs)->dispatch();
            } else {
                Log::info("DeleteServer: Deleting server {$server->name} by ".user()->name);
                $server->forceDelete();

                Log::info("DeleteServer: Deleting bills of server  {$server->name} with provider by ".user()->name);
                DeleteServerBills::dispatch($server);
            }

            return back()->with('flash', [
                'type' => 'success',
                'message' => $server->name . ' Server Delete Initiated'
            ]);
        }

        return back()->with('flash', [
            'type' => 'error',
            'message' => 'Server not found'
        ]);
    }

    public function billingReportByTeam(Request $request)
    {
        $missingBillsTeams = DB::table('teams')
            ->leftJoin('bills', function ($join) {
                $join->on('teams.id', '=', 'bills.team_id')
                    ->where('bills.service_is_active', '=', 1)
                    ->where('bills.renewal_period', '=', BillRenewalPeriod::Monthly->value)
                    ->where('bills.has_offer', '=', 0)
                    ->where('bills.is_deleted', '=', false);
            })
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('bills as prev_bills')
                    ->whereColumn('prev_bills.team_id', 'teams.id')
                    ->whereNull('prev_bills.deleted_at')
                    ->where('prev_bills.service_is_active', '=', 1);
            })
            ->where(function ($query) {
                $query->whereNull('bills.id')
                    ->orWhereNotExists(function ($subquery) {
                        $subquery->select(DB::raw(1))
                            ->from('bills as recent_bills')
                            ->whereColumn('recent_bills.team_id', 'bills.team_id')
                            ->whereRaw('MONTH(recent_bills.bill_from) = ?', [now()->month])
                            ->whereNull('recent_bills.deleted_at');
                    });
            })
            ->select(
                'teams.id',
                'teams.name',
                'teams.email',
                'bills.generator_type',
                'bills.generator_id',
                DB::raw('MIN(bills.bill_from) as first_generated_time'),
                DB::raw('MAX(bills.bill_from) as last_generated_time'),
                DB::raw('TIMESTAMPDIFF(MONTH, MIN(bills.bill_from), CURDATE()) + 1 as total_months_should_be_generated'),
                DB::raw('COUNT(DISTINCT CONCAT(YEAR(bills.bill_from), "-", MONTH(bills.bill_from))) as total_months_generated')
            )
            ->groupBy('teams.id', 'bills.generator_id', 'bills.generator_type')
            ->havingRaw('total_months_should_be_generated > total_months_generated')
            ->get();

        $baseUrl = config('app.url');
        $formattedTeams = $missingBillsTeams->groupBy('id')->map(function ($teamBills) use ($baseUrl) {
            return [
                'team_id' => $teamBills[0]->id,
                'team_name' => $teamBills[0]->name,
                'team_email' => $teamBills[0]->email,
                'team_url' => $baseUrl . '/admin/resources/teams/' . $teamBills[0]->id,
                'none_of_generator_exists' => $teamBills->map(function ($bill) {
                    return (new $bill->generator_type())->where('id', $bill->generator_id)->exists();
                })->isNotEmpty(),
                'generators' => $teamBills->map(function ($bill) {
                    return [
                        'generator_type' => $bill->generator_type,
                        'generator_id' => $bill->generator_id,
                        'is_exists' => (new $bill->generator_type())->where('id', $bill->generator_id)->exists(),
                        'nova_route' => url('/admin/resources/' . Str::plural(Str::kebab(class_basename($bill->generator_type))) . '/' . $bill->generator_id),
                        'first_generated_time' => $bill->first_generated_time,
                        'last_generated_time' => $bill->last_generated_time,
                        'total_months_should_be_generated' => $bill->total_months_should_be_generated,
                        'total_months_generated' => $bill->total_months_generated
                    ];
                })->toArray(),
            ];
        })->paginate(30);

       return Inertia::render('Reports/BillingReportByTeam', [
            'missing_teams' => $missingBillsTeams,
            'teams' => $formattedTeams,
            'search_filter' => $request->search ?? '',
        ]);
    }

    public function resellerReport(Request $request)
    {
        $this->authorize('createBilling', team());

        $subscriptionProductTeams = TeamSubscription::query()
            ->searchFilter($request->get('search') ?? '')
            ->with(['team:id,name','subscriptionProduct:id,name'])
            ->latest()
            ->paginate(10);

        return Inertia::render('Reports/WhiteLabelReseller', [
            'subscriptionProductTeams' => $subscriptionProductTeams,
            'search_filter' => $request->get('search') ?? '',
        ]);
    }

    public function resellerReportExport(Request $request)
    {

    }

    public function resellerReportExportUrl()
    {

    }
}
