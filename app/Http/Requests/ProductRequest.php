<?php

namespace App\Http\Requests;

use App\Models\Product;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProductRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'custom_price' => [
                'required',
                'numeric',
                function ($attribute, $value, $fail) {
                    $parentProductId = $this->input('product_id');

                    if ($parentProductId) {
                        $parentProduct = Product::find($parentProductId);
                        $minPrice = round($parentProduct?->price * 1.1, 2);
                        $customPrice = round(doubleval($value), 2);
                        if ($parentProduct && $customPrice < $minPrice) {
                            $fail("The custom price must be at least 10% higher than the original product's price of {$parentProduct->price}. Minimum allowed is {$minPrice}.");
                        }
                    }
                },
            ],
            'sku' => [
                'required',
                'string',
                'max:20',
                Rule::unique('products')->where(function ($query) {
                    return $query->where('white_label_id', team()->ownedWhiteLabel?->id)
                        ->whereNull('deleted_at');
                })
            ],
            'limit_model_type' => [
                'nullable',
                'string'
            ],
            'limit_model_quantity' => [
                Rule::requiredIf(fn () => $this->input('limit_model_type') !== null),
                'nullable',
                'integer',
                'min:1'
            ]
        ];
    }

    public function authorize(): bool
    {
        return user()->can('manageProducts', team()->ownedWhiteLabel);
    }
}
