<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OnboardingBrandSetupRequest extends FormRequest
{
    public function rules()
    {
        return [
            'brand_name' => 'required|string',
            'email' => 'required|email|string',
            'support_email' => 'required|email|string',
            'contact_number' => 'required|string',
            'address' => 'required|string'
        ];
    }

    public function authorize()
    {
        return user()->can('manageBrand', team()->ownedWhiteLabel);
    }
}
