<?php

namespace App\Http\Requests;

use App\Rules\DatabasePassword;
use App\Rules\DoManagedDatabaseValidation;
use App\Rules\SiteNameRule;
use App\Rules\SiteUserRule;
use App\Rules\StagingDemoSitesOneLevelDomainRule;
use App\Services\Database\DatabaseNameGenerator;
use App\Services\Database\DatabaseProvider;
use App\Models\PhpVersion;
use App\Services\WordPress\WordPressVersion;
use App\Validator\AliasesValidation;
use App\Validator\ReservedDomainValidationForAdditionalDomains;
use App\Validator\TagValidation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;

class SiteCreationRequest extends FormRequest
{
    public function authorize(): bool
    {
       return Gate::allows('addSite', $this->route('server'));
    }

    public function rules(): array
    {
        return [
            'name' => SiteNameRule::nameRules(
                $this->get('domain_parking_method'),
                $this->filled('name'),
                $this->route('server')->id
            ),

            'additional_domains' => [
                'nullable',
                new AliasesValidation,
                $this->get('domain_parking_method') === 'go_live' ? new ReservedDomainValidationForAdditionalDomains : null
            ],

            'title' => ['required', 'max:256'],

            'type' => ['in:wordpress'],

            'ssl_provider' => [
                'nullable', 'in:xcloud,custom,cloudflare', function ($attribute, $value, $fail) {
                    if ($value == 'xcloud' && !$this->route('server')->hasDnsRecord($this->get('name'))) {
                        $fail(__('dns.verification_failed'));
                    }
                }
            ],
            'ssl_certificate' => ['required_if:ssl_provider,custom'],
            'ssl_private_key' => ['required_if:ssl_provider,custom'],

            'database_provider' => ['required', DatabaseProvider::asRule()],
            'enable_full_page_cache' => 'nullable|boolean',
            'enable_redis_object_caching' => 'nullable|boolean',

            'managed_database_options.do_cluster_name' => new DoManagedDatabaseValidation($this->input('database_provider'), $this->input('managed_database_mode')),
            'managed_database_options.do_cluster_size' => new DoManagedDatabaseValidation($this->input('database_provider'), $this->input('managed_database_mode')),
            'managed_database_options.do_cluster_node' => new DoManagedDatabaseValidation($this->input('database_provider'), $this->input('managed_database_mode')),
            'managed_database_options.do_cluster_region' => new DoManagedDatabaseValidation($this->input('database_provider'), $this->input('managed_database_mode')),
            'managed_database_options.do_cluster_id' => new DoManagedDatabaseValidation($this->input('database_provider'), $this->input('managed_database_mode')),
            'managed_database_options.do_existing_database' => new DoManagedDatabaseValidation($this->input('database_provider'), $this->input('managed_database_mode')),

            'database_name' => ['nullable', 'max:32','lowercase','alpha_dash'],
            'database_user' => ['nullable', 'max:32','lowercase','alpha_dash'],
            'database_password' => DatabasePassword::rules(),
            'database_host' => ['required_if:database_provider,custom', 'nullable', 'max:256'],
            'database_port' => ['required_if:database_provider,custom', 'nullable', 'max:5'],

            'admin_user' => ['nullable', 'max:32'],
            'admin_password' => ['nullable', 'max:64'],
            'admin_email' => ['nullable', 'email'],

            'site_user' => SiteUserRule::rules($this->route('server')->id),
            'blueprint_id' => ['nullable', 'exists:blue_prints,id'],
            'prefix' => [
                'required', 'ends_with:_', 'max:32',
            ],

            'php_version' => [
                'required', 'string', PhpVersion::asRule(acceptNull: false, server: $this->route('server'))
            ],

            'wordpress_version' => [
                'required', 'string', WordPressVersion::asRule()
            ],
            'tags' => TagValidation::tagsRules(),
            'tags.*' => TagValidation::tagRules(),
            'deploy_script' => 'nullable|string',
            'enable_multisite' => 'nullable|boolean',
            'multisite_subdomain' => [
                'nullable',
                'boolean',
                function ($attribute, $value, $fail) {
                    if ($value && filter_var($this->get('name'), FILTER_VALIDATE_IP)) {
                        $fail("Subdomain multisite is not supported for IP addresses.");
                    }
                }
            ],
        ];
    }

    public function messages(): array
    {
        return [
            ...TagValidation::tagMessage(),
            ...DatabasePassword::messages(),
            ...SiteUserRule::messages(),
        ];
    }

    public function getSiteData()
    {
        $validated = collect($this->validated())->except(['enable_multisite','multisite_subdomain','admin_email','ssl_certificate', 'ssl_private_key', 'tags', 'deploy_script','enable_full_page_cache','enable_redis_object_caching','blueprint_id'])->toArray();
        $validated['site_user'] ??= DatabaseNameGenerator::generate($this->get('name'));
        $validated['database_name'] ??= DatabaseNameGenerator::generate('s_'.$this->get('name'));
        $validated['database_user'] ??= DatabaseNameGenerator::generate('u_'.$this->get('name'));
        $validated['meta->admin_email'] = $this->get('admin_email');
        $validated['meta->enable_multisite'] = $this->boolean('enable_multisite');
        $validated['meta->multisite_subdomain'] = $this->boolean('multisite_subdomain');
        $validated['meta->user_id'] = user()?->id;
        $validated['database_password'] ??= DatabasePassword::randomPassword();
        $validated['redis_password'] ??= Str::random(32);
        $validated['admin_user'] ??= Str::random(40);
        $validated['admin_user'] ??= Str::random(40);
        $validated['admin_password'] ??= Str::random(40);
        $validated['managed_database_options']['do_cluster_name'] ??= DatabaseNameGenerator::generateDOClusterName($this->get('name'));
        if($this->boolean('enable_multisite')){
            $validated['meta->enable_multisite'] = true;
            $validated['meta->multisite_subdomain'] = $this->boolean('multisite_subdomain') && !filter_var($this->get('name'), FILTER_VALIDATE_IP);
        }
        if($this->filled('blueprint_id')){
            $validated['meta->blueprint_id'] = $this->get('blueprint_id');
        }
        return $validated;
    }

    public function sslCertificate(): array
    {
        $validated = $this->validated();

        return [
            'status' => 'new',
            'provider' => $validated['ssl_provider'],
            'ssl_certificate' => $validated['ssl_certificate'],
            'ssl_private_key' => $validated['ssl_private_key'],
        ];
    }
}
