<?php

namespace App\Http\Requests;

use App\Models\Product;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProductUpdateRequest extends FormRequest
{
    public function rules()
    {
        return [
            'custom_price' => [
                'required',
                'numeric',
                function ($attribute, $value, $fail) {
                    $parentProductId = $this->input('product_id');

                    if ($parentProductId) {
                        $currentProduct = Product::where('id', $parentProductId)->select('source_product_id')->first();
                        if (!blank($currentProduct) && isset($currentProduct->source_product_id)) {
                            $parentProduct = Product::find($currentProduct->source_product_id);
                            $minPrice = round($parentProduct?->price * 1.1, 2);
                            $customPrice = round(doubleval($value), 2);
                            if ($parentProduct && $customPrice < $minPrice) {
                                $fail("The custom price must be at least 10% higher than the original product's price of {$parentProduct->price}. Minimum allowed is {$minPrice}.");
                            }
                        }
                    }
                },
            ],
            'sku' => [
                'required',
                'string',
                'max:20',
                Rule::unique('products', 'sku')
                    ->where(function ($query) {
                        return $query->where('white_label_id', $this->route('product')?->id)
                            ->whereNull('deleted_at');
                    })
                    ->ignore($this->input('product_id')),
            ],
            'limit_model_type' => [
                'nullable',
                'string'
            ],
            'limit_model_quantity' => [
                'nullable',
                'integer',
                'min:1'
            ]
        ];
    }

    public function authorize(): bool
    {
        return user()->can('manageProducts', $this->route('product')?->whiteLabel);
    }
}
