<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StackConfigureUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return user()->can('update', $this->route('site'));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'enable_php_execution_on_upload_directory' => 'boolean',
            'enable_xml_rpc' => 'boolean',
            'modify_x_frame_options' => 'boolean',
            'enable_custom_robots_txt' => 'boolean',
            'x_frame_options' => [
                'required_if:modify_x_frame_options,1',
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    #allow from * by regex
                    // Allow from * or specific domain by regex
                    if (preg_match('/^ALLOW-FROM\s+(https?:\/\/\*|https?:\/\/[a-z0-9.-]+(?:\s+https?:\/\/[a-z0-9.-]+)*)$/i', $value)) {
                        return;
                    }
                    if (!in_array(strtoupper($value), ['SAMEORIGIN', 'DENY', 'ALLOWALL'])) {
                        $fail('Invalid value for X-Frame-Options. Please use one of: SAMEORIGIN, DENY, ALLOWALL, ALLOW-FROM https://example.com or ALLOW-FROM https://example2.com https://example3.com');
                    }
                },
            ],
            'enable_7g_firewall' => ['boolean', function ($attribute, $value, $fail){
                if ($value && $this->boolean('enable_8g_firewall')) {
                    $fail('You can not enable 7G and 8G firewall at the same time');
                }
            }],
            'enable_8g_firewall' => ['boolean', function ($attribute, $value, $fail) {
                if ($value && $this->boolean('enable_7g_firewall')) {
                    $fail('You can not enable 7G and 8G firewall at the same time');
                }
            }],
            'enable_ai_bot_blocker' => ['boolean'],
            'wp_fail2ban' => 'array',
            'wp_fail2ban.enable' => 'boolean',
            'wp_fail2ban.log_failed_logins' => 'boolean',
            'wp_fail2ban.block_common_usernames' => 'boolean', 
            'wp_fail2ban.block_user_enumeration' => 'boolean',
            'wp_fail2ban.protect_comments' => 'boolean',
            'wp_fail2ban.block_spam' => 'boolean',
            'wp_fail2ban.guard_password_resets' => 'boolean',
            'wp_fail2ban.guard_pingbacks' => 'boolean'
        ];
    }
}
