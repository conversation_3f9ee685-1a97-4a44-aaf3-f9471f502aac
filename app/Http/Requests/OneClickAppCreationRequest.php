<?php

namespace App\Http\Requests;

use App\Enums\SiteStatus;
use App\Enums\SiteType;
use App\Models\PhpVersion;
use App\Models\Server;
use App\Models\Site;
use App\Models\SslCertificate;
use App\Rules\DatabasePassword;
use App\Rules\SiteNameRule;
use App\Rules\SiteUserRule;
use App\Services\Database\DatabaseNameGenerator as Generator;
use App\Services\Database\DatabaseProvider;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class OneClickAppCreationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if ($this->route('server') instanceof Server) {
            return $this->user()->can('addSite', $this->route('server'));
        }

        $server = Server::find($this->route('server'));

        return $server && $this->user()->can('addSite', $server);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => SiteNameRule::nameRules(
                $this->get('domain_parking_method'),
                $this->filled('name'),
                $this->route('server')->id
            ),
            'title' => ['required', 'string', 'max:255'],
            'app_slug' => ['required', 'string', 'max:255', SiteType::asOneClickRule()],
            'tags' => ['nullable', 'array'],
            'tags.*' => ['nullable', 'string', 'max:255'],
            'domain_parking_method' => ['required', 'string', 'in:go_live,staging_env'],
            'selected_staging_domain' => ['required_if:domain_parking_method,staging_env', 'string'],
            'site_user' => SiteUserRule::rules($this->route('server')->id),

            // Admin user fields
            'admin_user' => ['nullable', 'max:32'],
            'admin_password' => ['nullable', 'max:64'],
            'admin_email' => ['nullable', 'email'],

            // Cloudflare integration fields
            'domain_active_on_cloudflare' => ['nullable', 'boolean'],
            'cloudflare_account_id' => ['nullable', 'string'],
            'cloudflare_zone_id' => ['nullable', 'string'],
            'subdomain' => ['nullable', 'string'],
            'site_name' => ['nullable', 'string'],
        ];

        // Get the app details from SiteType enum
        $appSlug = request()->input('app_slug');
        $appDetails = SiteType::getAppDetailsBySlug($appSlug);

        $needsDatabase = $appDetails['needs_database'] ?? false;
        $isNodeApp = $appDetails['is_node_app'] ?? false;

        if ($needsDatabase) {
            // Database fields
            $rules['database_provider'] = ['nullable', DatabaseProvider::asRule()];
            $rules['database_name'] = ['nullable', 'max:32', 'lowercase', 'alpha_dash'];
            $rules['database_user'] = ['nullable', 'max:32', 'lowercase', 'alpha_dash'];
            $rules['database_password'] = DatabasePassword::rules();
            $rules['database_host'] = ['required_if:database_provider,custom', 'nullable', 'max:256'];
            $rules['database_port'] = ['required_if:database_provider,custom', 'nullable', 'max:5'];
        }

        // Add port validation only for Node.js apps
        if ($isNodeApp) {
            $rules['port'] = [
                'required',
                'integer',
                'min:1024',
                'max:65535',
                function ($value, $fail) {
                    // Get the server ID from the route
                    $server = request()->route('server');
                    $serverId = $server instanceof Server ? $server->id : $server;

                    // Check if port is already in use by another site
                    $portExists = Site::where('server_id', $serverId)
                        ->where('port', $value)
                        ->exists();

                    if ($portExists) {
                        $fail("The port is already in use by another site on this server.");
                    }
                }
            ];
        }

        // Add php_version validation for non-Node.js apps
        if (!$isNodeApp) {
            // This is only case for PHPMyAdmin app, it doesn't need Database fields
            $rules['php_version'] = ['required', PhpVersion::asOneClickAppRule(acceptNull: false, server: $this->route('server'),appSlug: $this->get('app_slug'))];
        }

        // Add specific rules for Mautic app
        if ($appSlug === SiteType::MAUTIC->value) {
            $rules['admin_user'] = ['required', 'string', 'max:32'];
            $rules['admin_password'] = ['required', 'string', 'min:8', 'max:64'];
            $rules['admin_email'] = ['required', 'email'];
        }

        return $rules;
    }

    public function getSiteData(): array
    {
        $data = $this->validated();

        $siteData = [
            'name' => rtrim($data['name'], '/'),
            'title' => $data['title'],
            'type' => $data['app_slug'],
            'status' => SiteStatus::NEW,
        ];

        // Get the app details from SiteType enum
        $appSlug = request()->input('app_slug');
        $appDetails = SiteType::getAppDetailsBySlug($appSlug);

        $isNodeApp = $appDetails['is_node_app'] ?? false;
        $needsDatabase = $appDetails['needs_database'] ?? false;

        // Add php_version if not Node.js app
        if (!$isNodeApp) {
            $siteData['php_version'] = $data['php_version'];
        }

        // Add port if Node.js app
        if ($isNodeApp && isset($data['port'])) {
            $siteData['port'] = $data['port'];
        }

        // Add site user
        $siteData['site_user'] = empty($data['site_user'])
            ? Generator::generate($data['name'])
            : $data['site_user'];

        // Add database fields
        if ($needsDatabase) {
            $siteData['database_name'] = $data['database_name'] ?? Generator::generate('s_'.$data['name']);
            $siteData['database_user'] = $data['database_user'] ?? Generator::generate('u_'.$data['name']);
            $siteData['database_password'] = $data['database_password'] ?? DatabasePassword::randomPassword();
            $siteData['database_host'] = $data['database_host'] ?? 'localhost';
            $siteData['database_port'] = $data['database_port'] ?? '3306';
            $siteData['database_provider'] = $data['database_provider'] ?? DatabaseProvider::IN_SERVER;
        }

        // Add admin user fields
        if ($data['app_slug'] === SiteType::MAUTIC->value) {
            $siteData['admin_user'] = $data['admin_user'] ?? Str::slug(auth()->user()->name);
            $siteData['admin_password'] = $data['admin_password'] ?? Str::random(12);
            $siteData['meta->admin_email'] = $data['admin_email'] ?? auth()->user()->email;
        }

        if (($data['domain_parking_method'] ?? '') === 'staging_env') {
            $siteData['ssl_provider'] = SslCertificate::PROVIDER_STAGING;
            $siteData['environment'] = Site::DEMO;
        }

        // Handle Cloudflare integration data
        if (($data['domain_active_on_cloudflare'] ?? false)) {
            // Set SSL provider based on domain
            $siteData['ssl_provider'] = canGenerateSslCertificateOnCloudflare($data['name'])
                ? SslCertificate::PROVIDER_CLOUDFLARE
                : SslCertificate::PROVIDER_XCLOUD;

            // Store Cloudflare integration data in site meta
            $siteData['meta->cloudflare_integration->domain_active_on_cloudflare'] = $data['domain_active_on_cloudflare'];
            $siteData['meta->cloudflare_integration->account_id'] = $data['cloudflare_account_id'] ?? null;
            $siteData['meta->cloudflare_integration->zone_id'] = $data['cloudflare_zone_id'] ?? null;
            $siteData['meta->cloudflare_integration->subdomain'] = $data['subdomain'] ?? null;
            $siteData['meta->cloudflare_integration->site_name'] = $data['site_name'] ?? null;
        }

        return $siteData;
    }

    public function messages(): array
    {
        return [
            ...DatabasePassword::messages(),
            ...SiteUserRule::messages(),
        ];
    }
}
