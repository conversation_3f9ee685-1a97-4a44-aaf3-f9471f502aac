<?php

namespace App\Http\Resources;

use App\Models\AddonStorageProvider;
use App\Models\EmailProvider;
use App\Models\PatchstackVulnerability;
use App\Models\Server;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class BillResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $bill = $this->only([
            'id',
            'title',
            'is_lifetime',
            'bill_forwards_to',
            'comment',
            'service_pricing_comment',
            'service_deactivated_from',
            'service_is_active',
            'adjusted_amount_comment',
            'get_usage_hours',
            'next_billing_amount_comment',
            'short_description',
            'billing_plan',
            'bill_generator',
            'description',
            'source_bill_amount',
            'billing_amount',
            'amount_to_pay',
            'adjustable_amount',
            'adjust_with_previous',
            'refundable_amount',
            'actual_application_fee',
            'application_fee_to_charge',
            'adjustable_application_fee',
            'service',
            'renewal_period',
            'type',
            'currency',
            'status',
            'created_at',
            'next_billing',
            'next_billing_date',
            'due_on',
            'invoice_id',
            'meta',
            'paid_on',
            'bill_from',
            'has_offer'
        ]);
        $bill['date'] = $this->created_at?->format('d M Y');
        $bill['next_date'] = $this->next_billing_date?->format('d M Y');
        $bill['due_date'] = $this?->invoice?->due_date ? $this->invoice->due_date?->format('d-m-Y') : null;
        $bill['due_date_readable'] = $this?->invoice?->due_date ? Carbon::parse($this->invoice->due_date)?->format('jS F, Y') : null;
        $bill['next_billing_date_readable'] = Carbon::parse($this->next_billing_date)?->format('jS F, Y');
        $bill['paid_on_readable'] = Carbon::parse($this->paid_on)?->format('jS F, Y');
        $bill['bill_from_readable'] = Carbon::parse($this->bill_from)?->format('jS F, Y');
        // $taskResponse['created_at'] = $this->created_at?->diffForHumans();
        //match the instance of the bill generator
        if ($this->bill_generator && isset($this->bill_generator['type'])) {
            match ($this->bill_generator['type']) {
                Server::class => $bill['generator_info'] = [
                    'name' => ($this->bill_generator['name'] ?? '') .
                                (
                                    isset($this->bill_generator['public_ip']) && $this->bill_generator['public_ip']
                                    ? ' - (' . ($this->bill_generator['public_ip']) . ')'
                                    : ''
                                ),
                    'url' => route('server.show', $this->bill_generator['id'] ?? null),
                    'type' => 'Server',
                    'name_without_server_ip' => $this->bill_generator['name'] . ' - ' . $this->bill_generator['id']
                ],
                Site::class => $bill['generator_info'] = [
                    'name' => $this->bill_generator['name'] ?? '',
                    'url' => route('site.show', $this->bill_generator['id'] ?? null),
                    'type' => 'Site'
                ],
                EmailProvider::class => $bill['generator_info'] = [
                    'name' => $this->bill_generator['label'] ?? $this->bill_generator['provider'] ?? '',
                    'url' => route('team.email_provider'),
                    'type' => 'Email Provider'
                ],
                AddonStorageProvider::class => $bill['generator_info'] = [
                    'name' => AddonStorageProvider::find($this->bill_generator['id'])?->bucket_name ?? '',
                    'url' => route('addons.s3-storage-provider'),
                    'type' => 'Storage Provider'
                ],
                PatchstackVulnerability::class => $bill['generator_info'] = [
                    'name' => 'Vulnerability Shield Pro',
                    'url' => route('user.patchstack.subscriptions'),
                    'type' => 'Patchstack Addon'
                ],
                default => $bill['generator_info'] = [
                    'name' => 'Unknown',
                    'url' => null
                ],
            };
        } else {
            $bill['generator_info'] = [
                'name' => 'Unknown',
                'url' => null
            ];
        }
        return $bill;
    }
}
