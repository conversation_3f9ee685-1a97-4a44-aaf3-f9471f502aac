<?php

namespace App\Http\Resources;

use App\Enums\XcloudBilling\BillingServices;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * Class ServerResource
 * @mixin  \App\Models\Server
 */
class ServerResource extends JsonResource
{
    public function toArray($request): array
    {
        if ($this->relationLoaded('latestBill')) {
            $planTitle = $this->latestBill?->title;
        } else {
            $planTitle = $this->bills()
                ->where('service_is_active', true)
                ->orderBy('id', 'desc')->first(['title'])?->title ?? null;
        }

        return [
            'id' => $this->id,
            'name' => $this->name,
            'sites_count' => $this->sites_count,
            'is_connected' => $this->is_connected,
            'is_loading' => $this->is_loading,
            'is_error' => $this->is_error,
            'provider' => $this->cloudProvider?->name,
            'provider_readable' => $this->cloud_provider_readable,
            'database_type_readable' => $this->database_type->readable(),
            'provider_name' => $this->cloud_provider_name,
            'region' => strtolower($this->region ?: ''),
            'location' => $this->location,
            'region_flag' => get_region(strtolower($this->region ?: '')),
            'size' => $this->size,
            'ubuntu_version' => $this->ubuntu_version,
            'ssh_port' => $this->ssh_port,
            'notes' => $this->notes,
            'status' => $this->status,
            'stack' => $this->stack->value,
            'stack_readable' => $this->stack->toReadableString(),
            'status_readable' => $this->readableStatus(),
            'is_provisioned' => $this->is_provisioned,
            'php_version' => $this->php_version,
            'public_ip' => user()->can('view', $this->resource) ? $this->public_ip : null,
            'time_zone' => $this->time_zone,
            'created_at' => $this->created_at,
            'created_at_readable' => Carbon::parse($this->created_at)->format('jS F, Y'),
            'updated_at' => $this->updated_at,
            'updated_at_readable' => Carbon::parse($this->updated_at)->format('jS F, Y'),
            'services' => $this->services,
            'monitoring' => $this->getServerMonitoringInfo(),
            'permissions' => $this->permissions(),
            'state' => $this->state,
            'tags' => $this->tags(),
            'team' => $this->team(),
            'has_low_storage' => $this->hasLowStorage(),
            'plan_title' => $planTitle,
            'progress_data' => !$this->isProvisioned() ? $this->progressPageData() : null,
            'reboot_require' => $this->doesRebootRequire(),
        ];
    }

    public static function globalResource($resource)
    {
        $cacheKey = get_class($resource).":".$resource->id;

        return $GLOBALS[$cacheKey] ?? ($GLOBALS[$cacheKey] = self::make($resource)->resolve());
    }

    private function tags()
    {
        $cacheKey = get_class($this).":tags-".$this->id;
        if (!isset($GLOBALS[$cacheKey])) {
            $GLOBALS[$cacheKey] = $this->whenLoaded('tags') ? $this->resource->tags : $this->tags()->select('name', 'id')->get()->toArray();
        }
        return $GLOBALS[$cacheKey];
    }

    private function team()
    {
        $cacheKey = get_class($this).":team-".$this->team_id;
        if (!isset($GLOBALS[$cacheKey])) {
            $GLOBALS[$cacheKey] = $this->whenLoaded('team') ? $this->team : $this->team()->select('name', 'id', 'active_plan_id')->get()->toArray();
        }
        return $GLOBALS[$cacheKey];
    }

    public static function monitor($server)
    {
        $cacheKey = get_class($server).":monitor:".$server->id;

        if (!isset($GLOBALS[$cacheKey])) {
            if ($server->relationLoaded('latestMonitor')) {
                $GLOBALS[$cacheKey] = $server->latestMonitor;
            } else {
                $GLOBALS[$cacheKey] = $server->monitors()->latest('id')->first() ?? false;
            }
        }

        return $GLOBALS[$cacheKey] ? $GLOBALS[$cacheKey] : null;
    }

    public static function minimal($server): array
    {
        return [
            'id' => $server->id,
            'name' => $server->name,
            'public_ip' => $server->public_ip,
            'size' => $server->size,
            'cloud_provider_id' => $server->cloud_provider_id,
            'status' => $server->status,
            'total_site' => $server->sites->count(),
            'provider' => [
                'id' => $server->cloudProvider?->id,
                'name' => $server->cloudProvider?->name,
                'provider' => $server->cloudProvider?->provider,
                'img' => $server->cloudProvider?->getDefinedAttribute['img'] ?? null,
                'defined_name' => $server->cloudProvider?->getDefinedAttribute['name'] ?? null,
                'description' => $server->cloudProvider?->getDefinedAttribute['description'] ?? null,
            ],
            'created_at_formatted' => $server->created_at->format('jS F, Y h:i:s A') . ' UTC',
            'updated_at_formatted' => $server->updated_at->format('jS F, Y h:i:s A') . ' UTC',
            'created_at' => $server->created_at,
            'updated_at' => $server->updated_at
        ];
    }
}
