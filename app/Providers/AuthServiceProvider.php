<?php

namespace App\Providers;

use App\Models\Alert;
use App\Models\AutoSiteClone;
use App\Models\AutoSiteMigration;
use App\Models\Bill;
use App\Models\CartForm;
use App\Models\Cloudflare;
use App\Models\CustomNginx;
use App\Models\EmailProvider;
use App\Models\FeaturePermission;
use App\Models\FirewallRule;
use App\Models\GeneralInvoice;
use App\Models\ImpersonateAction;
use App\Models\ManualInvoice;
use App\Models\ManualSiteMigration;
use App\Models\Package;
use App\Models\PaymentMethod;
use App\Models\PhpVersion;
use App\Models\Product;
use App\Models\Redirection;
use App\Models\Server;
use App\Models\ServerMigration;
use App\Models\ServerMonitor;
use App\Models\Site;
use App\Models\SiteClone;
use App\Models\SiteMigration;
use App\Models\SshKeyPair;
use App\Models\SslCertificate;
use App\Models\SudoUser;
use App\Models\Tag;
use App\Models\Team;
use App\Models\TeamPackage;
use App\Models\User;
use App\Models\Webhook;
use App\Models\WhiteLabel;
use App\Models\WhiteLabelProduct;
use App\Policies\AutoSiteClonePolicy;
use App\Policies\AutoSiteMigrationPolicy;
use App\Policies\BillsPolicy;
use App\Policies\CloudflareIntegrationPolicy;
use App\Policies\FirewallRulePolicy;
use App\Policies\GeneralInvoicePolicy;
use App\Policies\ManualInvoicePolicy;
use App\Policies\ManualSiteMigrationPolicy;
use App\Policies\Nova\AlertNovaPolicy;
use App\Policies\Nova\BillNovaPolicy;
use App\Policies\Nova\CartFormNovaPolicy;
use App\Policies\Nova\CloudflareNovaPolicy;
use App\Policies\Nova\CustomAddedPackagePolicy;
use App\Policies\Nova\CustomNginxNovaPolicy;
use App\Policies\Nova\EmailProviderNovaPolicy;
use App\Policies\Nova\FeaturePermissionNovaPolicy;
use App\Policies\Nova\GeneralInvoiceNovaPolicy;
use App\Policies\Nova\ImpersonateActionPolicy;
use App\Policies\Nova\PackagesNovaPolicy;
use App\Policies\Nova\PhpVersionPolicy;
use App\Policies\Nova\ProductNovaPolicy;
use App\Policies\Nova\RedirectionPolicy;
use App\Policies\Nova\ServerMigrationNovaPolicy;
use App\Policies\Nova\ServerMonitorPolicy;
use App\Policies\Nova\ServerNovaPolicy;
use App\Policies\Nova\SiteNovaPolicy;
use App\Policies\Nova\SshKeyPairPolicy;
use App\Policies\Nova\SslCertificatePolicy;
use App\Policies\Nova\SudoUserPolicy;
use App\Policies\Nova\TagPolicy;
use App\Policies\Nova\UserPolicy;
use App\Policies\Nova\WebhookNovaPolicy;
use App\Policies\Nova\WhiteLabelNovaPolicy;
use App\Policies\Nova\WhiteLabelProductNovaPolicy;
use App\Policies\PaymentGatewayPolicy;
use App\Policies\TeamPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        Team::class                => TeamPolicy::class,
        SiteMigration::class       => AutoSiteMigrationPolicy::class,
        AutoSiteMigration::class   => AutoSiteMigrationPolicy::class,
        ManualSiteMigration::class => ManualSiteMigrationPolicy::class,
        AutoSiteClone::class       => AutoSiteClonePolicy::class,
        SiteClone::class           => AutoSiteClonePolicy::class,
        PaymentMethod::class       => PaymentGatewayPolicy::class,
        GeneralInvoice::class      => GeneralInvoicePolicy::class,
        ManualInvoice::class       => ManualInvoicePolicy::class,
        Bill::class                => BillsPolicy::class,
        Cloudflare::class          => CloudflareIntegrationPolicy::class,
        FirewallRule::class        => FirewallRulePolicy::class,
    ];

    protected array $novaPolicies = [
        SudoUser::class            => SudoUserPolicy::class,
        Cloudflare::class          => CloudflareNovaPolicy::class,
        ImpersonateAction::class   => ImpersonateActionPolicy::class,
        PhpVersion::class          => PhpVersionPolicy::class,
        Redirection::class         => RedirectionPolicy::class,
        ServerMonitor::class       => ServerMonitorPolicy::class,
        SshKeyPair::class          => SshKeyPairPolicy::class,
        SslCertificate::class      => SslCertificatePolicy::class,
        Tag::class                 => TagPolicy::class,
        TeamPackage::class         => CustomAddedPackagePolicy::class,
        Site::class                => SiteNovaPolicy::class,
        Server::class              => ServerNovaPolicy::class,
        ServerMigration::class     => ServerMigrationNovaPolicy::class,
        Alert::class               => AlertNovaPolicy::class,
        CustomNginx::class         => CustomNginxNovaPolicy::class,
        EmailProvider::class       => EmailProviderNovaPolicy::class,
        FeaturePermission::class   => FeaturePermissionNovaPolicy::class,
        Bill::class                => BillNovaPolicy::class,
        CartForm::class            => CartFormNovaPolicy::class,
        Package::class             => PackagesNovaPolicy::class,
        Product::class             => ProductNovaPolicy::class,
        Webhook::class             => WebhookNovaPolicy::class,
        WhiteLabelProduct::class   => WhiteLabelProductNovaPolicy::class,
        WhiteLabel::class          => WhiteLabelNovaPolicy::class,
        GeneralInvoice::class      => GeneralInvoiceNovaPolicy::class,
        User::class                => UserPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        // Determine if the request is for Nova.
        // Be cautious: the request may not be available when running in console.
        $isNova = false;

        if ($this->app->runningInConsole() === false) {
            $isNova = request()->path() === Str::remove('/', config('nova.path', 'admin')) ||
                request()->is('admin/*') ||
                request()->is('nova-api/*') ||
                request()->is('nova/*');
        }

        if ($isNova) {
            $this->registerNovaPolicies();
        } else {
            $this->registerPolicies();
        }

        Gate::before(function ($user, $ability) {
            if ($user->isAdmin()) {
                return true;
            }
        });
    }

    public function registerNovaPolicies(): void
    {
        foreach ($this->novaPolicies as $model => $policy) {
            Gate::policy($model, $policy);
        }
    }
}
