<?php

namespace App\Jobs\Server;

use App\Callbacks\MarkAsServerProvisioned;
use App\Callbacks\MarkAsSiteProvisioned;
use App\Enums\ServerStatus;
use App\Models\PhpVersion;
use App\Models\Server;
use App\Scripts\ProvisionWebServer;
use App\Scripts\ProvisionxCloudServer;
use App\Services\Provisioning\ServerProvisioning;
use App\Traits\HandlesServerProvisioningError;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class StartProvisionScript implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, HandlesServerProvisioningError;

    public function __construct(public Server $server)
    {

    }

    /**
     * @throws Exception
     */
    public function handle()
    {
        $this->server->log("Starting provisioning script : ".$this->server->ipAddress());

        if ($this->server->getMeta('is_demo', false)) {
            $this->server->provisioningProgress(ServerProvisioning::CONNECTING);
            $this->server->markAsProvisioning();
            new MarkAsServerProvisioned($this->server->id);
            $this->server->log("Provisioning script skipped for demo server.");
            return;
        }

        if (!$this->server->ipAddress()) {
            throw new Exception('Server IP address is not set.');
        }

        $this->server->provisioningProgress(ServerProvisioning::CONNECTING);

        $script = $this->server->isXCloudOrWhiteLabel() ? new ProvisionxCloudServer($this->server) : new ProvisionWebServer($this->server);

        // fetch and save cpu architecture & ubuntu version
        $this->server->getCpuArchitecture();
        $this->server->getUbuntuVersion();

        // Adjust PHP version if it's not available for this server based on the CPU architecture
        $availablePhpVersions = PhpVersion::getVersions($this->server);

        if (!in_array($this->server->php_version, $availablePhpVersions)) {
            $this->server->log("PHP version {$this->server->php_version} is not available for this server. Updating to the first available version.");
            $this->server->update(['php_version' => collect($availablePhpVersions)->first()]);
            $this->server->log("Updated PHP version to {$this->server->php_version}");
        }

        $this->server->runInBackground($script, [
            'skipOutput' => true,
            'then' => [
                new MarkAsServerProvisioned($this->server->id)
            ],
        ]);

        $this->server->log("Provisioning script started.");
    }
}
