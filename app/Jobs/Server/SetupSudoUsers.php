<?php

namespace App\Jobs\Server;

use App\Enums\ServerStatus;
use App\Models\Server;
use App\Models\SshKeyPair;
use App\Scripts\AddKeyToServer;
use App\Services\Shell\SshConnector;
use App\Traits\HandlesServerProvisioningError;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SetupSudoUsers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, HandlesServerProvisioningError;

    public function __construct(public Server $server)
    {

    }

    public function handle()
    {
        $this
            ->server
            ->team
            ->sshKeyParis()
            ->where('always_provision', true)
            ->each(function (SshKeyPair $keypair) {

                $sudoUser = $this->server->sudoUsers()->updateOrCreate([
                    'sudo_user' => $keypair->default_sudo_user,
                ], [
                    'sudo_password' => $keypair->default_sudo_password,
                ]);

                $sudoUser->sshKeyParis()->syncWithoutDetaching($keypair);
            });

        dump('Sudo Users are created.');
    }
}
