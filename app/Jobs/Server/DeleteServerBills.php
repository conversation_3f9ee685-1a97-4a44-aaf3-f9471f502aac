<?php

namespace App\Jobs\Server;

use App\Enums\XcloudBilling\BillingStatus;
use App\Models\Server;
use App\Models\Site;
use App\Services\Site\SslManager;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Laravel\Nova\Actions\Action;

class DeleteServerBills implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300;

    public function __construct(public Server $server)
    {
        //
    }

    /**
     * @throws Exception
     */
    function handle()
    {
        $bills = $this->server->bills()->get();
        if ($bills->count() > 0) {

            foreach ($bills as $bill) {
                if ($bill->invoice_id) {
                    $invoiceModel = $bill?->billingPlan?->supported_invoice_type?->model();
                    if (!$invoiceModel) continue;

                    $invoiceModel = app($invoiceModel);

                    $invoice = $invoiceModel->where('id', $bill->invoice_id)->first();

                    if ($invoice->status === BillingStatus::Paid) {
                        Log::error('Server has paid invoices. To delete bills please cancel or refund the invoices first.');
                        return;
                    }
                }
            }

            $this->server->bills()->delete();

        }
    }
}
