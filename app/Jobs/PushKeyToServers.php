<?php

namespace App\Jobs;

use App\Models\Server;
use App\Models\SshKeyPair;
use App\Scripts\UpdateSudoUser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PushKeyToServers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public SshKeyPair $keyPair)
    {
    }

    public function handle()
    {
        $this->keyPair->servers()->each(function (Server $server) {
            $sudoUser = $server->sudoUsers()->updateOrCreate([
                'sudo_user' => $this->keyPair->default_sudo_user,
            ], [
                'sudo_password' => $this->keyPair->default_sudo_password,
            ]);

            $sudoUser->sshKeyParis()->syncWithoutDetaching($this->keyPair);

            UpdateSudoUserJob::dispatch($sudoUser);
        });
    }
}
