<?php

namespace App\Jobs\Site;

use App\Callbacks\SiteDomainIsUpdated;
use App\Models\Site;
use App\Scripts\Site\StopPm2Process;
use App\Scripts\Site\UpdateDomain;
use App\Services\Site\ReferenceUpdater;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateDomainName implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 900;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public Site $site, public string $newDomainName, public bool $hasCloudflareIntegration = false)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        // storing old domain info in site->meta
        $oldDomain = $this->site->name;

        $this->site->saveMeta('domainChangeInfo', [
            'oldDomain' => $oldDomain,
            'newDomain' => $this->newDomainName,
            'status' => 'updating'
        ]);

        $this->site->log('initiating domain update');

        // Update domain references in deploy script, supervisor processes, and cronjobs
        $referenceUpdater = new ReferenceUpdater($this->site);
        $referenceUpdater->updateDomainReferences($oldDomain, $this->newDomainName);

        // Stop PM2 process for Node.js apps
        if ($this->site->isNodeApp()) {
            $this->site->runInline(new StopPm2Process($this->site));
        }

        // Run the domain update script in the background
        $this->site->runInBackground(new UpdateDomain($this->site, $this->newDomainName, $this->site->name), [
                'then' => [
                    new SiteDomainIsUpdated($this->site, $this->hasCloudflareIntegration)
                ]
            ]
        );
    }
}
