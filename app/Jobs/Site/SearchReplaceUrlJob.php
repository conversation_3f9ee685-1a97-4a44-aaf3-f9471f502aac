<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\Site\SearchReplaceUrl;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SearchReplaceUrlJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site)
    {
        //
    }

    public function handle()
    {
        $this->site->runInline(new SearchReplaceUrl($this->site, $this->site->siteMigration));
    }
}
