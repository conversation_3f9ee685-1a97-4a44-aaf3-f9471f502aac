<?php

namespace App\Jobs\Site;

use App\Enums\EmailProvider;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\xCloudEmailProviderPlanEnum;
use App\Models\Product;
use App\Models\Site;
use App\Scripts\InlineScript;
use App\Scripts\Site\HandleSiteIndexingScript;
use App\Scripts\Site\SettingUpXcloudEmailProviderScript;
use App\Traits\MigrationHelper;
use Arr;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class HandleSiteIndexingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MigrationHelper;

    public function __construct(public Site $site)
    {
        //
    }

    public function handle(): void
    {
        $this->checkIfMigrationCancelled($this->site);

        $this->site->runInline(new HandleSiteIndexingScript($this->site));
    }
}
