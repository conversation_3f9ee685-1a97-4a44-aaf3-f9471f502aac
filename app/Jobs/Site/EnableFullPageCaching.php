<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\Site\InstallFullPageCache;
use App\Scripts\Site\InstallLiteSpeedCache;
use App\Services\WordPress\FullPageCaching;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class EnableFullPageCaching implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site, public bool $reloadNginx = true)
    {
    }

    public function handle()
    {
        dump('EnableFullPageCaching');

        if ($this->site->isWordpress() && $this->site->hasFullPageCaching()) {
            $this->site->update([
                'meta->cache_duration' => $this->site->meta['cache_duration'] ?? FullPageCaching::DEFAULT_CACHE_DURATION,
                'meta->cache_duration_unit' => $this->site->meta['cache_duration_unit'] ?? FullPageCaching::DEFAULT_CACHE_DURATION_UNIT,
                'meta->cache_exclusion_http_rules' => $this->site->meta['cache_exclusion_http_rules'] ?? str(FullPageCaching::DEFAULT_HTTP)->explode('|')->implode(PHP_EOL),
                'meta->cache_exclusion_cookie_rules' => $this->site->meta['cache_exclusion_cookie_rules'] ?? str(FullPageCaching::DEFAULT_COOKIE)->explode('|')->implode(PHP_EOL),
            ]);

            if ($this->site->server->stack->isNginx()) {
                dump('InstallFullPageCache for Nginx');
                $this->site->run(new InstallFullPageCache($this->site));
            }

            if ($this->site->server->stack->isOpenLiteSpeed()) {
                dump('InstallLiteSpeedCache for OpenLiteSpeed');
                $this->site->run(new InstallLiteSpeedCache($this->site));
            }

            if ($this->reloadNginx) {
                $this->site->regenerateNginxConf();
            }
        }
    }
}
