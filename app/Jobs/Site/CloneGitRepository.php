<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\CreateDatabase;
use App\Scripts\CreateDatabaseUser;
use App\Scripts\Site\GitCloneRepository;
use App\Scripts\TestDatabaseConnection;
use App\Services\Database\DatabaseProvider;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class CloneGitRepository implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 300;

    public int $tries = 10;

    public function __construct(public Site $site, public $createConfig = true)
    {
    }

    public function handle()
    {
        if ($this->site->isLaravel() && $this->site->server->isRedisSeven()) {
            $this->site->update([
                'redis_password' => $this->site->redis_password ?? Str::random(32)
            ]);
        }

        $this->site->runInline(new GitCloneRepository(
            $this->site,
            $this->site->getMeta('git_info'),
            $this->createConfig
        ));
    }
}
