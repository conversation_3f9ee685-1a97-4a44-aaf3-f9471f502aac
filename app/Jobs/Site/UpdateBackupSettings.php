<?php

namespace App\Jobs\Site;

use App\Callbacks\MarkAsSiteProvisioned;
use App\Callbacks\SiteBackRestoreUpdated;
use App\Models\BackupFile;
use App\Models\BackupSetting;
use App\Models\Site;
use App\Models\User;
use App\Scripts\InlineScript;
use App\Scripts\InstallWordpressSite;
use App\Scripts\GenerateNginxConfig;
use App\Scripts\Site\InstallSiteBackupScript;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateBackupSettings implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site, public BackupSetting $backupSetting,public ?User $user=null,public bool $takeBackup=false)
    {
    }

    public function handle()
    {
        if ($this->backupSetting->isIncremental() && !$this->takeBackup){
            $this->takeBackup = $this->backupSetting->backupFiles()->where('type',BackupFile::INCREMENTAL_FULL)->doesntExist();
        }
        $this->site->runInBackground(new InstallSiteBackupScript(server: $this->site->server,site:  $this->site,backupSetting: $this->backupSetting,take_backup: $this->takeBackup), [
            'then' => [
                new SiteBackRestoreUpdated($this->site,$this->backupSetting,$this->user)
            ],
        ]);
    }
}
