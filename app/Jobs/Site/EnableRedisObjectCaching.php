<?php

namespace App\Jobs\Site;

use App\Models\Server;
use App\Models\Site;
use App\Scripts\Site\InstallLiteSpeedCache;
use App\Scripts\Site\InstallRedisObjectCache;
use App\Services\WordPress\FullPageCaching;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class EnableRedisObjectCaching implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site)
    {
    }

    public function handle()
    {
        dump('EnableFullPageCaching');

        if ($this->site->isWordpress() && $this->site->hasRedisObjectCaching()) {
            if ($this->site->server->isRedisSeven()) {
                $this->site->update([
                    'redis_password' => $this->site->redis_password ?? Str::random(32)
                ]);
            }
            $cache_plugin_script = new InstallRedisObjectCache($this->site);
            if ($this->site->server->stack->isOpenLiteSpeed()) {
                $cache_plugin_script = new InstallLiteSpeedCache($this->site);
            }
            $this->site->runInBackground($cache_plugin_script);
        }
    }
}
