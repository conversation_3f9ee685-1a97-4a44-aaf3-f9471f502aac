<?php

namespace App\Jobs\Site;

use App\Models\Site;
use App\Scripts\Site\UpdateSiteUrl;
use App\Services\Integrations\CloudflareService;
use App\Services\Site\SslManager;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class EnableSsl implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300;

    public function __construct(public Site $site, public bool $reloadNginx = false)
    {
        //
    }

    /**
     * @throws Exception
     */
    function handle()
    {
        dump('Enable Ssl');

        (new SslManager($this->site))->obtain();

        if ($this->site->isWordpress() && $this->site->hasSslCertificate()) {
            $this->site->runInline(new UpdateSiteUrl($this->site));
        }

        if ($this->reloadNginx) {
            $this->site->regenerateNginxConf();
        }
    }
}
