<?php

namespace App\Jobs\Site;

use App\Enums\SiteCloneStatus;
use App\Enums\SiteStatus;
use App\Events\SiteCloneStatusChanged;
use App\Events\SiteProvisioned;
use App\Models\Site;
use App\Scripts\Site\ExecuteDeployScript;
use App\Scripts\Site\HandleSiteIndexingScript;
use App\Scripts\Site\SearchReplaceUrl;
use App\Services\Clone\SiteCloning;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MarkAsClonedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Site $site)
    {

    }

    /**
     * @throws Exception
     */
    public function handle()
    {
        dump('Marking As Cloned');

        // update permissions & generate nginx conf
        $this->site->siteClone->cloneProgress(SiteCloning::CONFIGURING_NGINX);
        $this->site->regenerateNginxConf(createSymblink: true);

        // replicate custom_nginx configs
        $fromSiteId = \Arr::get($this->site->meta, 'cloneInfo.cloneFromSiteId');
        if($fromSiteId){
            $sourceSite = Site::findOrFail($fromSiteId);
            $customNginxConfigs = $sourceSite->customNginxConfigs()->get();

            foreach ($customNginxConfigs as $customNginxConfig){
                $this->site->customNginxConfigs()->updateOrCreate([
                    'type' => $customNginxConfig->type,
                    'file' => $customNginxConfig->file,
                ], [
                    'content' => $customNginxConfig->content,
                    'status' => $customNginxConfig->status,
                    'response' => $customNginxConfig->response
                ]);
            }
        }

        // execute deploy script
        if(!empty($this->site->getMeta('deploy_script'))){
            $this->site->siteClone->cloneProgress(SiteCloning::DEPLOY_SCRIPT);
            $this->site->runInline(new ExecuteDeployScript($this->site, $this->site->getMeta('deploy_script')));
        }
        // search replace with new url
//        $this->site->runInline(new SearchReplaceUrl($this->site,$this->site->siteClone)); // it is placed on the clone site script

        $this->site->siteClone->cloneProgress(SiteCloning::INSTALLING_MONITORING);
        $this->site->installMonitoringInline();

        $this->site->siteClone->cloneProgress(SiteCloning::INSTALLING_WP_CRON_JOB);
        $this->site->installWpCronJob();

        // handle site indexing
        $this->site->runInline(new HandleSiteIndexingScript($this->site));

        $this->site->siteClone->cloneProgress(SiteCloning::FINISHING_UP);


        // mark as success
        $this->site->update(['status' => SiteStatus::PROVISIONED]);
        $this->site->siteClone->update(['status' => SiteCloneStatus::FINISHED]);
        $this->site->saveMeta('show_clone_banner', true);
        SiteCloneStatusChanged::dispatch($this->site->siteClone);
        $notification_mails = $this->site->siteClone->notification_mails;
        if (!is_array($notification_mails)) {
            $notification_mails = [];
        }
        SiteProvisioned::dispatch($this->site,$notification_mails);
    }
}
