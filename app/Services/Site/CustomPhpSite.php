<?php

namespace App\Services\Site;

use App\Contracts\SiteContract;
use App\Models\Site;

class CustomPhpSite extends SiteManager
{
    public function geLogFilePaths(): string
    {
        return $this->siteBasePath().'/wp-content/debug.log';
    }

    public function getEnvironmentFilePath(): string
    {
        return $this->siteBasePath().'/wp-config.php';
    }

    public function siteBasePath(): string
    {
        return $this->siteBasePathWithWebRoot();
    }
}
