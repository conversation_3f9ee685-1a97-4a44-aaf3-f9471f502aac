<?php

namespace App\Services\Site;

use App\Contracts\SiteContract;
use App\Models\Site;

abstract class SiteManager implements SiteContract
{
    public Site $site;

    public function __construct(Site $site)
    {
        $this->site = $site;
    }

    public function siteBasePath(): string
    {
        return '/var/www/'.$this->site->name;
    }
    public function siteBasePathWithWebRoot(): string
    {
        return str(implode(DIRECTORY_SEPARATOR,[
            '/var/www',
            $this->site->name,
            $this->site->web_root
        ]))
            ->rtrim(DIRECTORY_SEPARATOR);
    }

    public function sitePluginPath(): string
    {
        return $this->siteBasePath().'/wp-content/plugins';
    }

    public function siteDocumentRoot(): string
    {
        return $this->siteBasePath();
    }

    public function siteNginxServerName(): string
    {
        $serverName = $this->site->getDomainNames()->implode(' ');

        if ($this->site->isMultiSiteSubdomain()){
            $serverName .= ' '.$this->site->getDomainNames()->map(function($domain) { return '*.'.$domain; })->implode(' ');
        }

        return $serverName;
    }

    public function getNginxLogPath(): string
    {
        $dir = $this->site->server->stack->isNginx() ? 'nginx' : 'lsws';
        return "/var/log/{$dir}/{$this->site->name}*.log";
    }
    public function getLswsLogPath(): string
    {
        return "/var/log/lsws/{$this->site->name}*.log";
    }

    public function getAccessLogPath(): string
    {
        $dir = $this->site->server->stack->isNginx() ? 'nginx' : 'lsws';
        return "/var/log/{$dir}/{$this->site->name}-access.log";
    }

    public function getErrorLogPath(): string
    {
        $dir = $this->site->server->stack->isNginx() ? 'nginx' : 'lsws';
        return "/var/log/{$dir}/{$this->site->name}-error.log";
    }

    #get nginx conf path
    public function getNginxConfPath(): string
    {
        return "/etc/nginx/sites-available/{$this->site->name}";
    }

    #get nginx conf
    public function getNginxConfFileName(): string
    {
        return "{$this->site->name}";
    }
}
