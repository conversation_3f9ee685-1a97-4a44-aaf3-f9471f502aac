<?php

namespace App\Services\Site;

use App\Contracts\SiteContract;
use App\Models\Site;

class UptimeKumaSite extends SiteManager
{
    public function geLogFilePaths(): string
    {
        return "/home/<USER>/.pm2/logs/{$this->site->pm2ProcessName()}-*.log";
    }

    public function getEnvironmentFilePath(): string
    {
        return $this->siteBasePath().'/start.sh';
    }

    public function siteBasePath(): string
    {
        return $this->siteBasePathWithWebRoot();
    }
}
