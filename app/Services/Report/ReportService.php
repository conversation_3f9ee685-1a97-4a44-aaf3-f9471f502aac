<?php

namespace App\Services\Report;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Models\Bill;
use App\Models\EmailProvider;
use App\Models\NotificationIntegration;
use App\Models\Server;
use App\Models\Site;
use App\Models\Team;
use App\Services\SitePlayGround\RequestToJoinTeam;
use Carbon\Carbon;

class ReportService
{
    public function getSalesReports($service)
    {
        $last30Days = Carbon::parse(Carbon::now()->subMonth())->format('Y-m-d');
        $today = Carbon::parse(Carbon::now())->format('Y-m-d');

        return Bill::where('has_offer', false)
            ->whereBetween('bill_from', [$last30Days, $today])
            ->where('renewal_period', BillRenewalPeriod::Monthly->value)
            ->where('service', $service)
            ->sum('amount_to_pay');
    }

    public function getTotalServers($filterByMonth, $filterByYear): int
    {
        return Bill::where('service_is_active', 1)
            ->where('generator_type', Server::class)
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getServerCount($type, $filterByMonth, $filterByYear): int
    {
        return Bill::where('service_is_active', 1)
            ->where('generator_type', Server::class)
            ->where('service', $type)
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getWebServerCount($type, $filterByMonth, $filterByYear): int
    {
        $serverIds = $this->serverIds($filterByMonth, $filterByYear);
        return Server::whereIn('id', $serverIds)
            ->where('stack', $type)
            ->count();
    }

    public function getServerDeletedCount($filterByMonth, $filterByYear): int
    {
        return Server::onlyTrashed()
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(created_at) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getServerFailedCount($filterByMonth, $filterByYear): int
    {
        return Server::where('is_provisioned', 0)
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(created_at) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getLtdServerCount($filterByMonth, $filterByYear): int
    {
        return Bill::where('renewal_period', BillRenewalPeriod::Lifetime->value)
            ->where('generator_type', Server::class)
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getSelfManagedLtdServerCount($filterByMonth, $filterByYear): int
    {
        return Bill::where('renewal_period', BillRenewalPeriod::Lifetime->value)
            ->where('generator_type', Server::class)
            ->where('service', BillingServices::SelfManagedHosting->value)
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getXcloudProviderLtdServerCount($filterByMonth, $filterByYear): int
    {
        return Bill::where('renewal_period', BillRenewalPeriod::Lifetime->value)
            ->where('generator_type', Server::class)
            ->where('service', BillingServices::xCloudProviderHosting->value)
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getXcloudManagedWithoutLtdServerCount($filterByMonth, $filterByYear): int
    {
        return Bill::where('renewal_period', BillRenewalPeriod::Monthly->value)
            ->where('generator_type', Server::class)
            ->where('service', BillingServices::xCloudManagedHosting->value)
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getSelfManagedWithoutLtdServerCount($filterByMonth, $filterByYear): int
    {
        return Bill::where('renewal_period', BillRenewalPeriod::Monthly->value)
            ->where('generator_type', Server::class)
            ->where('service', BillingServices::SelfManagedHosting->value)
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getTotalSitesCount($filterByMonth, $filterByYear): int
    {
        return Site::where('status', 'provisioned')
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(created_at) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getTotalMainSitesCount($filterByMonth, $filterByYear): int
    {
        return Site::where('status', 'provisioned')->where('environment', 'production')
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(created_at) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getTotalStagingSitesCount($filterByMonth, $filterByYear): int
    {
        return Site::where('status', 'provisioned')->where('environment', 'staging')
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(created_at) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getTotalPlaygroundSitesCount($filterByMonth, $filterByYear): int
    {
        return Team::when($filterByMonth, function ($query) use ($filterByMonth) {
            $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
            return $query->whereRaw('MONTH(created_at) = ?', [$month]);
        })
        ->when($filterByYear, function ($query) use ($filterByYear) {
            return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
        })->where('email', RequestToJoinTeam::playgroundTeamOwnerUserEmail)?->first()?->sites()?->count() ?: 0;
    }

    public function getTotalSiteWebServerCount($type, $filterByMonth, $filterByYear): int
    {
        return Site::where('status', 'provisioned')->whereHas('server', function ($query) use ($type) {
            $query->where('stack', $type);
        })
        ->when($filterByMonth, function ($query) use ($filterByMonth) {
            $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
            return $query->whereRaw('MONTH(created_at) = ?', [$month]);
        })
        ->when($filterByYear, function ($query) use ($filterByYear) {
            return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
        })
        ->count();
    }

    public function getTotalSiteFailedCount($filterByMonth, $filterByYear): int
    {
        return Site::where('status', 'provisioning_failed')
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(created_at) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
            })
            ->count();
    }
    public function getTotalIntegrationCount($type, $filterByMonth, $filterByYear): int
    {
        return NotificationIntegration::where('type', $type)
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(created_at) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(created_at) = ?', [$filterByYear]);
            })
            ->count();
    }

    public function getTotalEmailSold($filterByMonth, $filterByYear): int
    {
        return Bill::query()
            ->whereNotNull('invoice_id')
            ->where('service', BillingServices::EmailProvider)
            ->where('renewal_period', BillRenewalPeriod::Monthly)
            ->where('status', BillingStatus::Paid)
            ->where('description', '!=', 'xCloud 100 Emails(Free)')
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
            })
            ->sum('amount_to_pay');
    }

    public function getTotalFreeEmail($filterByMonth, $filterByYear): int
    {
        return Bill::query()
            //->whereNull('invoice_id')
            ->where('service', BillingServices::EmailProvider)
            ->where('renewal_period', BillRenewalPeriod::Monthly)
            ->where('status', BillingStatus::Unpaid)
            ->where('description', 'xCloud 100 Emails(Free)')
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
            })
            ->count();
    }

    protected function serverIds($filterByMonth, $filterByYear): array
    {
        return Bill::where('service_is_active', 1)
            ->where('generator_type', Server::class)
            ->when($filterByMonth, function ($query) use ($filterByMonth) {
                $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
                return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
            })
            ->when($filterByYear, function ($query) use ($filterByYear) {
                return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
            })
            ->pluck('generator_id')
            ->toArray();
    }
}
