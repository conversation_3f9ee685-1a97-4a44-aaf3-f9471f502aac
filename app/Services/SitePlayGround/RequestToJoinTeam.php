<?php

namespace App\Services\SitePlayGround;

use App\Models\Site;
use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Laravel\Jetstream\Contracts\InvitesTeamMembers;
use Lara<PERSON>\Jetstream\Jetstream;

class RequestToJoinTeam
{
    const playgroundTeamOwnerUserEmail = '<EMAIL>';
    public User $user;
    public Site $site;

    public static function toUser(User $user = null): self
    {
        $self = new self();
        $self->user = $user;
        return $self;
    }

    public function forSite(Site $site): self
    {
        $this->site = $site;

        return $this;
    }

    public function send(): void
    {
        $team = Team::where('personal_team', true)
                    ->whereHas('owner', fn($query) => $query->where('email', self::playgroundTeamOwnerUserEmail))
                    ->first();

        if ($team->users()->where('email', $this->user->email)->doesntExist() &&
            $team->teamInvitations()->where('email', $this->user->email)->doesntExist()) {
            app(InvitesTeamMembers::class)->invite(
                $team->owner,
                $team,
                $this->user->email,
                Team::SITE_ADMIN,
                [
                    "permissions" => $this->getPermissions()->values()->toArray(),
                    "site_access" => "choose",
                    "selected_sites" => [
                        $this->site->id
                    ]
                ]

            );
        }

        if ($this->user->belongsToTeam($team)) {
            $this->user->sitesByInvitation()->syncWithoutDetaching([$this->site->id]);
        }
    }

    private function getPermissions()
    {
        $permissions = collect(Jetstream::findRole(Team::SITE_ADMIN)->permissions);
        $forbidden_permissions = [
            "site:manage-domain",
            "site:manage-ssl",
            "site:manage-wpconfig",
            "site:manage-ssh-sftp",
            "site:manage-logs"
        ];
        return collect($permissions->filter(fn($permission, $key) => $key == 'site')->first())
            ->keys()
            ->filter(fn($site_permission) => !in_array($site_permission, $forbidden_permissions));
    }
}
