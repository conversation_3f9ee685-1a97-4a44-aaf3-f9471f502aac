<?php

namespace App\Services\Provisioning;

use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Models\Server;
use Illuminate\Support\Facades\Lang;

class ServerProvisioning
{
    const PROGRESS_MAX = 28;

    const VERIFYING_CARD_AND_TAKING_PAYMENT = 1;

    const INIT = 2;
    const CREATING_SERVER = 3;
    const SERVER_CREATED = 4;
    const WAITING_FOR_SERVER_TO_START = 5;

    const CONNECTING = 6;
    const CONNECTED = 7;
    const CREATING_SWAP = 8;

    const UPGRADING_SYSTEM = 9;
    const INSTALLING_BASE = 10;


    const AUTHENTICATION_METHOD = 11;
    const UPDATING_HOSTNAME = 12;
    const UPDATING_TIMEZONE = 13;
    const XCLOUD_USER = 14;

    const SETUP_SSH = 15;
    const SETTING_UP_SUDO_PERMISSIONS = 16;
    const SETTING_UP_GIT = 17;
    const SETUP_FIREWALL = 18;
    const SETTING_UP_CLEANING_SCRIPT = 19;

    const INSTALLING_PHP = 20;
    const INSTALLING_WEBSERVER = 21;
    const INSTALLING_NODE = 22;
    const INSTALLING_REDIS = 23;

    const INSTALLING_DATABASE = 24;
    const INSTALLING_WP_CLI = 25;

    const SETTING_SSH_PERMISSIONS = 26;
    const INSTALL_MONITORING_SCRIPT = 27;
    const READY_TO_DO_MAGIC = 28;

    /**
     * @throws \Exception
     */
    static function get(Server $server): array|string|null
    {
        $creatingSteps = [];
        $amount = 0;

        if($server->isBillable && $server->getPricing($server->getDefaultBillingService())
            && $server->team?->activePlan?->requires_billing){
            if(!$server->team->hasActivePaymentMethod()){
                throw new \Exception('No active payment method found!.');
            }

            $amount = $server->bills->whereNotNull('invoice_id')->where('status', BillingStatus::Unpaid)->sum('amount_to_pay');

            $creatingSteps += Lang::get('provisioning.verifying_payment', [
                'amount' => $amount
            ]);
        }

        if ($amount <= 0) {
            $creatingSteps = [];
        }

        $creatingSteps = $server->cloudProvider ? array_merge($creatingSteps, Lang::get('provisioning.server_creating')) : $creatingSteps;

        return array_merge($creatingSteps, Lang::get('provisioning.server_provisioning'));
    }
}
