<?php

namespace App\Services\Slack;

use App\Models\Server;

class ServerStatisticsService extends AbstractStatisticsService
{
    private StatisticsFormatter $formatter;
    private Server $server;

    public function __construct(StatisticsFormatter $formatter, string $reportingTime = '08:00', string $reportingTimezone = 'Asia/Dhaka')
    {
        parent::__construct($reportingTime, $reportingTimezone);
        $this->formatter = $formatter;
        $this->server = (new Server())->setReportingParams($this->reportingTime, $this->reportingTimezone);
    }

    public function createServerFieldsSection(array $vultrData): array 
    {
        $serverFilter = $this->server->applyTimezoneAndTime();

        // Simplify the process of getting counts and formatting them
        $counts = [
            'created' => ['allTime' => $this->server->withTrashed()->count(), 'aDay' => $serverFilter->withTrashed()->count()],
            'active' => ['allTime' => $this->server->withoutTrashed()->count(), 'aDay' => $serverFilter->withoutTrashed()->count()],
            'deleted' => ['allTime' => $this->server->onlyTrashed()->count(), 'aDay' => $serverFilter->onlyTrashed()->count()],
            'disconnected' => ['allTime' => $this->server->getDisconnected()->count(), 'aDay' => $this->server->getDisconnected(allTime: false)->count()],
            'delete_failed' => ['allTime' => $this->server->getDeletionFailed()->count(), 'aDay' => $this->server->getDeletionFailed(allTime: false)->count()],

            'xCloudAppAllType' => ['allTime' => $this->server->countXCloudHosted(), 'aDay' => $this->server->countXCloudHosted(allTime: false)],
            'xCloudAppManaged' => ['allTime' => $this->server->countXCloudHosted('managed'), 'aDay' => $this->server->countXCloudHosted('managed', allTime: false)],
            'xCloudAppProvider' => ['allTime' => $this->server->countXCloudHosted('provider'), 'aDay' => $this->server->countXCloudHosted('provider', allTime: false)],
            'whiteLabelVultr' => ['allTime' => $this->server->countXCloudHosted('whitelabel'), 'aDay' => $this->server->countXCloudHosted('whitelabel', allTime: false)],
        ];

        // Format counts for display
        foreach ($counts as $key => &$value) {
            $value['displayADay'] = $this->formatDisplayCount($value['aDay']);
        }

        // Handle provider counts separately due to different structure
        $providerCountsAllTime = $this->server->countOnDifferentProviders();
        $providerCountsADay = $this->server->countOnDifferentProviders(allTime: false);
        $providerCounts = $this->formatter->formattingProviderCounts($providerCountsAllTime, $providerCountsADay);

        $alertEmoji = $counts['xCloudAppAllType']['allTime'] < $vultrData['all_time_count'] ? ":keno:" : ":done:";

        $basic = "• :new: *Servers Created:* {$counts['created']['allTime']} {$counts['created']['displayADay']} \n\n" .
            "• :large_green_circle: *Active Servers:* {$counts['active']['allTime']} {$counts['active']['displayADay']} \n\n " .
            "• :wastebasket: *Servers Deleted:* {$counts['deleted']['allTime']} {$counts['deleted']['displayADay']} \n\n" .
            "• :red_circle: *Server Disconnected:* {$counts['disconnected']['allTime']} {$counts['disconnected']['displayADay']} \n\n" .
            "• :broken_wifi: *Server Deletion Failed:* {$counts['delete_failed']['allTime']} {$counts['delete_failed']['displayADay']} \n\n";

        $allTypeXCloud = "*UNDER XCLOUD (All TYPE): {$alertEmoji}*\n\n• :desktop_computer: *Vultr Server Count:* {$vultrData['all_time_count']} {$this->formatDisplayCount($vultrData['day_total_count'])}" .
            "\n\n• :page_with_curl: *On xCloud Database:* {$counts['xCloudAppAllType']['allTime']} {$counts['xCloudAppAllType']['displayADay']}";

        $xCloudManaged = "*XCLOUD MANAGED: *\n\n• :desktop_computer: *Vultr Server Count:* {$vultrData['xcloud_managed_count']} {$this->formatDisplayCount($vultrData['day_xcloud_managed_count'])}" .
            "\n\n• :page_with_curl: *On xCloud Database:* {$counts['xCloudAppManaged']['allTime']} {$counts['xCloudAppManaged']['displayADay']}";

        $xCloudProvider = "*XCLOUD PROVIDER: *\n\n• :desktop_computer: *Vultr Server Count:* {$vultrData['xcloud_provider_count']} {$this->formatDisplayCount($vultrData['day_xcloud_provider_count'])}" .
            "\n\n• :page_with_curl: *On xCloud Database:* {$counts['xCloudAppProvider']['allTime']} {$counts['xCloudAppProvider']['displayADay']}";

        $whiteLabelVultr = "*WHITE LABEL: *\n\n• :desktop_computer: *Vultr Server Count:* {$vultrData['whitelabel_count']} {$this->formatDisplayCount($vultrData['day_whitelabel_count'])}" .
            "\n\n• :page_with_curl: *On xCloud Database:* {$counts['whiteLabelVultr']['allTime']} {$counts['whiteLabelVultr']['displayADay']}";

        $otherProviders = "*OTHER PROVIDERS:* \n\n$providerCounts";

        return [
            $this->formatter->createSimpleSection($basic),
            ["type" => "divider"],
            $this->formatter->createSimpleSection($allTypeXCloud),
            ["type" => "divider"],
            $this->formatter->createSimpleSection($xCloudManaged),
            ["type" => "divider"],
            $this->formatter->createSimpleSection($xCloudProvider),
            ["type" => "divider"],
            $this->formatter->createSimpleSection($whiteLabelVultr),
            ["type" => "divider"],
            $this->formatter->createSimpleSection($otherProviders),
        ];
    }

    public function generateServerDetails(array $vultrData): array
    {
        $serversIdNotInDatabase = $this->server->findServersAndMissingIds($vultrData['server_ids']);
        $vultrService = new VultrStatisticsService('', $this->reportingTime, $this->reportingTimezone);
        $duplicateVultrServerIds = $vultrService->findDuplicateServerIds($vultrData['server_ids']);

        $serverCountDetails = array_merge(
            [$this->formatter->createHeaderSection("SERVER UPDATES :")],
            $this->createServerFieldsSection($vultrData),
        );

        // Conditional blocks for Vultr data
        if (!empty($serversIdNotInDatabase) || !empty($vultrData['outside_servers'])) {
            array_push($serverCountDetails, ["type" => "divider"], $this->formatter->createHeaderSection("ORPHAN SERVERS IN VULTR :"));
        }

        if (!empty($serversIdNotInDatabase)) {
            $serversList = implode(', ', $serversIdNotInDatabase);
            $serverCountDetails[] = $this->formatter->createSimpleSection(":no-god-no-pls: *Server IDs that are removed from xCloud but Present in Vultr:* `{$serversList}` \n\n");
        }

        if(!empty($duplicateVultrServerIds)){
            $serversList = implode(', ', $duplicateVultrServerIds);
            $serverCountDetails[] = $this->formatter->createSimpleSection(":twin: *Duplicate Servers ID on Vultr:* `{$serversList}` \n\n");
        }

        if (!empty($vultrData['outside_servers'])) {
            $serversList = implode(', ', $vultrData['outside_servers']);
            $serverCountDetails[] = $this->formatter->createSimpleSection(":whats-happening-here: *Servers that are not created by xCloud:* `{$serversList}` \n\n");
        }

        // get ids of disconnected servers hosted in Vultr
        $disconnectedServers = $this->server->getDisconnected()->pluck('id')->toArray();

        if(!empty($disconnectedServers)){
            $serversList = implode(', ', $disconnectedServers);
            $serverCountDetails[] = $this->formatter->createSimpleSection(":skull: *xCloud hosted Servers that are disconnected:* `{$serversList}` \n\n");
        }

        // get ids of deletion failed servers hosted in Vultr
        $deletionFailedServers = $this->server->getDeletionFailed()->pluck('id')->toArray();

        if(!empty($deletionFailedServers)){
            $serversList = implode(', ', $deletionFailedServers);
            $serverCountDetails[] = $this->formatter->createSimpleSection(":broken_wifi: *xCloud hosted Servers that are on deletion failed status:* `{$serversList}` \n\n");
        }

        return $serverCountDetails;
    }
}
