<?php

namespace App\Services\Slack;

use App\Models\EmailProvider;
use App\Models\PatchstackVulnerability;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;

class AddonsStatisticsService extends AbstractStatisticsService
{
    private StatisticsFormatter $formatter;

    public function __construct(StatisticsFormatter $formatter, string $reportingTime = '08:00', string $reportingTimezone = 'Asia/Dhaka')
    {
        parent::__construct($reportingTime, $reportingTimezone);
        $this->formatter = $formatter;
    }

    public function getMailDeliveryStatistics(): array
    {
        Log::info('AddonsStatisticsService: Getting Mail Delivery statistics...');

        try {
            [$startReportingTime, $endReportingTime] = $this->getReportingTimeRange();
            $allTimeCount = $this->baseEmailProviderQuery()->count();
            $dayCount = $this->baseEmailProviderQuery()
                ->whereBetween('created_at', [$startReportingTime, $endReportingTime])
                ->count();

            $displayCount = $this->formatDisplayCount($dayCount);

            $result = [
                'allTimeCount' => $allTimeCount,
                'dayCount' => $dayCount,
                'displayText' => "• :email: *Mail Delivery:* {$allTimeCount} {$displayCount}"
            ];

            Log::info('AddonsStatisticsService: Mail Delivery statistics', $result);

            return $result;
        } catch (Exception $e) {
            Log::error('Error getting Mail Delivery statistics: ' . $e->getMessage());

            return [
                'allTimeCount' => 0,
                'dayCount' => 0,
                'displayText' => "• :email: *Mail Delivery:* 0"
            ];
        }
    }

    public function getPatchstackStatistics(): array
    {
        Log::info('AddonsStatisticsService: Getting Patchstack statistics...');

        try {
            [$startReportingTime, $endReportingTime] = $this->getReportingTimeRange();
            $allTimeCount = $this->basePatchstackQuery()->count();
            $dayCount = $this->basePatchstackQuery()
                ->whereBetween('created_at', [$startReportingTime, $endReportingTime])
                ->count();

            $displayCount = $this->formatDisplayCount($dayCount);

            $result = [
                'allTimeCount' => $allTimeCount,
                'dayCount' => $dayCount,
                'displayText' => "• :shield: *Patchstack:* {$allTimeCount} {$displayCount}"
            ];

            Log::info('AddonsStatisticsService: Patchstack statistics', $result);

            return $result;
        } catch (Exception $e) {
            Log::error('Error getting Patchstack statistics: ' . $e->getMessage());

            return [
                'allTimeCount' => 0,
                'dayCount' => 0,
                'displayText' => "• :shield: *Patchstack:* 0"
            ];
        }
    }

    public function generateAddonsDetails(): array
    {
        Log::info('AddonsStatisticsService: Generating add-ons statistics...');

        $mailDeliveryStats = $this->getMailDeliveryStatistics();
        $patchstackStats = $this->getPatchstackStatistics();

        $addonsText = $mailDeliveryStats['displayText'] . "\n\n" . $patchstackStats['displayText'];

        $result = [
            $this->formatter->createHeaderSection("ADD-ONS:"),
            $this->formatter->createSimpleSection($addonsText)
        ];

        Log::info('AddonsStatisticsService: Add-ons statistics generated successfully.');

        return $result;
    }

    protected function getReportingTimeRange(): array
    {
        $startReportingTime = Carbon::createFromFormat('H:i', $this->reportingTime, $this->reportingTimezone)
            ->subDay()
            ->setTimezone('UTC');

        $endReportingTime = Carbon::createFromFormat('H:i', $this->reportingTime, $this->reportingTimezone)
            ->setTimezone('UTC');

        return [$startReportingTime, $endReportingTime];
    }

    protected function baseEmailProviderQuery(): Builder
    {
        return EmailProvider::query()
            ->where('provider', \App\Enums\EmailProvider::XCLOUD_EMAIL_PROVIDER)
            ->where('price', '>', 0);
    }

    protected function basePatchstackQuery(): Builder
    {
        return PatchstackVulnerability::query()
            ->where('is_purchase', true);
    }
}
