<?php

namespace App\Services\Slack;

use App\Models\Team;
use App\Models\User;

class GeneralStatisticsService extends AbstractStatisticsService
{
    private StatisticsFormatter $formatter;
    private User $user;
    private Team $team;
    private SiteStatisticsService $siteStatisticsService;

    public function __construct(StatisticsFormatter $formatter, SiteStatisticsService $siteStatisticsService, string $reportingTime = '08:00', string $reportingTimezone = 'Asia/Dhaka')
    {
        parent::__construct($reportingTime, $reportingTimezone);
        $this->formatter = $formatter;
        $this->siteStatisticsService = $siteStatisticsService;
        $this->user = (new User())->setReportingParams($this->reportingTime, $this->reportingTimezone);
        $this->team = (new Team())->setReportingParams($this->reportingTime, $this->reportingTimezone);
    }

    public function generateGeneralDetails(): array
    {
        $billingTeamCountAllTime = $this->team->where('billing_status', 'active')->count();
        $billingTeamCountToday = $this->formatDisplayCount($this->team->applyTimezoneAndTime()->where('billing_status', 'active')->count());

        return [
            $this->formatter->createHeaderSection("GENERAL DETAILS:"),
            $this->siteStatisticsService->getNonSoftDeletedData($this->user, ":prince:", ":cry:"),
            $this->siteStatisticsService->getNonSoftDeletedData($this->team, ":man-woman-boy-boy:", ":-1:"),
            $this->formatter->createSimpleSection("• :money_with_wings: *Active Billing Team:* {$billingTeamCountAllTime} {$billingTeamCountToday}")
        ];
    }
}
