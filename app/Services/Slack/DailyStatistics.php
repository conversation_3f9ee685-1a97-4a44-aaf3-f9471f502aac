<?php

namespace App\Services\Slack;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class DailyStatistics
{
    private VultrStatisticsService $vultrService;
    private ServerStatisticsService $serverService;
    private SiteStatisticsService $siteService;
    private GeneralStatisticsService $generalService;
    private AddonsStatisticsService $addonsService;
    private SlackNotificationService $slackService;
    private StatisticsFormatter $formatter;
    private string $reportingTime;
    private string $reportingTimezone;

    public function __construct(string $vultrApiKey, $slackPostMessageUrl, $slackOAuthToken, $slackChannelId)
    {
        $this->reportingTime = '08:00'; // 8 AM
        $this->reportingTimezone = 'Asia/Dhaka';

        // Initialize formatter first as it's needed by other services
        $this->formatter = new StatisticsFormatter();

        // Initialize all services
        $this->vultrService = new VultrStatisticsService($vultrApiKey, $this->reportingTime, $this->reportingTimezone);
        $this->siteService = new SiteStatisticsService($this->formatter, $this->reportingTime, $this->reportingTimezone);
        $this->generalService = new GeneralStatisticsService($this->formatter, $this->siteService, $this->reportingTime, $this->reportingTimezone);
        $this->serverService = new ServerStatisticsService($this->formatter, $this->reportingTime, $this->reportingTimezone);
        $this->addonsService = new AddonsStatisticsService($this->formatter, $this->reportingTime, $this->reportingTimezone);
        $this->slackService = new SlackNotificationService($slackPostMessageUrl, $slackOAuthToken, $slackChannelId, $this->reportingTimezone, $this->formatter);
    }

    public function generateReport(): array
    {
        logger('DailyStatistics: Generating daily statistics report...');

        // Get Vultr data first as it's needed by server statistics
        $vultrData = $this->vultrService->getVultrInstanceCounts();

        // Generate all sections of the report
        $generalDetails = $this->generalService->generateGeneralDetails();
        $serverDetails = $this->serverService->generateServerDetails($vultrData);
        $siteDetails = $this->siteService->generateSiteDetails();
        $addonsDetails = $this->addonsService->generateAddonsDetails();

        // Create initial block
        $initialBlockKit = $this->slackService->createInitialBlock();

        // Store cache items from all services
        $cacheItems = array_merge(
            $this->siteService->getCacheItems(),
            $this->generalService->getCacheItems()
        );

        // Store in cache for 7 days
        Cache::put('daily_statistics_previous_day_data', $cacheItems, now()->addDays(7));

        logger('DailyStatistics: Daily statistics report generated successfully.');

        // Combine all sections into the final report
        $data = array_merge(
            [$initialBlockKit],
            [["type" => "divider"]],
            $generalDetails,
            [["type" => "divider"]],
            $serverDetails,
            [["type" => "divider"]],
            $siteDetails,
            [["type" => "divider"]],
            $addonsDetails
        );

        logger('DailyStatistics: Daily statistics report generated successfully.', $data);

        return $data;
    }

    public function sendToSlack(): void
    {
        try {
            $blocks = $this->generateReport();
            $this->slackService->sendToSlack($blocks);
        } catch (\Exception $e) {
            print($e->getMessage());
            Log::error('Slack notification sending failed', [
                'message' => $e->getMessage()
            ]);
        }
    }
}

