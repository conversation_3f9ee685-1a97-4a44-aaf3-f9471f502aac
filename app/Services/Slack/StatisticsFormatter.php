<?php

namespace App\Services\Slack;

use App\Enums\CloudProviderEnums;

class StatisticsFormatter
{
    public function createHeaderSection($text): array
    {
        return [
            "type" => "section",
            "text" => ["type" => "mrkdwn", "text" => "*{$text}*\n\n"],
        ];
    }

    public function createSimpleSection($text): array
    {
        return [
            "type" => "section",
            "text" => ["type" => "mrkdwn", "text" => $text . "\n\n"],
        ];
    }

    public function formattingProviderCounts($countsAllTime, $countsADay): string
    {
        $collectionAllTime = collect($countsAllTime);
        $collectionADay = collect($countsADay);

        // Combine all keys from both collections and remove duplicates
        $allProviders = $collectionAllTime->keys()->merge($collectionADay->keys())->unique()->sort();

        // Map each provider to its formatted string and implode the collection into a single string
        return $allProviders->map(function ($provider) use ($collectionAllTime, $collectionADay) {
            $cloudProviderEnum = CloudProviderEnums::tryFrom($provider);

            if ($cloudProviderEnum === null) {
                return "Error: $provider is not found in CloudProviderEnum";
            }

            $readableName = $cloudProviderEnum->getProviderReadableAttribute();
            $emoji = $cloudProviderEnum->getEmoji();

            $allTimeCount = $collectionAllTime->get($provider, 0);
            $aDayCount = $collectionADay->get($provider, 0);

            // Format the line based on the presence of the daily count
            $formattedCount = $aDayCount > 0 ? "{$allTimeCount} (`+{$aDayCount}`)" : "{$allTimeCount}";

            return "• $emoji *$readableName:* $formattedCount";
        })->implode("\n\n");
    }

    public function formatDisplayCount(int $count): string 
    {
        return $count > 0 ? "(`+{$count}`)" : "";
    }
}
