<?php

namespace App\Services\Shell;

use App\Services\Shell\ShellOutput;
use App\Services\Shell\ShellResponse;
use Exception;
use Facades\App\Services\Shell\ShellProcessRunner as Facade;
use Symfony\Component\Process\Exception\ProcessTimedOutException;
use Symfony\Component\Process\Process;
use function collect;
use function tap;

class ShellProcessRunner
{
    /**
     * Run the given process and return it.
     *
     * @param  Process  $process
     * @return ShellResponse
     */
    public static function run(Process $process)
    {
        try {
            $process = tap($process)->run($output = new ShellOutput);
        } catch (ProcessTimedOutException $e) {
            $timedOut = true;
        } catch (Exception $e) {
            return new ShellResponse(500, (string) ($output ?? $e->getMessage()), $timedOut ?? false);
        }

        return new ShellResponse(
            $process->getExitCode(), (string) ($output ?? ''), $timedOut ?? false
        );
    }

    /**
     * Mock the responses for the process runner.
     *
     * @param  array  $responses
     * @param  int  $times
     * @return void
     */
    public static function mock(array $responses, int $times = 3)
    {
        Facade::shouldReceive('run')->times($times)->andReturn(...collect($responses)->flatMap(function ($response) {
            return [
                (object) ['exitCode' => 0], // Ensure Directory Exists...
                (object) ['exitCode' => 0], // Upload...
                (object) $response,
            ];
        })->all());
    }

    /**
     * Mock the responses for the process runner.
     *
     * @param  array  $responses
     * @param  int  $times
     * @return void
     */
    public static function mockInline(array $responses, int $times = 1)
    {
        Facade::shouldReceive('run')->times($times)->andReturn(
            ...collect($responses)->flatMap(function ($response) {
            return [
                (object) $response,
            ];
        })->all()
        );
    }
}
