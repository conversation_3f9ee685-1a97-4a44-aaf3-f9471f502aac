<?php

namespace App\Services\Server\Web\Providers;

use App\Enums\CloudProviderEnums;
use App\Enums\XcloudBilling\BillingServices;
use App\Models\CloudProvider;
use App\Services\CloudServices\Fetchers\Providers\AWSEC2Fetcher;
use App\Services\CloudServices\Fetchers\Providers\AWSLightsailFetcher;
use App\Services\Database\DatabaseEngine;
use App\Services\Server\Web\BaseCloudProviderService;
use Illuminate\Http\RedirectResponse;
use Inertia\Response;


class AWSService extends BaseCloudProviderService {

    /**
     * @throws \Exception
     */
    function __construct() {
        $this->billingServices = BillingServices::SelfManagedHosting;
        $this->checkingPaymentMethod();
    }

    public function viewChooseCredentials(): Response|RedirectResponse
    {
        $providers = team()->cloudProviders()
            ->where('provider', CloudProviderEnums::AWS)
            ->select([
                'id',
                'name',
                'provider',
                'service_names',
                'credentials',
                'created_at',
                'updated_at'
            ])->get();

        $parameters = [
            'cloud_provider_name' => CloudProviderEnums::AWS,
            'providers' => $providers,
            'provider' => $providers->count() > 0 ? '' : 'new',
            'can_add_provider' => user()->can('create', CloudProvider::class),
        ];

        return $this->buildInertiaRender('Server/Create/AWS/ChooseProvider', $parameters);
    }

    /**
     * @throws \Exception
     */
    public function viewCreateServer(CloudProvider $cloudProvider): Response
    {

//        to create EC2 instances, we need instance name from user, AMI id (OS), Instance Type,
        $regions = [];
        $awsEC2Fetcher = new AWSEC2Fetcher($cloudProvider->getAccessToken());
        $regions['ec2'] = $awsEC2Fetcher->getRegions();

        $awsLightsailFetcher = new AWSLightsailFetcher($cloudProvider->getAccessToken());
        $regions['lightsail'] = $awsLightsailFetcher->getRegions();

        $parameters = [
            'regions' => $regions,
            'provider' => $cloudProvider,
            'can_create_demo'=> user()->canCreateDemoServer(),
            'enabled_service_names' => $cloudProvider->service_names,
            'database_type' => DatabaseEngine::MYSQL_8,
            'database_type_list' => DatabaseEngine::getVersionList(),
            // Other properties will be based on region and the service type, they will be loaded from frontend using API
        ];

        return $this->buildInertiaRender('Server/Create/AWS/Index', $parameters);
    }
}
