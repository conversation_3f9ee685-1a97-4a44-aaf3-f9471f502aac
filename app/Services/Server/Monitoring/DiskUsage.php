<?php

namespace App\Services\Server\Monitoring;
class DiskUsage
{
    private array $data;

    #constant for the low disk space
    public const LOW_DISK_SPACE = 5;

    #constant for high disk space usage and need to be emergency action
    public const CRITICAL_DISK_SPACE = 1;

    public function __construct(array $data) {
        $this->data = $data;
    }

    public function getTotalUsed(): string {
        $totalUsedBytes = 0;

        foreach ($this->data as $dataset) {
            $totalUsedBytes += $this->convertToBytes($dataset['used']);
        }

        return $this->formatBytes($totalUsedBytes);
    }

    public function getTotalSize(): string {
        $totalSizeBytes = 0;

        foreach ($this->data as $dataset) {
            $totalSizeBytes += $this->convertToBytes($dataset['total']);
        }

        return $this->formatBytes($totalSizeBytes);
    }

    public function getAvailable(): string {
        $totalUsedBytes = 0;
        $totalSizeBytes = 0;

        foreach ($this->data as $dataset) {
            $totalUsedBytes += $this->convertToBytes($dataset['used']);
            $totalSizeBytes += $this->convertToBytes($dataset['total']);
        }

        $availableBytes = $totalSizeBytes - $totalUsedBytes;

        return $this->formatBytes($availableBytes);
    }

    public function getUsedPercentage(): string {
        $totalUsedBytes = 0;
        $totalSizeBytes = 0;

        foreach ($this->data as $dataset) {
            $totalUsedBytes += $this->convertToBytes($dataset['used']);
            $totalSizeBytes += $this->convertToBytes($dataset['total']);
        }

        $usedPercentage = ($totalUsedBytes / $totalSizeBytes) * 100;

        return round($usedPercentage, 2);
    }

    private function convertToBytes(string $size): float
    {
        $unit = strtoupper(substr($size, -1));
        $size = (float) $size;
        switch ($unit) {
            case 'K':
                return $size * 1024;
            case 'M':
                return $size * 1024 * 1024;
            case 'G':
                return $size * 1024 * 1024 * 1024;
            case 'T':
                return $size * 1024 * 1024 * 1024 * 1024;
            default:
                return $size;
        }
    }

    //format the bytes to GB
     private function formatBytes(float $bytes): string {
        $gb = $bytes / (1024 * 1024 * 1024);
        //return the size in GB
        return round($gb, 2);
    }

    public function isLow(): bool
    {
        return $this->getAvailable() < self::LOW_DISK_SPACE;
    }

    public function isCritical(): bool
    {
        return $this->getAvailable() < self::CRITICAL_DISK_SPACE;
    }
}
