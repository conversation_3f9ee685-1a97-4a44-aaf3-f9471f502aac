<?php

namespace App\Services\Server\Monitoring;
use Illuminate\Support\Str;

class WhiteLabelProcesses
{

    public function update(array $processes,string $brandName = 'xCloud'): array{
        //byCpu processes, if the user is xCloud update user with brandName
        $byCpu = collect($processes['byCpu'])->map(function ($process) use ($brandName) {
            if (Str::lower($process['user']) === 'xcloud') {
                $process['user'] = $brandName;
            }
            return $process;
        });

        //byRam prcesses, if the user is xCloud update user with brandName
        $byRam = collect($processes['byRam'])->map(function ($process) use ($brandName) {
            if (Str::lower($process['user']) === 'xcloud') {
                $process['user'] = $brandName;
            }
            return $process;
        });

        //return the updated processes as an array
        $processes['byCpu'] = $byCpu->toArray();
        $processes['byRam'] = $byRam->toArray();
        return $processes;

    }
}
