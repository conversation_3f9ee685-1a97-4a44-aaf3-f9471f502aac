<?php

namespace App\Services\PaymentGateway\InvoiceServices;

use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\XcloudBilling\InvoiceTypesEnum;
use App\Enums\XcloudBilling\PaymentGateway;
use App\Enums\XcloudBilling\TeamBillingStatus;
use App\Models\Bill;
use App\Models\CartForm;
use App\Models\Coupon;
use App\Models\Refund;
use App\Models\Team;
use App\Models\User;
use App\Repository\AffiliateRepository;
use App\Services\PaymentGateway\BillingServices\InvoiceCreation;
//use App\Traits\BelongsToWhiteLabel;
use App\Traits\InvoiceServiceProvider;
use App\Traits\MetaAccessors;
use App\Traits\XcSoftDelete;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Stripe\PaymentIntent;

/**
 * @property mixed|null $invoice_number
 * @property mixed $affiliatedFrom
 * @property mixed $promoter_id
 */
abstract class Invoiceable extends Model implements InvoiceCreation
{
    use HasFactory, XcSoftDelete, MetaAccessors, InvoiceServiceProvider;// BelongsToWhiteLabel;

    protected $table = 'invoices';

    protected $guarded = [
        'id',
        'reference_no',
        'invoice_number'
    ];

    protected $casts = [
        'currency' => BillingCurrency::class,
        'status' => BillingStatus::class,
        'type' => InvoiceTypesEnum::class,
        'source' => InvoiceSourceEnum::class,
        'payment_gateway' => PaymentGateway::class,
        'gateway_invoice_or_intent_id' => 'string',
        'logs' => 'array',
        'metadata' => 'array',
        'meta' => 'array',
        'is_deleted' => 'boolean',
        'paid_at' => 'datetime',
        'refunded_at' => 'datetime',
        'due_date' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $appends = [
        'status_readable',
        'number',
        'currency_symbol'
    ];

    const MIN_AMOUNT = 0.50;

    public function __construct()
    {
        parent::__construct();

        static::saving(function ($model) {
            // if invoice is being created manually, then it should be created by the user
            if ($model->isDirty('status') && $model->status === BillingStatus::Cancelled) {
                if ($model->bills()->where('service_is_active', true)->exists()) {
                    throw new \Exception('Invoice# ' . $model->invoice_number . ' can not be cancelled because it has active bills');
                } else {
                    $model->bills()->where('status', '!=', BillingStatus::Paid)->update([
                        'status' => BillingStatus::Cancelled
                    ]);
                }
            }

            // If the invoice is paid set paid_at
            if ($model->isDirty('status') && $model->status === BillingStatus::Paid) {
                $model->paid_at = now();
            }

            if ($model->isDirty('status') && $model->status !== BillingStatus::Paid && $model->paid_at) {
                $model->paid_at = null;
            }

            // If the invoice is paid set refunded_at
            if ($model->isDirty('status') && $model->status === BillingStatus::Refunded) {
                $model->refunded_at = now();
            }

            if ($model->isDirty('status') && $model->status !== BillingStatus::Refunded && $model->refunded_at) {
                $model->refunded_at = null;
            }
        });

        static::creating(function ($model) {
            $refNo = strtoupper(Str::random(12));
            $model->reference_no = $refNo;
            $model->invoice_number = 'XC-INV-' . date('Ymd') . '-' . $refNo;
            $model->manually_created_by = auth()->check() ? auth()->id() : null;

            if(!$model->currency){
                $model->currency = BillingCurrency::USD;
            }
        });

        static::updating(function ($model) {
            $model->manually_updated_by = auth()->check() ? auth()->id() : null;
        });

        static::saved(function ($model) {
            if ($model->status === BillingStatus::Paid &&
                $model->team->activePlan->supported_invoice_type === InvoiceTypesEnum::General &&
                $model->team->billing_status === TeamBillingStatus::Inactive && !$model->team->hasDuesToPay())
            {
                $model->team->setBillingStatusActive();
            }
        });
    }


    abstract public function renderInvoicePdf(): string;

    abstract public function invoicePdfView(User $user): \Barryvdh\DomPDF\PDF;

    public function scopeUnsuccessful($query)
    {
        return $query->whereNotIn('status', BillingStatus::noIssueWithInvoices());
    }

    public function getStatusReadableAttribute() : string|null
    {
        return $this->status?->asTitle();
    }

    public function getCurrencySymbolAttribute() : string
    {
        return $this->currency ? $this->currency->symbol() : '$';
    }

    public function getNumberAttribute() : string
    {
        return $this->invoice_number ?: $this->reference_no ?: $this->id;
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function refunds() : HasMany
    {
        return $this->hasMany(Refund::class, 'invoice_id');
    }

    public function bills(): HasMany
    {
        return $this->hasMany(Bill::class, 'invoice_id');
    }

    public function unpaidBills(): HasMany
    {
        return $this->hasMany(Bill::class, 'invoice_id')->where('status', BillingStatus::Unpaid);
    }

    public function cartForm(): HasMany
    {
        return $this->hasMany(CartForm::class, 'invoice_id');
    }

    public function manuallyCreatedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manually_created_by');
    }

    public function manuallyUpdatedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manually_updated_by');
    }

    public function getPurchaseTitle() : string
    {
        if($this->bills->count() < 1){
            return 'Our services';
        }

        if ($this->bills->count() >= 1) {
            $bills = [];

            $this->bills->each(function ($bill) use (&$bills) {
                $bills[] = $bill->service->toReadableSentence();
            });

            return implode(', ', $bills);
        }

        return $this->invoice_number;
    }

    public function isPaid(): bool
    {
        return $this->status === BillingStatus::Paid;
    }

    public function isCancelled(): bool
    {
        return $this->status === BillingStatus::Cancelled;
    }

    public function requiresAction(): bool
    {
        return $this->status === BillingStatus::RequireAction;
    }

    public function paid(): void
    {
        $this->bills()->update([
            'status' => BillingStatus::Paid,
            'paid_on' => now()
        ]);
    }

    public function refunded(): void
    {
        $this->bills()->update([
            'status' => BillingStatus::Refunded,
        ]);
    }

    public function calculateRefundableAmount() : float
    {
        if ($this->bills) {
            return $this->bills->sum('refundable_amount');
        }

        if ($this->cartForm) {
            return $this->cartForm->sum('refundable_amount');
        }

        return $this->amount;
    }

    ## TODO: Remove later (@Faisal)(Only for testing)
    /**
     * @throws \Exception
     */
    public function generateInvoiceToLocalStorage()
    {
        $pdf = Pdf::loadView('Bills.invoice', [
            'invoice' => $this,
            'bills' => $this->team->unpaidBills,
            'currentMonth' => Carbon::now()->format('F'),
            'billableAmount' => ($this->team->unpaidBills->sum('amount') + $this->team->unpaidBills->sum('adjustable_amount')),
            'taxAmount' => ($this->team->unpaidBills->sum('amount') + $this->team->unpaidBills->sum('adjustable_amount')) * .05 // 5% tax for example
        ]);
        $pdf->render();

        // store the invoice in storage/invoices folder
        file_put_contents(storage_path('invoices/invoice_' . $this->reference_no . '.pdf'), $pdf->output());
    }

    public function setStatusPaid(): bool
    {
        return $this->update([
            'status' => BillingStatus::Paid->value
        ]);
    }

    public function setStatusFailed(): void
    {
        $this->updateQuietly([
            'status' => BillingStatus::Failed->value
        ]);
    }

    public function setStatusPaymentFailed(): void
    {
        $this->update([
            'status' => BillingStatus::PaymentFailed->value
        ]);
    }

    public function setStatusPending(): void
    {
        $this->update([
            'status' => BillingStatus::Pending->value
        ]);
    }

    public function setStatusRequiresAction(): void
    {
        $this->update([
            'status' => BillingStatus::RequireAction->value
        ]);
    }

    public function setStatusCancelled(): void
    {
        $this->update([
            'status' => BillingStatus::Cancelled
        ]);
    }

    public function saveLog($key, $value): bool
    {
        $logs = $this->logs;

        $logs[$key] = $value;

        return $this->update([
            'logs' => $logs
        ]);
    }

    /**
     * @throws GuzzleException
     */
    public function handleAffiliateCommission(): void
    {
        // if team is created with affiliate promoter and this invoice does not have promoter id, then proceed to generate affiliate commission
        if ($this->team?->promoter && !$this->promoter_id) {
            (new AffiliateRepository($this))->generateAffiliateCommission();
        }
    }
    public function markAsCancelled(): void
    {
        $this->update([
            'status' => BillingStatus::Cancelled
        ]);
    }

    /**
     * @throws Exception
     */
    protected function canCreate() : void
    {
        $trace = optional(debug_backtrace()[2]);

        $creatingFromFunction = $trace['function'];

        $creatingFromClass = $trace['class'];

        if (($creatingFromFunction !== 'generate') && ($creatingFromClass !== InvoiceGenerator::class)) {
            throw new Exception('You can not create invoice directly. Use InvoiceGenerator::generate() instead.');
        }
    }

    public function sendNotification(Notification $toNotify = null): void
    {
        $notification = $this->toNotify ?: $this->source->getNotificationClass();

        $this->team?->notify(new $notification($this));
    }

    public function coupon() : BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    public function retrievePaymentIntentFromStripe() : PaymentIntent | null
    {
        try {
            if($this->team->whiteLabel){
                $paymentIntent = PaymentIntent::retrieve($this->gateway_invoice_or_intent_id, [
                    'stripe_account' => $this->team->whiteLabel->connectedAccount->stripe_account_id
                ]);
            } else {
                $paymentIntent = PaymentIntent::retrieve($this->gateway_invoice_or_intent_id);
            }

            return $paymentIntent;
        } catch (\Exception $e) {
            Log::error('Failed to retrieve payment intent', [
                'invoice_id' => $this->id,
                'invoice_number' => $this->invoice_number,
                'payment_intent_id' => $this->gateway_invoice_or_intent_id,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }
}
