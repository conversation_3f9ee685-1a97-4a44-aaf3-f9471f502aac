<?php

namespace App\Services\PaymentGateway\InvoiceServices;

use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\InvoiceSourceEnum;
use App\Enums\XcloudBilling\InvoiceTypesEnum;
use App\Models\CartForm;
use App\Models\Coupon;
use App\Models\PaymentMethod;
use App\Models\Team;
use App\Nova\Bill;
use App\Repository\StripePaymentRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * @property $team
 * @method title(string $string)
 */
class InvoiceGenerator extends InvoiceGeneratorHelper
{
    protected Team $team;
    private InvoiceSourceEnum $source;
    protected ?CartForm $cartForm = null;
    private ?InvoiceTypesEnum $type = null;
    private ?Notification $toNotify = null;
    private ?PaymentMethod $paymentMethod = null;
    private ?Collection $bills = null;
    private bool $sendNotification = false;
    private bool $ignoreStripePIGeneration = false;
    private array $attributes = [];
    private array $allowedMethods = [
//        'amount', 'discount', 'referenceNo', 'invoiceNumber'
    ]; // Add more allowed methods as needed
    private array $requiredMethodValues = [
        'amount', 'team_id', 'source'
    ]; // Add the names of required methods

    private function __construct()
    {
        // Private constructor to prevent external instantiation
    }

    private static function getInstance(): self
    {
        static $instance = null;

        if ($instance === null) {
            $instance = new self();
        }

        return $instance;
    }

    public static function __callStatic($method, $args): self
    {
        $self = self::getInstance();
        $self->setAttributes($method, $args[0]);
        return $self;
    }

    public function __call($method, $args): self
    {
        $this->setAttributes($method, $args[0]);
        return $this;
    }

    private function setAttributes($method, $value): void
    {
        if (empty($this->allowedMethods) || in_array($method, $this->allowedMethods)) {
            $this->attributes[$method] = $value;
        } else {
            throw new \BadMethodCallException("Method $method is not allowed.");
        }
    }

    public static function amount(float $amount): self
    {
        $self = self::getInstance();
        $self->setAmount($amount);
        return $self;
    }

    public static function bills(Collection|Bill $bills) : self
    {
        $self = self::getInstance();
        $self->setBills($bills);
        return $self;
    }

    public static function team(Team $team): self
    {
        $self = self::getInstance();
        $self->setTeam($team);
        return $self;
    }

    public static function source(InvoiceSourceEnum $source): self
    {
        $self = self::getInstance();
        $self->setSource($source);
        return $self;
    }

    public static function status(BillingStatus $status): self
    {
        $self = self::getInstance();
        $self->setStatus($status);
        return $self;
    }

    public static function type(InvoiceTypesEnum $type): self
    {
        $self = self::getInstance();
        $self->setType($type);
        return $self;
    }

    public static function cartForm(CartForm $cartForm): self
    {
        $self = self::getInstance();
        $self->setCartForm($cartForm);
        return $self;
    }

    public static function toNotify(Notification $toNotify): self
    {
        $self = self::getInstance();
        $self->setToNotify($toNotify);
        return $self;
    }

    public static function paymentMethod(PaymentMethod $paymentMethod): self
    {
        $self = self::getInstance();
        $self->setPaymentMethod($paymentMethod);
        return $self;
    }

    public static function sendNotification(bool $yes = true) : self
    {
        $self = self::getInstance();
        $self->setSendNotification($yes);
        return $self;
    }

    public static function coupon(Coupon $coupon = null): self
    {
        $self = self::getInstance();
        $self->setCoupon($coupon);
        return $self;
    }

    public static function dueDate(Carbon $dueDate = null): self
    {
        $self = self::getInstance();
        $self->setDueDate($dueDate);
        return $self;
    }

    public static function ignoreStripePIGeneration(bool $yes = true): self
    {
        $self = self::getInstance();
        $self->setIgnoreStripePIGeneration($yes);
        return $self;
    }

    private function setAmount(float $amount) : self
    {
        $this->attributes['amount'] = $amount;

        return $this;
    }

    private function setBills(Collection $bills) : self
    {
        $this->bills = $bills;

        return $this;
    }

    private function setTeam(Team $team): self
    {
        $this->team = $team;

        $this->attributes['team_id'] = $team->id;

        return $this;
    }

    private function setSource(InvoiceSourceEnum $source): self
    {
        $this->attributes['source'] = $source;

        $this->source = $source;

        return $this;
    }

    private function setStatus(BillingStatus $status = BillingStatus::Unpaid): self
    {
        $this->attributes['status'] = $status;

        return $this;
    }

    private function setType(InvoiceTypesEnum $type): self
    {
        $this->type = $type;

        return $this;
    }

    private function setCartForm(CartForm $cartForm): self
    {
        $this->cartForm = $cartForm;

        return $this;
    }

    private function setToNotify(Notification $toNotify): self
    {
        $this->toNotify = $toNotify;

        return $this;
    }

    private function setPaymentMethod(PaymentMethod $paymentMethod): self
    {
        if ($paymentMethod->team_id !== $this->team->id) {
            throw new \BadMethodCallException("Payment method does not belong to the team.");
        }

        $this->paymentMethod = $paymentMethod;

        return $this;
    }

    private function setSendNotification(bool $yes = true) : self
    {
        $this->sendNotification = $yes;

        return $this;
    }

    private function setCoupon(Coupon $coupon = null): self
    {
        $this->attributes['coupon_id'] = $coupon?->id;

        return $this;
    }

    private function setDueDate(Carbon $dueDate = null): self
    {
        if ($dueDate && $dueDate->isPast()) {
            throw new \BadMethodCallException('Due date cannot be in the past.');
        }

        $this->attributes['due_date'] = $dueDate?: now()->endOfDay();

        return $this;
    }

    private function setIgnoreStripePIGeneration(bool $yes = true): self
    {
        $this->ignoreStripePIGeneration = $yes;

        return $this;
    }

    private function filteredData() : array
    {
        $paymentMethodData = [];

        $paymentMethod = $this->paymentMethod ?: $this->team?->activePaymentMethod()?->first();

        $type = $this->type ?: $this->team?->activePlan?->supported_invoice_type;

        if ($this->bills) {
            $query = $this->bills
                            ->where('has_offer', false)
                            ->where('status', BillingStatus::Unpaid)
                            ->whereNull('invoice_id');

            $this->attributes['amount'] = $query->sum('amount_to_pay') + $query->sum('additional_usage_charge');

            if ($query?->count() && !isset($this->attributes['description'])) {
                $this->attributes['description'] = $this->generateDynamicDescriptionFromBills($query);
            }

            if ($query?->count() && !isset($this->attributes['title'])) {
                $this->attributes['title'] = $this->generateDynamicTitleFromBills($query);
            }

            if ($this->cartForm && !isset($this->attributes['description'])) {
                $this->attributes['description'] = 'Purchased services - ' . $this->cartForm?->getTitleForInvoice .
                    ($this->cartForm?->getShortDescriptionForInvoice ?
                        ' - ' . $this->cartForm?->getShortDescriptionTitleForInvoice . ' : ' . $this->cartForm?->getShortDescriptionForInvoice :
                        ' - ' . $this->cartForm?->service?->toReadableSentence()) . PHP_EOL . ' - Auto Generated.';
            }

            if ($query->where('status', BillingStatus::Paid)?->isNotEmpty()) {
                Log::error('Team# '.$this?->team?->email. ' - One or more bills are already paid.
                            Call trace: '.$this?->team?->email. " - ".debug_backtrace()[1]['file'].':'.debug_backtrace()[1]['line']);
            }
        }

        if ($this->cartForm && !isset($this->attributes['description'])) {
            $this->attributes['description'] = $this->generateDynamicDescriptionFromCartForm();
        }

        if ($this->cartForm && !isset($this->attributes['title'])) {
            $this->attributes['title'] = $this->generateDynamicTitleFromCartForm();
        }

        if (!isset($this->attributes['title'])) {
            $this->attributes['title'] = $this->attributes['source']->getTitleFromSource($this->team);
        }

        if ($type) {
            $this->attributes['type'] = $type->value;
        }

        if (!isset($this->attributes['status'])) {
            $this->attributes['status'] = BillingStatus::Unpaid;
        }

        if (!isset($this->attributes['due_date'])) {
            $this->attributes['due_date'] = now()->endOfDay();
        }

        if (isset($this->attributes['status']) && $this->attributes['status'] === BillingStatus::Paid) {
            $this->attributes['paid_at'] = now();
        }

        if (!$paymentMethod && $type->isRequiredPaymentMethod()) {
            Log::error('Team# '.$this?->team?->email. " - The billing plan does not support manual invoices and no payment method was provided.");
            throw new \BadMethodCallException('Team# '.$this?->team?->email. " - The billing plan does not support manual invoices and no payment method was provided.");
        }

        if ($type?->isRequiredPaymentMethod()) {
            $paymentMethodData = [
                'payment_method_id' => $paymentMethod?->id,
                'gateway_payment_method_id' => Arr::get($paymentMethod?->meta, 'stripe.payment_method'),
                'gateway_customer_id' => $paymentMethod?->customer_id,
                'customer_email' => $paymentMethod?->getMeta('customer.email') ?: $this->team->email
            ];
        }

        return array_merge($paymentMethodData, array_filter($this->attributes, fn($value) => !empty($value)));
    }

    private function checkRequiredMethods(): void
    {
        $missingMethods = array_diff($this->requiredMethodValues, array_keys($this->attributes));

        if (in_array('team_id', $missingMethods) && $this->source->canGenerateWithoutTeam() && $this->cartForm) {
            return;
        }

        if (in_array('amount', $missingMethods) && $this->bills) {
            return;
        }

        if (!empty($missingMethods)) {
            throw new \BadMethodCallException("Required values not found: " . implode(', ', $missingMethods) . '.
            Please note that you can only generate invoice without team if the source is cart form.');
        }
    }

    public function generate() : Invoiceable
    {
        $this->checkRequiredMethods();

        $type = $this->type ?: $this->team?->activePlan?->supported_invoice_type;

        if ($this->filteredData()['amount'] < 0.50) {
            throw new \BadMethodCallException('Invoice amount must be greater than or equal to 0.50.');
        }

        $invoice = app($type->model())->create($this->filteredData());

        if ($this->bills) {
            $this->bills->each(function ($bill) use ($invoice) {
//                $bill->update([
//                    'invoice_id' => $invoice->id // This creates issue with duplicate hashes for bills on update
//                ]);
                DB::table('bills')
                    ->where('id', $bill->id)
                    ->update([
                        'invoice_id' => $invoice->id
                    ]);
            });
        }

        $this->cartForm?->update([
            'invoice_id' => $invoice->id
        ]);

        $backtrace = debug_backtrace() ?? null;

        if (is_array($backtrace) && count($backtrace) > 0) {

            $paths = [];

            // Loop through the first three backtrace entries
            for ($i = 0; $i < 3; $i++) {
                if (isset($backtrace[$i]['file'])) {
                    // If 'file' exists, include file, line, and function
                    $paths[] = $backtrace[$i]['file'] . ':' . $backtrace[$i]['line'] . ':' . $backtrace[$i]['function'] . '()';
                } else {
                    // Otherwise, just include the function name
                    $paths[] = $backtrace[$i]['function'] . '()';
                }
            }

            // Find the common prefix for all paths
            $commonPrefix = '';
            if (count($paths) > 1) {
                $segments = array_map(function ($path) {
                    return explode('/', $path); // Split paths into directory segments
                }, $paths);

                // Identify the common prefix across all paths
                $commonPrefixSegments = [];
                foreach (array_keys($segments[0]) as $index) {
                    $currentSegment = array_column($segments, $index);
                    if (count(array_unique($currentSegment)) === 1) {
                        $commonPrefixSegments[] = $currentSegment[0];
                    } else {
                        break; // Stop at the first non-matching segment
                    }
                }

                $commonPrefix = implode('/', $commonPrefixSegments);
            }

            // Prepare call traces, keeping only the portion starting from 'app/' or the last meaningful directory
            $callTraces = array_map(function ($call, $index) use ($commonPrefix) {
                if (isset($call['file'])) {
                    // First common prefix
                    $firstCommonPrefix = array_reverse(explode('/', $commonPrefix));
                    $firstCommonPrefix = isset($firstCommonPrefix[0]) ? $firstCommonPrefix[0].'/' : '';

                    // Trim the common prefix from the file path
                    $relativePath = str_replace($commonPrefix . '/', '', $call['file'] . ':' . $call['line'] . ':' . $call['function'] . '()');

                    // Ensure the path starts cleanly with 'app/' or the relevant directory
                    $relativePath = ltrim($relativePath, '/');

                    // Format trace string with $firstCommonPrefix line number and function name
                    return sprintf(($index === 0 ? '* ' : '←').' %s%s', $firstCommonPrefix, $relativePath);
                }
                return sprintf('%02d. %s()', $index + 1, $call['function']);
            }, array_slice($backtrace, 0, 3), array_keys(array_slice($backtrace, 0, 3)));

            // Combine traces into a single string
            $formattedTrace = implode(PHP_EOL, $callTraces);

            // Check the total length and adjust if it exceeds 255 characters
            if (strlen($formattedTrace) > 255) {
                // Keep the latest two traces
                $formattedTrace = implode(PHP_EOL, array_slice($callTraces, 0, 2));

                // Check again if this exceeds 255 characters
                if (strlen($formattedTrace) > 255) {
                    // Keep only the latest trace
                    $formattedTrace = $callTraces[0];
                }
            }

            if (empty($formattedTrace)) {
                $formattedTrace = $backtrace[0]['file'] ?? 'Unknown';
            }

            // Update the invoice with the formatted call trace
            $invoice->update([
                'call_trace' => $formattedTrace,
            ]);
        }

        if ($invoice->team->white_label_id) {

            $applicationFee = 0;

            $applicationFeeLog = '';

            $billsCount = $invoice->bills->count();
            $billsLoop = 0;

            if ($billsCount > 0) {
                $invoice->bills->each(function ($bill) use (&$applicationFee, &$applicationFeeLog, $billsCount, &$billsLoop) {
                    $applicationFee += $bill->application_fee_to_charge;

                    if ($bill->product?->source) {
                        $applicationFeeLog .= $bill->product?->source?->title.' ('.$bill->product?->source?->service_type?->toReadableSentence().'): '.$bill->product?->source?->price. ' ('.$bill->currency->toUpperCase()."); \n ";
                    }

                    if (!$bill->product?->source && $bill->actual_application_fee) {
                        $applicationFeeLog .= $bill->service->toReadableSentence().': '.($applicationFee ?: $bill->amount_to_pay). ' ('.$bill->currency->toUpperCase()."); \n ";
                    }

                    if($applicationFee) {
                        $applicationFeeLog .= 'Application Fee: '.$applicationFee. ' ('.$bill->currency->toUpperCase()."); \n ";
                    }

//                    if ($billsCount > $billsLoop) {
//                        $applicationFeeLog .= ', ';
//                    } else {
//                        $applicationFeeLog .= '. ';
//                    }

                    $billsLoop++;
                });
            }

            $cartFormCount = $invoice->cartForm->count();
            $cartFormLoop = 0;

            if ($cartFormCount > 0) {
                $invoice->cartForm->each(function ($cartForm) use (&$applicationFee, &$applicationFeeLog, $cartFormCount, &$cartFormLoop) {
                    $applicationFee += $cartForm->product?->source?->price;

                    if ($cartForm->product?->source) {
                        $applicationFeeLog .= $cartForm->product?->source?->title.' ('.$cartForm->product?->source?->service_type?->toReadableSentence().'): '.$cartForm->product?->source?->price. "; \n ";
                    }

//                    if ($cartFormCount > $cartFormLoop) {
//                        $applicationFeeLog .= ', ';
//                    } else {
//                        $applicationFeeLog .= '. ';
//                    }

                    $cartFormLoop++;
                });
            }

            if ($applicationFee) {
                $invoice->update([
                    'application_fee_amount' => $applicationFee,
                    'meta->application_fee_log' => $applicationFeeLog
                ]);
            }
        }

        if($invoice?->type?->is(InvoiceTypesEnum::General) && !$invoice->gateway_invoice_or_intent_id && $invoice->amount > 0 && !$this->ignoreStripePIGeneration) {
            // create payment intent
            $paymentIntent =  app(StripePaymentRepository::class)->createPaymentIntent($invoice);

            if(empty($paymentIntent->id)){
                $invoice->setStatusFailed();
                $invoice->saveLog('error', 'Payment intent creation failed');
            }else{
                // update invoice
                $invoice->updateQuietly([
                    'gateway_invoice_or_intent_id' => $paymentIntent->id,
                ]);
            }
        }

//        if ($this->sendNotification) {
//            $notification = $this->toNotify ?: $this->source->getNotificationClass();
//            $this->team?->notify(new $notification($invoice));
//        }

        return $invoice;
    }
}
