<?php

namespace App\Services\PaymentGateway\API;

use App\Enums\XcloudBilling\InvoicePaymentTypeAPI;
use App\Http\Resources\InvoiceResourceResponse;
use App\Jobs\ExecuteStoredInvoiceJob;
use App\Models\GeneralInvoice;
use App\Models\PaymentMethod;
use App\Models\SubscriptionProduct;
use App\Models\WhiteLabel;
use App\Repository\StripePaymentRepository;
use Closure;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\Stripe;
use Stripe\Subscription;

class InvoicePaymentService
{
    private GeneralInvoice $invoice;
    private ?PaymentMethod $paymentMethod = null;
//    private ?Closure $onSuccessCallback = null;
//    private ?Closure $onFailCallback = null;
    private bool $usingDefaultPaymentMethod = false;
    private ?object $successJob = null;
    private ?object $failureJob = null;
    private ?string $successRedirectUrl = null;
    private ?string $failureRedirectUrl = null;
    private ?string $secure3dFailureRedirectUrl = null;

    /**
     * Create a new instance for the given invoice
     *
     * @param GeneralInvoice $invoice
     * @return static
     */
    public static function forInvoice(GeneralInvoice $invoice): static
    {
        $instance = new static();
        $instance->invoice = $invoice;
        return $instance;
    }

    /**
     * Set the payment method to use
     *
     * @param PaymentMethod|null $paymentMethod
     * @return $this
     */
    public function withPaymentMethod(?PaymentMethod $paymentMethod): static
    {
        $this->paymentMethod = $paymentMethod;
        return $this;
    }

//    /**
//     * Set the success callback
//     *
//     * @param callable $callback
//     * @return $this
//     */
//    public function onSuccess(callable $callback): static
//    {
//        $this->onSuccessCallback = $callback;
//        return $this;
//    }
//
//    /**
//     * Set the failure callback
//     *
//     * @param callable $callback
//     * @return $this
//     */
//    public function onFail(callable $callback): static
//    {
//        $this->onFailCallback = $callback;
//        return $this;
//    }

    /**
     * Set the success job to be dispatched on payment success
     *
     * @param object $job
     * @return $this
     */
    public function onSuccessJob(object $job): static
    {
        $this->successJob = $job;
        return $this;
    }

    /**
     * Set the failure job to be dispatched on payment failure
     *
     * @param object $job
     * @return $this
     */
    public function onFailureJob(object $job): static
    {
        $this->failureJob = $job;
        return $this;
    }

    /**
     * Set the redirect URL for successful payment
     *
     * @param string|null $url
     * @return $this
     */
    public function redirectOnPaymentSuccess(?string $url = null): static
    {
        $this->successRedirectUrl = $url ?? route('user.detailedInvoices', $this->invoice->invoice_number);
        return $this;
    }

    /**
     * Set the redirect URL for failed payment
     *
     * @param string|null $url
     * @return $this
     */
    public function redirectOnPaymentFail(?string $url = null): static
    {
        $this->failureRedirectUrl = $url;
        return $this;
    }

    /**
     * Set the redirect URL for 3D Secure payment failure
     *
     * @param string|null $url
     * @return $this
     */
    public function redirectOnPayment3dSecuredFailure(?string $url = null): static
    {
        $this->secure3dFailureRedirectUrl = $url ?? route('invoice.pay', [
            'invoice' => $this->invoice->invoice_number,
            'success_redirect' => $this->successRedirectUrl ?: route('user.detailedInvoices', $this->invoice->invoice_number)
        ]); return $this;
    }

    /**
     * Process the payment with the configured options
     *
     * @return array
     * @throws ApiErrorException
     */
    public function process(): array
    {
        // Set default properties and methods
        $this->beforeProcessingPayment();

        // Store jobs in invoice before processing payment
        $this->storeJobsInInvoice();

        // Initialize Stripe
        Stripe::setApiKey(config('services.stripe.secret_key'));

        // Validate invoice payment eligibility
        $validationResponse = $this->validateInvoicePayment();
        if ($validationResponse) {
            return $this->handleResult($validationResponse);
        }

        // Handle payment method selection
        $paymentMethodResult = $this->handlePaymentMethodSelection();
        if ($paymentMethodResult['error'] ?? false) {
            $errorResponse = $this->generateResponse(InvoicePaymentTypeAPI::Error, $paymentMethodResult['message']);
            return $this->handleResult($errorResponse);
        }

        $this->paymentMethod = $paymentMethodResult['payment_method'];
        $this->usingDefaultPaymentMethod = $paymentMethodResult['using_default'];

        // Handle payment intent creation and processing
        $paymentIntentResult = $this->handlePaymentIntentCreation();
        if ($paymentIntentResult['error'] ?? false) {
            $errorResponse = $this->generateResponse(InvoicePaymentTypeAPI::Error, $paymentIntentResult['message']);
            return $this->handleResult($errorResponse);
        }

        $paymentIntent = $paymentIntentResult['payment_intent'];

        // Process payment confirmation
        $result = $this->processPaymentConfirmation($paymentIntent);
        return $this->handleResult($result);
    }

    /**
     * Handle the result by calling appropriate callbacks and dispatching jobs
     *
     * @param array $result
     * @return array
     */
    private function handleResult(array $result): array
    {
        $isSuccess = ($result['type'] ?? '') === 'success';

//        if ($isSuccess && $this->onSuccessCallback) {
//            call_user_func($this->onSuccessCallback, $result);
//        } elseif (!$isSuccess && $this->onFailCallback) {
//            call_user_func($this->onFailCallback, $result);
//        }

        // Dispatch stored jobs based on payment result
        $this->dispatchStoredJobs($isSuccess);

        return $result;
    }

    /**
     * Validate invoice payment eligibility
     *
     * @return array|null
     */
    private function validateInvoicePayment(): ?array
    {
        // Fetch payment webhook from stripe
        if ($this->invoice->status->isFailed() && $this->invoice?->gateway_invoice_or_intent_id) {
            $this->invoice->fetchPaymentWebhookFromStripe();
        }

        // Check if invoice is already paid from stripe
        if ($this->invoice?->gateway_invoice_or_intent_id && $this->invoice->isPaid()) {
            return $this->generateResponse(InvoicePaymentTypeAPI::Success, 'Invoice already paid!', $this->successRedirectUrl);
        }

        // Check if the invoice is paid without payment intent
        if (!$this->invoice?->gateway_invoice_or_intent_id && $this->invoice->status->isPaid()) {
            return $this->generateResponse(InvoicePaymentTypeAPI::Success,
                'Invoice already paid manually. Please contact support if you need any assistance.',
                $this->successRedirectUrl
            );
        }

        // Check if the invoice amount is less than the minimum amount
        if ($this->invoice->amount < $this->invoice->getMinimumAmountForTransaction()) {
            return $this->generateResponse(InvoicePaymentTypeAPI::Error,
                'Invoice amount is less than '.$this->invoice->currency->symbol().$this->invoice->getMinimumAmountForTransaction().'. Please contact support if you need any assistance.'
            );
        }

        return null;
    }

    /**
     * Handle payment method selection
     *
     * @return array
     */
    private function handlePaymentMethodSelection(): array
    {
        $usingDefaultPaymentMethod = false;

        if (!$this->paymentMethod) {
            $this->paymentMethod = $this->invoice->team->activePaymentMethod()->first();
            $usingDefaultPaymentMethod = true;
        }

        if (!$this->paymentMethod) {
            return [
                'error' => true,
                'message' => 'Please select a valid payment method.'
            ];
        }

        // switch card with the specified payment method
        $this->invoice->switchToPaymentMethod($this->paymentMethod);

        return [
            'payment_method' => $this->paymentMethod,
            'using_default' => $usingDefaultPaymentMethod
        ];
    }

    /**
     * Handle payment intent creation and validation
     *
     * @return array
     * @throws ApiErrorException
     */
    private function handlePaymentIntentCreation(): array
    {
        $paymentIntent = $this->invoice->retrievePaymentIntentFromStripe();
        $PaymentIntendResetLogMessage = null;

        if($paymentIntent && $paymentIntent->status === PaymentIntent::STATUS_CANCELED) {
            $PaymentIntendResetLogMessage = 'Payment intent cancelled from stripe. Resetting invoice for retry payment';
        }

        if ($paymentIntent && $paymentIntent->status === PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD) { // This action set by stripe
            try {
                (new StripePaymentRepository())->cancelPaymentIntent($this->invoice);
            } catch (\Exception $e) {
                Log::warning('Payment intent cancellation failed '.$this->invoice->invoice_number, [
                    'invoice_id' => $this->invoice->id,
                    'payment_intent_id' => $paymentIntent->id,
                    'payment_intent_status' => $paymentIntent->status,
                    'xcloud_team_id' => $this->invoice->team_id,
                    'xcloud_payment_method_id' => $this->invoice->payment_method_id,
                    'error' => $e->getMessage()
                ]);
            }

            $PaymentIntendResetLogMessage = 'Payment has issue from stripe. The status was '.$paymentIntent->status.'. Resetting invoice for retry payment';
        }

        if ($this->invoice->gateway_customer_id !== $this->paymentMethod->customer_id) {
            $PaymentIntendResetLogMessage = 'Payment method customer id mismatch. Resetting invoice for retry payment';
        }

        if($PaymentIntendResetLogMessage){
            $this->invoice->update([
                'gateway_invoice_or_intent_id' => null,
                'logs->reset_invoice' => $PaymentIntendResetLogMessage. ', action taken ~ by '.auth()->user()->email.' at '. now()->toDateTimeString(),
            ]);
        }

        // If payment intent is not created yet
        if (!$this->invoice->gateway_invoice_or_intent_id) {
            $paymentIntent = (new StripePaymentRepository())->createPaymentIntent($this->invoice);

            $this->invoice->update([
                'gateway_invoice_or_intent_id' => $paymentIntent->id,
                'gateway_customer_id' => $paymentIntent->customer,
                'gateway_payment_method_id' => $paymentIntent->payment_method
            ]);
        }

        // Todo: Need refactor with @Faisal vai
        if ($this->paymentMethod->security_protocol) {
            // v2 payment method(update existing payment method instead of creating new one)
            $stripeAccount = $this->invoice->team->whiteLabel ? $this->invoice->team->whiteLabel->connectedAccount->stripe_account_id : null;

            if ($paymentIntent->payment_method !== Arr::get($this->paymentMethod->meta, 'stripe.payment_method')) {
                $updateParams = [
                    'payment_method' => Arr::get($this->paymentMethod->meta, 'stripe.payment_method'),
                ];

                $paymentIntent = PaymentIntent::update($paymentIntent->id, $updateParams, $stripeAccount ? [
                    'stripe_account' => $stripeAccount
                ] : []);
            }
        }

        // take charge with the selected payment method
        try {
            $paymentIntent = $paymentIntent->confirm();
        }catch (\Exception $e){
            Log::warning('Confirming payment intent failed', [
                'invoice_id' => $this->invoice->id,
                'payment_intent_id' => $paymentIntent->id,
                'payment_intent_status' => $paymentIntent->status,
                'xcloud_team_id' => $this->invoice->team_id,
                'xcloud_payment_method_id' => $this->invoice->payment_method_id,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'message' => 'Payment intent created successfully',
            'payment_intent' => $paymentIntent
        ];
    }

    /**
     * Process payment confirmation and handle different payment statuses
     *
     * @param PaymentIntent $paymentIntent
     * @return array
     */
    private function processPaymentConfirmation(PaymentIntent $paymentIntent): array
    {
        if($paymentIntent->status === PaymentIntent::STATUS_REQUIRES_ACTION) {
            if (!$this->paymentMethod->security_protocol) {
                $this->paymentMethod->markAs3dsCard();
            }

            $this->invoice->setStatusRequiresAction();

            // Handle white label purchase 3D secure
            if($this->invoice->isWhiteLabelPurchase()){
                $whiteLabelID = Arr::get($this->invoice->meta, 'whitelabel_id');
                if($whiteLabelID){
                    $subscriptionProductID = Arr::get($this->invoice->meta, 'subscription_product_id');
                    $stripeSubscriptionID = Arr::get($this->invoice->meta, 'stripe_subscription_id');

                    if($subscriptionProductID && $stripeSubscriptionID){
                        $subscriptionProduct = SubscriptionProduct::findOrFail($subscriptionProductID);
                        if($this->invoice->team->whiteLabel){
                            $stripeSubscription = Subscription::retrieve($stripeSubscriptionID, [
                                'stripe_account' => $this->invoice->team->whiteLabel->connectedAccount->stripe_account_id
                            ]);
                        }else{
                            $stripeSubscription = Subscription::retrieve($stripeSubscriptionID);
                        }

                        return $this->generateResponse(InvoicePaymentTypeAPI::Warning,
                            'Payment requires 3D secure authentication',
                            route('stripe.subscription-product.checkout.requires3dsecure', [
                                'affiliateId' => null,
                                'paymentIntentId' => $paymentIntent->id,
                                'cartId' => null,
                                'invoiceId' => $this->invoice->id,
                                'nextRoute' => 'white-label.onboarding.brand-setup',
                                'routeParam' => [
                                    'model' => WhiteLabel::class,
                                    'id' => $whiteLabelID
                                ],
                                'subscriptionProductId' => $subscriptionProduct->id,
                                'stripeSubscriptionId' => $stripeSubscription->id,
                                'success_redirect' => $this->successRedirectUrl,
                                'failure_redirect' => $this->secure3dFailureRedirectUrl,
                            ])
                        );
                    }
                }
            }

            return $this->generateResponse(InvoicePaymentTypeAPI::Warning,
                'Payment requires 3D secure authentication',
                route('stripe.api.checkout.requires3Dsecure', [
                    'invoice' => $this->invoice->invoice_number,
                    'paymentIntentId' => $paymentIntent->id,
                    'invoiceId' => $this->invoice->id,
                    'success_redirect' => $this->successRedirectUrl,
                    'failure_redirect' => $this->secure3dFailureRedirectUrl,
                ])
            );
        }

        $confirmPaymentIntent = StripePaymentRepository::confirmPayment($this->invoice);

        if($confirmPaymentIntent->status === PaymentIntent::STATUS_SUCCEEDED){
            return $this->generateResponse(InvoicePaymentTypeAPI::Success, 'Payment successfully processed.', $this->successRedirectUrl);
        }

        $this->invoice->setStatusPaymentFailed();

        $errorMessage = $confirmPaymentIntent?->last_payment_error?->message. ' Payment failed, please try again with a different payment method.';
        $redirectUrl = $this->failureRedirectUrl ?? ($this->usingDefaultPaymentMethod ? route('invoice.pay', $this->invoice->invoice_number) : null);

        return $this->generateResponse(InvoicePaymentTypeAPI::Error, $errorMessage, $redirectUrl);
    }

    /**
     * Generate a standardized response
     *
     * @param string $type Response type (success, error, warning)
     * @param string $message Response message
     * @param string|null $redirectUrl Optional redirect URL
     * @return array
     */
    private function generateResponse(InvoicePaymentTypeAPI $type, string $message, string $redirectUrl = null): array
    {
        return [
            'message' => $message,
            'type' => $type,
            'data' => InvoiceResourceResponse::make($this->invoice),
            'status' => $this->invoice->status,
            'redirect_url' => $redirectUrl,
        ];
    }

    /**
     * Store jobs in the invoice for later execution
     *
     * @return void
     */
    private function storeJobsInInvoice(): void
    {
        $updateData = [];

        if ($this->successJob) {
            $successJobId = $this->dispatchJobAndGetId($this->successJob);
            $updateData['on_success_dispatch_job_id'] = $successJobId;
        }

        if ($this->failureJob) {
            $failureJobId = $this->dispatchJobAndGetId($this->failureJob);
            $updateData['on_failure_dispatch_job_id'] = $failureJobId;
        }

        if (!empty($updateData)) {
            $updateData['job_payload'] = [
                'success_job_class' => $this->successJob ? get_class($this->successJob) : null,
                'failure_job_class' => $this->failureJob ? get_class($this->failureJob) : null,
                'stored_at' => now()->toISOString(),
            ];

            $this->invoice->update($updateData);
        }
    }

    /**
     * Dispatch a job and return its ID
     *
     * @param object $job
     * @return string
     */
    private function dispatchJobAndGetId(object $job): string
    {
        // Dispatch the job but don't execute it immediately
        $pendingDispatch = Queue::push($job);

        // Return the job ID (this might vary based on your queue driver)
        return $pendingDispatch ?? uniqid('job_', true);
    }

    /**
     * Dispatch stored jobs based on payment result
     *
     * @param bool $isSuccess
     * @return void
     */
    private function dispatchStoredJobs(bool $isSuccess): void
    {
        $jobType = $isSuccess ? 'success' : 'failure';
        $jobId = $isSuccess ? $this->invoice->on_success_dispatch_job_id : $this->invoice->on_failure_dispatch_job_id;

        if ($jobId) {
            // Dispatch the job execution handler
            ExecuteStoredInvoiceJob::dispatch($this->invoice, $jobType);
        }
    }

    private function beforeProcessingPayment() : void
    {
        if (!$this->secure3dFailureRedirectUrl) {
            $this->redirectOnPayment3dSecuredFailure();
        }
    }
}
