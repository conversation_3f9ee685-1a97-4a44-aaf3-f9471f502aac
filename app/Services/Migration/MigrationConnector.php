<?php

namespace App\Services\Migration;

use App\Models\AutoSiteMigration;
use App\Enums\SiteMigrationStatus;
use App\Models\SiteMigration;
use Illuminate\Encryption\Encrypter;
use Illuminate\Support\Facades\Http;

class MigrationConnector
{
    public SiteMigration $siteMigration;

    public function __construct(SiteMigration $siteMigration)
    {
        $this->siteMigration = $siteMigration;
    }

    function getBaseurl(): string
    {
        return rtrim($this->siteMigration->existing_site_url, '/');
    }

    function getRestEndpoint(): string
    {
        // return $this->getBaseurl().'/wp-json/xcloud-migration/v1';
        return $this->getBaseurl().'/?rest_route=/xcloud-migration/v1';
    }

    function getEncryptionKey()
    {
        return $this->siteMigration->encryption_key;
    }

    function getCallbackUrl()
    {
        return callback_url('/api/callback/migrating/'.hashid_encode($this->siteMigration->id));
    }

    function getAuthToken()
    {
        return $this->siteMigration->auth_token;
    }

    function get($path, $query = [])
    {
        return $this->request('GET', $path, $query);
    }

    function post($path, $query = [])
    {
        return $this->request('POST', $path, $query);
    }

    function request($method, $path, $query = [])
    {
        $endpoint = $this->getRestEndpoint().$path;

        $query = [
            'auth_token' => $this->getAuthToken(),
            'payload' => $query ? base64_encode(json_encode($query)) : null,
            'v' => time(),
        ];

        $endpoint = self::add_query($endpoint, $query);

        if ($method === 'GET') {
            $response = Http::get($endpoint);
        } else {
            $response = Http::post($endpoint);
        }

        // dump($endpoint, $query);

        $json = $response->json();

        if (!$json) {
            throw new \Exception('Invalid response received.');
        }

        if ($response->ok()) {
            return $this->getDecryptedResponse($json['data'] ?? null);
        }

        return $json;
    }

    /**
     * @param $data
     * @return mixed
     */
    private function getDecryptedResponse($data): mixed
    {
        return (new Encrypter($this->getEncryptionKey(), config('app.cipher', 'AES-256-CBC')))->decrypt($data ?? '');
    }

    static function add_query($path, $query = [])
    {
        if ($query) {

            $parts = parse_url($path);
            if (isset($parts['query'])) {
                parse_str($parts['query'], $queryParts);
                $query = array_merge($queryParts, $query);
            }

            $parts['query'] = http_build_query($query);
            $path = self::build_url($parts);
        }

        return $path;
    }

    static function build_url(array $parts)
    {
        return (isset($parts['scheme']) ? "{$parts['scheme']}:" : '').
            ((isset($parts['user']) || isset($parts['host'])) ? '//' : '').
            (isset($parts['user']) ? "{$parts['user']}" : '').
            (isset($parts['pass']) ? ":{$parts['pass']}" : '').
            (isset($parts['user']) ? '@' : '').
            (isset($parts['host']) ? "{$parts['host']}" : '').
            (isset($parts['port']) ? ":{$parts['port']}" : '').
            (isset($parts['path']) ? "{$parts['path']}" : '').
            (isset($parts['query']) ? "?{$parts['query']}" : '').
            (isset($parts['fragment']) ? "#{$parts['fragment']}" : '');
    }

    function sendStatusToPlugin(SiteMigrationStatus $state = SiteMigrationStatus::INIT): void
    {
        $statues = $this->siteMigration->getProgressData();

        foreach ($statues['additionalData'] ?? [] as $key => $value) {
            foreach ($statues['list'] as &$listItem) {
                foreach ($listItem['tasks'] as &$task) {
                    $task = str_replace($key, $value, $task);
                }
            }
        }

        unset($statues['additionalData']);

        try {
            $this->post('/receive_processing_statuses', [
                'state' => $state->value, // $this->siteMigration->status === SiteMigrationStatus::FAILED->value ? SiteMigrationStatus::FAILED->value :
                'migrating_from' => $this->siteMigration->existing_site_url,
                'migrating_to' => $this->siteMigration->domain_name ?: $this->siteMigration->existing_site_url,
                'statuses' => $statues
            ]);
        } catch (\Exception $exception) {
            logger($exception);
        }
    }
}
