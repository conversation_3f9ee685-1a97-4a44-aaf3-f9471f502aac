<?php

namespace App\Services\DataFormat;

class ValidJsonMaker
{

    public function __construct()
    {

    }

    public function make(string $data): string
    {
        $startIndexJsonArray = strpos($data, '[');
        $startIndexJsonObject = strpos($data, '{');

        if ($startIndexJsonArray === false && $startIndexJsonObject === false) {
            return "[]"; // Invalid JSON
        }

        if ($startIndexJsonArray === false) {
            $startIndex = $startIndexJsonObject;
        } elseif ($startIndexJsonObject === false) {
            $startIndex = $startIndexJsonArray;
        } else {
            $startIndex = min($startIndexJsonArray, $startIndexJsonObject);
        }

        $jsonString = substr($data, $startIndex); // Extract the JSON string

        // Validate and format the JSON string
        $jsonData = json_decode($jsonString);
        if ($jsonData === null && json_last_error() !== JSON_ERROR_NONE) {
            return "[]"; // Invalid JSON
        }

        return $jsonString;
    }

}
