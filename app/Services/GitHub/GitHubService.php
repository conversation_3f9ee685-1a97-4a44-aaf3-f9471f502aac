<?php

namespace App\Services\GitHub;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class GitHubService
{
    protected $baseUri = 'https://api.github.com';
    protected $apiVersion = '2022-11-28';
    protected $accessToken;

    public function __construct()
    {
        $this->accessToken = config('services.github.release_access_token');
    }

    public function getLatestRelease($owner = 'xCloudDev', $repo = 'xCloud')
    {
        $cacheKey = "github.{$owner}.{$repo}.latest_release";
        if (!app()->environment('local')) {
            return Cache::remember($cacheKey, now()->addWeek(), function () use ($owner, $repo) {
                $response = Http::withHeaders([
                    'Accept' => 'application/vnd.github+json',
                    'Authorization' => 'Bearer '.$this->accessToken,
                    'X-GitHub-Api-Version' => $this->apiVersion,
                ])->get("{$this->baseUri}/repos/{$owner}/{$repo}/releases/latest", [
                    'per_page' => 1
                ]);

                return $response->json('tag_name');
            });
        }
        return Cache::get($cacheKey);
    }

    function clearCache(): string
    {
        $validator = Validator::make(request()->all(), [
            'token' => 'required|in:'.config('services.github.release_cache_bust_token'),
        ]);

        if ($validator->fails()) {
            return "Invalid token";
        }

        Cache::forget('github.xCloudDev.xCloud.latest_release');

        return "OK";
    }
}
