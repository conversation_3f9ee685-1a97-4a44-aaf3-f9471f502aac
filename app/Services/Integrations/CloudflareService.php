<?php

namespace App\Services\Integrations;

use App\Models\Cloudflare;
use App\Models\Site;
use App\Models\SslCertificate;
use App\Scripts\InstallCustomSslCertificate;
use App\Scripts\Site\GenerateCSRForCloudflare;
use App\Services\Shell\CertificateSigningRequestGenerator;
use Carbon\Carbon;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Laravel\VaporUi\Support\Cloud;

class CloudflareService
{
    const BASE_URL = 'https://api.cloudflare.com';

    // Cache rule constants
    const CACHE_RULE_PHASE = 'http_request_cache_settings';

    public PendingRequest $client;

    public PendingRequest $clientWithoutServiceKey;
    public PendingRequest $clientWithGlobalAPIKey;

    public Cloudflare $cloudflare;

    public function __construct(Cloudflare $cloudflare)
    {
        $this->cloudflare = $cloudflare;

        $this->client = Http::withHeaders([
            'X-Auth-Email' => $cloudflare->email,
            'X-Auth-User-Service-Key' => $cloudflare->global_ca_key,
            'Content-Type' => 'application/json',
        ])
            ->withToken($cloudflare->api_token)
            ->timeout(180)
            ->baseUrl(self::BASE_URL)
            ->acceptJson();

        $this->clientWithoutServiceKey = Http::withToken($this->cloudflare->api_token)
            ->baseUrl('https://api.cloudflare.com')
            ->timeout(180)
            ->acceptJson();

        $this->clientWithGlobalAPIKey = Http::withHeaders([
            'X-Auth-Email' => $this->cloudflare->email,
            'X-Auth-Key' => $this->cloudflare->global_api_key,
            'Content-Type' => 'application/json',
        ])
            ->timeout(180)
            ->baseUrl(self::BASE_URL)
            ->acceptJson();
    }

    public static function verify($email, $apiToken)
    {
        $client = Http::withHeaders([
            'X-Auth-Email' => $email,
            'Content-Type' => 'application/json',
        ])
            ->withToken($apiToken)
            ->baseUrl(self::BASE_URL)
            ->acceptJson();

        return $client->get('/client/v4/user/tokens/verify')->json();
    }

    public function checkDomainExists($domain): array
    {
        $page = 1;
        $per_page = 100;
        $total_pages = 1; // Initialize with 1 to enter the loop
        $responseData = [
            'domain_active_on_cloudflare' => false,
            'success' => false,
            'zone_id' => null,
            'account_id' => null,
            'message' => ''
        ];

        while ($page <= $total_pages) {
            $response = $this->clientWithGlobalAPIKey->get('/client/v4/zones?page='.$page.'&per_page='.$per_page)->json();

            $responseData['success'] = $response['success'] ?? false;

            if (!$responseData['success']) {
                $responseData['message'] = Arr::get($response, 'errors.0.message') ?? 'An error occurred while fetching the zones.';
                break; // Exit the loop if the request was unsuccessful
            }

            // Update pagination
            $total_pages = $response['result_info']['total_pages'] ?? $total_pages;
            $page++; // Move to the next page

            // Extract subdomains from the domain
            $subdomains = explode('.', $domain);
            $num_subdomains = count($subdomains);

            // brute force search to check each subdomain(atleast level 1 i.e domain.tld) exists on cloudflare domain list
            for ($i = $num_subdomains - 2; $i >= 0; $i--) {
                $siteName = implode('.', array_slice($subdomains, $i));
                // check if $subdomain exists on $response->result
                foreach (Arr::get($response, 'result') as $item) {
                    if ($item['name'] === $siteName && $item['status'] === 'active') {
                        $responseData['domain_active_on_cloudflare'] = true;
                        $responseData['zone_id'] = Arr::get($item, 'id');
                        $responseData['account_id'] = Arr::get($item, 'account.id');
                        $responseData['site_name'] = $siteName;
                        // get remaining subdomains
                        $responseData['subdomain'] = implode('.', array_slice($subdomains, 0, $i));

                        return $responseData;
                    }
                }
            }
        }

        return $responseData;
    }

    function updateOrCreateRecord(string $name, string $content, string $zoneId)
    {
        Log::info('updateOrCreateRecord'.' : '.$name.' : '.$content.' : '.$zoneId);

        // check if CNAME record exists with this domain(i.e www.faisalibnaziz.com)
        $record = $this->dnsRecords(zoneId: $zoneId, type: 'CNAME', name: $name);

        // if record exists, delete that cname record
        if ($id = Arr::get($record, 'result.0.id')) {
            Log::info('deleting cname record'.' : '.$id.' : '.$name);
            $this->deleteDns(zoneId: $zoneId, name: $name, type: 'CNAME');
        }

        // check if A record exists with this domain
        $record = $this->dnsRecords(zoneId: $zoneId, type: 'A', name: $name);

        if ($id = Arr::get($record, 'result.0.id')) {
            return $this->updateDnsRecord(zoneId: $zoneId, id: $id, name: $name, content: $content);
        } else {
            return $this->addDnsRecord(zoneId: $zoneId, name: $name, content: $content);
        }
    }

    function dnsRecords($zoneId, $type = 'A', $name = null)
    {
        Log::info('dnsRecords'.' : '.$type.' : '.$name.' : '.$zoneId);

        $query = [
            'type' => $type,
        ];

        if ($name) {
            $query['name'] = $name;
        }

        return $this->clientWithoutServiceKey->get(
            '/client/v4/zones/'.$zoneId.'/dns_records',
            $query
        )->json();
    }

    function addDnsRecord(
        $zoneId,
        $name,
        $content,
        $type = 'A',
        $proxied = true,
        $comment = 'Domain verification record added via API',
    ) {
        Log::info('addDnsRecord'.' : '.$type.' : '.$name.' : '.$content.' : '.$zoneId);

        return $this->clientWithoutServiceKey->post('/client/v4/zones/'.$zoneId.'/dns_records', [
            'type' => $type,
            'name' => $name,
            'content' => $content,
            'proxied' => canGenerateSslCertificateOnCloudflare($name) ? $proxied : false,
            'comment' => $comment,
            'priority' => 10,
            'tags' => [],
            'ttl' => 3600,
        ])->json();
    }

    function updateDnsRecord($zoneId, $id, $name, $content)
    {
        Log::info('updateDnsRecord'.' : '.$id.' : '.$name.' : '.$content);

        return $this->clientWithoutServiceKey->patch('/client/v4/zones/'.$zoneId.'/dns_records/'.$id, [
            'name' => $name,
            'content' => $content,
        ])->json();
    }

    function updateDnsProxyStatus($zoneId, $id, $proxied = true)
    {
        Log::info('updateDnsProxyStatus'.' : '.$id.' : '.$proxied);

        return $this->clientWithoutServiceKey->patch('/client/v4/zones/'.$zoneId.'/dns_records/'.$id, [
            'proxied' => $proxied,
        ])->json();
    }

    /**
     * @throws \Exception
     */
    public function createOriginSSLCertificate(Site $site)
    {
        $certificateResponse = CertificateSigningRequestGenerator::generate($site);

        if (empty($certificateResponse)) {
            throw new \Exception('Error generating CSR');
        }

        $csr = Arr::get($certificateResponse, 'csrFile');

        $additionalDomains = collect($site->additional_domains)->where('status', 'valid')->pluck('value')->toArray();

        $requestBody = [
            'csr' => $csr,
            'hostnames' => array_values(array_unique(array_filter(array_merge(
                [
                    $site->name,
                    $site->isMultiSite() && $site->isMultiSiteSubdomain()
                        ? '*.'.$site->name
                        : null,
                    Arr::get($site->meta, 'cloudflare_integration.subdomain')
                        ? Arr::get($site->meta, 'cloudflare_integration.subdomain').'.'.Arr::get($site->meta, 'cloudflare_integration.site_name')
                        : null,
                ],
                !empty($additionalDomains) ? $additionalDomains : [],
            )))),
            'request_type' => 'origin-rsa',
            'requested_validity' => 5475, // 15 years (5475 / 365 = 15 years)
        ];

        $response = $this->client->post('/client/v4/certificates', $requestBody)->json();
//        $response = $this->clientWithGlobalAPIKey->post('/client/v4/certificates', $requestBody)->json();

        if (Arr::get($response, 'success')) {
            $site->update([
                'meta->cloudflare_integration->origin_certificate->certificate_id' => Arr::get($response, 'result.id'),
                'meta->cloudflare_integration->origin_certificate->expires_on' => Arr::get($response, 'result.expires_on'),
                'meta->cloudflare_integration->origin_certificate->hostnames' => Arr::get($response, 'result.hostnames'),
                'meta->cloudflare_integration->origin_certificate->messages' => Arr::get($response, 'result.messages'),
                'meta->cloudflare_integration->origin_certificate->errors' => Arr::get($response, 'result.errors'),
            ]);
        }

        $response['result']['privateKey'] = Arr::get($certificateResponse, 'keyFile');

        return $response;
    }

    public function getOriginCertificateList($zoneId)
    {
        return $this->client->get('/client/v4/certificates', [
            'zone_id' => $zoneId,
        ])->json();
    }

    public function getOriginCertificate($certificateId)
    {
        return $this->client->get('/client/v4/certificates/'.$certificateId)->json();
    }

    /**
     * @param $zoneId
     * @param  string  $name
     * @param  string  $type
     * @return PromiseInterface|Response|void
     */
    public function deleteDns($zoneId, string $name, $type = 'A')
    {
        if ($zoneId) {
            $record = $this->dnsRecords(zoneId: $zoneId, type: $type, name: $name);

            if ($id = Arr::get($record, 'result.0.id')) {
                Log::info('deleting dns record'.' : '.$id.' : '.$name);
                return $this->clientWithoutServiceKey->delete('/client/v4/zones/'.$zoneId.'/dns_records/'.$id);
            }
        }
    }

    /**
     * @param $certificateId
     * @return PromiseInterface|Response|void
     */
    public function revokeOriginCertificate($certificateId)
    {
        if ($certificateId) {
            return $this->client->delete('/client/v4/certificates/'.$certificateId)->json();
        }
    }

    /**
     * Create a cache rule in Cloudflare
     *
     * @param  string  $zoneId  The Cloudflare zone ID
     * @param  string  $domain  The domain to create the cache rule for
     * @param  array  $exclusions  Array of exclusion rules (http_rules and cookie_rules)
     * @return array Response from Cloudflare API
     */
    public function createCacheRule(string $zoneId, string $domain, array $exclusions = [])
    {
        Log::info('Creating Cloudflare cache rule for domain: '.$domain);

        // First, check if a ruleset exists for the cache settings phase
        $rulesetId = $this->getCacheRulesetId($zoneId);

        // If no ruleset exists, create one
        if (empty($rulesetId)) {
            $rulesetResponse = $this->createCacheRuleset($zoneId);
            if (!Arr::get($rulesetResponse, 'success')) {
                Log::error('Failed to create Cloudflare cache ruleset', [
                    'response' => $rulesetResponse
                ]);
                return $rulesetResponse;
            }
            $rulesetId = Arr::get($rulesetResponse, 'result.id');
        }

        // Check if a rule with this name already exists and delete it
        $ruleName = $this->buildCacheRuleName($domain);
        $existingRuleId = $this->findCacheRuleByDescription($zoneId, $ruleName);

        if ($existingRuleId) {
            Log::info('Found existing cache rule with the same name. Deleting it first.', [
                'rule_id' => $existingRuleId,
                'rule_name' => $ruleName
            ]);

            $deleteResult = $this->deleteCacheRule($zoneId, $existingRuleId);

            if (!Arr::get($deleteResult, 'success')) {
                Log::warning('Failed to delete existing cache rule. Proceeding with creation anyway.', [
                    'rule_id' => $existingRuleId,
                    'response' => $deleteResult
                ]);
            }
        }

        // Build the rule expression
        $expression = $this->buildCacheRuleExpression($domain, $exclusions);

        // Create the rule
        $ruleData = [
            'action' => 'set_cache_settings',
            'action_parameters' => [
                'cache' => true,
                'edge_ttl' => [
                    'mode' => 'respect_origin',
                ],
                'browser_ttl' => [
                    'mode' => 'respect_origin',
                ],
                'serve_stale' => [
                    'disable_stale_while_updating' => false
                ]
            ],
            'expression' => $expression,
            'description' => $this->buildCacheRuleName($domain),
            'enabled' => true
        ];

        $url = "/client/v4/zones/{$zoneId}/rulesets/{$rulesetId}/rules";

        Log::info('Creating Cloudflare cache rule', [
            'url' => $url,
            'rule_data' => $ruleData
        ]);

        return $this->clientWithGlobalAPIKey->post($url, $ruleData)->json();
    }

    /**
     * Get the cache ruleset ID for a zone
     *
     * @param  string  $zoneId  The Cloudflare zone ID
     * @return string|null The ruleset ID or null if not found
     */
    protected function getCacheRulesetId(string $zoneId): ?string
    {
        $url = "/client/v4/zones/{$zoneId}/rulesets/phases/".self::CACHE_RULE_PHASE."/entrypoint";
        $response = $this->clientWithGlobalAPIKey->get($url)->json();

        if (Arr::get($response, 'success') && isset($response['result']['id'])) {
            return $response['result']['id'];
        }

        return null;
    }

    /**
     * Create a cache ruleset for a zone
     *
     * @param  string  $zoneId  The Cloudflare zone ID
     * @return array Response from Cloudflare API
     */
    protected function createCacheRuleset(string $zoneId): array
    {
        $url = "/client/v4/zones/{$zoneId}/rulesets";
        $data = [
            'name' => 'xCloud Edge Cache Settings',
            'description' => 'Cache settings ruleset created by xCloud',
            'kind' => 'zone',
            'phase' => self::CACHE_RULE_PHASE
        ];

        return $this->clientWithGlobalAPIKey->post($url, $data)->json();
    }

    /**
     * Build the cache rule expression
     *
     * @param  string  $domain  The domain to create the rule for
     * @param  array  $exclusions  Array of exclusion rules
     * @return string The rule expression
     */
    protected function buildCacheRuleExpression(string $domain, array $exclusions): string
    {
        // Use a simpler approach with single quotes to avoid escaping issues
        $expression = '(http.host wildcard "'.$domain.'*"';

        // Add cookie exclusions
        $cookieExclusions = [
            'wordpress_logged_in',
            'wordpress_no_cache',
        ];

        // Add custom cookie exclusions
        if (!empty($exclusions['cookie_rules'])) {
            $cookieExclusions = array_merge($cookieExclusions, $exclusions['cookie_rules']);
        }

        $cookieExclusions = array_unique($cookieExclusions);

        // Add cookie exclusions to expression
        foreach ($cookieExclusions as $cookie) {
            $expression .= ' and not http.cookie contains "'.$cookie.'"';
        }

        // Add URI path exclusions
        $uriExclusions = [
            '/wp-admin',
            '/wp-login'
        ];

        // Add custom URI exclusions
        if (!empty($exclusions['http_rules'])) {
            $uriExclusions = array_merge($uriExclusions, $exclusions['http_rules']);
        }

        $uriExclusions = array_unique($uriExclusions);

        // Add URI exclusions to expression
        foreach ($uriExclusions as $uri) {
            $expression .= ' and not http.request.uri contains "'.$uri.'"';
        }

        // Add file extension exclusions
        $expression .= ')';

        // Log the expression for debugging
        Log::info('Generated Cloudflare cache rule expression', [
            'expression' => $expression
        ]);

        return $expression;
    }

    /**
     * Get all rules in a ruleset
     *
     * @param  string  $zoneId  The Cloudflare zone ID
     * @param  string  $rulesetId  The ruleset ID
     * @return array Response from Cloudflare API
     */
    public function getRulesetRules(string $zoneId, string $rulesetId): array
    {
        $url = "/client/v4/zones/{$zoneId}/rulesets/{$rulesetId}";
        $response = $this->clientWithGlobalAPIKey->get($url)->json();

        if (Arr::get($response, 'success') && isset($response['result']['rules'])) {
            return $response['result']['rules'];
        }

        return [];
    }

    /**
     * Find a cache rule by description
     *
     * @param  string  $zoneId  The Cloudflare zone ID
     * @param  string  $description  The rule description to find
     * @return string|null The rule ID if found, null otherwise
     */
    public function findCacheRuleByDescription(string $zoneId, string $description): ?string
    {
        // Get the ruleset ID
        $rulesetId = $this->getCacheRulesetId($zoneId);

        if (empty($rulesetId)) {
            return null;
        }

        // Get all rules in the ruleset
        $rules = $this->getRulesetRules($zoneId, $rulesetId);

        // Find the rule with the matching description
        foreach ($rules as $rule) {
            if (isset($rule['description']) && $rule['description'] === $description) {
                return $rule['id'];
            }
        }

        return null;
    }

    /**
     * Delete a cache rule in Cloudflare
     *
     * @param  string  $zoneId  The Cloudflare zone ID
     * @param  string  $ruleId  The rule ID to delete
     * @return array Response from Cloudflare API
     */
    public function deleteCacheRule(string $zoneId, string $ruleId)
    {
        Log::info('Deleting Cloudflare cache rule', [
            'zone_id' => $zoneId,
            'rule_id' => $ruleId
        ]);

        // First, get the ruleset ID
        $rulesetId = $this->getCacheRulesetId($zoneId);

        if (empty($rulesetId)) {
            Log::error('No cache ruleset found for zone', ['zone_id' => $zoneId]);
            return ['success' => false, 'errors' => [['message' => 'No cache ruleset found']], 'messages' => []];
        }

        // Directly attempt to delete the rule without verification
        // According to Cloudflare API docs, the correct endpoint is DELETE /zones/{zone_id}/rulesets/{ruleset_id}/rules/{rule_id}
        $url = "/client/v4/zones/{$zoneId}/rulesets/{$rulesetId}/rules/{$ruleId}";
        $response = $this->clientWithGlobalAPIKey->delete($url)->json();

        // If the delete operation fails with a specific error code, handle it appropriately
        if (!Arr::get($response, 'success')) {
            // Log the error for debugging
            Log::error('Failed to delete Cloudflare cache rule', [
                'zone_id' => $zoneId,
                'rule_id' => $ruleId,
                'response' => $response
            ]);
        } else {
            Log::info('Successfully deleted Cloudflare cache rule', [
                'zone_id' => $zoneId,
                'rule_id' => $ruleId
            ]);
        }

        return $response;
    }

    public function getSSLSetting($zoneId)
    {
        return $this->clientWithGlobalAPIKey->get('/client/v4/zones/'.$zoneId.'/settings/ssl')->json();
    }

    /**
     * Purge the cache for a specific domain
     *
     * @param  string  $zoneId  The Cloudflare zone ID
     * @return array Response from Cloudflare API
     */
    public function purgeDomainCache(string $zoneId)
    {
        Log::info('Purging Cloudflare cache for zone', ['zone_id' => $zoneId]);

        $url = "/client/v4/zones/{$zoneId}/purge_cache";
        $data = ['purge_everything' => true];

        return $this->clientWithGlobalAPIKey->post($url, $data)->json();
    }

    /**
     * @param $zoneId
     * @param $value
     * @return array|mixed|void
     */
    public function setSslEncryptionMode($zoneId, $value = 'strict')
    {
        if ($zoneId) {
            return $this->clientWithGlobalAPIKey->patch('/client/v4/zones/'.$zoneId.'/settings/ssl', [
                'value' => $value,
            ])->json();
        }
    }

    public function getAllZones()
    {
        return $this->client->get('/client/v4/zones')->json();
    }

    public function testCreateOriginCertificate($zoneId)
    {
        $site = Site::find(59);
        $certificateResponse = CertificateSigningRequestGenerator::generate($site);

        $requestBody = [
            'hostnames' => ['xcloud-test.fwdlifeinsurance.com'],
            'requested_validity' => 5475,
            'request_type' => 'origin-rsa',
            'csr' => Arr::get($certificateResponse, 'csrFile'),
        ];

        $requestBody = [
            'hostnames' => [
                Arr::get($site->meta, 'cloudflare_integration.subdomain')
                    ? Arr::get($site->meta, 'cloudflare_integration.subdomain').'.'.Arr::get($site->meta, 'cloudflare_integration.site_name')
                    : null,
            ],
            'requested_validity' => 5475,
            'request_type' => 'origin-rsa',
            'csr' => Arr::get($certificateResponse, 'csrFile'),
        ];
        $response = $this->client->post("/client/v4/certificates", $requestBody)->json();

        if (Arr::get($response, 'success')) {
            $site->update([
                'meta->cloudflare_integration->origin_certificate->certificate_id' => Arr::get($response, 'result.id'),
                'meta->cloudflare_integration->origin_certificate->expires_on' => Arr::get($response, 'result.expires_on'),
                'meta->cloudflare_integration->origin_certificate->hostnames' => Arr::get($response, 'result.hostnames'),
                'meta->cloudflare_integration->origin_certificate->messages' => Arr::get($response, 'result.messages'),
                'meta->cloudflare_integration->origin_certificate->errors' => Arr::get($response, 'result.errors'),
            ]);
        }

        $response['result']['privateKey'] = Arr::get($certificateResponse, 'keyFile');

        $site->sslCertificate()->updateOrCreate(
            [
                'site_id' => $site->id,
                'provider' => SslCertificate::PROVIDER_CLOUDFLARE
            ], [
                'status' => SslCertificate::STATUS_OBTAINED,
                'obtained_from' => SslCertificate::PROVIDER_CLOUDFLARE,
                'ssl_certificate' => Arr::get($response, 'result.certificate'),
                'ssl_private_key' => Arr::get($response, 'result.privateKey'),
                'expires_at' => Carbon::parse(Arr::get($response, 'result.expires_on'))->toDateTimeString(),
            ]
        );

        $task = $site->run(new InstallCustomSslCertificate($site->sslCertificate));

        if ($task->successful()) {
            $site->sslCertificate->update(['status' => SslCertificate::STATUS_INSTALLED]);
        } else {
            $site->sslCertificate->update(['status' => SslCertificate::STATUS_FAILED]);
        }

        $site->regenerateNginxConf();

        dd($response);
    }

    /**
     * @param  string  $domain
     * @return string
     */
    static function buildCacheRuleName(string $domain): string
    {
        return 'xCloud Edge Cache for '.$domain;
    }
}
