<?php

namespace App\Models;

use App\Enums\SiteCloneStatus;
use App\Enums\SiteStatus;
use App\Enums\SiteType;
use App\Events\SiteCloneStatusChanged;
use App\Events\SiteProvisioningFailed;
use App\Services\Clone\SiteCloning;
use App\Traits\MetaAccessors;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class SiteClone extends Model
{
    use HasFactory, MetaAccessors;

    const DESTINATION = 'destination';
    const DOMAINS = 'domains';

    const DATABASE = 'database';
    const CONFIRM = 'confirm';
    const PROCESSING = 'processing';

    // Types
    const DEFAULT = 'default';
    const MANUAL = 'manual';

    const SETTINGS = 'settings';

    public static array $types = [
        self::DEFAULT,
        self::MANUAL
    ];

    protected $appends = [
        'domain_name_with_http'
    ];

    protected static function booted(): void
    {
        parent::applyScopes(function ($query) {
            return $query->where('type', self::DEFAULT);
        });
    }

    public array $stepsToFill = [];

    public array $multiStepHierarchy = [];


    public static array $canSkipSteps = [];

    public static array $canSkipStepsIf = [];

    public array $unModifiableSteps = [];

    public array $stepGetRouteNames = [];

    public function getStepRouteNames(): array
    {
        return array_merge([
            self::DESTINATION => 'site.clone.destination',
            self::DOMAINS => 'site.clone.auto.domains',
            self::SETTINGS => 'site.clone.settings',
            self::CONFIRM => 'site.clone.confirm'
        ], $this->stepGetRouteNames);
    }


    public array $stepPostRouteNames = [];

    public function getStepPostRouteNames(): array
    {
        return array_merge([
            self::DESTINATION => 'api.site.clone.store.destination',
            self::DOMAINS => 'api.site.clone.store.domains.auto',
            self::SETTINGS => 'api.site.clone.store.settings',
            self::CONFIRM => 'api.site.clone.store.confirm'
        ], $this->stepPostRouteNames);
    }


    public array $stepIcons = [];

    public function getStepIcons(): array
    {
        return array_merge([
            self::DESTINATION => 'xc-destination',
            self::DOMAINS => 'xc-domains',
            self::SETTINGS => 'xc-settings',
            self::DATABASE => 'xc-database',
            self::CONFIRM => 'xc-confirm'
        ], $this->stepIcons);
    }

    protected $guarded = [
        'id'
    ];

    protected $casts = [
        'form' => 'array',
        'meta' => 'array',
        'logs' => 'array',
        'notification_mails' => 'array',
        'status' => SiteCloneStatus::class
    ];

    protected $with = [
        'server'
    ];

    protected $hidden = [
        'form'
    ];

    public function getStep($key = '')
    {
        if (!$key) {
            return $this->form;
        }

        $keys = explode(".", $key);
        $value = $this->form;
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return null;
            }
            $value = $value[$k];
        }
        return $value;
    }

    public function getDestination(string $key = ''): ?string
    {
        return $this->getStep(SiteClone::DESTINATION.'.'.$key);
    }

    public function getDomains(string $key = ''): ?string
    {
        return $this->getStep(SiteClone::DOMAINS.'.'.$key);
    }

    public function getDatabase(string $key = ''): ?string
    {
        return $this->getStep(SiteClone::DATABASE.'.'.$key);
    }
    public function getConfirm(string $key = ''): ?string
    {
        return $this->getStep(SiteClone::CONFIRM.'.'.$key);
    }

    public function server(): BelongsTo
    {
        return $this->belongsTo(Server::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     *
     */
    public function getFormSteps(): array
    {
        $steps = [];
        foreach ($this->stepsToFill as $key => $step) {

            if (!isset($this->getStepRouteNames()[$step]) ||
                !isset($this->getStepPostRouteNames()[$step]) ||
                !isset($this->getStepIcons()[$step])) {
                continue;
            }

            $routes = [];
            if ($this->server) {
                $routes = [
                    'get_route' => route($this->getStepRouteNames()[$step], [
                        'siteClone' => $this->id,
                        'server' => $this->server->id,
                        'site' => $this->site->id
                    ]),
                    'post_route' => route($this->getStepPostRouteNames()[$step], [
                        'siteClone' => $this->id,
                        'server' => $this->server->id,
                        'site' => $this->site->id
                    ])
                ];

            } elseif (!$this->server && $step == self::DESTINATION) {
                $routes = [
                    'get_route' => route($this->getStepRouteNames()[$step], [
                        'server' => request('server'),
                        'siteClone' => request('siteClone') ?? 'new',
                        'site' => request('site')
                    ]),
                    'post_route' => route($this->getStepPostRouteNames()[$step])
                ];
            }
            $steps[$step] = array_merge([
                'step_no' => $key + 1,
                'name' => title($step),
                'icon' => $this->getStepIcons()[$step],
                'completed_steps' => $key,
                'remaining_steps' => count($this->stepsToFill) - $key,
                'total_steps' => count($this->stepsToFill),
            ], $routes);


        }
        return $steps;
    }


    public function setStatusFilling(): bool
    {
        return $this->updateQuietly([
            'status' => SiteCloneStatus::FILLING
        ]);
    }

    public function setStatusInit(): bool
    {
        return $this->updateQuietly([
            'status' => SiteCloneStatus::INIT
        ]);
    }

    public function setStatusCloning(): void
    {
        $this->update(['status' => SiteCloneStatus::CLONING]);
        $this->site->update(['status' => SiteStatus::CLONING]);
    }

    function isMigrating(): bool
    {
        return $this->status === SiteCloneStatus::CLONING;
    }

    public function setStatusMigrating(): bool
    {
        return $this->updateQuietly([
            'status' => SiteCloneStatus::CLONING
        ]);
    }


    function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    function getProgressDataValueByKey($key): int
    {
        return (int ) $this->getMeta('cloneProgressData->'.$key);
    }

    private function getProgressPercentageForSection(int $doneCount, int $totalCount): float|int|string|null
    {
        $percentage = $totalCount > 0 ? round(($doneCount / $totalCount) * 100) : 0;

        if ($percentage) {
            $percentage = number_format($doneCount).'/'.number_format($totalCount).' ('.$percentage.'%)';
        } elseif ($totalCount > 0) {
            $percentage = number_format($doneCount).'/'.number_format($totalCount);
        }

        return $percentage ?: '';
    }

    public function cloneProgress(int $status, $progressData = []): void
    {
        $this->update([
            'status' => SiteCloneStatus::CLONING,
            'meta->cloningStatus' => $status,
            'meta->cloningStatusPercentage' => number_format(($status / SiteCloning::PROGRESS_MAX) * 100),
            'meta->cloningErrorMessage' => '',
            'meta->cloneProgressData' => $progressData + $this->getMeta('cloneProgressData', []),
        ]);

        $this->site->update([
            'status' => SiteCloneStatus::CLONING,
            'meta->cloningStatus' => $status,
            'meta->cloningStatusPercentage' => number_format(($status / SiteCloning::PROGRESS_MAX) * 100),
            'meta->cloningErrorMessage' => '',
            'meta->cloneProgressData' => $progressData + $this->site->getMeta('cloneProgressData', []),
        ]);

        SiteCloneStatusChanged::dispatch($this);
    }

    public function markAsCloneFailure($messages = null): void
    {
        $this->update([
            'status' => SiteCloneStatus::FAILED,
            'meta->cloningErrorMessage' => $messages
        ]);

        $this->site->update([
            'status' => SiteStatus::CLONE_FAILED,
            'meta->cloningErrorMessage' => $messages
        ]);

        SiteCloneStatusChanged::dispatch($this);
        SiteProvisioningFailed::dispatch($this->site);
    }

    function getFormNextStepRoute()
    {
        $nextStep = $this->form['next_step'] ?? self::DOMAINS;

        return (new AutoSiteClone)->stepGetRouteNames[$nextStep];
    }

    function startCloning(): void
    {
        $this->setStatusCloning();

        \App\Jobs\SiteClone::dispatch(AutoSiteClone::findOrFail($this->id));
    }

    public function getSettings(string $key = ''): ?string
    {
        return $this->getStep(self::SETTINGS.'.'.$key);
    }

    function updateSite(Site $cloneSite = null): Site
    {
        $site = Site::updateOrCreate([
            'id' => $this->site_id,
        ], [
            'server_id' => $this->server_id,
            'name' => $this->domain_name,
            'title' => $this->getDomains('site_title') ?: $this->domain_name,
            'type' => SiteType::WORDPRESS,
            'database_user' => $cloneSite->database_provider,
            'database_name' => $cloneSite->database_name,
            'database_password' => $cloneSite->database_password,
            'database_host' => $cloneSite->database_host,
            'database_port' => $cloneSite->database_port,
            'managed_database_mode' => $cloneSite->managed_database_mode,
            'managed_database_options' => $cloneSite->managed_database_options,
            'site_user' => $cloneSite->site_user,
            'prefix' => $cloneSite->site_user,
            'php_version' => $cloneSite->php_version,

            'wordpress_version' => $cloneSite->wordpress_version,
            'status' => SiteStatus::CLONE_INIT,
            'admin_user' => $cloneSite->admin_user,
            'admin_password' => $cloneSite->admin_password,
            'wp_updates' => $cloneSite->wp_updates,
            'redis_password' => $this->server->isRedisSeven() ? Str::random(32) : null,
            'ssl_provider' =>  $this->isDemo()
                ? SslCertificate::PROVIDER_STAGING
                : $this->getDomains('ssl_provider'),
            'environment' => $this->isDemo() ? Site::DEMO : Site::PRODUCTION
        ]);

        if ($site->ssl_provider == SslCertificate::PROVIDER_CUSTOM) {
            $site->sslCertificates()->updateOrCreate([
                'site_id' => $site->id,
                'provider' => SslCertificate::PROVIDER_CUSTOM,
            ], [
                'status' => 'new',
                'ssl_certificate' => $this->getDomains('ssl_certificate'),
                'ssl_private_key' => $this->getDomains('ssl_private_key'),
            ]);
        }

        return $site;
    }

    function getAdditionalProgressData(): array
    {
        return [
            ':php_version' => $this->site->php_version,
        ];
    }

    function isFinished(): bool
    {
        return $this->status === SiteCloneStatus::FINISHED;
    }
    function setDomainNameAttribute($value): void
    {
        $this->attributes['domain_name'] = get_domain($value);
    }

    function getDomainNameWithHttpAttribute()
    {
        return insert_http($this->domain_name);
    }

    public function isDemo(): bool
    {
        return Arr::get($this, 'form.domains.domain_parking_method') === 'staging_env';
    }

    /**
     * @return HasOne
     */
    function sshKeypair(): BelongsTo
    {
        return $this->belongsTo(SshKeyPair::class);
    }

    public function getPrivateKeyFilePath($user = 'root'): string
    {
        return '/root/.ssh/' . $this->sshKeypair->getSlug();
    }

    public function sourceSite(): BelongsTo
    {
        return $this->belongsTo(Site::class, 'source_site_id');
    }

    public function destinationSite(): BelongsTo
    {
        return $this->belongsTo(Site::class, 'site_id');
    }
}
