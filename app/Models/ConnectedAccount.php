<?php

namespace App\Models;

use App\Traits\BelongsToWhiteLabel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use ParagonIE\CipherSweet\EncryptedRow;
use <PERSON><PERSON>\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;

class ConnectedAccount extends Model implements CipherSweetEncrypted
{
    use HasFactory, BelongsToWhiteLabel, UsesCipherSweet;

    protected $guarded = ['id'];

    protected $casts = [
        'billing_details' => 'array',
        'account_activated_at' => 'datetime',
    ];

//    public function whiteLabel(): BelongsTo
//    {
//        return $this->belongsTo(WhiteLabel::class);
//    }

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('publishable_key');
        $encryptedRow->addField('stripe_access_token');
    }
}
