<?php

namespace App\Models;

use App\Enums\CronJobFrequency;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
class CronJob extends Model
{

    protected $guarded = [];

    const PROCESSING = 'processing';
    const ACTIVE = 'active';
    const INACTIVE = 'inactive';

    protected $appends = ['frequency_label'];

    public function getFrequencyLabelAttribute(): string
    {
        return $this->frequency->getFrequencyLabel();
    }

    public function getNextRunAtAttribute(): string
    {
        return $this->nextRunAt();
    }

    protected $casts = [
        'frequency' => CronJobFrequency::class,
    ];

    function server(): BelongsTo
    {
        return $this->belongsTo(Server::class);
    }

    function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    public function getCronPattern(): string
    {
        $pattern = $this->pattern ?? $this->frequency->getPatterByFrequency($this->pattern);
        if ($this->pattern !==$pattern && $this->frequency !== CronJobFrequency::CUSTOM) {
           $this->update(['pattern' => $pattern]);
        }
        return $pattern;
    }

    public function installCronJob(): bool
    {
       return $this->server->installCronJob(
           cronJob: $this,
        );
    }
    public function removeCronJob(): bool
    {
       return $this->server->removeCronJob(
           cronJob: $this,
        );
    }

    public function getOutput()
    {
        return $this->server->getCronJobOutput(
            cronJob: $this,
        );
    }

    public function isRootUser(): bool
    {
        return $this->user === 'root';
    }
    public function getOutputFile(): string
    {
        return $this->getOutputDirectory().DIRECTORY_SEPARATOR."server-cron-{$this->id}.out";
    }

    public function getOutputDirectory(): string
    {
        if ($this->isRootUser()){
            return "/root/.xcloud";
        }
        return "/home/<USER>/.xcloud";
    }

    public static function dailyRandomPattern(): string
    {
        return rand(1, 59).' '.rand(1, 23).' * * *';
    }
}
