<?php

namespace App\Models;

use App\Enums\ProductProvider;
use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\ProductSources;
use App\Enums\XcloudBilling\XcloudProductType;
use App\Traits\BelongsToWhiteLabel;
use App\Traits\MetaAccessors;
use App\Traits\XcSoftDelete;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property mixed $is_active
 * @method static onlyxCloud()
 * @method static allowedForWhiteLabel()
 * @method static onlyForWhiteLabelClient()
 */
class Product extends Model
{
    use HasFactory, XcSoftDelete, BelongsToWhiteLabel, MetaAccessors;

    protected $table = 'products';

    protected $guarded = [
        'id'
    ];

    protected $with = [
        'source:id,title,slug,price,source_product_id,source_model,white_label_id,is_active,source_model,service_type,renewal_type'
    ];

    protected $casts = [
        'price' => 'float',
        'options' => 'array',
        'resource' => 'array',
        'form' => 'array',
        'user_session' => 'array',
        'should_expire_on' => 'date',
        'provider' => ProductProvider::class,
        'type' => XcloudProductType::class,
        'service_type' => BillingServices::class,
        'renewal_type' => BillRenewalPeriod::class,
        'expected_product_source' => ProductSources::class,
        'currency' => BillingCurrency::class,
        'is_active' => 'boolean',
        'required_purchase_any_of_packages' => 'array',
        'required_purchase_any_of_products' => 'array',
        'is_deleted' => 'boolean',
        'meta' => 'array',
    ];

    public function scopeWithoutDependency($query)
    {
        return $query->whereNull('depends_on_product_id');
    }

    public function scopeOnlyDependency($query)
    {
        return $query->whereNotNull('depends_on_product_id');
    }

    public function dependency() : BelongsTo
    {
        return $this->belongsTo(Product::class, 'depends_on_product_id');
    }

    public function dependents() : HasMany
    {
        return $this->hasMany(Product::class, 'depends_on_product_id');
    }

    public function source(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'source_product_id');
    }

    public function derivedProducts(): HasMany
    {
        return $this->hasMany(Product::class, 'source_product_id');
    }

    public function cartForms() : HasMany
    {
        return $this->hasMany(CartForm::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeAllowedForWhiteLabel($query)
    {
        return $query->whereIn('service_type', [
            BillingServices::ManagedHosting,
            BillingServices::BackupManagedHosting
        ])->whereNull('white_label_id');
    }

    public function scopeSearch($query, string $slug, BillRenewalPeriod $renewal_type, BillingServices $service_type, $requires_billing_plan = null)
    {
        return $query->where('slug', $slug)
            ->where('renewal_type', $renewal_type)
            ->where('service_type', $service_type)
            ->where('requires_billing_plan', $requires_billing_plan);
    }

    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_product', 'product_id')
            ->using(TeamProduct::class)->withPivot('id', 'attached_at');
    }

    public function bills(): HasMany
    {
        return $this->hasMany(Bill::class);
    }

    public function hasBillingPlan() : bool
    {
        return $this->requires_billing_plan !== null;
    }

    public function requiredBillingPlan(): BelongsTo
    {
        return $this->belongsTo(BillingPlan::class, 'requires_billing_plan', 'id');
    }

    public function scopeS3StorageProvider($query)
    {
        return  $query->where([
            'service_type' => BillingServices::S3Storage,
        ]);
    }
}
