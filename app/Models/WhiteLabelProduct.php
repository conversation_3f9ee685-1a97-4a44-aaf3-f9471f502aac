<?php

namespace App\Models;

use App\Enums\ProductProvider;
use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\ProductSources;
use App\Enums\XcloudBilling\XcloudProductType;
use App\Traits\BelongsToWhiteLabel;
use App\Traits\MetaAccessors;
use App\Traits\XcSoftDelete;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property mixed $is_active
 * @method static onlyxCloud()
 * @method static allowedForWhiteLabel()
 * @method static onlyForWhiteLabelClient()
 */
class WhiteLabelProduct extends Product
{
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('white_label', function ($builder) {
            $builder->whereNotNull('source_product_id');
        });
    }
}
