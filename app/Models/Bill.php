<?php

namespace App\Models;

use App\Enums\PromoterStatusEnum;
use App\Enums\XcloudBilling\BillingCurrency;
use App\Enums\XcloudBilling\BillingServices;
use App\Enums\XcloudBilling\BillingStatus;
use App\Enums\XcloudBilling\BillRenewalPeriod;
use App\Enums\XcloudBilling\BillType;
use App\Interfaces\BillInterface;
use App\Repository\FirstPromoterRepository;
use App\Services\PaymentGateway\BillingServices\CanGenerateInvoice;
use App\Services\XcloudProduct\CalculateBill;
use App\Traits\MetaAccessors;
use App\Traits\XcSoftDelete;
use Arr;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\ApiErrorException;

/**
 * @property int|mixed|string|null $generated_by
 * @property mixed $team_id
 * @property mixed $type
 * @property mixed $next_billing_date
 * @property mixed|null $renewal_period
 * @property \Illuminate\Support\Carbon|mixed $due_on
 * @property mixed $bill_from
 * @property mixed $status
 * @property \Illuminate\Support\Carbon|mixed $paid_on
 * @property mixed $billing_amount
 */
class Bill extends Model implements BillInterface, CanGenerateInvoice
{
    use HasFactory, XcSoftDelete, MetaAccessors;

    protected $guarded = ['id'];

    protected $casts = [
        'unique_hash' => 'string',
        'amount' => 'float',
        'billing_amount' => 'float',
        'amount_to_pay' => 'float',
        'adjustable_amount' => 'float',
        'actual_application_fee' => 'float',
        'adjustable_application_fee' => 'float',
        'paid_on' => 'datetime',
        'next_billing_date' => 'date',
        'bill_from' => 'date',
        'due_on' => 'date',
        'service_deactivated_from' => 'datetime',
        'meta' => 'array',
        'renewal_period' => BillRenewalPeriod::class,
        'service' => BillingServices::class,
        'type' => BillType::class,
        'status' => BillingStatus::class,
        'currency' => BillingCurrency::class,
        'has_offer' => 'boolean',
        'adjust_with_previous' => 'boolean',
        'bill_forwards_to' => 'integer',
        'generated_by' => 'integer',
        'team_id' => 'integer',
        'invoice_id' => 'integer',
    ];

    protected $with = [
        'billingPlan',
        // 'generator',
        'product:id,title,slug,price,source_product_id,source_model,white_label_id,is_active,source_model,service_type,renewal_type',
    ];

    protected $appends = [
        'transparent_cost',
        'is_lifetime',
        'comment',
        'adjusted_amount_comment',
        'next_billing_amount_comment',
        'next_billing',
        'short_description',
        'get_usage_hours',
        'application_fee_to_pay'
    ];

    public function getApplicationFeeToPayAttribute() : float
    {
        return min($this->actual_application_fee + $this->adjustable_application_fee, 0);
    }

    public function getGetUsageHoursAttribute()
    {
        if ($this?->generator && ($this?->generator?->billingStartedFrom()->month === $this?->created_at?->month)) {
            return $this?->generator?->billingStartedFrom()?->diffInHours(min($this?->next_billing_date, ($this->service_deactivated_from ?: now())));
        }

        return $this->created_at?->diffInHours(min($this?->next_billing_date, ($this->service_deactivated_from ?: now())));
    }

    public function getShortDescriptionAttribute()
    {
        return $this->getGeneratorInfo()['generator_info']['name'] ?? '';
    }

    public function getTransparentCostAttribute(): float
    {
        if ($this->refundable_amount) {
            return $this->billing_amount - $this->refundable_amount;
        }

        return $this->billing_amount + $this->adjustable_amount;
    }

    public function billingPlan(): BelongsTo
    {
        return $this->belongsTo(BillingPlan::class, 'plan_id');
    }

    public function hashKey(): string
    {
        return hash('sha256', implode('', [
            $this->billing_amount,
            $this->plan_id,
//            $this->service_is_active,
            $this->team_id,
            $this->next_billing_date,
            $this->renewal_period?->value,
            $this->service?->value,
            $this->type?->value,
            $this->generator_id,
            $this->generator_type,
            $this->adjust_with_previous
        ]));
    }
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function relatedBills() : HasMany
    {
        return $this->hasMany(Bill::class, 'generator_id', 'generator_id')
            ->where('service', '!=', $this->service)
            ->where('generator_type', $this->generator_type);
    }

    public function previousBills() : HasMany
    {
        return $this->hasMany(Bill::class, 'generator_id', 'generator_id')
            ->where('generator_type', $this->generator_type)
            ->where('service', $this->service);
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(GeneralInvoice::class);
    }

    public function manualInvoice(): BelongsTo
    {
        return $this->belongsTo(ManualInvoice::class, 'invoice_id');
    }

    public function updateBillingTitle(): void
    {
        $this->updateQuietly([
            'title' => (new $this->generator_type)->getBillingTitle($this->service, $this)
//            'title' => $this->service->toReadableSentence() . ' - ' . $this->billingPlan->name_as_title,
        ]);
    }

    public function regenerateBillingUniqueId(): void
    {
        $this->updateQuietly([
            'unique_hash' => $this->hashKey()
        ]);
    }

    public function cancel(): bool
    {
        return $this->update([
            'status' => BillingStatus::Cancelled->value,
            'next_billing_date' => null,
        ]);
    }

    public function forwardedTo(): BelongsTo
    {
        return $this->belongsTo(self::class, 'bill_forwards_to');
    }

    public function generatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function getIsLifetimeAttribute(): bool
    {
        return $this->renewal_period === BillRenewalPeriod::Lifetime;
    }

    public function getNextBillingAttribute(): string|null
    {
        return $this->renewal_period === BillRenewalPeriod::Monthly
            ? $this?->next_billing_date?->firstOfMonth()?->addMonth()?->format('d M Y')
            : $this?->next_billing_date?->format('d M Y');
    }

    public function getCommentAttribute(): string
    {
        $calculationData = optional($this?->meta['calculation_data'] ?? 0);
        $currencySymbol = $this->currency ? $this->currency->symbol() : '$';

        $comment = '';

        if ($this->adjust_with_previous) {
            $comment .= 'This bill is adjusted with previous bill. '.$this?->forwardedTo?->title.'. ';
        }

        if ($this->amount_to_pay) {
            $comment .= 'You paid ' . $currencySymbol . $this->amount_to_pay . ' for this bill. ';
        }

        if ($this->adjustable_amount && $this->adjustable_amount > 0) {
            $comment .= 'This bill has adjustable amount. ';
        }

        if ($calculationData->currentAdditionalCostToSpendInTotal) {
            $comment .= 'This bill has additional cost to spend. Because you\'ve purchased after the billing date.
                         28 th of each month is the billing date. ';

            $comment .= 'You\'ve purchased ' . $calculationData->currentAdditionalCostToSpendInTotal . ' additional cost to spend. ';
            $comment .= 'You\'ll be paying ' . $calculationData->currentAdditionalCostToSpendInTotal . ' additional cost to spend. ';
        }

        if ($this->additional_usage_charge) {
            if (!empty($this->additional_usage_comment)) {
                // Check if {amount} placeholder exists in the comment
                if (strpos($this->additional_usage_comment, '{amount}') !== false) {
                    // Replace {amount} placeholder with the actual amount
                    $formattedComment = str_replace('{amount}', $currencySymbol . $this->additional_usage_charge, $this->additional_usage_comment);
                    $comment .= $formattedComment;
                } else {
                    // If {amount} placeholder doesn't exist, use the comment as is and append the amount
                    $comment .= $this->additional_usage_comment . ' ' . $currencySymbol . $this->additional_usage_charge . '.';
                }
            } else {
                // Use default message if no custom comment is provided
                $comment .= 'This bill includes an additional usage charge of ' . $currencySymbol . $this->additional_usage_charge . '.';
            }
        }

        return $comment;
    }

    public function getServicePricingCommentAttribute(): string
    {
        return $this->billingPlan->description ?: '';
    }

    public function getAdjustedAmountCommentAttribute(): string
    {
        $calculationData = optional($this?->meta['calculation_data'] ?? 0);
        $currencySymbol = $this->currency ? $this->currency->symbol() : '$';

        $comment = '';
        if ($this->adjustable_amount < 0) {
            if ($calculationData['totalHoursPassedInCurrentMonth'] && $calculationData['costPerHour']) {
                $comment .= 'Pay unused ' . $currencySymbol . abs($this->adjustable_amount) .' ('.$calculationData['totalHoursPassedInCurrentMonth'].'h * ' . $currencySymbol . round($calculationData['costPerHour'], 6).') less on next month. ';
            } else {
                $comment .= 'Pay unused ' . $currencySymbol . abs($this->adjustable_amount) .' less on next month. ';

                if ($this->cost_per_hour) {
                    $comment .= 'Your cost per hour is ' . $currencySymbol . round($this->cost_per_hour, 6).'. ';
                }
            }
        } elseif ($this->adjustable_amount > 0 && $this->service_is_active) {
            if ($calculationData['totalHoursLeftInCurrentMonth'] && $calculationData['costPerHour']) {
                $comment .= 'Pay overused ' . $currencySymbol . abs($this->adjustable_amount) .' ('.$calculationData['totalHoursLeftInCurrentMonth'].'h * ' . $currencySymbol . round($calculationData['costPerHour'], 6).') more on next billing '.$this->next_billing_date->addMonth()->firstOfMonth()->format('m/d/Y').'. ';
            } else {
                $comment .= 'Pay overused ' . $currencySymbol . abs($this->adjustable_amount) .' more on next billing '.$this->next_billing_date->addMonth()->firstOfMonth()->format('m/d/Y').'. ';

                if ($this->cost_per_hour) {
                    $comment .= 'Your cost per hour is ' . $currencySymbol . round($this->cost_per_hour, 6).'. ';
                }
            }
        }

        if($calculationData->currentAdditionalCostToSpendInTotal) {
            $comment .= 'You\'ve purchased after billing date, you won\'t be billed any amount this month. ';
            $comment .= 'You\'ll be charged ' . $currencySymbol . $calculationData->currentAdditionalCostToSpendInTotal.' on next billing. ';
        }

        if ($this->billing_amount > $this->amount_to_pay) {
//            $comment .= $this->renewal_period->toReadableString().' amount is ' . $currencySymbol . $this->billing_amount;
//            $comment .= ', you\'ve paid ' . $currencySymbol . format_billing($this->billing_amount - $this->amount_to_pay) .' in advance.';

            $comment .= 'You\'ve paid ' . $currencySymbol . format_billing($this->billing_amount - $this->amount_to_pay) .' in advance.';
        }

        if ($this->billing_amount < $this->amount_to_pay) {
//            $comment .= $this->renewal_period->toReadableString().' amount is ' . $currencySymbol . $this->billing_amount;
//            $comment .= ', you were not charged ' . $currencySymbol . format_billing($this->amount_to_pay - $this->billing_amount) .' on previous billing.';
            $comment .= 'You were not charged ' . $currencySymbol . format_billing($this->amount_to_pay - $this->billing_amount) .' on previous billing.';
        }

        if ($comment && $this->next_billing_date) {
            if ($this->renewal_period->is(BillRenewalPeriod::Monthly)) {
                $billFor = $this->bill_from->format('F').' ('. $this->bill_from->isoFormat('Do') .' to '. $this->next_billing_date->isoFormat('Do'). '). ';
            } else {
                $billFor = ' ('. $this->bill_from->format('d M Y') .' to '. $this->next_billing_date->format('d M Y'). '). ';
            }

            $comment = 'Bill for ' . $billFor . $comment;
        }

        if ($this->billing_amount > $this->amount_to_pay) {
            $comment .= $this->renewal_period->toReadableString().' amount is ' . $currencySymbol . $this->billing_amount;
            $comment .= ', you\'ve paid ' . $currencySymbol . format_billing($this->billing_amount - $this->amount_to_pay) .' in advance.';
        }

        if ($this->billing_amount < $this->amount_to_pay) {
            $comment .= $this->renewal_period->toReadableString().' amount is ' . $currencySymbol . $this->billing_amount;
            $comment .= ', you were not charged ' . $currencySymbol . format_billing($this->amount_to_pay - $this->billing_amount) .' on previous billing.';
        }

        if ($this->additional_usage_charge) {
            $comment .= 'This bill includes an additional usage charge of ' . $currencySymbol . $this->additional_usage_charge . '.';
        }

        return $comment;
    }

    public function getNextBillingAmountCommentAttribute(): string
    {
        $currencySymbol = $this->currency ? $this->currency->symbol() : '$';
        $comment = '';

        $comment = 'Next billing Amount is ' . $currencySymbol . ($this->billing_amount + $this->adjustable_amount) . '. ';

//        if ($this->adjustable_amount > 0) {
//            $comment .= 'You\'ve purchased after billing date. You have to pay additional hours for first month.';
//        } else {
//            $comment .= 'You paid ' . $currencySymbol . $this->adjustable_amount . ' advance for next month. ';
//        }

        return $comment;
    }

    public function getBillGeneratorAttribute() : array | null
    {
        return $this?->meta['generator'] ?? $this->generator?->toArray();
    }

    public function scopeUnpaid(Builder $query): Builder
    {
        return $query->where('status', BillingStatus::Unpaid);
    }

    public function scopeUnpaidWithoutOffer(Builder $query): Builder
    {
        return $query->where('status', BillingStatus::Unpaid)->where('has_offer', false);
    }

    public function scopeWithoutInvoice(Builder $query): Builder
    {
        return $query->whereNull('invoice_id');
    }

    public function scopeUnpaidWithOffer(Builder $query): Builder
    {
        return $query->where('status', BillingStatus::Unpaid)->where('has_offer', true);
    }

    public function scopeHasOffer(Builder $query): Builder
    {
        return $query->where('has_offer', true);
    }

    public function scopeWithOffer(Builder $query): Builder
    {
        return $query->where('has_offer', true);
    }

    public function scopeWithoutOffer(Builder $query): Builder
    {
        return $query->where('has_offer', false);
    }

    public function scopeWithoutRefundOrCancellation(Builder $query): Builder
    {
        return $query->whereNotIn('status', [
            BillingStatus::Refunded,
            BillingStatus::Cancelled,
        ]);
    }

    public function scopePrepaid($query): mixed
    {
        return $query->where('type', BillType::Prepaid->value);
    }

    public function scopePostpaid($query): mixed
    {
        return $query->where('type', BillType::Postpaid->value);
    }

    public function billForwardedTo(): BelongsTo
    {
        return $this->belongsTo(self::class, 'bill_forwards_to');
    }

    public function scopeWithInRunningMonth(Builder $query): Builder
    {
        return $query->where('bill_from', Carbon::parse(now())->startOfMonth());
    }

    public function scopeOnNextMonth(Builder $query): Builder
    {
        return $query->where('bill_from', Carbon::parse(now())->addMonth()->startOfMonth());
    }

    public function scopeCreatedInThisMonth(Builder $query): Builder
    {
        return $query->where('created_at', '>=', Carbon::parse(now())->startOfMonth());
    }

    public function scopeMonthly(Builder $query): Builder
    {
        return $query->where('renewal_period', BillRenewalPeriod::Monthly);
    }

    public function scopeYearly(Builder $query): Builder
    {
        return $query->where('renewal_period', BillRenewalPeriod::Yearly);
    }

    public function scopeLifetime(Builder $query): Builder
    {
        return $query->where('renewal_period', BillRenewalPeriod::Lifetime);
    }

    public function scopeWithInNextBilling($query): mixed
    {
        return $query->whereDate('next_billing_date', $this->getNextBillingDate(now(), BillRenewalPeriod::Monthly));
    }

    public function scopeWithInRunningPeriod($query) : Builder
    {
        ### TODO: Refactor (CC Mishuk)
        if(!$this->next_billing_date) return $query;
        return $query->where('bill_from', Carbon::parse($this->next_billing_date)->startOfMonth());
    }
//
//    public function scopeBeforeRunningPeriod($query) : Builder
//    {
//        ### TODO: Refactor (CC Mishuk)
//        if(!$this->next_billing_date) return $query;
//        return $query->where('bill_from', Carbon::parse($this->next_billing_date)->startOfMonth());
//    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('service_is_active', true);
    }

    public function scopeFailed(Builder $query): Builder
    {
        return $query->where('status', BillingStatus::Failed->value);
    }

    public function scopePaymentFailed(Builder $query): Builder
    {
        return $query->where('status', BillingStatus::PaymentFailed->value);
    }

    public function isFailed(): bool
    {
        return $this->status === BillingStatus::Failed->value;
    }

    public function scopePaid(Builder $query): Builder
    {
        return $query->where('status', BillingStatus::Paid);
    }

    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', BillingStatus::Pending->value);
    }

    public function scopeCancelled(Builder $query): Builder
    {
        return $query->where('status', BillingStatus::Cancelled->value);
    }

    public function scopeBillingDateIsToday(Builder $query): Builder
    {
        return $query->where('next_billing_date', Carbon::today()->toDateString());
    }

    public function scopeBillFilter($query, $filters)
    {
        $filterByMonth = $filters['filter_by_month'] ?? Carbon::now()->format('F');
        $filterByYear = $filters['filter_by_year'] ?? Carbon::now()->format('Y');

        if (isset($filters['billing_plan']) && count($filters['billing_plan']) > 0) {
            $query->whereHas('billingPlan', function ($q) use ($filters) {
                $q->whereIn('name', $filters['billing_plan']);
            });
        }

        if (isset($filters['bill_status']) && count($filters['bill_status']) > 0) {
            $query->whereIn('status', $filters['bill_status']);
        }

        if (isset($filters['bill_type']) && count($filters['bill_type']) > 0) {
            $query->whereIn('service', $filters['bill_type']);
        }

        if (isset($filters['offer_filter']) && $filters['offer_filter'] !== 'Bill Has Offer Filter') {
            if ($filters['offer_filter'] === 'Has Offer') {
                $query->where('has_offer', true);
            } else {
                $query->where('has_offer', false);
            }
        }

        if (isset($filters['period_filter']) && $filters['period_filter'] !== 'Bill Period Filter') {
            $query->where('renewal_period', strtolower($filters['period_filter']));
        }

        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $startDate = Carbon::parse($filters['start_date'])->format('Y-m-d');
            $endDate = Carbon::parse($filters['end_date'])->format('Y-m-d');
            $query->where('created_at', '>=', $startDate)->where('created_at', '<=', $endDate);
        }

        if (isset($filters['package_filter']) && $filters['package_filter'] !== 'No Package') {
            $query->whereHas('package', function ($q) use ($filters) {
                $q->where('name', $filters['package_filter']);
            });
        }

        if (!isset($filters['package_filter']) || $filters['package_filter'] === 'No Package') {
            $query->whereNull('package_id');
        }

        if (isset($filters['product_filter']) && $filters['product_filter'] !== 'Product Filter') {
            $query->whereHas('product', function ($q) use ($filters) {
                $q->where('title', $filters['product_filter']);
            });
        }

        if (isset($filters['team_filter']) && count($filters['team_filter']) > 0) {
            $query->whereIn('team_id', $filters['team_filter']);
        }

        $query->when($filterByMonth, function ($query) use ($filterByMonth) {
            $month = intval(ltrim(date('m', strtotime($filterByMonth)), '0'));
            return $query->whereRaw('MONTH(bill_from) = ?', [$month]);
        });

        $query->when($filterByYear, function ($query) use ($filterByYear) {
            return $query->whereRaw('YEAR(bill_from) = ?', [$filterByYear]);
        });

        return $query;
    }

    public function scopeBillSearchFilter($query, $search)
    {
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', '%' . $search . '%')
                    ->orWhere('description', 'like', '%' . $search . '%')
                    ->orWhere('billing_amount', 'like', '%' . $search . '%')
                    ->orWhere('amount_to_pay', 'like', '%' . $search . '%')
                    ->orWhere('adjustable_amount', 'like', '%' . $search . '%')
                    ->orWhere('refundable_amount', 'like', '%' . $search . '%')
                    ->orWhere('type', 'like', '%' . $search . '%')
                    ->orWhere('service', 'like', '%' . $search . '%')
                    ->orWhere('renewal_period', 'like', '%' . $search . '%')
                    ->orWhere('status', 'like', '%' . $search . '%')
                    ->orWhere('bill_from', 'like', '%' . $search . '%')
                    ->orWhere('next_billing_date', 'like', '%' . $search . '%');
            });
        }

        return $query;
    }

    public function isGeneratedFromProduct(): bool
    {
        return $this->product_id !== null;
    }

    public function isGeneratedFromPackage(): bool
    {
        return $this->package_id !== null;
    }

    public function whiteLabelProduct() : BelongsTo
    {
        return $this->belongsTo(WhiteLabelProduct::class, 'product_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }

    public function generatedByTeam(): BelongsTo
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    public function getNextBillingDate(Carbon $from = null, BillRenewalPeriod $renewalPeriod = null) : string
    {
        $from = $from ?: $this->bill_from ?: now();

        $renewalPeriod = $renewalPeriod ?: $this->renewal_period;

        if ($renewalPeriod === BillRenewalPeriod::Monthly) {
            $totalHoursLeftInPeriodFromStartOfTheMonth =
                round(Carbon::parse($from)->diffInSeconds(Carbon::parse($from)->startOfMonth()) / 3600) >= CalculateBill::totalHoursInMonthlyPeriod ? 0 :
                    round(Carbon::parse(
                            Carbon::parse($from)
                                ->startOfMonth()
                                ->day(CalculateBill::billingDate)->endOfDay())
                            ->diffInSeconds() / 3600);

            return $totalHoursLeftInPeriodFromStartOfTheMonth ? Carbon::parse($from)->day(CalculateBill::billingDate)->endOfDay()->toDateString() :
                Carbon::parse($from)->day(CalculateBill::billingDate)->endOfDay()->addMonth()->toDateString();

        } elseif($renewalPeriod === BillRenewalPeriod::Yearly) {
            return Carbon::parse($from)->addYear()->toDateString();
        } else {
            return '';
        }
    }

    public function getBillingDate(Carbon $from = null, BillRenewalPeriod $renewalPeriod = null) : string
    {
        $from = $from ?: now();

        $renewalPeriod = $renewalPeriod ?: $this->renewal_period;

        if ($renewalPeriod === BillRenewalPeriod::Monthly) {
            $totalHoursLeftInPeriodFromStartOfTheMonth =
                round(Carbon::parse($from)->diffInSeconds(Carbon::parse($from)->startOfMonth()) / 3600) >= CalculateBill::totalHoursInMonthlyPeriod ? 0 :
                    round(Carbon::parse(
                            Carbon::parse($from)
                                ->startOfMonth()
                                ->day(CalculateBill::billingDate)->endOfDay())
                            ->diffInSeconds() / 3600);

            return $totalHoursLeftInPeriodFromStartOfTheMonth ? Carbon::parse($from)->day(CalculateBill::billingDate)->endOfDay()->toDateString() :
                Carbon::parse($from)->day(CalculateBill::billingDate)->endOfDay()->addMonth()->toDateString();

        } elseif($renewalPeriod === BillRenewalPeriod::Yearly) {
            return Carbon::parse($from)->addYear()->toDateString();
        } else {
            return '';
        }
    }

    public function generator(): MorphTo
    {
        return $this->morphTo();
    }

    public function getAmount(): float
    {
        return $this->amount_to_pay;
    }

    public function isPaid(): bool
    {
        return $this->status === BillingStatus::Paid->value;
    }

    public function isRefundable(): bool
    {
        return $this->status === BillingStatus::Refundable->value;
    }

    /**
     * @return GeneralInvoice
     */
    public function generateInvoice() : GeneralInvoice
    {
        $invoice = $this->invoice()->create([
            'reference_no' => uniqid(),
            'title' => uniqid("Invoice_"),
            'description' => 'Invoice for bill#' . $this->id . ' (' . $this->description . ')',
            'amount' => $this->getAmount(),
            'currency' => $this->currency,
            'status' => BillingStatus::Unpaid->value,
            'team_id' => $this->team->id,
            'type' => $this->type,
            'payment_method_id' => $this->team->activePaymentMethod()->first()->id,
            'gateway_payment_method_id' => \Arr::get($this->team->activePaymentMethod()->first()->meta, 'stripe.payment_method'),
            'gateway_customer_id' => $this->team->activePaymentMethod()->first()->customer_id,
            'customer_email' => $this->team?->activePaymentMethod()?->first()?->getMeta('customer.email') ?: $this->team->owner->email
        ]);

        $this->invoice()->associate($invoice);
        $this->save();

        return $invoice;
    }

    /**
     * @throws ApiErrorException
     */
    public function makePrepaidPayment(): void
    {
        $this->invoice()->first()->requestPayment();
    }
    public function setStatusPaid(): bool
    {
        return $this->update([
            'status' => BillingStatus::Paid,
        ]);
    }

    public function setStatusUnpaid(): void
    {
        $this->update([
            'status' => BillingStatus::Unpaid->value,
        ]);
    }

    public function setStatusFailed(): void
    {
        $this->update([
            'status' => BillingStatus::Failed->value,
        ]);
    }
    public function setStatusPaymentFailed(): void
    {
        $this->update([
            'status' => BillingStatus::PaymentFailed->value,
        ]);
    }

    public function setStatusPending(): void
    {
        $this->update([
            'status' => BillingStatus::Pending->value,
        ]);
    }

    public function shouldBeGenerateForNext(): bool
    {
        return !empty($this->next_billing_date);
    }

    public function makeRefundable(): bool
    {
        return $this->update([
            'status' => BillingStatus::Refundable,
            'refund_requested_on' => now(),
        ]);
    }

    public function refunded(): bool
    {
        return $this->update([
            'status' => BillingStatus::Refunded,
            'refunded_on' => now(),
        ]);
    }

    public function generatedForNext(): bool
    {
        return Bill::where('next_billing_date', $this->getNextBillingDate(now()))
                    ->where('service', $this->service)
                    ->where('type', $this->type)
                    ->where('team_id', $this->team_id)
//                    ->where('plan_id', $this->plan_id)
                    ->where('renewal_period', $this->renewal_period)
                    ->where('generated_by', $this->generated_by)
                    ->where('generator_id', $this->generator_id)
                    ->exists();
    }

    public function reGenerateForNext(): Bill | null
    {
        if (!$this->generator) {
            Log::error("Bill generator not found. Bill ID: {$this->id}");
            return null;
        }

        if (!$this->next_billing_date) {
            Log::error("Bill next billing date not found. Bill ID: {$this->id}");
            return null;
        }

        if ($this->isGeneratedFromPackage()) {
            $billingAmount = $this->generator?->getPricing(
                $this->service,
                $this->renewal_period,
                $this->package,
                $this->billing_amount
            ) ?: $this->billing_amount;
        } else if ($this->isGeneratedFromProduct()) {
            $billingAmount = $this->generator?->getPricing(
                $this->service,
                $this->renewal_period,
                $this->product,
                $this->billing_amount
            ) ?: $this->billing_amount;
        } else {
            $billingAmount = $this->billing_amount;
        }

        $amountToPay = $billingAmount + $this->adjustable_amount;
        $adjustableAmount = min($amountToPay, 0);

        if ($amountToPay < 0) {
            $amountToPay = 0;
            $adjustableAmount = $amountToPay;
        }

        // Replicate the current bill instance
        $newBill = $this->replicate();

        $newBill->exploit_unique_hash = false;
        // Set the properties of the new bill instance
        $newBill->title = $this->generator->getBillingTitle($this->service, $this);
        $newBill->billing_amount = $billingAmount;
        $newBill->amount_to_pay = $amountToPay;
        if ($this->generator && $this->generator?->getAdditionalUsageChargeForCurrentPeriod($this)) {
            $newBill->additional_usage_charge = $this->generator?->getAdditionalUsageChargeForCurrentPeriod($this) ?: 0;
            $newBill->additional_usage_comment = $this->generator?->getAdditionalUsageDetailsAsComment() ?: [];
            $newBill->meta->additional_usage_log = $this->generator?->getAdditionalUsageLog() ?: [];
        }
        $newBill->adjustable_amount = $adjustableAmount;
        $newBill->refundable_amount = 0;
        $newBill->refund_requested_on = null;
        $newBill->paid_on = null;
        $newBill->refunded_on = null;
        $newBill->bill_from = BillRenewalPeriod::getNextBillFrom($this->renewal_period, $this->bill_from);
        $newBill->status = BillingStatus::Unpaid;
        $newBill->bill_forwards_to = null;
        $newBill->invoice_id = null;
        $newBill->plan_id = $this->team->activePlan->id; // $this->generator->teamInfo()->activePlan->id;
        $newBill->paid_on = null;
        $newBill->call_trace = _backtrace();
        $newBill->created_at = now();
        $newBill->updated_at = now();

        // Calculate the next billing date if renewal period is defined
        if ($this->renewal_period && $this->next_billing_date) {
            $newBill->next_billing_date = BillRenewalPeriod::getNextBillTo($this->renewal_period, $this->next_billing_date);
            $newBill->due_on = $newBill->next_billing_date->addDays(CalculateBill::dueDateToWaitAfterBillingDate);
        } else {
            $newBill->next_billing_date = null;
            $newBill->due_on = null;
        }

        if ($this?->product?->source) {
            $afterAdjustWithActual = $this->product->source->price + $this->adjustable_application_fee;

            // Ensure actual_application_fee is never negative
            $newBill->actual_application_fee = $this->product->source->price;

            // Set application_fee_to_charge based on the sign of $afterAdjustWithActual
            $newBill->application_fee_to_charge = max($afterAdjustWithActual, 0);

            // Set adjustable_application_fee based on the sign of $afterAdjustWithActual
            $newBill->adjustable_application_fee = min($afterAdjustWithActual, 0);
        }

        if (!$this?->product?->source && $this->actual_application_fee) {
            $afterAdjustWithActual = $this->actual_application_fee + $this->adjustable_application_fee;

            // Ensure actual_application_fee is never negative
            $newBill->application_fee_to_charge = max($afterAdjustWithActual, 0);

            // Set adjustable_application_fee based on the sign of $afterAdjustWithActual
            $newBill->adjustable_application_fee = min($afterAdjustWithActual, 0);
        }

        $bill = Bill::where('unique_hash', $newBill->hashKey());

        if ($bill->exists()) {
            Log::info("Bill already exists. Bill ID: {$bill->first()->id}");
            return null;
        }

        $newBill->save();


        if ($newBill->additional_usage_charge) {
            $this->generator->resetLastAdditionalUsageChargeAndComment($newBill);
        }

        $this->update([
            'bill_forwards_to' => $newBill->id,
        ]);

        return $newBill;
    }

    public function getGeneratorInfo(): array
    {
        if ($this->generator) {
            match ($this->generator_type) {
                Server::class => $generatorInfo['generator_info'] = [
                    'name' => $this->generator?->name . ' - (' . ($this->generator?->public_ip ?? 'xx.xx.xx.xx') . ')',
                    'url' => route('server.show', $this->generator_id),
                    'type' => 'Server',
                    'name_without_server_ip' => $this->bill_generator['name'] . ' - ' . $this->bill_generator['id']
                ],
                Site::class => $generatorInfo['generator_info'] = [
                    'name' => $this->generator?->name,
                    'url' => route('site.show', $this->generator_id),
                    'type' => 'Site'
                ],
                EmailProvider::class => $generatorInfo['generator_info'] = [
                    'name' => $this->bill_generator['label'] ?? $this->bill_generator['provider'] ?? '',
                    'url' => route('team.email_provider'),
                    'type' => 'Email Provider'
                ],
                default => $generatorInfo['generator_info'] = [
                    'name' => 'Unknown',
                    'url' => null
                ],
            };
        } else {
            $generatorInfo['generator_info'] = [
                'name' => $this->service?->toReadableSentence() . ' - ' . $this?->billingPlan?->name_as_title,
                'url' => null
            ];
        }

        return $generatorInfo;
    }

    public function handleAffiliateRefund()
    {

    }

    public function adjustPreviousOffer() : Bill
    {
        $amount = $this->generator->getPricing($this->service, $this->renewal_period) ?: $this->billing_amount;

        $calculatedBill = CalculateBill::amount($amount, now(), $this->renewal_period)
                                        ->advanceAmount($amount)
                                        ->payFullBillNowAndAdjustAdvanceAmountOnNextBilling();

        if (auth()->check()) {
            Log::info("Adjusting previous bill by ". auth()->user()->email . " for bill#{$this->id}");
        }

        $this->updateQuietly([
            'bill_from' => Carbon::parse($calculatedBill->getNextBillingDate())->firstOfMonth(),
            'title' => $this->generator->getBillingTitle($this->service, $this),
            'has_offer' => false,
            'amount_to_pay' => $calculatedBill->getAmountToPay(),
            'adjustable_amount' => $calculatedBill->getAdjustableAmount(),
            'plan_id' => $this->team->activePlan->id,
            'next_billing_date' => $calculatedBill->getNextBillingDate(),
            'meta->calculation_data' => $calculatedBill,
        ]);

        return $this;
    }

    public function getGetNameForInvoiceAttribute(): string
    {
        if ($this->generator) {
            return $this->generator->getBillingName($this->service);
        }

        if ($this->getMeta('generator_details')) {
            return $this->getMeta('generator_details')['name'] ?: '';
        }

        return '';
    }

    public function getGetTitleForInvoiceAttribute(): string
    {
        $currencySymbol = $this->currency ? $this->currency->symbol() : '$';

        if ($this->product) {
//            return $this->product->sku ? $this->product->title.' ('.$this->product->sku.')' : $this->product->title;
            return $this->product->title . ' (' . $currencySymbol . ($this->product->price) . '/' . $this->renewal_period->twoDigitShortForm() . ')';
        }

        if ($this->package) {
            return $this->package->name . ' (' . $currencySymbol . $this->package->price . '/' . $this->renewal_period->twoDigitShortForm() . ')';
        }

        if ($this->billingPlan && $this->service === BillingServices::SelfManagedHosting) {
            $pricing = $this->billingPlan->getPricing($this->service, $this->renewal_period);

            return $this->billingPlan->name_as_title . ($pricing ? ' (' . $currencySymbol . $pricing . '/' . $this->renewal_period->twoDigitShortForm() . ')' : '');
        }

        if ($this->getMeta('generator_details')) {
            return $this->getMeta('generator_details')['title'] ?: '';
        }

        return '(' . $currencySymbol . $this->billing_amount . '/' . $this->renewal_period->twoDigitShortForm() . ')';
    }

    public function getGetShortDescriptionTitleForInvoiceAttribute(): string
    {
        if ($this->generator) {
            return $this->generator->getBillingShortDescriptionTitle($this->service);
        }

        if ($this->getMeta('generator_details')) {
            return $this->getMeta('generator_details')['short_description_title'] ?: '';
        }

        return '';
    }

    public function getGetShortDescriptionForInvoiceAttribute(): string
    {
        if ($this->generator) {
            return $this->generator->getBillingShortDescription($this->service);
        }

        if ($this->getMeta('generator_details')) {
            return $this->getMeta('generator_details')['short_description'] ?: '';
        }

        return '';
    }
}
