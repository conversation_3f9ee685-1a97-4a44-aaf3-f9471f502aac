<?php

namespace App\Models;

use App\Scripts\Site\GetEventsOutput;
use App\Services\Shell\SshConnector;
use App\Traits\BatchPrunable;
use App\Traits\InteractsWithSsh;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\MassPrunable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;

class Task extends Model
{
    use HasFactory, InteractsWithSsh, BatchPrunable;

    /**
     * The default timeout for tasks.
     *
     * @var int
     */
    const DEFAULT_TIMEOUT = 3600;

    const STATUS_QUEUED = 'queued';
    const STATUS_RUNNING = 'running';
    const STATUS_FINISHED = 'finished';

    const STATUS_TIMEOUT = 'timeout';
    const STATUS_FAILED = 'failed';
    const STATUS_KILLED = 'killed';

    const TASK_POSTFIX = '-script-xcloud.sh';

    protected $guarded = ['id'];

    protected $dates = ['started_at', 'finished_at'];

    /**
     * Get the value of the options array.
     *
     * @param  string  $value
     * @return array
     */
    public function getOptionsAttribute($value)
    {
        return unserialize($value);
    }

    /**
     * Set the value of the options array.
     *
     * @param  array  $value
     * @return array
     */
    public function setOptionsAttribute(array $value)
    {
        $this->attributes['options'] = serialize($value);
    }

    function server(): BelongsTo
    {
        return $this->belongsTo(Server::class);
    }

    function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    function initiatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'initiated_by');
    }

    function parent()
    {
        return $this->morphTo();
    }

    function getRunTimeAttribute()
    {
        if (!$this->started_at) {
            return;
        }

        return (($this->finished_at ? $this->finished_at->getTimestamp() : now()->getTimestamp()) - $this->started_at->getTimestamp()).'s';
    }

    public function getMeta(string $key)
    {
        return Arr::get($this->meta, $key);
    }

    /**
     * Determine if the task was successful.
     *
     * @return bool
     */
    public function successful()
    {
        return (int) $this->exit_code === 0;
    }

    /**
     * Get the maximum execution time for the task.
     *
     * @return int
     */
    public function timeout()
    {
        return (int) ($this->options['timeout'] ?? Task::DEFAULT_TIMEOUT);
    }

    /**
     * Mark the task as finished and gather its output.
     *
     * @param  int  $exitCode
     * @return void
     * @throws Exception
     */
    public function finish(int $exitCode = 0)
    {
        $this->markAsFinished($exitCode);

        $this->doOnSuccessful();

        if (!($this->options['skipOutput'] ?? false)) {
            $this->update([
                'output' => $this->retrieveOutput(),
            ]);
        }

        if ($this->status === self::STATUS_KILLED) {
            //break here
            return;
        }

        foreach ($this->options['then'] ?? [] as $callback) {
            is_object($callback)
                ? $callback->handle($this)
                : app($callback)->handle($this);
        }
    }

    public function doOnSuccessful()
    {
        if (!$this->successful()) {
            return $this;
        }

        if ($this->server) {
            $this->server->checkedNow();
        }

        return $this;
    }

    /**
     * Mark the task as running.
     *
     * @return $this
     */
    protected function markAsRunning(bool $isBackground = false)
    {
        return tap($this)->update([
            'status' => self::STATUS_RUNNING,
            'is_background' => $isBackground,
        ]);
    }

    /**
     * Determine if the task is running.
     *
     * @return bool
     */
    public function isRunning()
    {
        return $this->status === self::STATUS_RUNNING;
    }

    /**
     * Mark the task as timed out.
     *
     * @param  string  $output
     * @return $this
     */
    protected function markAsTimedOut($output = '')
    {
        return tap($this)->update([
            'exit_code' => 1,
            'status' => self::STATUS_TIMEOUT,
            'output' => $output,
        ]);
    }

    /**
     * Mark the task as timed out.
     *
     * @param  string  $output
     * @return $this
     */
    protected function markAsFailed($statusCode = 1, $output = '')
    {
        return tap($this)->update([
            'exit_code' => $statusCode,
            'status' => self::STATUS_FAILED,
            'output' => $output,
        ]);
    }

    /**
     * Mark the task as finished.
     *
     * @param  int  $exitCode
     * @param  string  $output
     * @return $this
     */
    protected function markAsFinished($exitCode = 0, $output = '')
    {
        return tap($this)->update([
            'exit_code' => $exitCode,
            'status' => self::STATUS_FINISHED,
            'output' => $output,
        ]);
    }

    /**
     * Get the prunable model query.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function prunable()
    {
        return static::where('created_at', '<=', now()->subMonth());
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    function getOutputJsonAttribute()
    {
        $json = json_decode($this->output, true);

        // sometimes the output is not valid json, so we try to find the json in the output
        if (!$json) {
            $start_pos = strpos($this->output, '{');
            $end_pos = strrpos($this->output, '}');

            if ($start_pos !== false && $end_pos !== false) {
                $outputJson = substr($this->output, $start_pos, $end_pos - $start_pos + 1);
                $json = json_decode($outputJson, true);
            }
        }

        return $json;
    }

    public function kill()
    {
        $this->update([
            'status' => self::STATUS_KILLED,
            'output' => 'Killed by user',
        ]);

        if ($this->is_background) {
            //if background task, kill the process
            $this->runInlineScript('kill -f '.$this->getTaskPath());
        }

    }

    public function getOutput(): string
    {
        return SshConnector::runScriptOnServer(
            server: $this->server,
            script: (new GetEventsOutput(task: $this))->script()
        )->output;
    }

    public function getOutputFile(): string
    {
        return $this->getOutputDirectory().DIRECTORY_SEPARATOR."{$this->id}.out";
    }

    public function isRootUser(): bool
    {
        return $this->user === 'root';
    }

    public function getOutputDirectory(): string
    {
        if ($this->isRootUser()) {
            return "/root/.xcloud";
        }
        return "/home/<USER>/.xcloud";
    }
}
