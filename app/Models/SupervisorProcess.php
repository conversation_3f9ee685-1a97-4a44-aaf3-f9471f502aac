<?php

namespace App\Models;

use App\Jobs\SupervisorProcessSyncJob;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SupervisorProcess extends Model
{
    protected $guarded = [];

    public const NEW = 'new';
    public const INSTALLING = 'installing';
    public const RESTARTING = 'restarting';
    public const INACTIVE = 'inactive';

    // Supervisor-specific statuses
    public const RUNNING = 'running';
    public const STOPPED = 'stopped';
    public const FATAL = 'fatal';
    public const BACKOFF = 'backoff';
    public const STARTING = 'starting';
    public const STOPPING = 'stopping';
    public const EXITED = 'exited';
    public const UNKNOWN = 'unknown';

    public function server(): BelongsTo
    {
        return $this->belongsTo(Server::class);
    }

    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    public function isRootUser(): bool
    {
        return $this->user === 'root';
    }

    public function getConfigFile(): string
    {
        return "/etc/supervisor/conf.d/xcloud-supervisor-{$this->id}.conf";
    }

    public function getLogDirectory(): string
    {
        if ($this->isRootUser()) {
            return "/root/.xcloud";
        }
        return "/home/<USER>/.xcloud";
    }

    public function getLogFile(): string
    {
        return $this->getLogDirectory()."/supervisor-{$this->id}.log";
    }

    public function installSupervisorProcess(): bool
    {
        return $this->runScript(\App\Scripts\Supervisor\InstallSupervisorProcess::class);
    }

    public function syncInQueue($delayInSeconds = 5): void
    {
        SupervisorProcessSyncJob::dispatch($this)->delay(now()->addSeconds($delayInSeconds));
    }

    /**
     * Run a script on the appropriate model (site or server)
     *
     * @param string $scriptClass The fully qualified class name of the script to run
     * @param bool $returnOutput Whether to return the output or successful status
     * @return mixed The result of running the script (output string or success boolean)
     */
    private function runScript(string $scriptClass, bool $returnOutput = false): mixed
    {
        $script = new $scriptClass($this->server, $this);
        $result = $this->site_id
            ? $this->site->runInline($script)
            : $this->server->runInline($script);

        return $returnOutput ? $result->output : $result->successful();
    }

    public function removeSupervisorProcess(): bool
    {
        return $this->runScript(\App\Scripts\Supervisor\RemoveSupervisorProcess::class);
    }

    public function getOutput(): string
    {
        return $this->runScript(\App\Scripts\Supervisor\GetSupervisorProcessOutput::class, true);
    }

    public function restartSupervisorProcess(): bool
    {
        return $this->runScript(\App\Scripts\Supervisor\RestartSupervisorProcess::class);
    }

    public function pauseSupervisorProcess(): bool
    {
        return $this->runScript(\App\Scripts\Supervisor\PauseSupervisorProcess::class);
    }

    public function checkStatus(): string
    {
        return $this->runScript(\App\Scripts\Supervisor\CheckSupervisorProcessStatus::class, true);
    }

    /**
     * Check the status of all supervisor processes on a server
     *
     * @param Server $server The server to check
     * @return string The output of the status check
     */
    public static function checkAllStatus(Server $server): string
    {
        return $server->runInline(new \App\Scripts\Supervisor\CheckAllSupervisorProcessStatus($server))->output;
    }

    /**
     * Map supervisor status string to application status constant
     *
     * @param string $supervisorStatus
     * @return string
     */
    public static function mapSupervisorStatusToAppStatus(string $supervisorStatus): string
    {
        $supervisorStatus = strtolower($supervisorStatus);

        return match ($supervisorStatus) {
            'running' => self::RUNNING,
            'fatal' => self::FATAL,
            'backoff' => self::BACKOFF,
            'starting' => self::STARTING,
            'stopping' => self::STOPPING,
            'exited' => self::EXITED,
            'stopped' => self::STOPPED,
            default => self::UNKNOWN,
        };
    }

    public function syncStatus(): void
    {
        $statusLine = $this->checkStatus();
        $statusInfo = $this->parseStatusLine($statusLine);

        // Update the log field with the full status line for tooltip display
        $this->update(['log' => $statusLine]);

        // Map supervisor status to our application status
        if (!empty($statusInfo['status'])) {
            $this->update(['status' => self::mapSupervisorStatusToAppStatus($statusInfo['status'])]);
        } else {
            // If we can't parse the status, set it to inactive
            $this->update(['status' => self::INACTIVE]);
        }
    }

    /**
     * Parse the supervisor status line to extract status and other information
     *
     * @param  string  $statusLine
     * @return array
     */
    public static function parseStatusLine(string $statusLine): array
    {
        $result = [
            'name' => '',
            'status' => '',
            'details' => ''
        ];

        // Handle different status line formats:
        // "xcloud-supervisor-2:xcloud-supervisor-2_00   RUNNING   pid 3350069, uptime 0:00:23"
        // "xcloud-supervisor-3:xcloud-supervisor-3_00   FATAL     Exited too quickly (process log may have details)"
        // "xcloud-supervisor-6:xcloud-supervisor-6_00   STARTING"

        $statusLine = trim($statusLine);

        // First, try to match the standard format with details
        if (preg_match('/^(\S+)\s+(\S+)\s+(.+)$/', $statusLine, $matches)) {
            $result['name'] = $matches[1] ?? '';
            $result['status'] = $matches[2] ?? '';
            $result['details'] = $matches[3] ?? '';
        } // If that fails, try to match format without details (e.g., STARTING)
        elseif (preg_match('/^(\S+)\s+(\S+)\s*$/', $statusLine, $matches)) {
            $result['name'] = $matches[1] ?? '';
            $result['status'] = $matches[2] ?? '';
            $result['details'] = '';
        }

        return $result;
    }

    /**
     * Parse multiple supervisor status lines and extract process IDs and statuses
     *
     * @param  string  $output
     * @return array
     */
    public static function parseMultipleStatusLines(string $output): array
    {
        $results = [];
        $lines = explode("\n", trim($output));

        foreach ($lines as $line) {
            if (empty(trim($line))) {
                continue;
            }

            $statusInfo = self::parseStatusLine($line);

            // Extract the process ID from the name (e.g., "xcloud-supervisor-2:xcloud-supervisor-2_00")
            if (preg_match('/xcloud-supervisor-(\d+)/', $statusInfo['name'], $matches)) {
                $processId = (int) $matches[1];
                $results[$processId] = $statusInfo;
            }
        }

        return $results;
    }
}
