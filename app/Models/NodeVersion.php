<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\In;

class NodeVersion extends Model
{
    use HasFactory;

    protected $dates = [
        'node_version_updated_at'
    ];

    /**
     * Available versions of Node.js in the system
     *
     * @var array
     */
    const VERSIONS = [
        '14',
        '16',
        '18',
        '20',
        '21',
        '22',
    ];

    const DEFAULT = '20';

    /**
     * Get all versions
     *
     * @return array
     */
    static function getVersions(): array
    {
        return self::VERSIONS;
    }

    /**
     * Get the list of available versions of Node.js inside Rule::in
     *
     * @param  bool  $acceptNull
     * @return In
     */
    static function asRule(bool $acceptNull = false): In
    {
        if ($acceptNull) {
            return Rule::in(array_merge(self::getVersions(), [null]));
        }

        return Rule::in(self::getVersions());
    }

    function server()
    {
        return $this->belongsTo(Server::class);
    }
}
