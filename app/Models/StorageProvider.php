<?php

namespace App\Models;

use App\Traits\InteractsWithGoogleDrive;
use App\Traits\InteractWithS3Files;
use Aws\S3\S3Client;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use League\Flysystem\FileAttributes;
use League\Flysystem\FilesystemException;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;

class StorageProvider extends Model implements CipherSweetEncrypted
{
    use HasFactory, InteractWithS3Files, InteractsWithGoogleDrive, UsesCipherSweet;

    const DIGITAL_OCEAN = 'digital_ocean';
    const VULTR = 'vultr';
    const CLOUDFLARE_R2 = 'cloudflare_r2';
    const GOOGLE_DRIVE = 'google_drive';
    const BACKBLAZE_B2 = 'backblaze_b2';
    const GOOGLE_CLOUD_STORAGE = 'google_cloud_storage';
    const HETZNER = 'hetzner';

    const OTHER = 'other';

    const PROVIDERS = [
        self::DIGITAL_OCEAN => 'Digital Ocean Spaces',
        self::VULTR => 'Vultr Object Storage',
        self::CLOUDFLARE_R2 => 'Cloudflare R2',
        self::BACKBLAZE_B2 => 'Backblaze B2',
        self::HETZNER => 'Hetzner',
        self::GOOGLE_DRIVE => 'Google Drive',
        self::GOOGLE_CLOUD_STORAGE => 'Google Cloud Storage',
        self::OTHER => 'Other',
    ];

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('access_key_id');
        $encryptedRow->addField('secret_key');
    }


    public function hasStableConnection(): bool
    {
        if ($this->isGDrive()){
            return $this->checkConnection();
        }
        return StorageProvider::testConnection($this->toArray());
    }
    public static function testConnection($config): bool
    {
        if ($config && is_array($config)) {
            $required_keys = ['access_key_id','secret_key', 'region', 'bucket', 'endpoint'];
            if (count(array_intersect_key(array_flip($required_keys), array_filter($config))) !== count($required_keys)) {
                return false;
            }
            $token = Str::random(10);

            try {
                // put a token json
                self::storage($config)->put('test.json', json_encode(['verification_id' => $token]));
                // retrieve that json and make sure it's intact
                $response = static::storage($config)->get('test.json');
                if (data_get(json_decode($response, true), 'verification_id') == $token) {
                    // delete the token json
                    self::storage($config)->delete('test.json');
                    return true;
                } else {
                    throw new \Exception('Failed to read from this bucket');
                }
            } catch (\Exception $e) {
                return self::testConnectionByClient($config);
            }
        }
        return false;
    }
    public static function testConnectionByClient($config): bool
    {
        try {
            $s3 = new S3Client([
                'region'  => $config['region'],
                'version' => 'latest',
                'credentials' => [
                    'key'    => $config['access_key_id'],
                    'secret' => $config['secret_key'],
                ],
                'endpoint' =>  $config['endpoint'],
            ]);

            // $result = $s3->listBuckets();
            $result = $s3->headBucket([
                'Bucket' => $config['bucket'],
            ]);
            return $result->get('@metadata')['statusCode'] == 200;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function scopeSearch($query, $search)
    {
        return $query->where('bucket', 'like', "%$search%");
    }

    public function getConfig(): array
    {
        $config = $this->toArray();
        if ($this->provider == self::CLOUDFLARE_R2){
            $config['endpoint'] = $this->getEndPoint();
        }
        return $config;
    }

    public function deleteRemoteFiles($files, $dir):bool{
        try {
            $files = collect($files)->map(fn($file) => $this->isGDrive() ? $this->getFileId($file) : "{$dir}/{$file}")->filter()->toArray();
            if ($this->isGDrive()){
               return $this->deleteGoogleDriveFiles($files);
            }
            $files = collect($files)->filter(fn ($file) => StorageProvider::storage($this->getConfig())->exists($file))->toArray();
            if (empty($files)){
                return true;
            }
            return StorageProvider::storage($this->getConfig())->delete($files);
        } catch (\Exception $exception) {
            return false;
        }
    }

    public static function storage($config): Filesystem
    {
        $uniq_id = Str::random(10);
        config([
            'filesystems.disks.user-bucket-' . $uniq_id => [
                'driver' => 's3',
                'key' => $config['access_key_id'],
                'secret' => $config['secret_key'],
                'region' => $config['region'], //make random region
                'bucket' => $config['bucket'],
                'endpoint' => $config['endpoint'],
                'visibility' => 'private',
            ]
        ]);

        return Storage::disk('user-bucket-' . $uniq_id);
    }

    public static function s3Files(BackupSetting $backupSetting,$location=''):array{
        try {
            if ($backupSetting->storageProvider) {
                $files = StorageProvider::getListContents(storageProvider: $backupSetting->storageProvider,location: $location);
            } else {
                throw new \Exception('Storage provider not found');
            }
        } catch (\Exception|FilesystemException $exception) {
            $files = [];
        }
        return $files;
    }

    /**
     * @throws FilesystemException
     */
    public static function getListContents(StorageProvider $storageProvider, string $location = '/'): array
    {
        return StorageProvider::storage($storageProvider->getConfig())
            ->getDriver()
            ->listContents($location)->map(function (FileAttributes $file){
                return $file->jsonSerialize();
            })->toArray();
    }

    public static function s3IncrementalFiles(BackupSetting $backupSetting,string $location='', array $incrementalTimeStamps = null):array{
        try {
            if ($backupSetting->storageProvider) {
                $files = StorageProvider::storage($backupSetting->storageProvider->getConfig())
                    ->getDriver()
                    ->listContents($location)->filter(function (FileAttributes $file) use ($incrementalTimeStamps) {
                        #check file name contains any incremental timestamp
                        return collect($incrementalTimeStamps)->contains(function ($timestamp) use ($file){
                            return Str::contains($file->path(),$timestamp);
                        });
                    })
                    ->map(function (FileAttributes $file){
                        return basename($file->path());
                    } )
                    ->toArray();
            } else {
                throw new \Exception('Storage provider not found');
            }
        } catch (\Exception|FilesystemException $exception) {
            $files = [];
        }
        return $files;
    }

    public static function remoteBackupFiles($backup_settings,$site): array
    {
        $backupFiles =self::s3Files($backup_settings,$site->backupDirName());

        if (count($backupFiles)>0){
            $pattern = "/{$site->name}_s_([\d.]+)_(\d{14})\.(sql|tar\.gz)/";
            #filter files by pattern
            $backupFiles = collect($backupFiles)->filter(fn ($item) => preg_match($pattern, $item['path']))
            ->groupBy(function ($item) use ($pattern) {
                preg_match($pattern, $item['path'], $matches);
                return Carbon::parse($matches[2])->format('Y-m-d h:i:s');
            })->toArray();
        }
        return $backupFiles;
    }

    public static function localBackupFiles($site):array{
        $pattern = "/_local_s_([\d.]+)_(\d{14})\.(sql|tar\.gz)/";

        $files = $site->getMeta('local_file_backups',[]);
        $databases = $site->getMeta('local_database_backups',[]);
        $allFiles = [...$files,...$databases];
        $backupFiles = [];
        foreach ($allFiles as $file){
            if (preg_match($pattern, $file['file_name'], $matches)){
                $backupFiles[] = [
                    'path' => $file['file_name'],
                    'is_sql' => Str::endsWith($file['file_name'],'.sql'),
                    'taken_size' => $file['taken_size'] ?? $file['file_size'] ?? null,
                    'format_bytes' => $file['file_size'] ?? null,
                    'date' => $matches[2] ? Carbon::parse($matches[2])->format('Y-m-d h:i:s') : null,
                ];
            }
        }
        return collect($backupFiles)->groupBy(function ($item) use ($pattern) {
            return $item['date'];
        })->toArray();
    }

    public function backupSettings(): HasMany
    {
        return $this->hasMany(BackupSetting::class);
    }

    public function backupFiles(): HasMany
    {
        return $this->hasMany(BackupFile::class);
    }

    public function getEndPoint(): array|string|null
    {
        if($this->isCloudflareR2() || Str::contains($this->endpoint,'infomaniak.com')){
            #remove bucket name from endpoint if it exists
            return preg_replace('/\/'.$this->bucket.'$/','',$this->endpoint);
        }
        return rtrim($this->endpoint,'/');
    }

    public function getProfile():string
    {
        return $this->bucket.'-'.$this->id;
    }

    public function backblazeB2Endpoint(): string
    {
        //urlencode secret key for backblaze b2, because it contains special characters like `/`
        $secret_key = str_replace('/','%2F',$this->secret_key);
        return "b2://{$this->access_key_id}:{$secret_key}@{$this->bucket}";
    }
    public function cloudflareR2EndPoint(): string
    {
        return str('--s3-endpoint-url=')
            ->append($this->getEndPoint())
            ->append(' boto3+s3://')
            ->append($this->bucket)
            ->toString();
    }

    public function getS3EndPoint(): string
    {
        if($this->isBackBlaze()){
           return $this->backblazeB2Endpoint();
        }
        if ($this->isCloudflareR2()){
           return $this->cloudflareR2EndPoint();
        }
        $endpoint = str_replace('https://','s3://',$this->getEndPoint());
        return str($endpoint)
            ->append(DIRECTORY_SEPARATOR)
            ->append($this->bucket)
            ->toString();
    }

    public function getEndPointWithS3Flag(): string
    {
        if($this->isBackBlaze()){
           return $this->backblazeB2Endpoint();
        }
        if ($this->isCloudflareR2()){
            return $this->cloudflareR2EndPoint();
        }
        return str('--s3-endpoint-url=')
            ->append($this->getEndPoint())
            ->append(' s3://')
            ->append($this->bucket)
            ->toString();
    }

    public function isBackBlaze(): bool
    {
        return Str::contains($this->endpoint,'backblazeb2') || $this->provider == self::BACKBLAZE_B2;
    }
    public function isGDrive(): bool
    {
        return $this->provider == self::GOOGLE_DRIVE;
    }

    public function isCloudflareR2(): bool
    {
        return $this->provider == self::CLOUDFLARE_R2 || Str::contains($this->endpoint,'r2.cloudflarestorage.com');
    }

    public function supportsIncrementalBackup(): bool
    {
        if ($this->provider == self::GOOGLE_DRIVE){
            return false;
        }
        return true;
    }

    /**
     * @throws FilesystemException
     */
    public function getRemoteFiles($location = '/'): array
    {
        try{
            if ($this->isGDrive()){
                return $this->getGoogleDriveFiles(location: $location,perPage: 1);
            }
            return StorageProvider::storage($this->getConfig())
                ->getDriver()
                ->listContents($location)
                ->toArray();
        }catch (\Exception $exception){
            return [];
        }
    }
}
