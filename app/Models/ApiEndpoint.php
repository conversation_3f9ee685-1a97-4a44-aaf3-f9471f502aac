<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class ApiEndpoint extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'url',
        'api_key',
        'active',
        'count',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'active' => 'boolean',
        'count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'api_key',
    ];

    /**
     * Scope a query to only include active endpoints.
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope a query to only include inactive endpoints.
     */
    public function scopeInactive($query)
    {
        return $query->where('active', false);
    }

    /**
     * Increment the count for this endpoint.
     */
    public function incrementCount(): void
    {
        $this->increment('count');
    }

    /**
     * Reset the count for this endpoint.
     */
    public function resetCount(): void
    {
        $this->update(['count' => 0]);
    }

    /**
     * Get the masked API key for display purposes.
     */
    protected function maskedApiKey(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->api_key ? substr($this->api_key, 0, 8) . '...' : null,
        );
    }

    /**
     * Get the status label.
     */
    protected function statusLabel(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->active ? 'Active' : 'Inactive',
        );
    }
}
