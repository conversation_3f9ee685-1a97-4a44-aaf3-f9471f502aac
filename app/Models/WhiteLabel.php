<?php

namespace App\Models;

use App\Enums\WhiteLabel\SubdomainSources;
use App\Enums\WhiteLabel\WhiteLabelStatus;
use App\Enums\XcloudBilling\BillingServices;
use App\Services\PaymentGateway\BillingServices\EnsurePaymentServiceProvided;
use App\Services\WhiteLabelService;
use App\Traits\MetaAccessors;
use App\Traits\WhiteLabelJsonAccessors;
use App\Traits\XcSoftDelete;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Laravel\Jetstream\HasProfilePhoto;
use ParagonIE\CipherSweet\EncryptedRow;
use Spatie\LaravelCipherSweet\Concerns\UsesCipherSweet;
use Spatie\LaravelCipherSweet\Contracts\CipherSweetEncrypted;
use Stripe\PaymentIntent;

class WhiteLabel extends Model implements EnsurePaymentServiceProvided, CipherSweetEncrypted
{
    use HasFactory, MetaAccessors, WhiteLabelJsonAccessors, XcSoftDelete, HasProfilePhoto, UsesCipherSweet;

    protected $casts = [
        'status' => WhiteLabelStatus::class,
        'sub_domain_source' => SubdomainSources::class,
        'payment_info' => 'array',
        'meta' => 'array',
        'billing_activated_from' => 'datetime',
        'settings' => 'array',
        'branding' => 'array',
        'billing_details' => 'array',
    ];

    protected $appends = [
        'brand_photo_url',
        'favicon_photo_url',
        'sub_domain_url',
        'url',
        'domain_name',
    ];

    const BRAND_PHOTO_COLORS = [
        [
            'text' => 'FFFFFF',
            'background' => 'F4DF4E'
        ],
        [
            'text' => 'FFFFFF',
            'background' => '078282'
        ],
        [
            'text' => 'FFFFFF',
            'background' => 'b5910d'
        ],
        [
            'text' => 'FFFFFF',
            'background' => '6E8898'
        ],
        [
            'text' => 'FFFFFF',
            'background' => 'A37B73'
        ],
        [
            'text' => 'FFFFFF',
            'background' => 'D34F73'
        ]
    ];

    protected $hidden = [
        'smtp_password',
    ];

    public function toArray()
    {
        $array = parent::toArray(); // Get the default array representation

        // Remove the nested key if it exists
        if (isset($array['settings']['smtp'])) {
            unset($array['settings']['smtp']);
        }

        return $array;
    }

    public function toArrayParent(): array
    {
      return parent::toArray(); // Get the default array representation
    }

    protected static function boot(): void
    {
        parent::boot();

        // When a white label is updated, we need to clear the cache for the host
        static::created(fn($whiteLabel) => WhiteLabelService::purgeCache($whiteLabel));
        static::updated(fn($whiteLabel) => WhiteLabelService::purgeCache($whiteLabel));
        static::deleted(fn($whiteLabel) => WhiteLabelService::purgeCache($whiteLabel));
    }

    public static function configureCipherSweet(EncryptedRow $encryptedRow): void
    {
        $encryptedRow->addField('smtp_password');
    }

    public function owner() : BelongsTo
    {
        return $this->belongsTo(Team::class, 'owner_team_id', 'id');
    }

    public function products() : HasMany
    {
        return $this->hasMany(Product::class);
    }

    public function activeProducts() : HasMany
    {
        return $this->hasMany(Product::class)->where('is_active', true);
    }

    public function packages() : HasMany
    {
        return $this->hasMany(Package::class);
    }

    public function teams() : HasMany
    {
        return $this->hasMany(Team::class);
    }

    public function users() : HasMany
    {
        return $this->hasMany(User::class);
    }

    public function servers() : HasManyThrough
    {
        return $this->hasManyThrough(Server::class, Team::class, 'white_label_id', 'team_id');
    }

    public function sites()
    {
        $whiteLabelServers = $this->servers()->get()->pluck('id');
        if(count($whiteLabelServers) > 0){
            return Site::whereIn('server_id', $whiteLabelServers->toArray());
        }

        return Site::where('id', 0);

        // $this->hasManyThrough(Site::class, Server::class, 'team_id', 'server_id');
    }

    public function clients() : HasManyThrough
    {
        return $this->hasManyThrough(User::class, Team::class, 'white_label_id', 'current_team_id');
    }

    public function activeClients() : HasManyThrough
    {
        return $this->hasManyThrough(User::class, Team::class, 'white_label_id', 'current_team_id')
            ->where('is_active', true);
    }

    public function bills() : HasManyThrough
    {
        return $this->hasManyThrough(Bill::class, Team::class);
    }

    public function invoices() : HasManyThrough
    {
        return $this->hasManyThrough(GeneralInvoice::class, Team::class);
    }

    public function bluePrints() : HasManyThrough
    {
        return $this->hasManyThrough(BluePrint::class, Team::class);
    }

    public function cartForms() : HasManyThrough
    {
        return $this->hasManyThrough(CartForm::class, Team::class);
    }

    public function paymentMethods() : HasManyThrough
    {
        return $this->hasManyThrough(PaymentMethod::class, Team::class);
    }

    public function getDefaultBillingService(): BillingServices
    {
        return BillingServices::WhiteLabelSubscription;
    }

    public function getPhotoPath($extension = 'jpg'): string
    {
        return "brand-photos/".$this->id."-".md5($this->owner_team_id).".".$extension;
    }

    public function getFaviconPhotoPath($extension = 'png'): string
    {
        return "favicon-photos/".$this->id."-".md5($this->owner_team_id).".".$extension;
    }

    public function getSubDomainUrlAttribute(): string
    {
        // Remove any leading/trailing slashes from both sub_domain and sub_domain_source
        $cleanSubDomain = trim($this->sub_domain, '/');
        $cleanSubDomainSource = $this->sub_domain_source ? trim($this->sub_domain_source->value, '/') : '';

        // Concatenate the sanitized sub_domain and sub_domain_source
        return insert_http($cleanSubDomain . '.' . $cleanSubDomainSource, config('white-label.use_ssl') ? 'https' : 'http');
    }

    public function getUrlAttribute(): string
    {
        return $this->domain ? insert_http($this->domain, config('white-label.use_ssl') ? 'https' : 'http') : $this->sub_domain_url;
    }

    public function getDomainNameAttribute(): string
    {
        return $this->domain ? $this->domain : without_http($this->sub_domain_url);
    }

    public function getBrandPhotoUrlAttribute(): string
    {
        return isset($this->branding['brand_photo_path'])
            ? Storage::disk($this->profilePhotoDisk())->url($this->branding['brand_photo_path'])
            : $this->defaultBrandPhotoUrl();
    }

    public function getFaviconPhotoUrlAttribute(): string
    {
        return isset($this->branding['favicon_photo_path'])
            ? Storage::disk($this->profilePhotoDisk())->url($this->branding['favicon_photo_path'])
            : $this->defaultBrandPhotoUrl();
    }

    public function getLandingPageNavbarPhotoUrlAttribute(): string
    {
        return Arr::get($this->settings, 'landing_page.photo')
            ? Storage::disk('s3')->url(Arr::get($this->settings, 'landing_page.photo'))
            : $this->brand_photo_url;
    }

    public function enabledLandingPage(): bool
    {
        return Arr::get($this->settings, 'landing_page.enable_landing_page', false);
    }

    protected function defaultBrandPhotoUrl(): string
    {
        $name = trim(collect(explode(' ', $this->name))->map(function ($segment) {
            return mb_substr($segment, 0, 1);
        })->join(' '));

        $index = $this->id % count(self::BRAND_PHOTO_COLORS);
        $color = self::BRAND_PHOTO_COLORS[$index];

        return 'https://ui-avatars.com/api/?name='.urlencode($name).'&color='.Arr::get($color, 'text').'&background='.Arr::get($color, 'background');
    }

    public function connectedAccount(): BelongsTo
    {
        return $this->belongsTo(ConnectedAccount::class);
    }

    public function isAccountConnected(): bool
    {
        $connectedAccount = $this->connectedAccount()->latest()->first();
        return $this->connectedAccount()->exists() && $connectedAccount->account_activated;
    }

    public function getBillingName(BillingServices $service): string
    {
        return $service->toReadableString();
    }

    public function getBillingShortDescriptionTitle(BillingServices $service): string
    {
        return $service->toReadableString();
    }

    public function getBillingShortDescription(BillingServices $service): string
    {
        return $service->toReadableString();
    }

    public function paymentProcessed(): bool
    {
        return $this->getPaymentInfo('payment_status') === PaymentIntent::STATUS_SUCCEEDED;
    }

    public function onboardingCompleted(): bool
    {
        return $this->status === WhiteLabelStatus::ACTIVE;
    }

    function getFullSubDomainAttribute(): string
    {
        return $this->sub_domain . '.' . $this->sub_domain_source->value;
    }

    function getActiveDomainAttribute()
    {
        return $this->domain ?: $this->full_sub_domain;
    }

    public function hasCustomSmtp(): bool
    {
        return Arr::get($this->settings, 'smtp.enable_smtp_settings', false);
    }

    static function getByHostName($host = null): WhiteLabel|null
    {
        if (is_null($host)) {
            $host = get_request_host();
        }

        // If the host is reserved for an application, return null
        if (SubdomainSources::isUsingReservedApplicationHosts($host)) {
            return null;
        }

        // invalidate all cache keys for the host so adding v2
        return cache()->remember('white_label:host:v2:'.$host, now()->addHour(), function () use ($host) {
            return WhiteLabel::where('domain', $host)
                ->orWhere('domain', remove_www($host))
                ->orWhere('domain', inject_www($host))
                ->orWhere('sub_domain', request_subdomain($host))
                ->first();
        });
    }

    public function serviceProvidedAfterPayment(BillingServices $billingService): bool
    {
        // TODO: Implement serviceProvidedAfterPayment() method.
    }

    public function provideService(BillingServices $billingService): bool
    {
        // TODO: Implement provideService() method.
    }

    public function cancelService(BillingServices $billingService): bool
    {
        // TODO: Implement cancelService() method.
    }
}
