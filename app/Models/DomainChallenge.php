<?php

namespace App\Models;

use App\Enums\DomainChallengeStatusEnum;
use App\Traits\MetaAccessors;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DomainChallenge extends Model
{
    use HasFactory, MetaAccessors;

    protected $guarded = ['id'];

    protected $casts = [
        'meta' => 'array',
        'status' => DomainChallengeStatusEnum::class
    ];

    public function markAsNew(): void
    {
        $this->update([
            'status' => DomainChallengeStatusEnum::NEW
        ]);
    }

    public function markAsProcessing(): void
    {
        $this->update([
            'status' => DomainChallengeStatusEnum::PROCESSING
        ]);
    }

    public function markAsCheckingDns(): void
    {
        $this->update([
            'status' => DomainChallengeStatusEnum::CHECKING_DNS
        ]);
    }

    public function markAsDnsPropagated(): void
    {
        $this->update([
            'status' => DomainChallengeStatusEnum::DNS_PROPAGATED
        ]);
    }

    public function markAsVerifying(): void
    {
        $this->update([
            'status' => DomainChallengeStatusEnum::VERIFYING
        ]);
    }

    public function markAsChallenged(): void
    {
        $this->update([
            'status' => DomainChallengeStatusEnum::CHALLENGED
        ]);
    }

    public function markAsFailed(): void
    {
        $this->update([
            'status' => DomainChallengeStatusEnum::FAILED
        ]);
    }

    public function markAsCompleted(): void
    {
        $this->update([
            'status' => DomainChallengeStatusEnum::COMPLETED
        ]);
    }

    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    public function sslCertificate(): BelongsTo
    {
        return $this->belongsTo(SslCertificate::class);
    }
}
