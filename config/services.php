<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],
    'mailchimp' => [
        'api_key' => env('MAILCHIMP_API_KEY'),
        'server' => env('MAILCHIMP_SERVER_PREFIX'),
        'list_id' => env('MAILCHIMP_LIST_ID'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'digitalocean' => [
        'token' => env('XCLOUD_DO_TOKEN'),
        'client_id' => env('DIGITALOCEAN_CLIENT_ID'),
        'client_secret' => env('DIGITALOCEAN_CLIENT_SECRET'),
        'image_id' => env('XCLOUD_DO_IMAGE_ID', '131906687'),
    ],
    'linode' => [
        'client_id' => env('LINODE_CLIENT_ID'),
        'client_secret' => env('LINODE_CLIENT_SECRET'),
    ],

    'github' => [
        'release_access_token' => env('GITHUB_RELEASE_ACCESS_TOKEN'),
        'release_cache_bust_token' => 'IPPQ-m98-Kzp-KgA-cCs-M7v',
    ],

    'white_label_subscription' => [
        [
            'name' => '25 Servers $25/mo',
            'flag' => '25-servers-25-mo',
            'description' => 'White Label Client can create 25 servers for $25/mo',
            'price' => 25,
            'unit' => 25,
            'renewal_type' => \App\Enums\XcloudBilling\BillRenewalPeriod::Monthly->value,
            'currency' => \App\Enums\XcloudBilling\BillingCurrency::USD->value,
            'show_on_display' => true,
            'service_type' => \App\Enums\XcloudBilling\BillingServices::WhiteLabelSubscription->value,
        ],
        [
            'name' => '60 Servers $50/mo',
            'flag' => '60-servers-50-mo',
            'description' => 'White Label Client can create 50 servers for $50/mo',
            'price' => 50,
            'unit' => 60,
            'renewal_type' => \App\Enums\XcloudBilling\BillRenewalPeriod::Monthly->value,
            'currency' => \App\Enums\XcloudBilling\BillingCurrency::USD->value,
            'show_on_display' => true,
            'service_type' => \App\Enums\XcloudBilling\BillingServices::WhiteLabelSubscription->value,
        ],
        [
            'name' => '150 Servers $100/mo',
            'flag' => '150-servers-100-mo',
            'description' => 'White Label Client can create 100 servers for $100/mo',
            'price' => 100,
            'unit' => 150,
            'renewal_type' => \App\Enums\XcloudBilling\BillRenewalPeriod::Monthly->value,
            'currency' => \App\Enums\XcloudBilling\BillingCurrency::USD->value,
            'show_on_display' => true,
            'service_type' => \App\Enums\XcloudBilling\BillingServices::WhiteLabelSubscription->value,
        ],
        [
            'name' => '400 Servers $250/mo',
            'flag' => '400-servers-250-mo',
            'description' => 'White Label Client can create 200 servers for $250/mo',
            'price' => 250,
            'unit' => 400,
            'renewal_type' => \App\Enums\XcloudBilling\BillRenewalPeriod::Monthly->value,
            'currency' => \App\Enums\XcloudBilling\BillingCurrency::USD->value,
            'show_on_display' => true,
            'service_type' => \App\Enums\XcloudBilling\BillingServices::WhiteLabelSubscription->value,
        ]
    ],

    'xvultr' => [
        'token' => env('XCLOUD_VULTR_TOKEN'),
        'snapshot_id' => env('XCLOUD_VULTR_SNAPSHOT_ID'),
        'default_ssh_id' => env('XCLOUD_VULTR_DEFAULT_SSH_ID'),
        'supported_machines' => [
            'general' => [
                [
                    'slug' => 'vc2-1c-1gb',
                    'price' => 5.00,
                    'title' => "Newcomer",
                    'tag' => 'Newcomer',
                    'tooltip' => '10-15 static cached brochure sites / 1 small low traffic Woo site.',
                    'description' => '10-15 static cached brochure sites / 1 small low traffic Woo site.',
                ],
                [
                    'slug' => 'vc2-1c-2gb',
                    'price' => 12.00,
                    'title' => "Basic",
                    'tag' => 'Basic',
                    'tooltip' => '20-30 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '20-30 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vc2-2c-4gb',
                    'price' => 24.00,
                    'title' => "Standard",
                    'tag' => 'Standard',
                    'tooltip' => '40-60 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '40-60 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vc2-4c-8gb',
                    'price' => 45.00,
                    'title' => "Professional",
                    'tag' => 'Professional',
                    'tooltip' => '80-120 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '80-120 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vc2-6c-16gb',
                    'price' => 88.00,
                    'title' => "Business",
                    'tag' => 'Business',
                    'tooltip' => '160-240 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '160-240 static cached brochure sites / 1 medium traffic Woo site.',
                ]
            ],
            'premium' => [
                [
                    'slug' => 'vhf-1c-1gb',
                    'price' => 7.5,
                    'title' => "Lite",
                    'tag' => 'Lite',
                    'tooltip' => '10-15 static cached brochure sites / 1 small low traffic Woo site.',
                    'description' => '10-15 static cached brochure sites / 1 small low traffic Woo site.',
                ],
                [
                    'slug' => 'vhf-2c-2gb',
                    'price' => 20.00,
                    'title' => "Personal",
                    'tag' => 'Personal',
                    'tooltip' => '20-30 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '20-30 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vhf-2c-4gb',
                    'price' => 30.00,
                    'title' => "Growing",
                    'tag' => 'Growing',
                    'tooltip' => '40-60 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '40-60 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vhf-3c-8gb',
                    'price' => 50.00,
                    'title' => "Elite",
                    'tag' => 'Elite',
                    'tooltip' => '80-120 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '80-120 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vhf-4c-16gb',
                    'price' => 100.00,
                    'title' => "Titanium",
                    'tag' => 'Titanium',
                    'tooltip' => '160-240 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '160-240 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vhf-8c-32gb',
                    'price' => 200.00,
                    'title' => "Ultimate",
                    'tag' => 'Ultimate',
                    'tooltip' => '320-480 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '320-480 static cached brochure sites / 1 medium traffic Woo site.',
                ]
            ]
        ]
    ],

    'xvultr_provider' => [
        'token' => env('XCLOUD_VULTR_TOKEN'),
        'snapshot_id' => env('XCLOUD_VULTR_SNAPSHOT_ID'),
        'default_ssh_id' => env('XCLOUD_VULTR_DEFAULT_SSH_ID'),
        'supported_machines' => [
            'general' => [
                [
                    'slug' => 'vc2-1c-1gb',
                    'price' => 5.00,
                    'title' => "Newcomer",
                    'tag' => 'Newcomer',
                    'tooltip' => '10-15 static cached brochure sites / 1 small low traffic Woo site.',
                    'description' => '10-15 static cached brochure sites / 1 small low traffic Woo site.',
                ],
                [
                    'slug' => 'vc2-1c-2gb',
                    'price' => 12.00,
                    'title' => "Basic",
                    'tag' => 'Basic',
                    'tooltip' => '20-30 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '20-30 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vc2-2c-4gb',
                    'price' => 24.00,
                    'title' => "Standard",
                    'tag' => 'Standard',
                    'tooltip' => '40-60 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '40-60 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vc2-4c-8gb',
                    'price' => 45.00,
                    'title' => "Professional",
                    'tag' => 'Professional',
                    'tooltip' => '80-120 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '80-120 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vc2-6c-16gb',
                    'price' => 88.00,
                    'title' => "Business",
                    'tag' => 'Business',
                    'tooltip' => '160-240 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '160-240 static cached brochure sites / 1 medium traffic Woo site.',
                ]
            ],
            'premium' => [
                [
                    'slug' => 'vhf-1c-1gb',
                    'price' => 7.5,
                    'title' => "Lite",
                    'tag' => 'Lite',
                    'tooltip' => '10-15 static cached brochure sites / 1 small low traffic Woo site.',
                    'description' => '10-15 static cached brochure sites / 1 small low traffic Woo site.',
                ],
                [
                    'slug' => 'vhf-2c-2gb',
                    'price' => 20.00,
                    'title' => "Personal",
                    'tag' => 'Personal',
                    'tooltip' => '20-30 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '20-30 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vhf-2c-4gb',
                    'price' => 30.00,
                    'title' => "Growing",
                    'tag' => 'Growing',
                    'tooltip' => '40-60 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '40-60 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vhf-3c-8gb',
                    'price' => 50.00,
                    'title' => "Elite",
                    'tag' => 'Elite',
                    'tooltip' => '80-120 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '80-120 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vhf-4c-16gb',
                    'price' => 100.00,
                    'title' => "Titanium",
                    'tag' => 'Titanium',
                    'tooltip' => '160-240 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '160-240 static cached brochure sites / 1 medium traffic Woo site.',
                ],
                [
                    'slug' => 'vhf-8c-32gb',
                    'price' => 200.00,
                    'title' => "Ultimate",
                    'tag' => 'Ultimate',
                    'tooltip' => '320-480 static cached brochure sites / 1 medium traffic Woo site.',
                    'description' => '320-480 static cached brochure sites / 1 medium traffic Woo site.',
                ]
            ]
        ]
    ],
    'lifetime_packages' => [
        [
            'title' => "Lifetime bundle 1",
            'slug' => 'lifetime-bundle-1',
            'servers' => 5,
            'sites' => "Unlimited",
            'support' => "Lifetime",
            'features' => 'All',
            'price' => 599,
        ],
        [
            'title' => "Lifetime bundle 2",
            'slug' => 'lifetime-bundle-2',
            'servers' => 10,
            'sites' => "Unlimited",
            'support' => "Lifetime",
            'features' => 'All',
            'price' => 999,
        ],
        [
            'title' => "Lifetime bundle 3",
            'slug' => 'lifetime-bundle-3',
            'servers' => 20,
            'sites' => "Unlimited",
            'support' => "Lifetime",
            'features' => 'All',
            'price' => 1999,
        ],
        [
            'title' => "Free Plan",
            'slug' => 'free-trial',
            'servers' => 1,
            'sites' => 1,
            'support' => "Lifetime",
            'features' => 'All',
            'price' => 0,
        ]
    ],
    'gcp' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
    ],

    'xcloud_cloud_provider' => env('XCLOUD_CLOUD_PROVIDER', \App\Enums\XCloudCloudProviders::DIGITAL_OCEAN->value), // DO, Vultr

    'xcloud_default_database_password' => env('XCLOUD_CLOUD_PROVIDER') == \App\Enums\XCloudCloudProviders::DIGITAL_OCEAN->value
        ? env('XCLOUD_DO_DEFAULT_DATABASE_PASSWORD')
        : env('XCLOUD_VULTR_DEFAULT_DATABASE_PASSWORD'),

    'cloudflare' => [
        'dns' => [
            'token' => env('CLOUDFLARE_DNS_UPDATE_TOKEN'),
            'zone_id' => env('CLOUDFLARE_DNS_ZONE_ID'),
            'account_id' => env('CLOUDFLARE_DNS_ACCOUNT_ID'),
        ]
    ],
    'cloudflare_updated' => [
        'active' => env('ACTIVE_DOMAIN'),
        'active_playground' => env('ACTIVE_PLAYGROUND_DOMAIN'),
        'account_email' => env('CLOUDFLARE_ACCOUNT_EMAIL', '<EMAIL>'),
        'default' => [
            'dns' => [
                'token' => env('WP1_CLOUDFLARE_DNS_UPDATE_TOKEN', env('CLOUDFLARE_DNS_UPDATE_TOKEN')),
                'zone_id' => env('WP1_CLOUDFLARE_DNS_ZONE_ID'),
                'account_id' => env('WP1_CLOUDFLARE_DNS_ACCOUNT_ID', env('CLOUDFLARE_DNS_ACCOUNT_ID')),
            ]
        ],
        'xcloud_app' => [
            'dns' => [
                'token' => env('XCLOUD_APP_CLOUDFLARE_DNS_UPDATE_TOKEN', env('CLOUDFLARE_DNS_UPDATE_TOKEN')),
                'zone_id' => env('XCLOUD_APP_CLOUDFLARE_DNS_ZONE_ID'),
                'account_id' => env('XCLOUD_APP_CLOUDFLARE_DNS_ACCOUNT_ID', env('CLOUDFLARE_DNS_ACCOUNT_ID')),
            ],
        ],
        'xcloud_name' => [
            'dns' => [
                'token' => env('XCLOUD_NAME_CLOUDFLARE_DNS_UPDATE_TOKEN', env('CLOUDFLARE_DNS_UPDATE_TOKEN')),
                'zone_id' => env('XCLOUD_NAME_CLOUDFLARE_DNS_ZONE_ID'),
                'account_id' => env('XCLOUD_NAME_CLOUDFLARE_DNS_ACCOUNT_ID', env('CLOUDFLARE_DNS_ACCOUNT_ID')),
            ],
        ],
        'wp1_site' => [
            'dns' => [
                'token' => env('WP1_SITE_CLOUDFLARE_DNS_UPDATE_TOKEN', env('CLOUDFLARE_DNS_UPDATE_TOKEN')),
                'zone_id' => env('WP1_SITE_CLOUDFLARE_DNS_ZONE_ID'),
                'account_id' => env('WP1_SITE_CLOUDFLARE_DNS_ACCOUNT_ID', env('CLOUDFLARE_DNS_ACCOUNT_ID')),
            ],
        ],
        'wp1sh_site' => [
            'dns' => [
                'token' => env('WP1SH_SITE_CLOUDFLARE_DNS_UPDATE_TOKEN', env('CLOUDFLARE_DNS_UPDATE_TOKEN')),
                'zone_id' => env('WP1SH_SITE_CLOUDFLARE_DNS_ZONE_ID'),
                'account_id' => env('WP1SH_SITE_CLOUDFLARE_DNS_ACCOUNT_ID', env('CLOUDFLARE_DNS_ACCOUNT_ID')),
            ],
        ],
        'tryxcloud_cc' => [
            'dns' => [
                'token' => env('TRYXCLOUD_CC_CLOUDFLARE_DNS_UPDATE_TOKEN', env('CLOUDFLARE_DNS_UPDATE_TOKEN')),
                'zone_id' => env('TRYXCLOUD_CC_CLOUDFLARE_DNS_ZONE_ID'),
                'account_id' => env('TRYXCLOUD_CC_CLOUDFLARE_DNS_ACCOUNT_ID', env('CLOUDFLARE_DNS_ACCOUNT_ID')),
            ],
        ],
        'wp1_host' => [
            'dns' => [
                'token' => env('WP1_HOST_CLOUDFLARE_DNS_UPDATE_TOKEN', env('CLOUDFLARE_DNS_UPDATE_TOKEN')),
                'zone_id' => env('WP1_HOST_CLOUDFLARE_DNS_ZONE_ID'),
                'account_id' => env('WP1_HOST_CLOUDFLARE_DNS_ACCOUNT_ID', env('CLOUDFLARE_DNS_ACCOUNT_ID')),
            ],
        ],
        'onewp_site' => [
            'dns' => [
                'token' => env('ONE_WP_SITE_CLOUDFLARE_DNS_UPDATE_TOKEN', env('CLOUDFLARE_DNS_UPDATE_TOKEN')),
                'zone_id' => env('ONE_WP_SITE_CLOUDFLARE_DNS_ZONE_ID'),
                'account_id' => env('ONE_WP_SITE_CLOUDFLARE_DNS_ACCOUNT_ID', env('CLOUDFLARE_DNS_ACCOUNT_ID')),
            ],
        ],
        'xsql_app' => [
            'dns' => [
                'token' => env('XSQL_APP_CLOUDFLARE_DNS_UPDATE_TOKEN', env('CLOUDFLARE_DNS_UPDATE_TOKEN')),
                'zone_id' => env('XSQL_APP_CLOUDFLARE_DNS_ZONE_ID'),
                'account_id' => env('XSQL_APP_CLOUDFLARE_DNS_ACCOUNT_ID', env('CLOUDFLARE_DNS_ACCOUNT_ID')),
            ],
        ],
    ],
    'stripe' => [
        'publish_key' => env('STRIPE_PUBLISH_KEY'),
        'secret_key' => env('STRIPE_SECRET_KEY'),
        'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
        'connect_client_id' => env('STRIPE_CONNECT_CLIENT_ID'),
    ],
    'first_promoter' => [
        'api_key' => env('FIRST_PROMOTER_API_KEY'),
    ],
    'elastic_email' => [
        'api_key' => env('ELASTIC_EMAIL_API_KEY'),
        'staging_domain' => env('ELASTIC_EMAIL_STAGING_DOMAIN'),
        'production_domain' => env('ELASTIC_EMAIL_PRODUCTION_DOMAIN'),
    ],
    'split_payment' => [
        'api_secret' => env('SPLIT_PAYMENT_API_SECRET'),
    ],
    'slack' => [
        'oauth_token' =>  env('SLACK_BOT_USER_OAUTH_TOKEN'),
        'post_message_url' => env('SLACK_POST_MESSAGE_URL', 'https://slack.com/api/chat.postMessage'),
        'channel_id' => [
            'daily_statistics' => env('SLACK_DAILY_STAT_CHANNEL_ID'),
            'data_on_demand' => env('SLACK_DATA_ON_DEMAND_CHANNEL_ID', 'C06G90HRTLP'),
            'invoice_inspector' => env('SLACK_INVOICE_INSPECTOR_CHANNEL_ID', 'C06H08V0K1B'),
        ]
    ],
    'google_drive' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect_uri' => env('GOOGLE_REDIRECT_URI'),
    ],
    'pcloud' => [
        'client_id' => env('PCLOUD_CLIENT_ID'),
        'client_secret' => env('PCLOUD_CLIENT_SECRET'),
        'redirect_uri' => env('PCLOUD_REDIRECT_URI'),
    ],
    'chatgpt' => [
        'api_key' => env('CHATGPT_API_KEY'),
    ],
    'patchstack' => [
        'api_key' => env('PATCHSTACK_API_KEY'),
    ]
];
